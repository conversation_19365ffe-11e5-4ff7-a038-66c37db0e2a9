const { defineConfig } = require("@vue/cli-service");
const { isAirSchool } = require("./src/type");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");

module.exports = defineConfig({
  configureWebpack: {
    plugins: [new NodePolyfillPlugin()],
    resolve: {
      fallback: {
        child_process: false,
        fs: false
      }
    }
  },
  transpileDependencies: true,
  lintOnSave: false,
  publicPath: process.env.NODE_ENV === "production" ? "./" : "/",
  pluginOptions: {
    electronBuilder: {
      customFileProtocol: "./",
      nodeIntegration: true,
      enableRemoteModule: true,
      builderOptions: {
        appId: isAirSchool() ? "com.goelfin.school" : "com.elf-go.HigoApp",
        // ===== 公证相关 ========start
        afterSign: "notarize.js",
        // ===== 公证相关 ========end
        // asar打包
        asar: false,
        productName: isAirSchool() ? "聂卫平围棋网校" : "聂卫平围棋", // 项目名，也是生成的安装文件名，即mzDemo.exe
        copyright: "Copyright © 2019-2023 北京弈友围棋文化传播有限责任公司", // 版权信息
        buildVersion: "270",
        files: ["./**/*"],
        extraFiles: [
          // 把指定的资源复制到程序根目录，即把server文件夹的内容复制到程序根目录，这里server文件夹下的内容相当于我的后台，我在background.js中有相应的处理。
          "./server"
        ],
        directories: {
          output: "./app_dist" // 输出文件路径
        },
        mac: {
          // ===== 公证相关 ========start
          hardenedRuntime: true,
          gatekeeperAssess: false, // sign
          identity: "eStarGo Co.Ltd (QCFW6P26AQ)",
          entitlements: "entitlements.mac.plist",
          entitlementsInherit: "entitlements.mac.plist",
          // ===== 公证相关 ========end
          icon: isAirSchool() ? "./public/icon1.icns" : "./public/icon.icns",
          extendInfo: {
            ElectronTeamID: "QCFW6P26AQ",
            ITSAppUsesNonExemptEncryption: false,
            NSMicrophoneUsageDescription: "请允许本程序访问您的麦克风",
            NSCameraUsageDescription: "请允许本程序访问您的摄像头",
          },
          artifactName: "${productName}-${platform}-${arch}-${version}.${ext}",
          target: [
            "dmg",
            "zip", // 这里注意更新的时候，mac只认zip格式的包
            {
              target: "mas",
              arch: "x64"
            }

            // 要打的包的格式类型设置
          ]
        },
        win: {
          // win相关配置
          icon: isAirSchool()
            ? "./public/favicon.ico"
            : "./public/favicon1.ico", // 图标，当前图标在根目录下，注意这里有两个坑
          // requestedExecutionLevel: "requireAdministrator", //获取管理员权限
          artifactName: "${productName}-${platform}-${arch}-${version}.${ext}",
          target: [
            {
              target: "nsis", // 利用nsis制作安装程序
              arch: [
                "x64", // 64位
                "ia32"
              ]
            }
          ]
        },
        mas: {
          hardenedRuntime: false,
          gatekeeperAssess: false,
          provisioningProfile: "MacHiGoAPP.provisionprofile",
          entitlements: "entitlements.mas.plist",
          entitlementsInherit: "entitlements.mas.inherit.plist",
          entitlementsLoginHelper: "entitlements.mas.loginhelper.plist",
          gatekeeperAssess: false,
          asarUnpack: []
        },
        nsis: {
          oneClick: false, // 是否一键安装
          allowElevation: true, // 允许请求提升。 如果为false，则用户必须使用提升的权限重新启动安装程序。
          allowToChangeInstallationDirectory: true, // 允许修改安装目录
          perMachine: true,
          installerIcon: isAirSchool()
            ? "./public/favicon.ico"
            : "./public/favicon1.ico", // 安装图标
          uninstallerIcon: isAirSchool()
            ? "./public/favicon.ico"
            : "./public/favicon1.ico", // 卸载图标
          installerHeaderIcon: isAirSchool()
            ? "./public/favicon.ico"
            : "./public/favicon1.ico", // 安装时头部图标
          createDesktopShortcut: true, // 创建桌面图标
          createStartMenuShortcut: true, // 创建开始菜单图标
          shortcutName: isAirSchool() ? "聂卫平围棋网校" : "聂卫平围棋" // 图标名称(项目名称)
        }
      }
    }
  }
});
