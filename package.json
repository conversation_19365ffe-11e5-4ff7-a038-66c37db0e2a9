{"name": "name_default", "productName": "聂卫平围棋", "version": "3.0.9", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron:build": "vue-cli-service electron:build", "electron:build:win32": "vue-cli-service electron:build --win --ia32", "electron:build:win64": "vue-cli-service electron:build --win --x64", "electron:serve": "vue-cli-service electron:serve", "electron:serve1": "vue-cli-service electron:serve", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "main": "background.js", "dependencies": {"@rive-app/canvas": "^1.2.1", "alife-logger": "^1.8.30", "await-lock": "^2.2.2", "axios": "^1.2.2", "baidu-aip-sdk": "^4.16.10", "browserify-zlib": "^0.2.0", "core-js": "^3.8.3", "easy-circular-progress": "^1.0.4", "echarts": "^5.4.1", "electron-notarize": "^1.2.2", "electron-updater": "^5.3.0", "html2canvas": "^1.4.1", "js-sha256": "^0.9.0", "mint-ui": "^2.2.13", "moment": "^2.29.4", "net": "^1.0.2", "node-polyfill-webpack-plugin": "^2.0.1", "ping": "^0.4.3", "speak-tts": "^2.0.8", "swiper": "^5.4.5", "vant": "^4.0.7", "vue": "^2.6.14", "vue-aliplayer": "^1.0.0", "vue-aliplayer-v2": "^1.3.0", "vue-awesome-swiper": "^3.1.1", "vue-clipboards": "^1.3.0", "vue-infinite-loading": "^2.4.5", "vue-router": "^2.8.1", "vuex": "^4.1.0", "websocket-heartbeat-js": "^1.1.3", "trtc-js-sdk": "^4.15.20", "trtc-sdk-v5": "5.8.3", "@tencentcloud/chat": "^3.3.5", "@tencentcloud/chat-uikit-vue": "^2.0.3", "tim-profanity-filter-plugin": "^1.1.0", "tim-upload-plugin": "^1.3.0", "rtc-ai-denoiser": "^1.1.7", "vue-drag-resize": "^1.5.4", "vue-seamless-scroll": "^1.1.23", "sass": "^1.52.2", "sass-loader": "^13.0.0", "element-ui": "^2.15.14"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "electron": "^13.0.0", "electron-devtools-installer": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.3", "less-loader": "^11.1.0", "postcss-px-to-viewport": "^1.1.1", "vue-cli-plugin-electron-builder": "~2.1.1", "vue-devtools": "^5.1.4", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}