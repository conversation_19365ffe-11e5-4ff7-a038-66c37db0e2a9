module.exports = {
  isAirSchool: function () {
    return false;
  }
};
// 1.修改对应版本号 修改项目中其他isAirSchool,index.html替换图标
// 2.替换entitlements.mas.plist文件
// 3.替换.provisionprofile文件
// 4.脚本文件改名字
// 5.查看生产还是测试环境
// 6.查看node_modules/app-builder-lib/out/macPackager.js最后一行
// exports.default = MacPackager;
// function getCertificateTypes(isMas, isDevelopment) {
//     if (isDevelopment) {
//         return isMas ? ["Mac Developer", "Apple Development"] : ["Mac Developer","Developer ID Application"];
//     }
//     return isMas ? ["3rd Party Mac Developer Application", "Apple Distribution"] : ["Developer ID Application"];
// }
