<template>
  <transition name="van-fade">
    <div class="lose-dialog_wrap" v-if="lose_dislog_show">
      <div
        class="lose-dialog"
        :style="{
          'background-image': `url(${require('@/assets/game/对局失败弹窗.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <div class="lose-people_wrap">
          <div class="lose-people lose-black_people">
            <div
              class="lose-user_img"
              :style="{
                'background-image': 'url(' + status['black_user_avatar'] + ')',
                'background-size': '100% 100%'
              }"
            >
              <div class="lose-heizi"></div>
            </div>
            <p class="lose-user_name jcyt600">
              {{
                status["black_user_nick_name"] ??
                status["black_user_actual_name"]
              }}
            </p>

            <div class="lose-time jcyt500">
              {{
                lose_reason == "captured"
                  ? `提 ${black_captured} 子`
                  : lose_reason == "resign"
                  ? win_side == "black"
                    ? "中盘胜"
                    : "中盘负"
                  : lose_reason == "time_out"
                  ? win_side == "black"
                    ? "胜利"
                    : "超时负"
                  : lose_reason == "area_score"
                  ? ` ${black_score}子`
                  : lose_reason == "withdraw"
                  ? win_side == "black"
                    ? "胜利"
                    : "弃权负"
                  : ""
              }}
            </div>
          </div>
          <div
            class="lose-center_result"
            :class="
              win_side == 'black' || win_side == 'white'||win_side == 'withdraw'? 'default_height':''
            "
            :style="{
              'background-image':
                win_side == 'black'
                  ? `url(${require('@/assets/game/黑胜文字.png')})`
                  : win_side == 'white'
                  ? `url(${require('@/assets/game/白胜文字.png')})`
                  : win_side == 'withdraw'
                  ? `url(${require('@/assets/game/双方弃权.png')})`
                  : `url(${require('@/assets/game/和棋.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
          <div class="lose-people lose-white_people">
            <div
              class="lose-user_img"
              :style="{
                'background-image': 'url(' + status['white_user_avatar'] + ')',
                'background-size': '100% 100%'
              }"
            >
              <div class="lose-baizi"></div>
            </div>
            <p class="lose-user_name jcyt600">
              {{
                status["white_user_nick_name"] ??
                status["black_user_actual_name"]
              }}
            </p>
            <div class="lose-time jcyt500">
              {{
                lose_reason == "captured"
                  ? `提 ${white_captured} 子`
                  : lose_reason == "resign"
                  ? win_side == "white"
                    ? "中盘胜"
                    : "中盘负"
                  : lose_reason == "time_out"
                  ? win_side == "white"
                    ? "胜利"
                    : "超时负"
                  : lose_reason == "area_score"
                  ? ` ${white_score}子`
                  : lose_reason == "withdraw"
                  ? win_side == "white"
                    ? "胜利"
                    : "弃权负"
                  : ""
              }}
            </div>
          </div>
        </div>
        <div
          class="button_1 jcyt600"
          @click="$emit('checkReport')"
          :class="can_go_button ? '' : 'dis_button'"
          v-if="
            eInfo &&
            (eInfo?.match_status == 'not_pass' ||
              eInfo?.match_status == 'is_pass') &&
            type == 'unitest'
          "
        >
          查看报告
        </div>
        <div
          class="button_2 jcyt600"
          @click="$emit('continueGame')"
          :class="can_go_button ? '' : 'dis_button'"
          v-else-if="
            eInfo &&
            (!eInfo?.game_history[gameIndex - 1].is_last ?? false) &&
            type == 'unitest'
          "
        >
          继续对弈
        </div>
        <div
          class="button"
          :style="{
            'background-image': `url(${require('@/assets/game/返回button.png')})`,
            'background-size': '100% 100%'
          }"
          @click="close()"
          v-else-if="type != 'unitest'"
        ></div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: "loseDialog",
  data() {
    return {
      lose_dislog_show: false
    };
  },
  props: {
    status: {},
    gameIndex: {},
    eInfo: {},
    win_side: {},
    lose_reason: {},
    white_captured: {},
    black_captured: {},
    game_end: {},
    black_score: {},
    white_score: {},
    type: {},
    can_go_button: {
      default: true
    }
  },
  watch: {},
  methods: {
    open() {
      this.lose_dislog_show = true;
    },
    close() {
      this.lose_dislog_show = false;
    }
  }
};
</script>
<style scoped lang="less">
.lose-dialog_wrap {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  position: fixed;
  z-index: 20001;
  top:0;
}
.lose-dialog {
  width: 776px;
  height: 800px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -388px;
  margin-top: -400px;

  .lose-people_wrap {
    width: 560px;
    height: 248px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    margin: 360px auto 0;
    .lose-people {
      width: 184px;
      height: 248px;

      .lose-user_img {
        width: 136px;
        height: 136px;
        border-radius: 50%;
        margin: 0 auto;
        position: relative;
        border: 6.48px solid #ffffff;
        box-sizing: border-box;
        background: #ffffff;
        box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.1);

        .lose-heizi {
          width: 45.33px;
          height: 45.33px;
          border-radius: 50%;
          position: absolute;
          background: url("@/assets/game/black.png");
          background-size: cover;
          right: -8px;
          top: -8px;
        }
        .lose-baizi {
          width: 45.33px;
          height: 45.33px;
          border-radius: 50%;
          position: absolute;
          background: url("@/assets/game/white.png");
          background-size: cover;
          left: -8px;
          top: -8px;
        }
      }
      .lose-user_name {
        font-size: 32px;
        color: #333333;
        text-align: center;
        margin: 8px auto;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .lose-time {
        width: 184px;
        height: 56px;
        background: #ffe9c8;
        border-radius: 28.8px;
        font-size: 30px;
        color: #c87e18;
        letter-spacing: 0;
        text-align: center;
        line-height: 56px;
      }
      // .lose-captured {
      //   font-size: 28px;
      //   color: #ffffff;
      //   letter-spacing: 0;
      //   text-align: center;
      // }
    }
    .lose-black_people {
      margin-right: 32px;
    }
    .lose-center_result {
      width: 128px;
      
      margin-top: 50px;
    }
    .default_height {
      height: 74px;
    }
    .lose-white_people {
      margin-left: 32px;
    }
  }
  .button {
    width: 320px;
    height: 90px;

    // box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
    border-radius: 40px;
    margin: 48px auto;
    cursor: pointer;
  }
  .button_1 {
    width: 256px;
    height: 80px;
    background-image: linear-gradient(180deg, #fe8a3a 0%, #fd5b25 100%);
    border: 8px solid #ffffff;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1), inset 0 -6px 0 0 #f14b0e;
    border-radius: 40px;
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    line-height: 66px;
    text-shadow: 0 4px 4px #f14b0e;
    margin: 48px auto;
    cursor: pointer;
    box-sizing: border-box;
  }
  .button_2 {
    width: 256px;
    height: 80px;
    background-image: linear-gradient(180deg, #2fceff 0%, #2da7ff 100%);
    border: 8px solid #ffffff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #149eee;
    border-radius: 40px;
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    line-height: 66px;
    text-shadow: 0 4px 4px rgba(20, 158, 238, 0.85);
    margin: 48px auto;
    cursor: pointer;
    box-sizing: border-box;
  }
}
</style>
