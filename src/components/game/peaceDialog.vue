<template>
  <transition name="van-fade">
    <div class="peace-dialog_wrap" v-if="peace_dislog_show">
      <div
        class="win-dialog"
        :style="{
          'background-image': `url(${require('@/assets/game/和棋弹窗.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <div class="win-people_wrap">
          <div class="win-people win-black_people">
            <div
              class="win-user_img"
              :style="{
                'background-image': 'url(' + status['black_user_avatar'] + ')',
                'background-size': '100% 100%'
              }"
            >
              <div class="win-heizi"></div>
            </div>
            <p class="win-user_name jcyt600">
              {{
                status["black_user_nick_name"] ??
                status["black_user_actual_name"]
              }}
            </p>
            <div class="win-time jcyt500">
              {{ `提 ${black_captured} 子` }}
            </div>
          </div>
          <div class="win-center_result"></div>
          <div class="win-people win-white_people">
            <div
              class="win-user_img"
              :style="{
                'background-image': 'url(' + status['white_user_avatar'] + ')',
                'background-size': '100% 100%'
              }"
            >
              <div class="win-baizi"></div>
            </div>
            <p class="win-user_name jcyt600">
              {{
                status["white_user_nick_name"] ??
                status["white_user_actual_name"]
              }}
            </p>
            <div class="win-time jcyt500">
              {{ `提 ${white_captured} 子` }}
            </div>
          </div>
        </div>

        <div
          class="button_1 jcyt600"
          @click="$emit('checkReport')"
          :class="can_go_button ? '' : 'dis_button'"

          v-if="
            eInfo &&
            (eInfo?.match_status == 'not_pass' ||
              eInfo?.match_status == 'is_pass') &&
            type == 'unitest'
          "
        >
          查看报告
        </div>
        <div
          class="button_2 jcyt600"
          @click="$emit('continueGame')"
          :class="can_go_button ? '' : 'dis_button'"

          v-else-if="
            eInfo &&
            (!eInfo?.game_history[gameIndex - 1].is_last ?? false) &&
            type == 'unitest'
          "
        >
          继续对弈
        </div>
        <div
          class="button"
          :style="{
            'background-image': `url(${require('@/assets/game/返回button.png')})`,
            'background-size': '100% 100%'
          }"
          @click="close()"
          v-else-if="type != 'unitest'"
        ></div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: "winDialog",
  data() {
    return {
      peace_dislog_show: false
    };
  },
  props: {
    status: {},
    gameIndex: {},
    eInfo: {},
    win_side: {},
    lose_reason: {},
    white_captured: {},
    black_captured: {},
    game_end: {},
    black_score: {},
    white_score: {},
    type: {},
    can_go_button: {
      default: true
    }
  },
  watch: {},
  methods: {
    open() {
      this.peace_dislog_show = true;
    },
    close() {
      this.peace_dislog_show = false;
    }
  }
};
</script>
<style scoped lang="less">
.peace-dialog_wrap {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  position: fixed;
  z-index: 20001;
  top:0;
}
.win-dialog {
  width: 776px;
  height: 800px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -388px;
  margin-top: -400px;

  .win-people_wrap {
    width: 560px;
    height: 248px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    margin: 360px auto 0;
    .win-people {
      width: 184px;
      height: 248px;

      .win-user_img {
        width: 136px;
        height: 136px;
        border-radius: 50%;
        margin: 0 auto;
        position: relative;
        border: 6.48px solid #ffffff;
        box-sizing: border-box;
        background: #ffffff;
        box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.1);

        .win-heizi {
          width: 45.33px;
          height: 45.33px;
          border-radius: 50%;
          position: absolute;
          background: url("@/assets/game/black.png");
          background-size: cover;
          right: -8px;
          top: -8px;
        }
        .win-baizi {
          width: 45.33px;
          height: 45.33px;
          border-radius: 50%;
          position: absolute;
          background: url("@/assets/game/white.png");
          background-size: cover;
          left: -8px;
          top: -8px;
        }
      }
      .win-user_name {
        font-size: 32px;
        color: #333333;
        text-align: center;
        margin: 8px auto;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .win-time {
        width: 184px;
        height: 56px;
        background: #ffe9c8;
        border-radius: 28.8px;
        font-size: 30px;
        color: #c87e18;
        letter-spacing: 0;
        text-align: center;
        line-height: 56px;
      }
      // .win-captured {
      //   font-size: 28px;
      //   color: #ffffff;
      //   letter-spacing: 0;
      //   text-align: center;
      // }
    }
    .win-black_people {
      margin-right: 32px;
    }
    .win-center_result {
      width: 128px;
      margin-top: 50px;
    }
    .win-white_people {
      margin-left: 32px;
    }
  }
  .button {
    width: 320px;
    height: 90px;

    // box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
    border-radius: 40px;
    margin: 48px auto;
    cursor: pointer;
  }
  .button_1 {
    width: 256px;
    height: 80px;
    background-image: linear-gradient(180deg, #fe8a3a 0%, #fd5b25 100%);
    border: 8px solid #ffffff;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1), inset 0 -6px 0 0 #f14b0e;
    border-radius: 40px;
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    line-height: 66px;
    text-shadow: 0 4px 4px #f14b0e;
    margin: 48px auto;
    cursor: pointer;
    box-sizing: border-box;
  }
  .button_2 {
    width: 256px;
    height: 80px;
    background-image: linear-gradient(180deg, #2fceff 0%, #2da7ff 100%);
    border: 8px solid #ffffff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #149eee;
    border-radius: 40px;
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    line-height: 66px;
    text-shadow: 0 4px 4px rgba(20, 158, 238, 0.85);
    margin: 48px auto;
    cursor: pointer;
    box-sizing: border-box;
  }
}
</style>
