<template>
  <div class="functionButtonWrap">
    <div
      class="button_wrap"
      :style="{
        'background-image':
          status.game_status == 'is_end'
            ? active == 1
              ? `url(${require('@/assets/game/功能键-结束tab.png')})`
              : `url(${require('@/assets/game/对局-结束tab.png')})`
            : active == 1
            ? `url(${require('@/assets/game/落子.png')})`
            : active == 2
            ? `url(${require('@/assets/game/功能键.png')})`
            : `url(${require('@/assets/game/对局.png')})`,
        'background-size': '100% 100%'
      }"
    >
      <div class="wrap">
        <div class="top_tab">
          <p
            v-for="(item, index) in status.game_status == 'is_end' ? 2 : 3"
            :key="index"
            class="tab_item"
            :style="{ width: status.game_status == 'is_end' ? '50%' : '33%' }"
            @click="$emit('check_tab', index + 1)"
          ></p>
        </div>
        <div class="tab_content">
          <!-- <div class="" v-if="status.game_status == 'is_end'"></div> -->
          <div class>
            <div
              class="confirm_button"
              v-if="active == 1 && status.game_status != 'is_end'"
              :class="confirm_status ? '' : 'confirm_dis'"
              @click="$emit('confirm_move')"
              :style="{
                'background-image': confirm_status
                  ? `url(${require('@/assets/game/落子背景-可用.png')})`
                  : `url(${require('@/assets/game/落子背景.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="buttons"
              v-if="
                (status.game_status == 'is_end' && active == 1) ||
                (status.game_status != 'is_end' && active == 2)
              "
            >
              <img
                v-if="status.game_status != 'is_end'"
                src="@/assets/game/停一手-btn.png"
                alt
                @click="$emit('update_pass')"
              />
              <img
                src="@/assets/game/手数-btn.png"
                alt
                @click="$emit('show_step')"
              />
              <img
                v-if="
                  status.game_type == 'territory' &&
                  status.game_status != 'is_end'
                "
                src="@/assets/game/申请数目-btn.png"
                alt
                @click="$emit('check_area_score')"
              />
              <img
                src="@/assets/game/认输-btn.png"
                alt
                @click="$emit('resign')"
                v-if="status.game_status != 'is_end'"
              />
             
              <slide
                :min="0"
                :max="parseInt(status.step)"
                v-model="newStep"
                :step="setup"
                v-if="status.game_status == 'is_end'"
              ></slide>

              <div class="pre_buttons" v-if="status.game_status == 'is_end'">
                <img
                  :src="
                    setup > 0
                      ? require('@/assets/game/1.png')
                      : require('@/assets/game/1_dis.png')
                  "
                  class="image_24"
                  @click="changeStepStyle(0)"
                />
                <img
                  :src="
                    setup >= 5
                      ? require('@/assets/game/2.png')
                      : require('@/assets/game/2_dis.png')
                  "
                  class="image_23"
                  @click="changeStepStyle(-5)"
                />
                <img
                  :src="
                    setup >= 1
                      ? require('@/assets/game/3.png')
                      : require('@/assets/game/3_dis.png')
                  "
                  class="image_22"
                  @click="changeStepStyle(-1)"
                />

                <img
                  :src="
                    status.step >= setup + 1
                      ? require('@/assets/game/4.png')
                      : require('@/assets/game/4_dis.png')
                  "
                  class="image_22"
                  @click="changeStepStyle(1)"
                />
                <img
                  :src="
                    status.step >= setup + 5
                      ? require('@/assets/game/5.png')
                      : require('@/assets/game/5_dis.png')
                  "
                  class="image_23"
                  @click="changeStepStyle(5)"
                />
                <img
                  :src="
                    status.step != setup
                      ? require('@/assets/game/6.png')
                      : require('@/assets/game/6_dis.png')
                  "
                  class="image_24"
                  @click="changeStepStyle(361)"
                />
              </div>
            </div>
            <div
              class="start"
              v-if="
                (status.game_status == 'is_end' && active == 2) || active == 3
              "
            >
              <div v-if="status.game_status == 'is_end'">
                <div
                  v-if="
                    toolsGroup.waitArbitration &&
                    (status.user_side == 'black' || status.user_side == 'white')
                  "
                >
                  <img
                    class="arbitration_icon"
                    src="@/assets/game/审核icon.png"
                  />
                  <div class="text_1 jcyt500">裁判正在审核中</div>
                  <div class="text_2 jcyt400">裁判会尽快审核结果，请耐心等待</div>
                </div>
                <div
                  v-else-if="
                    toolsGroup.finishedArbitration &&
                    (status.user_side == 'black' || status.user_side == 'white')
                  "
                >
                  <p class="now jcty600" style="line-height: 10vw">
                    {{
                      toolsGroup.changeResultArbitration
                        ? "更改结果"
                        : "维持原判"
                    }}
                  </p>
                </div>
                <div v-else>
                  <p class="title jcyt600">对局结果</p>
                  <div class="result_wrap">
                    <div class="black_wrap">
                      <div class="img"></div>
                      <div class="jcyt500" v-if="status.lose_reason == 'captured'">
                        提{{ status.black_captured }}子
                      </div>
                      <div v-else-if="status.lose_reason == 'resign'" class="jcyt500">
                        {{ status.win_side == "black" ? "中盘胜" : "中盘负" }}
                      </div>
                      <div v-else-if="status.lose_reason == 'time_out'" class="jcyt500">
                        {{ status.win_side == "black" ? "胜利" : "超时负" }}
                      </div>
                      <div v-else-if="status.lose_reason == 'area_score'" class="jcyt500">
                        {{
                          status.win_side == "black"
                            ? calcWinAreaScore(
                                true,
                                status.area_score,
                                status.board_size
                              )
                            : calcLoseAreaScore(
                                true,
                                status.area_score,
                                status.board_size
                              )
                        }}子
                      </div>
                      <div v-else-if="status.lose_reason == 'withdraw'" class="jcyt500">
                        {{ status.win_side == "black" ? "胜利" : "弃权负" }}
                      </div>
                    </div>
                    <div class="white_wrap">
                      <div class="img"></div>
                      <div v-if="status.lose_reason == 'captured'" class="jcyt500">
                        提{{ status.white_captured }}子
                      </div>
                      <div v-else-if="status.lose_reason == 'resign'" class="jcyt500">
                        {{ status.win_side == "white" ? "中盘胜" : "中盘负" }}
                      </div>
                      <div v-else-if="status.lose_reason == 'time_out'" class="jcyt500">
                        {{ status.win_side == "white" ? "胜利" : "超时负" }}
                      </div>
                      <div v-else-if="status.lose_reason == 'area_score'" class="jcyt500">
                        {{
                          status.win_side == "white"
                            ? calcWinAreaScore(
                                false,
                                status.area_score,
                                status.board_size
                              )
                            : calcLoseAreaScore(
                                false,
                                status.area_score,
                                status.board_size
                              )
                        }}子
                      </div>
                      <div v-else-if="status.lose_reason == 'withdraw'" class="jcyt500">
                        {{ status.win_side == "white" ? "胜利" : "弃权负" }}
                      </div>
                    </div>
                  </div>
                  <div
                    class="arbitration_btn jcyt500"
                    @click="$emit('arbitration', true)"
                    v-if="
                      toolsGroup.hasArbitration &&
                      toolsGroup.isFinished == true &&
                      (status.user_side == 'black' ||
                        status.user_side == 'white') &&
                      status.game_type == 'territory' &&
                      from_url == 'playing'
                    "
                  >
                    申请仲裁
                  </div>
                </div>
              </div>
              <div v-else>
                <p class="pop jcyt400">
                  <span v-if="game_index"
                    >第{{ game_index }}局/共{{ game_length }}局 对局开始</span
                  >
                </p>
                <p
                  class="now jcyt600"
                  :style="{
                    'line-height': game_index ? '' : '3vw'
                  }"
                >
                  对局开始
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import slide from "./slide.vue";
export default {
  name: "functionButtonWrap",
  data() {
    return {
      userInfo: {},
      newStep: "",
      type: ""
    };
  },
  props: {
    game_end: {},
    active: {},
    confirm_status: {},
    status: {},
    setup: {},
    game_index: {},
    game_length: {},
    toolsGroup: {}
  },
  components: { slide },
  computed: {
    from_url() {
      return this.$route.query.from;
    }
  },
  watch: {
    newStep: {
      handler(e) {
        this.$emit("goToStep", e);
      },
      deep: true
    }
  },
  mounted() {
    if (
      this.toolsGroup.waitArbitration &&
      (this.status.user_side == "black" || this.status.user_side == "white")
    ) {
      this.type = "waitArbitration";
    }
    if (
      this.toolsGroup.finishedArbitration &&
      (this.status.user_side == "black" || this.status.user_side == "white")
    ) {
      this.type = "finishedArbitration";
    }
  },

  methods: {
    calcWinAreaScore(isBlack, areaScore, boardSize) {
      areaScore = Math.abs(areaScore);
      var result =
        boardSize == 19
          ? 180.5 + areaScore / 2
          : boardSize == 9
          ? 40.5 + areaScore / 2
          : boardSize == 13
          ? 84.5 + areaScore / 2
          : 0.0;
      if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
      } else {
        // 白方向下取整
        return Math.floor(result);
      }
    },
    calcLoseAreaScore(isBlack, areaScore, boardSize) {
      areaScore = Math.abs(areaScore);
      var result =
        boardSize == 19
          ? 361 - (180.5 + areaScore / 2)
          : boardSize == 9
          ? 81 - (40.5 + areaScore / 2)
          : boardSize == 13
          ? 169 - (84.5 + areaScore / 2)
          : 0.0;
      if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
      } else {
        // 白方向下取整
        return Math.floor(result);
      }
    },
    changeStepStyle(num) {
      this.$emit("changeStepStyle", num);
    }
  }
};
</script>

<style scoped lang="less">
.functionButtonWrap {
  margin-left: -2px;
  position: absolute;
  bottom: 50px;
  .button_wrap {
    width: 488px;
    height: 416px;
    // margin-top: 240px;

    padding: 20px 16px 20px 10px;
    box-sizing: border-box;
    .wrap {
      // width: 616px;
      // height: 514px;
      .top_tab {
        width: 100%;
        height: 74px;
        display: flex;
        justify-content: space-between;
        .tab_item {
          height: 74px;
          cursor: pointer;
        }
      }
      .tab_content {
        // width: 564px;
        height: 272px;
        // background-color: pink;
        // margin: 20px 25px;
        .confirm_button {
          width: 416px;
          height: 272px;
          cursor: pointer;
          margin-top: 16px;
          margin-left: 20px;
        }
        .confirm_dis {
          width: 416px;
          height: 272px;
          pointer-events: none;
          margin-top: 16px;
          margin-left: 20px;
        }
        .buttons {
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap;
          padding: 28px 36px;
          box-sizing: border-box;
          gap: 32px 48px;
          img {
            width: 96px;
            cursor: pointer;
          }
          .pre_buttons {
            width: 377px;
            height: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: -13px auto 0;
            .image_24 {
              width: 25px;
              height: 22px;
              cursor: pointer;
            }
            .image_23 {
              width: 34px;
              height: 22px;
              cursor: pointer;
            }
            .image_22 {
              width: 19px;
              height: 28px;
              cursor: pointer;
            }
          }
        }
        .start {
          font-size: 48px;
          text-align: center;
          color: #ff6e30;

          box-sizing: border-box;

          .arbitration_icon {
            width: 120px;
            height: 120px;
            margin-top: 30px;
          }
          .text_1 {
            font-size: 32px;
            color: #ff6e30;
            text-align: center;
            margin-top: 8px;
          }
          .text_2 {
            font-size: 24px;
            color: #777777;
            text-align: center;
            margin-top: 7px;
          }

          .pop {
            margin-bottom: 24px;
            margin-top: 100px;
            font-size: 24px;
            color: #777777;
          }

          .now {
            // line-height: 372px;
          }

          .title {
            font-size: 32px;
            color: #ff6e30;
            text-align: center;
            margin-top: 96px;
            line-height: 40px;
          }
        }
        .result_wrap {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 16px auto;

          .black_wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 24px;
            color: #1f242e;

            .img {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: url("@/assets/game/black.png") no-repeat;
              background-size: cover;
              margin-right: 20px;
              box-shadow: rgba(0, 0, 0, .5) 0 2px 3px 0;
            }
          }
          .white_wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 24px;
            color: #1f242e;
            margin-left: 50px;

            .img {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: url("@/assets/game/white.png") no-repeat;
              background-size: cover;
              margin-right: 20px;
              box-shadow: rgba(0, 0, 0, .5) 0 2px 3px 0;
            }
          }
        }
        .arbitration_btn {
          width: 320px;
          height: 80px;
          background-image: linear-gradient(180deg, #FE8A3A 6%, #FD5B25 94%);
          box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1),
            inset 0 -6px 0px 0 #F14B0E;
          border-radius: 40px;
          margin: 0 auto;
          font-size: 32px;
          color: #ffffff;
          text-align: center;
          line-height: 80px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
