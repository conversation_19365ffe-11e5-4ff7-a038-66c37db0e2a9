<template>
  <transition name="van-fade">
    <div class="win-dialog_wrap" v-if="win_dislog_show">
      <div
        class="win-dialog"
        :style="{
          'background-image': `url(${require('@/assets/game/对局胜利弹窗.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <div class="win-people_wrap">
          <div class="win-people win-black_people">
            <div
              class="win-user_img"
              :style="{
                'background-image':
                  'url(' + status['black_player_avatar'] + ')',
                'background-size': '100% 100%'
              }"
            >
              <div class="win-heizi"></div>
            </div>
            <p class="win-user_name jcyt600">{{ status["black_player_name"] }}</p>
            <div class="win-time jcyt500">
              {{
              status.lose_reason == "captured"
              ? `提 ${status.black_captured} 子`
              : status.lose_reason == "resign"
              ? status.win_side == "black"
              ? "中盘胜"
              : "中盘负"
              : status.lose_reason == "time_out"
              ? status.win_side == "black"
              ? "胜利"
              : "超时负"
              : status.lose_reason == "area_score"
              ? ` ${
              status.win_side == "black"
              ? calcWinAreaScore(
              true,
              status.area_score,
              status.board_size
              )
              : calcLoseAreaScore(
              true,
              status.area_score,
              status.board_size
              )
              }子`
              : status.lose_reason == "withdraw"
              ? status.black_player_enter
              ? "胜利"
              : "弃权负"
              : ""
              }}
            </div>
          </div>
          <div
            class="win-center_result"
            :style="{
              'background-image':
                status.win_side == 'black' && status.game_status == 'is_end'
                  ? `url(${require('@/assets/game/黑胜文字.png')})`
                  : status.win_side == 'white' && status.game_status == 'is_end'
                  ? `url(${require('@/assets/game/白胜文字.png')})`
                  : status.win_side == 'withdraw' &&
                    status.game_status == 'is_end'
                  ? `url(${require('@/assets/game/双方弃权.png')})`
                  : '',
              'background-size': '100% 100%'
            }"
          ></div>
          <div class="win-people win-white_people">
            <div
              class="win-user_img"
              :style="{
                'background-image':
                  'url(' + status['white_player_avatar'] + ')',
                'background-size': '100% 100%'
              }"
            >
              <div class="win-baizi"></div>
            </div>
            <p class="win-user_name jcyt600">{{ status["white_player_name"] }}</p>
            <div class="win-time jcyt500">
              {{
              status.lose_reason == "captured"
              ? `提 ${status.white_captured} 子`
              : status.lose_reason == "resign"
              ? status.win_side == "white"
              ? "中盘胜"
              : "中盘负"
              : status.lose_reason == "time_out"
              ? status.win_side == "white"
              ? "胜利"
              : "超时负"
              : status.lose_reason == "area_score"
              ? ` ${
              status.win_side == "white"
              ? calcWinAreaScore(
              false,
              status.area_score,
              status.board_size
              )
              : calcLoseAreaScore(
              false,
              status.area_score,
              status.board_size
              )
              }子`
              : status.lose_reason == "withdraw"
              ? status.black_player_enter
              ? "胜利"
              : "弃权负"
              : ""
              }}
            </div>
          </div>
        </div>

        <div
          class="button_1 jcyt600"
          @click="close()"
          v-if="eInfo.match_status == 'not_pass'||eInfo.match_status == 'is_pass'"
        >查看报告</div>
        <div
          class="button_2 jcyt600"
          @click="close()"
          v-else-if="!(eInfo.game_history[gameIndex-1].is_last)??false"
        >继续对弈</div>
        <div
          class="button"
          @click="close()"
          v-else
          :style="{
            'background-image': `url(${require('@/assets/game/返回button.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: "winDialog",
  data() {
    return {
      win_dislog_show: false
    };
  },
  props: {
    status: {},
    gameIndex: {},
    eInfo: {},
    matchId:{},
    urlFrom: {
      default: "unit"
    }
  },
  watch: {},
  methods: {
    open() {
      this.win_dislog_show = true;
    },
    close() {
      this.win_dislog_show = false;
      if (
        this.eInfo.match_status == "not_pass" ||
        this.eInfo.match_status == "is_pass"
      ) {
        if(this.urlFrom == "unit"){
          this.$router.push({
            path: "/unitTestReport",
            query: {
              course_id: this.$route.query.course_id,
              lesson_id: this.$route.query.lesson_id,
              matchId: this.eInfo.group_id,
              from:this.$route.query.from,
            }
          });
        }else {
          this.$router.push({path: "/evaluationReport", query: {
            "matchID": this.matchId
          }})
        }
      } else if (
        !this.eInfo.game_history[this.gameIndex - 1].is_last ??
        false
      ) {
        if(this.urlFrom == "unit"){
          this.$router.replace({
            path: "/unitTest/game",
            query: {
              course_id: this.$route.query.course_id,
              lesson_id: this.$route.query.lesson_id,
              matchId: this.matchId,
              gameIndex: Number(this.gameIndex) + 1,
              from: this.$route.query.from,
            }
          });
        }else {
          this.$router.replace({
            path: "/evaluationGame",
            query: {
              matchId: this.matchId,
              gameIndex: Number(this.gameIndex) + 1, 
              from: "evaluation"
            }
          });
        }
      }
    },
    calcWinAreaScore(isBlack, areaScore, boardSize) {
      areaScore = Math.abs(areaScore);
      var result =
        boardSize == 19
          ? 180.5 + areaScore / 2
          : boardSize == 9
          ? 40.5 + areaScore / 2
          : boardSize == 13
          ? 84.5 + areaScore / 2
          : 0.0;
      if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
      } else {
        // 白方向下取整
        return Math.floor(result);
      }
    },
    calcLoseAreaScore(isBlack, areaScore, boardSize) {
      areaScore = Math.abs(areaScore);
      var result =
        boardSize == 19
          ? 361 - (180.5 + areaScore / 2)
          : boardSize == 9
          ? 81 - (40.5 + areaScore / 2)
          : boardSize == 13
          ? 169 - (84.5 + areaScore / 2)
          : 0.0;
      if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
      } else {
        // 白方向下取整
        return Math.floor(result);
      }
    }
  }
};
</script>
<style scoped lang="less">
.win-dialog_wrap {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  position: fixed;
  z-index: 20001;
  top:0;
}
.win-dialog {
  width: 776px;
  height: 800px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -388px;
  margin-top: -400px;

  .win-people_wrap {
    width: 560px;
    height: 248px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    margin: 360px auto 0;
    .win-people {
      width: 184px;
      height: 248px;

      .win-user_img {
        width: 136px;
        height: 136px;
        border-radius: 50%;
        margin: 0 auto;
        position: relative;
        border: 6.48px solid #ffffff;
        box-sizing: border-box;
        background: #ffffff;
        box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.1);

        .win-heizi {
          width: 45.33px;
          height: 45.33px;
          border-radius: 50%;
          position: absolute;
          background: url("@/assets/game/black.png");
          background-size: cover;
          right: -8px;
          top: -8px;
        }
        .win-baizi {
          width: 45.33px;
          height: 45.33px;
          border-radius: 50%;
          position: absolute;
          background: url("@/assets/game/white.png");
          background-size: cover;
          left: -8px;
          top: -8px;
        }
      }
      .win-user_name {
        font-size: 32px;
        color: #333333;
        text-align: center;
        margin: 8px auto;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .win-time {
        width: 184px;
        height: 56px;
        background: #ffe9c8;
        border-radius: 28.8px;
        font-size: 30px;
        color: #c87e18;
        letter-spacing: 0;
        text-align: center;
        line-height: 56px;
      }
      // .win-captured {
      //   font-size: 28px;
      //   color: #ffffff;
      //   letter-spacing: 0;
      //   text-align: center;
      // }
    }
    .win-black_people {
      margin-right: 32px;
    }
    .win-center_result {
      width: 128px;
      
      margin-top: 50px;
    }
    .win-white_people {
      margin-left: 32px;
    }
  }
  .button {
    width: 320px;
    height: 90px;

    // box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1);
    border-radius: 40px;
    margin: 48px auto;
    cursor: pointer;
  }
  .button_1 {
    width: 256px;
    height: 80px;
    background-image: linear-gradient(180deg, #fe8a3a 0%, #fd5b25 100%);
    border: 8px solid #ffffff;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.1), inset 0 -6px 0 0 #f14b0e;
    border-radius: 40px;
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    line-height: 66px;
    text-shadow: 0 4px 4px #f14b0e;
    margin: 48px auto;
    cursor: pointer;
    box-sizing: border-box;
  }
  .button_2 {
    width: 256px;
    height: 80px;
    background-image: linear-gradient(180deg, #2fceff 0%, #2da7ff 100%);
    border: 8px solid #ffffff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #149eee;
    border-radius: 40px;
    font-size: 32px;
    color: #ffffff;
    text-align: center;
    line-height: 66px;
    text-shadow: 0 4px 4px rgba(20, 158, 238, 0.85);
    margin: 48px auto;
    cursor: pointer;
    box-sizing: border-box;
  }
}
</style>
