<template>
  <transition name="van-fade">
    <div
      class="people_wrap"
      :style="{
        'background-image': game_end
          ? `url(${require('@/assets/game/黑白方背景.png')})`
          : time_c == 1
          ? `url(${require('@/assets/game/黑方落子背景.png')})`
          : `url(${require('@/assets/game/白方落子背景.png')})`,
        'background-size': '100% 100%'
      }"
    >
      <div class="people black_people">
        <div
          class="user_img"
          :style="{
            'background-image': 'url(' + status['black_user_avatar'] + ')',
            'background-size': '100% 100%'
          }"
        >
          <div class="mask jcyt500" v-if="!black_enter">离线</div>
          <div class="heizi"></div>
        </div>
        <p class="user_name jcyt600">
          {{
            status["black_user_nick_name"] ?? status["black_user_actual_name"]
          }}
        </p>
        <div class="time jcyt500">
          <span
            v-if="
              ws_left_time.black_main_time == 0 &&
              ws_left_time.black_byo_yomi > 0
            "
            style="margin-right: 3px"
            >{{ ws_left_time.black_byo_yomi }}次</span
          >

          <span>{{ s_to_hs(black_left_time) }}</span>
        </div>
        <p class="captured jcyt600">提 {{ hum_captured }} 子</p>
      </div>
      <div
        class="center_result"
        :style="{
          'background-image':
            win_side == 'black' && game_end
              ? `url(${require('@/assets/game/黑胜.png')})`
              : win_side == 'white' && game_end
              ? `url(${require('@/assets/game/白胜.png')})`
              : win_side == 'withdraw' ||
                (lose_reason === 'Abstain' && game_end)
              ? `url(${require('@/assets/game/弃权.png')})`
              : win_side == 3 && game_end && lose_reason == 'Draw'
              ? `url(${require('@/assets/game/和棋.png')})`
              : '',
          'background-size': '100% 100%'
        }"
        :class="win_side == 'withdraw' ||
                (lose_reason === 'Abstain' && game_end) ? 'grey-shadow' : ((win_side == 'black' && game_end) || (win_side == 'white' && game_end) || (win_side == 3 && game_end && lose_reason == 'Draw')) ?  'yellow-shadow' : ''"
      ></div>
      <div class="people white_people">
        <div
          class="user_img"
          :style="{
            'background-image': 'url(' + status['white_user_avatar'] + ')',
            'background-size': '100% 100%'
          }"
        >
          <div class="mask jcyt500" v-if="!white_enter">离线</div>
          <div class="baizi"></div>
        </div>
        <p class="user_name jcyt600">
          {{
            status["white_user_nick_name"] ?? status["white_user_actual_name"]
          }}
        </p>
        <div class="time jcyt500">
          <span
            v-if="
              ws_left_time.white_main_time == 0 &&
              ws_left_time.white_byo_yomi > 0
            "
            style="margin-right: 3px"
            >{{ ws_left_time.white_byo_yomi }}次</span
          >
          <span>{{ s_to_hs(white_left_time) }}</span>
        </div>
        <p class="captured jcyt600">提 {{ ai_captured }} 子</p>
      </div>
      <speak ref="speak"></speak>
    </div>
  </transition>
</template>
<script>
import speak from "@/components/question/speak";

export default {
  name: "winDialog",
  data() {
    return {};
  },
  props: {
    game_end: {},
    status: {},
    black_enter: {},
    white_enter: {},
    ws_left_time: {},
    black_left_time: {},
    white_left_time: {},
    hum_captured: {},
    ai_captured: {},
    lose_reason: {},
    time_c: {},
    win_side: {},
    user_hash: {},
    start_byomi: {}
  },
  components: { speak },
  watch: {
    start_byomi: {
      handler(val) {
        if (val == "black") {
          if (this.status.black_user_hash === this.user_hash) {
            this.$refs.speak.play("开始读秒");
          }
        } else if (val == "white") {
          if (this.status.white_user_hash === this.user_hash) {
            this.$refs.speak.play("开始读秒");
          }
        }
      },
      deep: true
    },
    white_left_time: {
      handler(val) {
        if (this.status.white_user_hash === this.user_hash) {
          this.time_down(val);
        }
      },
      deep: true
    },
    black_left_time: {
      handler(val) {
        if (this.status.black_user_hash === this.user_hash) {
          this.time_down(val);
        }
      },
      deep: true
    }
  },
  methods: {
    time_down(val) {
      if (val === 30) {
        this.$refs.speak.play("30");
      } else if (val === 20) {
        this.$refs.speak.play("20");
      } else if (val === 10) {
        this.$refs.speak.play("10");
      } else if (val === 9) {
        this.$refs.speak.play("9");
      } else if (val === 8) {
        this.$refs.speak.play("8");
      } else if (val === 7) {
        this.$refs.speak.play("7");
      } else if (val === 6) {
        this.$refs.speak.play("6");
      } else if (val === 5) {
        this.$refs.speak.play("5");
      } else if (val === 4) {
        this.$refs.speak.play("4");
      } else if (val === 3) {
        this.$refs.speak.play("3");
      } else if (val === 2) {
        this.$refs.speak.play("2");
      } else if (val === 1) {
        this.$refs.speak.play("1");
      }
    },
    s_to_hs: function (s) {
      if (!s) {
        s = 0;
      }
      var h;
      h = Math.floor(s / 60);
      s = s % 60;
      h += "";
      s += "";
      h = h.length == 1 ? "0" + h : h;
      s = s.length == 1 ? "0" + s : s;
      return h + ":" + s;
    }
  }
};
</script>
<style scoped lang="less">
.people_wrap {
  width: 100%;
  height: 294px;

  margin-top: 14px;
  display: flex;
  justify-content: center;

  padding-top: 32px;
  box-sizing: border-box;
  .people {
    // width: 200px;
    height: 292px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    .user_img {
      width: 96px;
      height: 96px;
      border-radius: 50%;
      background: url("@/assets/game/default_arr.png");
      background-size: cover;
      position: relative;
      .mask {
        width: 96px;
        height: 96px;
        line-height: 96px;
        text-align: center;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.5);
        font-size: 28px;
        color: #ffffff;
        text-align: center;
        position: absolute;
        left: 0;
        top: 0;
      }
      .heizi {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        position: absolute;
        background: url("@/assets/game/black.png");
        background-size: cover;
        right: 0;
        top: 0px;
        box-shadow: rgba(0, 0, 0, 0.5) 0 1px 3px 0;
      }
      .baizi {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        position: absolute;
        background: url("@/assets/game/white.png");
        box-shadow: rgba(0, 0, 0, 0.5) 0 1px 3px 0;
        background-size: cover;
        left: 0;
        top: 0px;
      }
    }
    .user_name {
      font-size: 28px;
      color: #ffffff;
      text-align: center;
      margin-top: 8px;
      width: 144px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 40px;
    }
    .time {
      width: 136px;
      height: 32px;
      line-height: 32px;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 16px;
      font-size: 24px;
      color: #ffffff;
      text-align: center;
      margin-top: 8px;
    }
    .captured {
      font-size: 24px;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      margin-top: 8px;
      line-height: 32px;
    }
  }
  .center_result {
    width: 104px;
    height: 104px;
    border-radius: 50%;
    margin-top: 60px;
  }
  .yellow-shadow {
    box-shadow: rgba(255, 187, 16, .4) 0 0 42px 0, rgba(0, 0, 0, 0.1) 0 5px 17px 0;
  }
  .grey-shadow {
    box-shadow: rgba(0, 0, 0, .08) 0 0 42px 0, rgba(0, 0, 0, 0.1) 0 5px 17px 0;
  }
}
</style>
