<template>
  <div id="board" class="board-main">
    <div
      id="game-board"
      class="board"
      @click="
        ((userTurn === 1 && player.kifuReader.game.turn === 1) ||
          (userTurn === 2 && player.kifuReader.game.turn === -1)) &&
          board_click &&
          move_chess_pieces($event)
      "
      @mousemove="
        ((userTurn === 1 && player.kifuReader.game.turn === 1) ||
          (userTurn === 2 && player.kifuReader.game.turn === -1)) &&
          board_click &&
          mouse_move($event)
      "
      @mouseout="mouse_out"
    ></div>
    <audio id="move-audio" type="audio/mp3">
      <source src="@/assets/audio/fall.mp3" />
    </audio>
  </div>
</template>

<script>
import "@/public/wgo/wgo.min";
import "@/public/wgo/wgo.player.min";
import tool from "@/public/tool";

var timer;

window.requestAnimFrame = (function () {
  return (
    window.requestAnimationFrame ||
    window.webkitRequestAnimationFrame ||
    window.mozRequestAnimationFrame ||
    window.oRequestAnimationFrame ||
    window.msRequestAnimationFrame ||
    function (callback) {
      timer = window.setTimeout(callback, 1000 / 60);
    }
  );
})();

window.cancelAnimFrame = (function () {
  return (
    window.cancelAnimationFrame ||
    window.webkitCancelAnimationFrame ||
    window.mozCancelAnimationFrame ||
    window.oCancelAnimationFrame ||
    window.msCancelAnimationFrame ||
    function (callback) {
      window.clearTimeout(timer);
    }
  );
})();
import AwaitLock from "await-lock";

export default {
  name: "board",
  data() {
    return {
      player: "",
      move_audio: "",
      mark_list: [],
      mark_img: "",
      scale: 0.9,
      animIndex: "",
      black_stone_img: "",
      white_stone_img: "",
      old_stone: {},
      scale_arr: [0.9, 1, 1.1, 0.9], //缩放数组
      anmia_i: 0, //每requsetanima计一次
      scale_index: 0, //数组下标
      speed: 16, // speed * 16 毫秒，控制动画速度
      handler_locker: new AwaitLock(),
    };
  },
  props: {
    sgf: {
      default: "(CA[gb1312]SZ[9])",
      type: String,
    },
    board_click: {
      default: false,
      type: Boolean,
    },
    mark_chess: {
      default: false,
      type: Boolean,
    },
    section: {
      type: Object,
    },
    tsumego: {
      type: Array,
    },
    gridLinesColor: {
      default: "#D09357",
      type: String,
    },
    starColor: {
      default: "#AA6320",
      type: String,
    },
    userTurn: {
      default: 0,
      type: Number,
    },
  },
  computed: {
    chessMoveSound(){
      return this.$store.getters.getChessMoveSound;
    }
  },
  watch: {
    section: {
      handler: function (new_object) {
        if (
          new_object instanceof Object &&
          JSON.stringify(new_object) !== "{}"
        ) {
          this.player.board.setSection(new_object);
        }
      },
    },
    sgf: {
      handler: function (new_str) {
        console.log("========" + new_str);
        if (this.player) {
          this.player.loadSgf(new_str, "last");
          this.move_to_last();
          this.out_turn();
          this.out_setup();
          this.out_capture();
        } else {
          this.make_player();
        }
      },
      deep: true,
    },
    tsumego: {
      handler: function () {
        this.add_branch_mark();
      },
      deep: true,
    },
  },
  methods: {
    call_player(fun_name, fun_args = null) {
      let args;
      if (fun_args) {
        if (fun_args.constructor === Object) {
          args = JSON.stringify(fun_args);
          return eval(`this.player.${fun_name}(${args})`);
        } else if (fun_args.constructor === Array) {
          for (let index in fun_args) {
            args = args + "'" + fun_args[index] + "'";
          }
          return eval(`this.player.${fun_name}('${args}')`);
        } else if (
          fun_args.constructor === String ||
          fun_args.constructor === Number
        ) {
          args = fun_args;
          return eval(`this.player.${fun_name}('${args}')`);
        }
      } else {
        args = "";
        return eval(`this.player.${fun_name}()`);
      }
    },
    move_to_previous: function () {
      this.player.previous();
      this.out_turn();
      this.out_setup();
      this.out_capture();
    },
    move_to_next: function () {
      this.player.next();
      this.out_turn();
      this.out_setup();
      this.out_capture();
    },
    goTo: function (e) {
      let p = WGo.clone(this.player.kifuReader.path);
      p.m += e;
      this.player.goTo(p);
      this.out_turn();
      this.out_setup();
      this.out_capture();
    },
    goToStep: function (p) {
      this.player.goTo(p);
      this.out_turn();
      this.out_setup();
      this.out_capture();
    },
    move_to_first: function () {
      let p = WGo.clone(this.player.kifuReader.path);
      p.m -= 1000;
      this.player.goTo(p);
      this.out_turn();
      this.out_setup();
      this.out_capture();
    },
    move_to_last: function () {
      let p = WGo.clone(this.player.kifuReader.path);
      p.m += 1000;
      this.player.goTo(p);
      this.out_turn();
      this.out_setup();
      this.out_capture();
    },
    out_capture: function () {
      this.$emit("captured", {
        B: this.player.kifuReader.game.getCaptureCount(WGo.B),
        W: this.player.kifuReader.game.getCaptureCount(WGo.W),
      });
    },
    out_sgf: function () {
      this.$emit(
        "sgf",
        this.player.kifuReader.kifu.toSgf().replace(/[\r\n]/g, "")
      );
    },
    out_turn: function () {
      this.$emit("turn", this.player.kifuReader.game.turn);
    },
    out_setup: function () {
      this.$emit("setup", this.player.kifuReader.path.m);
    },
    mouse_move: function (event) {
      // 鼠标在棋盘范围内移动
      if (this.last_mark !== "") {
        this.player.board.removeObject(this.last_mark);
      }
      // 只有当前是人类落子时 才添加半透明棋子
      let coordinates = this.to_obtain_coordinate(event);
      let x = coordinates.x;
      let y = coordinates.y;
      if (this.player.kifuReader.game.isValid(x, y)) {
        this.last_mark = {
          type: "outline",
          x: x,
          y: y,
          c: this.player.kifuReader.game.turn,
        };
        this.player.board.addObject(this.last_mark);
        return this.last_mark;
      } else {
        return "";
      }
    },
    mouse_out: function () {
      // 鼠标移出棋盘
      if (this.last_mark !== "") {
        this.player.board.removeObject(this.last_mark);
      }
    },
    to_obtain_coordinate: function (event) {
      // 捕捉落子的x y 坐标
      let board = this.player.board;
      let x, y;
      x = event.offsetX * board.pixelRatio;
      y = event.offsetY * board.pixelRatio;
      x -= board.left;
      x /= board.fieldWidth;
      x = Math.round(x);
      y -= board.top;
      y /= board.fieldHeight;
      y = Math.round(y);
      return {
        x: x >= board.size ? -1 : x,
        y: y >= board.size ? -1 : y,
      };
    },
    //重绘正常子
    repain_chess_mark: function (event) {
      this.old_stone = {};
      window.cancelAnimationFrame(this.animIndex);
      this.player.board.removeObjectsAt(event.x, event.y);
      // this.player.board.addObject({
      //     x: event.x,
      //     y: event.y,
      //     c: event.c,
      // });
    },
    add_chess_mark: function (event) {
      if (JSON.stringify(this.old_stone) != "{}") {
        window.cancelAnimationFrame(this.animIndex);
        this.player.board.removeObjectsAt(this.old_stone.x, this.old_stone.y);
      }
      this.add_anim_chess_mark(event);
      this.old_stone = event;
    },
    add_anim_chess_mark(event) {
      this.anmia_i += 1;
      if (this.anmia_i % this.speed == 0) {
        this.scale_index += 1;
        if (this.scale_index == this.scale_arr.length) this.scale_index = 0;
        this.scale = this.scale_arr[this.scale_index];
      }
      this.player.board.removeObjectsAt(event.x, event.y);
      this.player.board.addObject({
        x: event.x,
        y: event.y,
        c: this.player.kifuReader.game.turn,
        type: "gif",
        scale: this.scale,
      });
      this.animIndex = window.requestAnimFrame(() => {
        this.add_anim_chess_mark(event);
      });
    },
    remove_anim_chess_mark() {
      window.cancelAnimationFrame(this.animIndex);
      //this.player.board.removeObjectsAt(this.old_stone.x,this.old_stone.y);
      this.old_stone = {};
    },
    remove_chess_mark: function () {
      if (JSON.stringify(this.old_stone) != "{}") {
        window.cancelAnimationFrame(this.animIndex);
        this.player.board.removeObjectsAt(this.old_stone.x, this.old_stone.y);
      }
    },
    update_pass: function () {
      this.player.kifuReader.node.appendChild(
        new WGo.KNode({
          move: {
            pass: true,
            c: this.player.kifuReader.game.turn,
          },
        })
      );
      this.player.next(this.player.kifuReader.node.children.length - 1);
      this.out_capture();
      this.out_sgf();
      this.out_turn();
      this.out_setup();
    },
    update_board: function (event) {
      if(this.chessMoveSound) {
        this.move_audio.play();
      }
      this.player.board.addObject({
        x: event.x,
        y: event.y,
        c: this.player.kifuReader.game.turn,
      });
      this.player.kifuReader.node.appendChild(
        new WGo.KNode({
          move: {
            x: event.x,
            y: event.y,
            c: this.player.kifuReader.game.turn,
          },
        })
      );
      this.player.next(this.player.kifuReader.node.children.length - 1);
      this.out_capture();
      this.out_sgf();
      this.out_turn();
      this.out_setup();
      return this.player.kifuReader.kifu.toSgf().replace(/[\r\n]/g, "");
    },
    move_chess_pieces: function (event) {
      let coordinates = this.to_obtain_coordinate(event);
      // 获取点击的x y
      let x = coordinates.x;
      let y = coordinates.y;
      if (
        this.player.kifuReader.game.isValid(x, y) &&
        this.board_click === true &&
        ((this.player.kifuReader.game.turn === 1 && this.userTurn === 1) ||
          (this.player.kifuReader.game.turn === -1 && this.userTurn === 2))
      ) {
        if (this.animIndex) {
          window.cancelAnimFrame(this.animIndex);
        }
        this.$emit("chess_move", {
          x: x,
          y: y,
          turn: this.player.kifuReader.game.turn,
        });
      }
    },
    make_player: function () {
      if (this.handler_locker.acquired) {
        this.handler_locker.acquireAsync({ timeout: 5000 });
      }
      // 创建棋盘
      let elem = document.getElementById("game-board");
      this.player = new WGo.BasicPlayer(elem, {
        sgf: "(CA[gb1312]SZ[9])",
        layout: {
          left: "",
          bottom: "",
        },
        board: {
          theme: {
            gridLinesColor: "#B75200",
            starColor: "#B75200",
          },
        },
        enableWheel: false,
      });
      this.player.board.removeEventListener("click", this.player.board._click);
      this.player.board.removeEventListener(
        "click",
        this.player.board._ev_click
      );
      tool.MoveToLast(this.player);
      if(this.handler_locker.acquired) {
          this.handler_locker.release();
      }
    },
    remove_branch_mark: function () {
      for (let index in this.mark_list) {
        this.player.board.removeObject({
          x: this.mark_list[index].x,
          y: this.mark_list[index].y,
          type: "LB",
        });
      }
    },
    show_move_number() {
      this.player.setShowNumMove(true, this.player.kifuReader);
    },
    close_move_number() {
      this.player.setShowNumMove(false);
    },
    hideLastMove() {
      this.player.hideLastMove();
    },
    add_branch_mark: function () {
      this.remove_branch_mark();
      this.mark_list = [];
      let move = {};
      let mark_numb = 0;
      if (this.tsumego.length !== 0) {
        for (let index in this.tsumego) {
          if ("B" in this.tsumego[index]) {
            move = tool.ConvertSgfToXY(this.tsumego[index].B);
            this.player.board.addObject({
              x: move.x,
              y: move.y,
              type: "LB",
              text: tool.GetAbc(mark_numb),
              c: tool.RandomColor(),
            });
            this.mark_list.push(move);
            mark_numb = mark_numb + 1;
          }
        }
      }
    },
    loadSgf(sgf) {
      this.player.loadSgf(sgf, "last");
      this.move_to_last();
    },
  },
  beforeDestroy() {
    if (this.animIndex) {
      window.cancelAnimFrame(this.animIndex);
    }
  },
  mounted() {
    this.move_audio = document.getElementById("move-audio");
    this.make_player();
  },
  created() {
    this.handler_locker.tryAcquire();
  },
};
</script>

<style scoped lang="less">
.board-main {
  width: 100%;
  height: 100%;

  #game-board {
    width: 100%;
    height: 100%;

    /deep/ .wgo-info-overlay {
      display: none;
    }

    /deep/ .wgo-player-center {
      text-align: start;
      width: 100%;
      height: 100%;
    }

    /deep/ .wgo-player-board {
      padding: 0 !important;
      width: 100% !important;
      height: 100% !important;

      /deep/ .wgo-board {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }
}
</style>
