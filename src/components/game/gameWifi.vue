<template>
  <transition name="van-fade">
    <div
      class="wifi_wrap jcyt400"
      :style="{
        color:
          delayHttp > 300
            ? '#FF6663'
            : delayHttp > 200
            ? '#FF6663'
            : delayHttp > 100
            ? '#FFD100'
            : '#7FFF5F'
      }"
    >
      <img
        :src="
          delayHttp > 300
            ? require('@/assets/game/wifi_0.png')
            : delayHttp > 200
            ? require('@/assets/game/wifi_1.png')
            : delayHttp > 100
            ? require('@/assets/game/wifi_2.png')
            : require('@/assets/game/wifi_3.png')
        "
        alt=""
      />
      {{ delayHttp }}ms
    </div>
  </transition>
</template>
<script>
import userApi from "@/api/user";
import moment from "moment";
export default {
  name: "wifi",
  data() {
    return {
      netType: "",
      delayHttp: 0,
      resultLevel: {
        normal: 100,
        frozen: 200
      },
      result: "",
      alreadyRequestDone: 0,
      alreadyPingDone: 0,
      timer: null,
      newtimer: null,
      checkPercent: 0,
      checkCircleRotate: 0,
      new_time: 60
    };
  },
  props: {
    game_end: {
      default: false
    }
  },
  computed: {
    network() {
      return this.$store.getters.getNetwork;
    },
  },
  watch: {
    game_end: {
      handler: function (val) {
        if (val) {
          if (this.newtimer) {
            clearInterval(this.newtimer);
          }
        }
      },
      deep: true
    },
    network: {
      handler(val){
        this.delayHttp = val;
      },
      immediate: true
    }
  },
  methods: {
    checkStart() {
      if (this.result == "inCheck") {
        return false;
      }
      this.result = "inCheck";
      this.delayHttp = 0;
      this.getAppVersion();
      this.timer = setInterval(() => {
        if (this.checkPercent >= 100) {
          this.checkDone();
        } else {
          var alreadyDone = this.alreadyRequestDone;
          //  this.alreadyPingDone > this.alreadyRequestDone
          //   ? this.alreadyRequestDone
          // : this.alreadyPingDone;
          var tempTop = alreadyDone * 20;
          this.checkCircleRotate += Math.PI * 4;
          if (this.checkPercent < tempTop) {
            this.checkPercent += 5;
          }
        }
      }, 100);
    },
    async getAppVersion() {
      var delaySum = 0;
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      this.delayHttp = Math.ceil(delaySum / 5);
    },
    async httpTest() {
      var justNow = new Date().getTime();
      var response = moment().utc().valueOf();
      //await networkApi.getNetwork();
      var difference_value = parseInt(response) - justNow; //.data['data']['t']
      var res = await userApi.meshPing({ request_time: justNow });
      this.alreadyRequestDone += 1;
      return res.data["latency"] - difference_value;
    },
    checkDone() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      if (!this.game_end) {
        this.reGetCountdown(60);
      }
      this.checkPercent = 0;
      this.alreadyRequestDone = 0;
      this.checkCircleRotate = 0;
      this.alreadyPingDone = 0;
      this.result =
        this.delayHttp > this.resultLevel.frozen
          ? "frozen"
          : this.delayHttp > this.resultLevel.normal
          ? "normal"
          : "perfect";
    },
    reGetCountdown(new_time) {
      this.newtimer = setInterval(() => {
        if (new_time > 0) {
          new_time--;
        } else {
          if (this.newtimer) {
            clearInterval(this.newtimer);
          }
          this.checkStart();
        }
      }, 1000);
    }
  },
  mounted() {
    // this.checkStart();
  },
  destroyed() {
    if (this.newtimer) {
      clearInterval(this.newtimer);
    }
  }
};
</script>
<style scoped lang="less">
.wifi_wrap {
  padding: 0 20px;
  height: 48px;
  line-height: 48px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 24px;
  background: rgba(0, 0, 0, 0.2);
  font-size: 28px;
  color: #7fff5f;
  letter-spacing: 0;
  text-align: right;
  line-height: 32px;
  float: right;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 32px;
    height: 26px;
    margin-right: 10px;
  }
}
</style>
