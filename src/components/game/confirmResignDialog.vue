<template>
  <!-- <transition name="van-fade">
    <div class="confirm-resign-dialog_wrap" v-if="show">
      <div class="confirm-resign-dialog">
        <p class="title">确定要认输吗？</p>
        <p class="content">认输结果将判为对局失败</p>
        <div class="buttons">
          <div class="confirm" @click="$emit('confirm_resign')">确定</div>
          <div class="cancle" @click="close()">取消</div>
        </div>
      </div>
    </div>
  </transition> -->
  <tips
    v-if="show"
    :isOpen="show"
    msg="确定要认输吗？<br/>认输结果将判为对局失败"
    cancelBtn="取消"
    reallyBtn="确定"
    @cancel="close"
    @really="$emit('confirm_resign');show = false;"
  >
  </tips>
</template>
<script>
import tips from "@/components/tips/tips";
export default {
  name: "confirmResignDialog",
  data() {
    return {
      show: false,
    };
  },
  props: {},
  watch: {},
  methods: {
    open() {
      this.show = true;
    },
    close() {
      this.show = false;
      this.$emit("cancel_resign");
    },
  },
  components: {
    tips
  }
};
</script>
<style scoped lang="less">
// .confirm-resign-dialog_wrap {
//   width: 2048px;
//   height: 1536px;
//   background: rgba(0, 0, 0, 0.5);
//   position: fixed;
//   z-index: 20001;
//   top:0
// }
// .confirm-resign-dialog {
//   width: 784px;
//   height: 416px;
//   background: #ffffff;
//   box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 30px 0 #ccfaff;
//   border-radius: 50px;
//   position: absolute;
//   left: 50%;
//   top: 50%;
//   margin-left: -392px;
//   margin-top: -208px;
//   .title {
//     font-size: 48px;
//     color: #333333;
//     text-align: center;
//     margin: 55px auto 16px;
//   }
//   .content {
//     font-size: 32px;
//     color: #999999;
//     text-align: center;
//   }
//   .buttons {
//     width: 656px;
//     height: 112px;
//     margin: 60px auto;
//     display: flex;
//     justify-content: space-between;
//     .confirm {
//       width: 312px;
//       height: 112px;
//       background: #ffffff;
//       border: 4px solid #00bdff;
//       border-radius: 60px;
//       font-size: 40px;
//       color: #00bdff;
//       text-align: center;
//       line-height: 112px;
//       cursor: pointer;
//     }
//     .cancle {
//       width: 312px;
//       height: 112px;
//       background: #08ccfd;
//       border-radius: 60px;
//       font-size: 40px;
//       color: #ffffff;
//       text-align: center;
//       line-height: 112px;
//       cursor: pointer;
//     }
//   }
// }
</style>
