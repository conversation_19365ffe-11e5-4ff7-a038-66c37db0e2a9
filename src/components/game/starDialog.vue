<template>
  <div class="starDialog" v-if="star_dialog_show">
    <div
      class="dialog_box"
      :style="{
        'background-image': `url(${require('@/assets/unitTest/unit_dialog.png')})`,
        'background-size': '100% 100%'
      }"
    >
      <div class="title jcyt600">
        {{ status.win_capture > 0 ? "吃子挑战" : "围地挑战" }}
      </div>
      <div class="dialog_content">
        <p class="text_1 jcyt500">获胜规则</p>
        <p class="text_2 jcyt600">
          {{ status.win_capture > 0 ? "吃掉对方" : "黑贴" }}
          <span style="color: #ff4752">{{
            status.win_capture > 0 ? status.win_capture : "7.5目"
          }}</span>
          {{ status.win_capture > 0 ? "颗棋子" : "" }}
        </p>
        <div class="line"></div>
        <div class="row">
          <div class="left">
            <div class="head">
              <img class="head" :src="status.black_user_avatar" />
              <div class="chess"></div>
            </div>
            <div class="name jcyt600">{{ status["black_user_nick_name"] ?? status["black_user_actual_name"] }}</div>
          </div>
          <div class="center"></div>
          <div class="right">
            <div class="head">
              <img class="head" :src="status.white_user_avatar" />
              <div class="chess"></div>
            </div>
            <div class="name jcyt600">{{ status["white_user_nick_name"] ?? status["white_user_actual_name"] }}</div>
          </div>
        </div>
      </div>
      <div class="star_btn jcyt600" @click="starGame">
        {{
          game_history[gameIndex - 1].status == "is_start"
            ? "继续对局"
            : "开始对局"
        }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    status: {},
    game_history: {},
    gameIndex: {}
  },
  data() {
    return {
      star_dialog_show: false
    };
  },
  methods: {
    open() {
      this.star_dialog_show = true;
    },
    close() {
      this.star_dialog_show = false;
    },
    starGame() {
      this.$emit("starGame", false);
    }
  }
};
</script>

<style lang="less">
.starDialog {
  width: 100vw;
  height: 100vh;
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  z-index: 311;
  display: flex;
  align-items: center;
  justify-content: center;
  top:0;
  .dialog_box {
    width: 786px;
    height: 800px;
    .title {
      color: #ffffff;
      font-size: 48px;
      text-align: center;
      margin-top: 140px;
    }
    .dialog_content {
      width: 664px;
      height: 360px;
      background: #ffe9c8;
      border-radius: 40px;
      margin: 60px auto 24px;
      padding: 28px 20px 22px;
      box-sizing: border-box;
      .text_1 {
        font-size: 28px;
        color: #666666;
        text-align: center;
        line-height: 32px;
      }
      .text_2 {
        font-size: 40px;
        color: #333333;
        letter-spacing: 0.5px;
        text-align: center;
        margin-top: 8px;
        line-height: 48px;
      }
      .line {
        width: 624px;
        height: 1.1px;
        background: rgba(200, 126, 24, 0.5);
        margin: 19.5px auto 23.5px;
      }
      .row {
        display: flex;
        justify-content: space-around;
        align-items: center;
        .left {
          .chess {
            width: 40px;
            height: 40px;
            position: absolute;
            background: url("@/assets/game/black.png") no-repeat;
            background-size: 100% 100%;
            right: 0;
            top: 0;
            border-radius: 50%;
            box-shadow: rgba(0, 0, 0, .5) 0 3px 4px 0;
          }
        }
        .center {
          width: 77px;
          height: 48px;
          background: url("@/assets/unitTest/vs.png") no-repeat;
          background-size: 100% 100%;
        }
        .right {
          .chess {
            width: 40px;
            height: 40px;
            position: absolute;
            background: url("@/assets/game/white.png") no-repeat;
            background-size: 100% 100%;
            left: 0;
            top: 0;
            border-radius: 50%;
            box-shadow: rgba(0, 0, 0, .5) 0 3px 4px 0;
          }
        }
        .head {
          width: 120px;
          height: 120px;
          background: pink;
          border-radius: 50%;
          position: relative;
        }
        .name {
          font-size: 32px;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          line-height: 40px;
          margin-top: 16px;
          width: 120px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow:ellipsis;
        }
      }
    }
    .star_btn {
      width: 320px;
      height: 80px;
      background-image: linear-gradient(180deg, #2fceff 0%, #2da7ff 100%);
      border: 4px solid #ffffff;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #149eee;
      border-radius: 40px;
      font-size: 32px;
      color: #ffffff;
      text-align: center;
      line-height: 80px;
      text-shadow: 0 4px 4px rgba(20, 158, 238, 0.85);
      margin: 0 auto;
    }
  }
}
</style>
