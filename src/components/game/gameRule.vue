<template>
  <transition name="van-fade">
    <div class="rule_bg" v-if="is_rule_show">
      <img class="triangle" src="@/assets/tips/triangle.png" />
      <div v-if="status.win_capture > 0">
        <p class="jcyt400 rule">
          1.获胜条件：在{{ status.max_step }}手前吃掉对方{{
            status.win_capture
          }}颗子获胜
        </p>
        <p class="rule_two jcyt400">
          2.时间规则：黑方{{ Math.round(status.black_time / 60) }}分钟
          <span v-if="status.black_byo_yomi > 0">
            {{ status.black_byo_yomi }}次{{
              status.black_byo_yomi_time
            }}秒</span
          >

          白方{{ Math.round(status.white_time / 60) }}分钟
          <span v-if="status.white_byo_yomi > 0"
            >{{ status.white_byo_yomi }}次{{
              status.white_byo_yomi_time
            }}秒</span
          >
        </p>
        <p class="rule_two jcyt400" v-if="status.enable_move_time == 1">
          3.落子规则：每方每手最多思考{{
            Math.round(status.move_time / 60)
          }}分钟,超时判负。黑方第一手最多思考60秒，超时判弃权。
        </p>
        <p class="rule_two jcyt400" v-else>3.落子规则：超时判负</p>
        <p class="rule_two jcyt400" v-show="showPeaceAndAcrose">
          4.和棋条件： 达到{{ status.max_step }}手时一方未吃到{{
            status.win_capture
          }}颗子自动判和棋
        </p>
        <p
          v-show="showPeaceAndAcrose"
          class="rule_two jcyt400"
          v-if="
            status.black_user_type != 3 &&
            status.white_user_type != 3 &&
            status.win_capture === 0
          "
        >
          5.申请和棋： 每方最多申请{{
            status.summation_count
          }}次，申请和棋期间暂停计时
        </p>
      </div>
      <div v-else>
        <p class="jcyt400">
          1. 获胜条件：{{
            status.km != 0 ? "黑贴" + status.km + "目" : "黑不贴目"
          }}
          {{
            status.black_return_stone != 0
              ? ",黑还" + status.black_return_stone + "子获胜"
              : ""
          }}
        </p>

        <p class="rule_two jcyt400">
          2.时间规则：黑方{{ Math.round(status.black_time / 60) }}分钟
          <span v-if="status.black_byo_yomi > 0">
            {{ status.black_byo_yomi }}次{{
              status.black_byo_yomi_time
            }}秒</span
          >

          白方{{ Math.round(status.white_time / 60) }}分钟
          <span v-if="status.white_byo_yomi > 0"
            >{{ status.white_byo_yomi }}次{{
              status.white_byo_yomi_time
            }}秒</span
          >
        </p>
        <p class="rule_two jcyt400" v-if="status.enable_move_time == 1">
          3.落子规则：每方每手最多思考{{
            Math.round(status.move_time / 60)
          }}分钟,超时判负。黑方第一手最多思考60秒，超时判弃权
        </p>
        <p class="rule_two jcyt400" v-else>3.落子规则：超时判负</p>
        <p class="rule_two jcyt400">
          4.停一手：黑白双方一共达到{{
            status.territory_step
          }}手后，一方连续停3手或两方连续停2手，进行数目判定
        </p>
        <p
          class="rule_two jcyt400"
          v-show="showPeaceAndAcrose"
          v-if="
            status.black_user_type != 3 &&
            status.white_user_type != 3 &&
            status.win_capture === 0 &&
            show_peace_button
          "
        >
          5.申请和棋：每方最多申请{{
            status.summation_count
          }}次，申请和棋期间暂停计时
        </p>
        <p class="rule_two jcyt400" v-show="showPeaceAndAcrose">
          {{
            status.black_user_type != 3 &&
            status.white_user_type != 3 &&
            status.win_capture === 0 &&
            show_peace_button
              ? 6
              : 5
          }}.{{ type === "people" ? "申请数目" : "数目" }}：黑白双方一共达到{{
            status.territory_step
          }}手后，落子方可以进行{{
            type === "people" ? "申请数目" : "数目"
          }}，{{ type === "people" ? "申请数目" : "数目" }}期间暂停计时
        </p>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: "winDialog",
  data() {
    return {
      is_rule_show: false
    };
  },
  props: {
    status: {},
    type: {},
    show_peace_button: {
      default: false
    },
    showPeaceAndAcrose:{
      default: true
    }
  },
  watch: {},
  methods: {
    open() {
      this.is_rule_show = true;
    },
    close() {
      this.is_rule_show = false;
    }
  }
};
</script>
<style scoped lang="less">
.rule_bg {
  width: 488px;
  background-color: #363941;
  box-shadow: rgba(0, 0, 0, .3) 0 6px 20px 0;
  position: absolute;
  left: 0;
  top: 122px;
  font-size: 32px;
  color: #ffffff;
  padding: 20px;
  box-sizing: border-box;
  z-index: 9;
  border-radius: 24px;
  .triangle {
    position: absolute;
    top: -28px;
    right: 85px;
    width: 80px;
    height: 67.5px;
  }

  .rule_two {
    margin-top: 10px;
    line-height: 48px;
  }
  .rule {
    line-height: 48px;
  }
}
</style>
