<template>
  <div class="com-appraise" v-if="isShow" :style="{
      'background-image': `url(${require('@/assets/living/appraise.png')})`,
      'background-size': '100% 100%',
    }">
    <div class="header">
      <!-- <span>评价</span> -->
      <!-- <div class="close">×</div> -->
    </div>
    <div class="content">
      <div class="appraise">
        <p class="star" style="padding-top: 0; padding-bottom: 0; height: 10px">
          请对教师授课做出评价
        </p>
        <div class="star" style="padding-top: 0">
          <span>评分</span>
          <span class="left_content">
            <el-rate v-model="appraiseInfo.level"></el-rate>
          </span>
        </div>
        <div class="star text" style="padding-top: 0">
          <span>评语</span>
          <!-- <textarea
            runat="server"
            id="textarea"
            placeholder=""
            class="left_content"
            v-model="text"
           maxlength="300"
          ></textarea> -->
          <ty-textarea
            :value.sync="appraiseInfo.memo"
            placeholder="请在此处输入内容"
            class="textarea left_content"
            :maxlength="300"
          ></ty-textarea>
        </div>
      </div>
      <div class="but">
        <button class="submit" @click="onSubmit">
            <img src="@/assets/living/submit.png" alt="">
        </button>
        <button class="submit clo" @click="close">
            <img src="@/assets/living/closel.png" alt="">
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import textarea from "@/component/counter-textarea";
import { Toast } from "mint-ui";
export default {
  name: "comAppraise",
  props: {},
  components: {
    "ty-textarea": textarea,
  },
  data() {
    return {
      isShow: false,
      appraiseInfo: {
        level: 0,
        memo: "",
      },
    };
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    onShow() {
      this.isShow = true;
    },
    close() {
      this.isShow = false;
    },
    onSubmit() {
        if(this.appraiseInfo.level === 0){
            Toast("请对教师授课做出评价!");
            return;
        }
        this.$emit("submit", this.appraiseInfo);
        this.isShow = false;
    },
  },
  created() {},
  mounted() {},
};
</script>

<style lang="scss" scoped>
.com-appraise {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
    height: 700px;
//   background: #fff;
  color: #435588;
  font-size: 30px;
  font-weight: 800;
  z-index: 99;
  border-radius: 20px;
  font-family: "JiangChengYuanTi";
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px;
    height: 40px;
    // border-bottom: 0.2px solid #ccc;
    .close {
      color: #999;
      cursor: pointer;
    }
  }
  .content {
    padding: 40px;
    .appraise {
      .star {
        display: flex;
        // justify-content: flex-start;
        // align-items: center;
        padding: 40px;
        // height: 130px;
      }
      .left_content {
        margin-left: 20px;
      }
      .text {
        // height: 400px;
        .textarea {
          width: 80%;
          height: 180px;
          border: 1px solid #435588;
          border-radius: 24px;
          padding:10px;
          //   border: none;
          //   outline: none;
          //   resize: none;
          //   background: aliceblue;
        }
      }
    }
    .but {
      display: flex;
    //   justify-content: flex-end;
    justify-content: center;
      /* line-height: 36px; */
    //   margin-top: 52px;
      margin-bottom: 52px;
      .submit {
        margin-top: 10px;
        // height: 60px;
        // width: 100px;
        line-height: 40px;
        text-align: center;
        // background: rgb(99, 117, 223);
        color: #fff;
        border-radius: 5px;
        img{
            width: 264px;
            height: 98px;
        }
      }
      .clo {
        // background: #ccc;
        margin-left: 20px;
      }
    }
  }
}
::v-deep .el-rate {
  height: 50px;
  .el-rate__icon {
    font-size: 50px;
  }
}
button{
  background-color: transparent;
  border: 0;
}
</style>
