<template>
  <div class="modal">
    <div class="tips" :class="isCompOpen ? 'opening' : 'closing'">
      <div class="content blue-bg">
        <div class="icon">
          <img src="@/assets/tips/bg_icon.png" />
        </div>
        <div class="title jcyt600">
          <span>提示</span>
        </div>
        <img src="@/assets/tips/bg.png" class="title-bg"/>
        <div class="white-bg">
          <div class="msg jcyt600" v-html="msg">
          </div>
          <div class="btn-box flex-row content-center">
            <div class="btn cancel jcyt600" :data-content="cancelBtn" :style="{
              'background-image': `url(${require('@/assets/tips/btn_blue.png')})`,
              'background-size': '100% 100%',
            }" @click="cancel" v-if="hasCancel">{{cancelBtn}}</div>
            <slot name="footerBtn"></slot>
            <div class="btn sure jcyt600" :data-content="reallyBtn" :style="{
              'background-image': `url(${require('@/assets/tips/btn_green.png')})`,
              'background-size': '100% 100%',
            }" @click="really" v-if="hasReally">{{reallyBtn}}</div>
          </div>
          <div class="footer-text jcyt400" v-if="footerText!=''">
            {{footerText}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data(){
    return {
      isCompOpen: false
    }
  },
  props: {
    msg: {
      type: String
    },
    cancelBtn: {
      type: String
    },
    reallyBtn: {
      type: String
    },
    hasCancel: {
      type: Boolean,
      default: true
    },
    hasReally: {
      type: Boolean,
      default: true
    },
    isOpen: {
      type: Boolean,
      default: false
    },
    footerText: {
      type: String,
      default: ''
    }
  },
  watch: {
    isOpen: {
      handler(val) {
        this.isCompOpen = val;
      },
      immediate: true
    }
  },
  methods: {
    cancel(){
      this.isCompOpen = false;
      setTimeout(() => {
        this.$emit("cancel");
      }, 200);
    },
    really(){
      this.isCompOpen = false;
      setTimeout(() => {
        this.$emit("really");
      }, 200);
    },
  }
}
</script>
<style lang="less" scoped>
@keyframes myScale {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes myCloseScale {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}
.tips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 100;
  width: 1020px;
  height: 500px;
  .title {
    width: 480px;
    height: 112px;
    background-image: linear-gradient(to bottom, #4EB2FF 75%, #40ACFF 25%);
    box-shadow: rgba(0, 83, 192, .5) 0 3px 6px 0,  0 6px 6px 0 #60D4FF inset, 0 -1px 1px 1px #2484F0 inset, 0 6px 12px 0px #65D2FF inset, 0 -2px 16px 0px #0080FF inset;
    font-size: 48px;
    line-height: 112px;
    color: #fff;
    text-align: center;
    border-radius: 56px;
    position: absolute;
    top: -69px;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 12;
  }
  .title-bg {
    width: 648px;
    height: 104px;
    position: absolute;
    z-index: 9;
    top: -39px;
    left: 0;
    right: 0;
    margin: 0 auto;
  }
  .content {
    width: 1020px;
    min-height: 500px;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
  }
  .white-bg {
    width: 980px;
    min-height: 460px;
    position: relative;
    z-index: 10;
    background-color: #FAFFFB;
    box-shadow: 0 2px 6px 4px rgba(0, 96, 255, .5), 0 -5px 8px 0 #F4E1C9 inset;
    border-radius: 80px;
  }
  .blue-bg {
    box-shadow: rgba(0,0,0,.1) 0 6px 30px 0, 0 -5px 8px 0 #2E7DFF inset, 0 5px 10px 0px rgba(151, 227, 255, .65) inset;
    border-radius: 110px;
    background-image: linear-gradient(to bottom, #74BEFF, #39A1FF);
    .icon {
      position: absolute;
      bottom: 0px;
      right: 80.61px;
      width: 168.78px;
      height: 54.12px;
      overflow: hidden;
      img {
        width: 168.78px;
        height: 95.14px;
      }
    }
  }
  .btn-box {
    margin-top: 68px;
    padding-bottom: 80px;
  }
  .btn {
    width: 304px;
    height: 108px;
    box-shadow: rgba(0, 0, 0, .3) 0 4px 4px 0;
    color: #fff;
    font-size: 40px;
    border-radius: 50px;
    text-align: center;
    line-height: 108px;
  }
  .btn + .btn {
    margin-left: 124px;
  }
  .cancel {
    text-stroke: 7px #0070BC;
    -webkit-text-stroke: 7px #0070BC;
  }
  .sure {
    text-stroke: 7px #008200;
    -webkit-text-stroke: 7px #008200;
  }
  [data-content]::before {
    /* attr()是用来获取被选中元素的某属性值,并且在样式文件中使用 */
    content: attr(data-content);
    position: absolute;
    /* 实现元素外描边的关键 */
    -webkit-text-stroke: 0;
    /* 文本颜色 */
    color: #fff;
  }
  .msg {
    width: 804px;
    min-height: 100px;
    padding-top: 104px;
    margin: 0 auto;
    line-height: 50px;
    font-size: 36px;
    text-align: center;
    color: #435588;
    word-break: break-all;
  }
}
.modal {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, .7);
}
.opening {
  animation: 0.2s myScale linear;
}
.closing {
  animation: 0.2s myCloseScale linear;
}
.footer-text {
  color: #959393;
  margin-top: 20px;
  font-size: 30px;
  margin-bottom: 20px;
}
</style>