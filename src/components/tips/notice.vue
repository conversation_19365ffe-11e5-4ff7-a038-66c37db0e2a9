<template>
  <div class="modal">
    <div class="notice" :class="isCompOpen ? 'opening' : 'closing'">
      <div class="blue-bg">
        <div class="title jcyt600" :style="{
          'background-image': `url(${require('@/assets/notice/title_bg.png')})`,
          'background-size': '100% 100%',
        }">
          <span>系统公告</span>
        </div>
        <div class="close" :style="{
          'background-image': `url(${require('@/assets/notice/close.png')})`,
          'background-size': '100% 100%',
        }" @click="close"></div>
        <div class="light-blue-bg flex-row">
          <div class="menu">
            <div class="menu-item-box " :class="{'menu-item-ac':chooseIndex == index}" :style="{
            'background-image': chooseIndex == index ? `url(${require('@/assets/notice/menu_ac_bg.png')})` : `url(${require('@/assets/notice/menu_bg.png')})`,
            'background-size': '100% 100%',
            }" v-for="(item,index) in menuList" :key="index">
              <div class="menu-item jcyt600" :class="{'text-ac':chooseIndex == index}" :data-content="item">{{item}}</div>
            </div>
            <!-- <div class="menu-item-box" :style="{
              'background-image': `url(${require('@/assets/notice/menu_bg.png')})`,
              'background-size': '100% 100%',
              }">
              <div class="menu-item jcyt600">学校公告</div>
            </div>
            <div class="menu-item-box jcyt600" :style="{
            'background-image': `url(${require('@/assets/notice/menu_bg.png')})`,
            'background-size': '100% 100%',
            }">
              <div class="menu-item jcyt600">联系我们</div>
            </div> -->
          </div>
          <div class="content jcyt500" v-html="content">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data(){
    return {
      isCompOpen: false,
      chooseIndex: 0,
      menuList: ["更新公告",], // "学校公告", "联系我们"
      content: ""
    }
  },
  computed: {
    // versionData(){
    //   return this.$store.getters.getVersionData;
    // }
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    content : {
      type:String
    }
  },
  watch: {
    isOpen: {
      handler(val) {
        this.isCompOpen = val;
      },
      immediate: true
    }
  },
  methods:{
    changeMenu(i){
      this.chooseIndex = i;
    },
    close(){
      this.isCompOpen = false;
      setTimeout(() => {
        this.$emit("close");
      }, 200);
    },
  }

}
</script>
<style lang="less" scoped>
.modal {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, .7);
}
.notice {
  width: 1660px;
  height: 736px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  .blue-bg {
    width: 1660px;
    height: 736px;
    position: relative;
    background-image: linear-gradient(to bottom, #74BEFF, #39A1FF);
    box-shadow: rgba(0, 0, 0, .1) 0 6px 30px 0, 0 -5px 8px 0 #2E7DFF inset, rgba(151, 227, 255,.65) 0 5px 10px 0 inset;
    border-radius: 100px;
    padding: 24px;
    box-sizing: border-box;
  }
  .title {
    width: 658px;
    height: 158px;
    line-height: 135px;
    color: #F96D02;
    font-size: 56px;
    text-align: center;
    position: absolute;
    top: -73px;
    left: 345px;
    right: 0;
    margin: 0 auto;
    span {
      margin-left: 8px;
    }
  }
  .close {
    width: 120px;
    height: 120px;
    position: absolute;
    right: -30px;
    top: -47px;
    cursor: pointer;
  }
  .light-blue-bg {
    background: #96CDF7;
    box-shadow: rgba(0, 34, 90, .5) 0 0 8px 0;
    border-radius: 80px;
    height: 688px;
    width: 1612px;
  }
  .menu {
    width: 352px;
    height: 688px;
    padding-left: 24px;
    box-sizing: border-box;
    padding-top: 40px;
    background: #3E649C;
    border-radius: 80px 0 0 80px;
    box-shadow: 0 4px 8px 0 rgba(24, 48, 99, .41), 0 8px 12px 0 rgba(24, 48, 99, .4) inset;
  }
  .menu-item-box {
    width: 328px;
    height: 104px;
    line-height: 96px;
    border-radius: 20px;
    margin-top: 4px;
    margin-left: -12px;
  }
  div.menu-item-box:first-child {
    margin-top: 0;
  }
  .menu-item {
    width: 328px;
    height: 104px;
    line-height: 96px;
    font-size: 40px;
    text-align: center;
    color: #fff;
  }
  .menu-item-ac {
    width: 351px;
  }
  .content {
    margin-left: 24px;
    width: 1212px;
    height: 616px;
    margin-top: 48px;
    padding: 68px 80px;
    box-sizing: border-box;
    color: #435588;
    font-size: 32px;
    line-height: 48px;
    word-break: break-all;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 0!important;
    }
    border-radius: 60px;
    background: #F6FCFF;
    box-shadow: rgba(94, 169, 255, .6) 0 2px 6px 4px,0 -5px 8px 0 #AFE2FF inset;
  }
  [data-content]::before {
    /* attr()是用来获取被选中元素的某属性值,并且在样式文件中使用 */
    content: attr(data-content);
    position: absolute;
    /* 实现元素外描边的关键 */
    -webkit-text-stroke: 0;
    /* 文本颜色 */
    color: #fff;
  }
  .text-ac {
    text-stroke: 7px #008CA5;
    -webkit-text-stroke: 7px #008CA5;
  }
}
@keyframes myScale {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes myCloseScale {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}
.opening {
  animation: 0.2s myScale linear;
}
.closing {
  animation: 0.2s myCloseScale linear;
}
</style>