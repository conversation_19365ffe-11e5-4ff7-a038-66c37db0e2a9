<template>
  <div class="modal">
    <div class="tips" :class="isCompOpen ? 'opening' : 'closing'">
      <div class="content blue-bg">
        <div class="title jcyt600">
          <span>设置</span>
        </div>
        <img src="@/assets/tips/bg.png" class="title-bg"/>
        <div class="close" :style="{
          'background-image': `url(${require('@/assets/notice/close.png')})`,
          'background-size': '100% 100%',
        }" @click="cancel"></div>
        <!-- <div class="menu">
          <div class="menu-item-label jcyt600" data-content3="系统设置" :style="{
              'background-image': `url(${require('@/assets/tips/menu_bg.png')})`,
              'background-size': '100% 100%',
            }" v-if="currentIndex != 0">
            系统设置
          </div>
          <div class="menu-item-label-ac jcyt600" data-content4="系统设置" :style="{
              'background-image': `url(${require('@/assets/tips/menu_bg_ac.png')})`,
              'background-size': '100% 100%',
            }" v-else>
            系统设置
          </div>
        </div> -->
        <div class="white-bg">
          <div class="msg">
            <div class="items" :class="{'item-close': !switchFlag}">
              <span class="items-label jcyt600">落子音</span>
              <div class="m-switch" :class="[switchFlag ? 'switch-bg-open' : 'switch-bg-close', chessMoveSound ? '' : 'm-switch-close']" @click="switchFlag = !switchFlag">
                <div class="m-switch-inner" :class="switchFlag ? 'switch-inner-bg-open' : 'switch-inner-bg-close'">
                  <div class="m-switch-label jcyt600" data-content1="开启" v-if="switchFlag">
                    开启
                  </div>
                  <div class="m-switch-label-close jcyt600" data-content2="关闭" v-else>
                    关闭
                  </div>
                </div>
                <img class="inner-btn" :src="switchFlag ? require('@/assets/tips/open.png') : require('@/assets/tips/close.png')" :class="switchFlag ? 'switch-open' : 'switch-close'"/>
              </div>
            </div>
          </div>
          <div class="btn-box flex-row content-center">
            <div class="btn cancel jcyt600" data-content="取消" :style="{
              'background-image': `url(${require('@/assets/tips/btn_yellow.png')})`,
              'background-size': '100% 100%',
            }" @click="cancel">取消</div>
            <div class="btn sure jcyt600" data-content="确定" :style="{
              'background-image': `url(${require('@/assets/tips/btn_green.png')})`,
              'background-size': '100% 100%',
            }" @click="really">确定</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data(){
    return {
      isCompOpen: false,
      switchFlag: true, //开启
      currentIndex: 0
    }
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    chessMoveSound(){
      return this.$store.getters.getChessMoveSound;
    }
  },
  watch: {
    isOpen: {
      handler(val) {
        this.isCompOpen = val;
      },
      immediate: true
    },
    chessMoveSound: {
      handler(val){
        this.switchFlag = val;
      },
      immediate: true
    }
  },
  methods: {
    cancel(){
      this.isCompOpen = false;
      setTimeout(() => {
        this.$emit("cancel");
      }, 200);
    },
    really(){
      this.isCompOpen = false;
      this.$store.commit("setChessMoveSound", this.switchFlag);
      window.localStorage.setItem("chessMoveSound" , this.switchFlag ? "true" : "false");
      setTimeout(() => {
        this.$emit("really");
      }, 200);
    },
  }
}
</script>
<style lang="less" scoped>
@keyframes myScale {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes myCloseScale {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

@keyframes myClose {
  0% {
    right: 0;
  }
  100% {
    right: 134px;
  }
}
@keyframes myOpen {
  0% {
    right: 134px;
  }
  100% {
    right: 0;
  }
}

@keyframes myBgOpen {
  0% {
    background-image: linear-gradient(to bottom, #D6DAD6, #F0F1F0);
  }
  100% {
    background-image: linear-gradient(to bottom, #BBE8F7, #E1FAFF);
  }
}
@keyframes myBgClose {
  0% {
    background-image: linear-gradient(to bottom, #BBE8F7, #E1FAFF);
  }
  100% {
    background-image: linear-gradient(to bottom, #D6DAD6, #F0F1F0);
  }
}

@keyframes myInnerBgClose {
  0% {
    background-image: linear-gradient(to bottom, #31B133, #B4E260);
  }
  100% {
    background-image: linear-gradient(to bottom, #818284, #BEBEBD);
  }
}
@keyframes myInnerBgOpen {
  0% {
    background-image: linear-gradient(to bottom, #818284, #BEBEBD);
  }
  100% {
    background-image: linear-gradient(to bottom, #31B133, #B4E260);
  }
}


.tips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 100;
  width: 1120px;
  height: 780px;
  .title {
    width: 480px;
    height: 112px;
    background-image: linear-gradient(to bottom, #4EB2FF 75%, #40ACFF 25%);
    box-shadow: rgba(0, 83, 192, .5) 0 3px 6px 0,  0 6px 6px 0 #60D4FF inset, 0 -1px 1px 1px #2484F0 inset, 0 6px 12px 0px #65D2FF inset, 0 -2px 16px 0px #0080FF inset;
    font-size: 48px;
    line-height: 112px;
    color: #fff;
    text-align: center;
    border-radius: 56px;
    position: absolute;
    top: -69px;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 12;
  }
  .title-bg {
    width: 648px;
    height: 104px;
    position: absolute;
    z-index: 9;
    top: -39px;
    left: 0;
    right: 0;
    margin: 0 auto;
  }
  .content {
    width: 1120px;
    min-height: 790px;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
  }
  .white-bg {
    width: 1080px;
    min-height: 750px;
    position: relative;
    z-index: 10;
    background-color: #FAFFFB;
    box-shadow: 0 2px 6px 4px rgba(0, 96, 255, .5), 0 -5px 8px 0 #F4E1C9 inset;
    border-radius: 80px;
  }
  .blue-bg {
    box-shadow: rgba(0,0,0,.1) 0 6px 30px 0, 0 -5px 8px 0 #2E7DFF inset, 0 5px 10px 0px rgba(151, 227, 255, .65) inset;
    border-radius: 110px;
    background-image: linear-gradient(to bottom, #74BEFF, #39A1FF);
  }
  .btn-box {
    padding-top: 60px;
    padding-bottom: 66px;
    background: rgba(198, 243, 255, .5);
    border-radius: 0 0 80px 80px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 1080px;
  }
  .btn {
    width: 304px;
    height: 108px;
    box-shadow: rgba(0, 0, 0, .3) 0 4px 4px 0;
    color: #fff;
    font-size: 40px;
    border-radius: 50px;
    text-align: center;
    line-height: 108px;
    cursor: pointer;
  }
  .btn + .btn {
    margin-left: 124px;
  }
  .cancel {
    text-stroke: 7px #A86200;
    -webkit-text-stroke: 7px #A86200;
  }
  .sure {
    text-stroke: 7px #008200;
    -webkit-text-stroke: 7px #008200;
  }
  [data-content]::before {
    /* attr()是用来获取被选中元素的某属性值,并且在样式文件中使用 */
    content: attr(data-content);
    position: absolute;
    /* 实现元素外描边的关键 */
    -webkit-text-stroke: 0;
    /* 文本颜色 */
    color: #fff;
  }
  .msg {
    width: 744px;
    min-height: 526px;
    padding-top: 104px;
    margin: 0 auto;
  }
}
.modal {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, .7);
}
.opening {
  animation: 0.2s myScale linear;
}
.closing {
  animation: 0.2s myCloseScale linear;
}
.close {
  width: 112px;
  height: 112px;
  position: absolute;
  right: -32px;
  top: -17px;
  cursor: pointer;
  z-index: 20;
}
.items {
  width: 100%;
  height: 78px;
  border-radius: 39px;
  background: #C6F2FF;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .items-label {
    font-size: 40px;
    color: #29B1BD;
    margin-left: 40px;
    line-height: 78px;
  }
}
.item-close {
  background: #E1E2E7;
  .items-label {
    color: #837E7C;
  }
}
.switch-close {
  animation: 0.2s myClose linear forwards;
}
.switch-open {
  animation: 0.2s myOpen linear forwards;
}

.switch-bg-close {
  animation: 0.2s myBgClose linear forwards;
}

.switch-bg-open {
  animation: 0.2s myBgOpen linear forwards;
}

.switch-inner-bg-close {
  animation: 0.2s myInnerBgClose linear forwards;
}

.switch-inner-bg-open {
  animation: 0.2s myInnerBgOpen linear forwards;
}
.m-switch {
  height: 82px;
  width: 216px;
  box-sizing: border-box;
  background-image: linear-gradient(to bottom, #BBE8F7, #E1FAFF);
  border-radius: 44px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  .m-switch-inner {
    width: calc(100% - 12px);
    height: calc(100% - 12px);
    background-image: linear-gradient(to bottom, #31B133, #B4E260);
    border-radius: 44px;
    .m-switch-label {
      font-size: 36px;
      line-height: 66px;
      margin-left: 24px;
      text-stroke: 7px #188810;
      -webkit-text-stroke: 7px #188810;
    }
    [data-content1]::before {
      /* attr()是用来获取被选中元素的某属性值,并且在样式文件中使用 */
      content: attr(data-content1);
      position: absolute;
      /* 实现元素外描边的关键 */
      -webkit-text-stroke: 0;
      /* 文本颜色 */
      color: #C0F35E;
    }
  }
  .inner-btn {
    width: 82px;
    height: 82px;
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 50%;
    box-shadow: rgba(0, 0, 0, .3) 0 1px 2px 0;
  }
  .m-switch-label-close {
    text-stroke: 7px #6B6D6B;
    -webkit-text-stroke: 7px #6B6D6B;
    font-size: 36px;
    line-height: 66px;
    margin-right: 24px;
    text-align: right;
  }
  [data-content2]::before {
    /* attr()是用来获取被选中元素的某属性值,并且在样式文件中使用 */
    content: attr(data-content2);
    position: absolute;
    /* 实现元素外描边的关键 */
    -webkit-text-stroke: 0;
    /* 文本颜色 */
    color: #D4D3D6;
  }
}

.m-switch-close {
  background-image: linear-gradient(to bottom, #D6DAD6, #F0F1F0);
  .m-switch-inner {
    background-image: linear-gradient(to bottom, #818284, #BEBEBD);
  }
  .inner-btn {
    right: 134px;
  }
}


.menu {
  position: absolute;
  top: 132px;
  left: -208px;
}
.menu-item-label {
  border-radius: 56px 0 0 56px;
  box-shadow: rgba(0, 17, 51, .25) 0 4px 2px 0;
  width: 224px;
  height: 112px;
  font-size: 36px;
  text-stroke: 7px #2370AA;
  -webkit-text-stroke: 7px #2370AA;
  line-height: 108px;
  padding-left: 40px;
  box-sizing: border-box;
}
[data-content3]::before {
  /* attr()是用来获取被选中元素的某属性值,并且在样式文件中使用 */
  content: attr(data-content3);
  position: absolute;
  /* 实现元素外描边的关键 */
  -webkit-text-stroke: 0;
  /* 文本颜色 */
  color: #fff;
}
.menu-item-label-ac {
  border-radius: 56px 0 0 56px;
  box-shadow: rgba(0, 17, 51, .25) 0 4px 2px 0;
  width: 224px;
  height: 112px;
  font-size: 36px;
  line-height: 108px;
  padding-left: 40px;
  box-sizing: border-box;
  text-stroke: 7px #84FF22;
  -webkit-text-stroke: 7px #84FF22;
}
[data-content4]::before {
  /* attr()是用来获取被选中元素的某属性值,并且在样式文件中使用 */
  content: attr(data-content4);
  position: absolute;
  /* 实现元素外描边的关键 */
  -webkit-text-stroke: 0;
  /* 文本颜色 */
  color: #005D08;
}
</style>