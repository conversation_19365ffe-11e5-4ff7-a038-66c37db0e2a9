<template>
  <div class="living-course">
    <swiper
      class="swiper swiper-no-swiping"
      :options="swiperOption"
      ref="Swiper"
    >
      <swiper-slide v-for="(item, index) in list" :key="index">
        <div class="living-course-pc swiper-item" @click="toHtml(item)">
          <img :src="item.cover_pic" class="course-pic" />
          <p class="title">{{ item.name }}</p>
          <div class="middle flex-row content-between">
            <div class="teacher flex-row items-center">
              <img :src="item.teacher_avatar" />
              <p class="name">{{ item.teacher_name }}</p>
            </div>
            <p class="name">{{ formatValue(item.student_numb) }}人</p>
          </div>
          <div class="bottom flex-row content-between">
            <p class="time">
              {{
                `${getDate(item.estimated_start_time)} 周${getWeek(
                  item.estimated_start_time
                )}`
              }}
              <span class="special">{{
                getTime(item.estimated_start_time)
              }}</span>
            </p>
            <div
              class="button flex-row items-center content-center"
              :style="{
                'background-image':
                  'url(' +
                  require(
                    item.status == 3
                    ? '../../assets/study/btn_bg_living.png'
                    : item.status <= 2
                    ? item.can_enter
                      ? '../../assets/study/btn_bg_living.png'
                      : '../../assets/study/btn_bg_start.png'
                    : '../../assets/study/btn_bg_restart.png') +
                  ')',
              }"
            >
              <img
                :src="
                  require(item.status == 3
                    ? '../../assets/study/status_living.png'
                    : item.status <= 2
                    ? '../../assets/study/status_start.png'
                    : '../../assets/study/status_restart.png')
                "
                :class="item.status == 3 ? 'img1' : 'img2'"
              />
              <span>{{
                item.status == 3
                  ? "直播中"
                  : item.status == 2 || item.status == 1
                  ? "即将开始"
                  : "课程回放"
              }}</span>
            </div>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide></swiper-slide>
      <div
        class="swiper-button-prev"
        slot="button-prev"
        :style="{
          'background-image': `url(${require('@/assets/index/左箭头.png')})`,
          'background-size': 'contain',
        }"
      ></div>
      <div
        class="swiper-button-next"
        slot="button-next"
        :style="{
          'background-image': `url(${require('@/assets/index/右箭头.png')})`,
          'background-size': 'contain',
        }"
      ></div>
    </swiper>
    <div class="modal" v-if="dialogVisible"></div>
    <div class="dialog" v-if="dialogVisible">
      <img
        src="../../assets/study/圆叉.png"
        class="dialog-clear"
        @click="dialogVisible = false"
      />
      <p class="title">输入房间密码</p>
      <div class="box">
        <input
          class=""
          v-model="pwd"
          placeholder="请输入房间密码"
          type="password"
        />
        <img
          src="../../assets/study/清除.png"
          class="clear"
          v-if="pwd != ''"
          @click="pwd = ''"
        />
      </div>
      <button
        class="button"
        :style="{
          'background-image':
            'url(' + require('../../assets/study/login_btn.png') + ')',
        }"
        @click="openHtml"
      >
        确认
      </button>
    </div>
  </div>
</template>
<script>
import studyApi from "@/api/study";
import timeFormat from "@/public/timeFormat";
import { sha256 } from "js-sha256";
import { Toast } from "mint-ui";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
import { ipcRenderer } from "electron";
import moment from "moment";

export default {
  data() {
    return {
      list: [],
      ticket: "",
      pwd: "",
      dialogVisible: false,
      id: "",
      itemPwd: "",
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 0,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      new_status: {},
    };
  },
  components: { swiper, swiperSlide },
  mounted() {
    this.getList();
    this.$nextTick(() => {
      if (this.$socket.conn.ws.readyState == 1) {
        this.$socket.send({
          message_type: "bind_group",
          data: { group_id: "live:list" },
        });
      }
    });
  },
  computed: {
    wsMessage() {
      return this.$store.getters.getMessage;
    },
  },
  watch: {
    wsMessage: {
      handler(val) {
        let msg = val.data;
        if (val["group"] !== "live:list") {
          return;
        }
        switch (msg.message_type) {
          case "live_status":
            let data = msg.data;
            this.list.forEach((item, index) => {
              if (item.id == data.id) {
                item.status = data.status;
                if (data.status == 2) {
                  let nowTime = moment().utc().valueOf();
                  let canEnterTime = item.student_early_entry_time * 60 * 1000;
                  let estimatedTime = item.estimated_start_time * 1000;
                  // console.log(nowTime, "now");
                  // console.log(canEnterTime, "can");
                  // console.log(estimatedTime, "estimatedTime");
                  item.can_enter = nowTime >= estimatedTime - canEnterTime;
                  //console.log(item.can_enter);
                }
                this.$set(this.list, index, item);
              }
            });
            break;
        }
      },
      deep: true,
    },
  },
  methods: {
    getTime(time) {
      return timeFormat.GetCustTime(time * 1000, "HH:mm");
    },
    getDate(time) {
      return timeFormat.GetCustTime(time * 1000, "MM月DD日");
    },
    getWeek(time) {
      let week = timeFormat.GetCustTime(time * 1000, "E");
      let arr = ["一", "二", "三", "四", "五", "六", "日"];
      return arr[week - 1];
    },
    formatValue(num) {
      var res = num.toString().replace(/\d+/, function (n) {
        // 先提取整数部分
        return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
          return $1 + ",";
        });
      });
      return res;
    },
    async getList() {
      let res = await studyApi.getLivingCourseApi();
      this.list = res.data.data == null ? [] : res.data.data;
      this.list.forEach((item,index) => {
        if(item.status == 2) {
          let nowTime = moment().utc().valueOf();
          let canEnterTime = item.student_early_entry_time * 60 * 1000;
          let estimatedTime = item.estimated_start_time * 1000;
          item.can_enter = nowTime >= estimatedTime - canEnterTime;
        }
        this.$set(this.list, index, item);
      });
    },
    async getTicket(id) {
      let res = await studyApi.getLivingCourseTicketApi({ live_id: id });
      this.ticket = res.data.data.ticket;
      this.dialogVisible = false;
      __bl.sum("直播课空中课堂学习");
      // window.open(
      //   "https://class.ushu.com/classroom/studentGetToken.html?ticket=" +
      //     this.ticket,
      //     "_blank",
      //     'width=' + (window.screen.availWidth - 10) + ',height=' + (window.screen.availHeight - 70) + ', top = 0, left = 0, status = no'
      // );
      ipcRenderer.send("openWindow", "https://class.ushu.com/classroom/studentGetToken.html?ticket=" + this.ticket);
      const os = require('os');
      if(os.platform() == 'darwin') {
        try {
          const mediaDevices = navigator.mediaDevices;
          const stream = await mediaDevices.getUserMedia({
            video: true,
            audio: true
          });
          // 进行摄像头和麦克风的操作
        } catch (err) {
          console.log('无法访问摄像头或麦克风');
        }
      }
    },
    toHtml(item) {
      if (item.status > 2 || (item.status == 2 && item.can_enter)) {
        if (item.status == 4) {
          if (item.url_list.length > 0 &&
              item.url_list != null && item.url_list[0] != "") {
            // 打开直播 嵌入aliyun
            window.localStorage.setItem("ty_url_list", item.url_list.toString());
            let winURL = process.env.NODE_ENV !== "production" ? `http://localhost:8080` : `app://./index.html`;
            ipcRenderer.send("openWindow", winURL + "#/livingCourseReplay");
          } else {
            Toast("暂无回放");
          }
        } else {
          if (item.type == 2) {
            this.dialogVisible = true;
            this.pwd = "";
            this.id = item.id;
            this.itemPwd = item.passwd;
          } else {
            this.getTicket(item.id);
          }
        }
      }
    },
    openHtml() {
      let p = sha256(`${this.pwd}_eStarGo2019`);
      if (p == this.itemPwd) {
        this.getTicket(this.id);
      } else {
        Toast("密码输入错误");
      }
    },
  },
  beforeDestroy() {
    if (this.$socket.conn) {
      this.$socket.send({
        message_type: "un_bind_group",
        data: { group_id: "live:list" },
      });
    }
  },
  beforeDestroy(){
    if(this.$socket.conn){
      this.$socket.send({"message_type":"unbind_group","data": {"group_id":"live:list"} });
    }
  }
};
</script>
<style lang="less" scoped>
.living-course {
  margin: 0 80px;
}

::-webkit-scrollbar {
  width: 0;
}

.items-center {
  align-items: center;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.content-between {
  justify-content: space-between;
}

.content-center {
  justify-content: center;
}

.living-course-pc {
  width: 620px;
  height: 624px;
  border-radius: 50px;
  background-color: #fff;
  box-shadow: 0 10px 50px 0 rgba(132, 184, 198, 0.25);
  padding: 24px;
  box-sizing: border-box;
  cursor: pointer;

  .course-pic {
    width: 572px;
    height: 288px;
    border-radius: 40px;
    object-fit: cover;
  }

  .title {
    font-size: 48px;
    color: #333333;
    font-weight: bold;
    margin-top: 32px;
  }

  .middle {
    margin-top: 16px;
  }

  .teacher {
    img {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-right: 16px;
    }
  }

  .name {
    font-size: 26px;
    color: #999999;
  }

  .time {
    font-weight: bold;
    color: #333333;
    font-size: 32px;

    .special {
      display: block;
    }
  }

  .button {
    width: 224px;
    height: 80px;
    border-radius: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;

    span {
      font-size: 32px;
      color: #fff;
      font-weight: bold;
      margin-left: 14px;
    }
  }

  .bottom {
    margin-top: 32px;
  }

  .img1 {
    width: 32px;
    height: 30px;
  }

  .img2 {
    width: 40px;
    height: 40px;
  }
}

.dialog {
  width: 1152px;
  height: 744px;
  border-radius: 110px;
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  z-index: 20;

  .title {
    font-size: 56px;
    font-weight: bold;
    color: #333333;
    padding-top: 64px;
    text-align: center;
  }

  input {
    border: none;
    background: #f6f8fb;
    border-radius: 60px;
    height: 120px;

    margin-left: 176px;
    width: 740px;
    padding-left: 60px;
    font-size: 40px;
    color: #333333;

    &::after {
      border: none;
    }

    outline: none;
  }

  input:focus {
    border: none;
  }

  .button {
    width: 800px;
    height: 120px;
    border-radius: 60px;
    background-size: cover;
    background-repeat: no-repeat;
    margin-top: 80px;
    border: none;
    font-size: 40px;
    color: #ffffff;
    margin-left: 176px;
    outline: none;
    cursor: pointer;
  }

  .clear {
    position: absolute;
    right: 216px;
    top: 34px;
    height: 56px;
    width: 56px;
    cursor: pointer;
  }

  .box {
    position: relative;
    margin-top: 120px;
  }
}

.dialog-clear {
  position: absolute;
  right: -120px;
  width: 96px;
  height: 96px;
  z-index: 30;
  cursor: pointer;
}

.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}

.swiper-button-prev {
  width: 148px;
  height: 148px;

  left: 40px;
  margin-top: -74px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

.swiper-button-prev::after {
  content: "";
}

.swiper-button-next {
  width: 148px;
  height: 148px;

  right: 40px;
  bottom: 10px;
  margin-top: -74px;
  border-radius: 50%;

  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
}

.swiper-button-next::after {
  content: "";
}

.swiper-slide {
  width: 620px !important;
  margin-right: 60px;
  height: 624px;
}
</style>
