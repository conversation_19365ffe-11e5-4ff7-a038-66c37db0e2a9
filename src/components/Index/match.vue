<template>
<!--废弃-->
  <div class="container">
    <div
      @click="goMatch('nwpMatch')"
      class="item evaluation"
      :style="{
        'background-image': `url(${require('@/assets/index/聂道赛.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div
      @click="goMatch('seasonIndex')"
      class="item evaluation"
      :style="{
        'background-image': `url(${require('@/assets/index/排位赛.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div
      @click="goMatch('rule')"
      class="item evaluation"
      :style="{
        'background-image': `url(${require('@/assets/index/好友约战.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
  </div>
</template>

<script>
import InfiniteLoading from "vue-infinite-loading";
import { Toast } from "mint-ui";

export default {
  name: "Matchlist",
  components: { InfiniteLoading },
  data() {
    return {};
  },
  created() {},
  computed: {},
  methods: {
    goMatch(route) {
      if (route) {
        this.$router.push(`/${route}`);
      } else {
        Toast({message: "研发中~敬请期待", duration: 1000 });
      }
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 1000px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  div {
    width: 674px;
    height: 810px;
  }
}
</style>
