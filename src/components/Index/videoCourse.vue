<template>
  <div class="wrap swiper-no-swiping" :class="courseList.length == 0 || courseList == null ? 'wrap-top' : 'wrap-course-top'">
    <div
      v-if="courseList.length == 0 || courseList == null"
      class="appointment"
      :style="{
        'background-image': `url(${require('@/assets/index/box.png')})`,
        'background-size': 'contain'
      }"
    >
      <div class="is_center">
        <p class="title">约课提醒</p>
        <p class="des">没有待上课程, 前往APP购买课程！</p>
        <div class="ma">
          <img src="" alt="" />
        </div>
        <div class="duan">iPhone/iPad客户端</div>
      </div>
    </div>

    <swiper class="swiper" :options="swiperOption" ref="Swiper" v-else>
      <swiper-slide v-for="(item, index) in courseList" :key="index">
        <div class="swiper-item" @click="goStudy(item.id)">
          <div class="item-box">
            <img :src="item.image_pad" alt="" class="left_img" />
            <p class="lesson_index jcyt600">{{ item.name }}</p>
            <p class="lesson_name jcyt400">{{ item.description }}</p>
            <div class="go_study jcyt600">去学习</div>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide> </swiper-slide>
      <div
        class="swiper-button-prev"
        slot="button-prev"
        :style="{
          'background-image': `url(${require('@/assets/index/左箭头.png')})`,
          'background-size': 'contain'
        }"
      ></div>
      <div
        class="swiper-button-next"
        slot="button-next"
        :style="{
          'background-image': `url(${require('@/assets/index/右箭头.png')})`,
          'background-size': 'contain'
        }"
      ></div>
    </swiper>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
import courseListApi from "@/api/course.js";

export default {
  name: "swiperS",
  data() {
    return {
      courseList: [],
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 0,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        }
      }
    };
  },
  components: { swiper, swiperSlide },
  methods: {
    goStudy(id) {
      __bl.sum("直播课堂");
      this.$router.push({
        path: "/lessonList",
        query: {
          course_id: id
        }
      });
    }
  },
  mounted() {
    courseListApi.CourseListApi().then((res) => {
      this.courseList = res.data ?? [];
    });
  }
};
</script>

<style scoped lang="less">
.wrap-top {
  padding-top: 130px;
}
.wrap-course-top {
  padding-top: 248px;
}
.wrap {
  position: relative;
  .appointment {
    width: 686px;
    height: 756px;
    margin: auto;
    text-align: center;
    overflow: hidden;
    .is_center {
      margin-right: 40px;
      .title {
        font-size: 40px;
        color: #303233;
        text-align: center;
        line-height: 50px;
        margin-top: 220px;
        font-weight: 700;
      }
      .des {
        font-size: 22px;
        color: #797d80;
        text-align: center;
        line-height: 22px;
        margin-top: 20px;
      }
      .ma {
        width: 200px;
        height: 200px;
        background: #dbebfc;
        border-radius: 25px;
        margin: 24px auto;
      }
      .duan {
        margin: 0px auto;
        width: 260px;
        height: 48px;
        line-height: 48px;
        background: #31bfff;
        border: 2px solid #00b0ff;
        box-shadow: 0 3px 0 0 rgba(0, 0, 0, 0.1);
        border-radius: 24px;
        font-size: 22px;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}
.swiper-slide-active {
  margin-left: 80px;
}
// .swiper-button-prev {
//   width: 100px;
//   height: 100px;
// }
// .swiper-button-next {
//   width: 100px;
//   height: 100px;
// }
.swiper-slide {
  height: 652px;
  margin-right: 56px;
  width: 620px!important;
}
.swiper-item {
  width: 620px;
  height: 632px;
  background-image: linear-gradient(to bottom,#DCF2A5,#B9CF6F);
  border-radius: 100px;
  box-shadow: 0 6px 20px 0 rgba(0, 85, 136, 0.3), 0 -5px 8px 0 #9DBF3D inset,  0 2px 6px 0 #F9FFF3 inset;
  cursor: pointer;
  padding: 12px 12px 24px 12px;
  box-sizing: border-box;
  .item-box {
    height: 596px;
    background: #fff;
    box-shadow: 0 4px 6px 2px rgba(108, 178, 27, .2), 0 -5px 8px 0 #E0DAD3 inset;
    border-radius: 100px;
  }
  .left_img {
    width: 556px;
    height: 304px;
    border-radius: 90px 90px 50px 50px;
    margin: 20px 20px 0 20px;
  }
  .lesson_index {
    width: 556px;
    line-height: 56px;
    font-size: 44px;
    color: #333333;
    text-align: center;
    margin: 16px auto 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .lesson_name {
    width: 556px;
    font-size: 30px;
    color: #999999;
    line-height: 40px;
    margin: 8px auto 0;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .go_study {
    width: 312px;
    height: 88px;
    background-image: linear-gradient(to bottom, #30C3FF , #00BAFF);
    box-shadow: inset 0 -6px 3px 0 #00A1EE, 0 4px 8px 2px rgba(99, 158, 198, .4);
    border-radius: 44px;
    font-size: 40px;
    color: #ffffff;
    text-align: center;
    line-height: 88px;
    margin: 24px auto 0;
  }
}
.swiper-button-prev {
  width: 128px;
  height: 128px;
  left: 20px;
  margin-top: -74px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}
.swiper-button-prev::after {
  content: "";
}
.swiper-button-next {
  width: 128px;
  height: 128px;

  right: 20px;
  bottom: 10px;
  margin-top: -74px;
  border-radius: 50%;

  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
}
.swiper-button-next::after {
  content: "";
}
// ::v-deep.swiper-slide{
//   width:472px !important;
//   margin-right: 48px !important;
// }
</style>
