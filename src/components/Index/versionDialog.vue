<template>
  <transition name="van-fade">
    <div class="version-wrap" v-if="version_dialog">
      <div class="version-dialog">
        <div
          class="head"
          :style="{
            background: `url(${require('@/assets/index/版本更新弹窗.png')}) center no-repeat transparent
    `,
            'background-size': 'contain'
          }"
        ></div>
        <!-- <img class="close" src="../../assets/index/cha.png" @click="close" v-if="!is_force_updated" /> -->

        <p class="version_n jcyt500">
          版本更新
          <span class="version_s jcyt500"> {{ versionData["version"] }}</span>
        </p>

        <div class="pop_text jcyt500" v-html="versionData.note"></div>
        <div class="button_reload jcyt600" @click="update">立即更新</div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: "versionDialog",
  props: {
    version_dialog: {
      type: Boolean
    },
    versionData: {},
    is_force_updated: {}
  },
  methods: {
    update() {
      window.open(this.versionData["url"], "_blank");
    },
    close(){
      this.$emit('close_version_dialog')
    
    }
  }
};
</script>
<style scoped lang="less">
.version-wrap {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 0;
  top: 0;
  z-index: 20000;
}
.version-dialog {
  position: absolute;
  width: 582px;
  height: 832px;
  top: 50%;
  left: 50%;
  margin-left: -291px;
  margin-top: -316px;
  z-index: 20001;
  background-color: #fff;
  border-radius: 35px;
  padding: 30px;
  box-sizing: border-box;
  .head {
    width: 431px;
    height: 429px;
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -200px;
    top: -200px;
  }

  .close {
    width: 50px;
    height: 50px;
    position: absolute;
    top: -80px;
    right: 6px;
    cursor: pointer;
  }
  .version_n {
    font-size: 44px;
    color: #333333;
    letter-spacing: 0;
    text-align: left;
    margin-top: 200px;
    .version_s {
      color: #00b0ff;
      font-weight: 700;
    }
  }
  .pop_text {
    height: 320px;
    overflow-y: scroll;
  }
  ::v-deep img {
    max-width: 100%;
  }
  .button_reload {
    width: 486px;
    height: 88px;
    line-height: 88px;
    background: #31bfff;
    border-radius: 44px;
    font-size: 34px;
    color: #ffffff;
    text-align: center;
    margin: 64px auto;
    cursor: pointer;
  }
}
</style>
