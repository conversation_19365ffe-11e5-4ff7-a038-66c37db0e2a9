<template>
  <div class="lesson_info_star_wrap">
    <div class="lesson_info_star_item" v-for="(star, index) in star_arr" :key="index">
      <img
       class="lesson_info_star_item_img"
        :src="
          star == true
            ? require('@/assets/lessonInfo/star.png')
            : require('@/assets/lessonInfo/star_dis.png')
        "
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "lessonInfoStarWrap",
  props: {
    total_num: {},
    finish_num: {}
  },
  data() {
    return {
      star_arr: []
    };
  },
  computed: {
    demo_width() {
      return (this.star_arr.length - 1) * 36;
    }
  },

  mounted() {
    if (this.finish_num > 0) {
      for (var i = 0; i < this.finish_num; i++) {
        this.star_arr.push(true);
      }
    }
    if (this.total_num - this.finish_num > 0) {
      for (var j = 0; j < this.total_num - this.finish_num; j++) {
        this.star_arr.push(false);
      }
    }
  }
};
</script>

<style lang="less">
.lesson_info_star_wrap {
  height: 64px;
  
  .lesson_info_star_item {
    width: 40px;
    height: 40px;
    float: left;
    margin-left:8px;

    .lesson_info_star_item_img {
      width: 40px;
      height: 40px;
    }
  }
  .lesson_info_star_item:nth-child(1){
    margin-left:0;
  }
}
</style>
