<template>
  <div class="living-course">
    <swiper
      class="swiper swiper-no-swiping"
      :options="swiperOption"
      ref="Swiper"
      v-if="list.length > 0"
    >
      <swiper-slide v-for="(item, index) in list" :key="index">
        <div class="live_timer">
          <span>{{ item.start_time }}</span>
          <span>{{ item.week_name }}</span>
          <span>{{ item.start_hour_minute }}</span>
        </div>
        <div
          :class="
            item.course_status == 1
              ? 'living-course-pc swiper-item live-go'
              : 'living-course-pc swiper-item live-have'
          "
          @click.stop="toHtml(item)"
        >
          <div class="live-bg">
            <img :src="item.cover_img" class="course-pic" />
            <p class="title">
              <comHidtext>{{ item.name }}</comHidtext>
            </p>
            <div class="middle flex-row content-between">
              <div class="teacher flex-row items-center">
                <p class="name">主讲老师：{{ item.teacher.name }}</p>
              </div>
            </div>
            <div class="bottom flex-row content-between">
              <p class="time"></p>
              <div
                class="button flex-row items-center content-center"
                :style="{
                  'background-image':
                    'url(' +
                    require(item.course_status == 1 || item.come_is
                      ? '../../assets/study/btn_bg_living.png'
                      : item.course_status == 3
                      ? '../../assets/study/btn_bg_start.png'
                      : '../../assets/study/btn_bg_start.png'),
                }"
              >
                <img
                  :src="
                    require(item.course_status == 1
                      ? '../../assets/living/status_living.png'
                      : item.course_status == 3
                      ? '../../assets/living/status_end.png'
                      : '../../assets/living/status_start.png')
                  "
                  class="img2"
                />
                <span>{{
                  item.course_status == 1
                    ? "直播中"
                    : item.course_status == 3
                    ? "已结束"
                    : "即将开始"
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide></swiper-slide>
      <div
        class="swiper-button-prev"
        slot="button-prev"
        :style="{
          'background-image': `url(${require('@/assets/index/左箭头.png')})`,
          'background-size': 'contain',
        }"
      ></div>
      <div
        class="swiper-button-next"
        slot="button-next"
        :style="{
          'background-image': `url(${require('@/assets/index/右箭头.png')})`,
          'background-size': 'contain',
        }"
      ></div>
    </swiper>
    <div class="empty" v-else>
      <div class="empty-bg">
        <!-- <div class="empty-bgt"> -->
        <img src="@/assets/living/zan.png" />
        <p class="text">当前暂无直播哦～</p>
        <!-- </div> -->
      </div>
    </div>
    <div class="modal" v-if="dialogVisible"></div>
    <div class="dialog" v-if="dialogVisible">
      <img
        src="../../assets/study/圆叉.png"
        class="dialog-clear"
        @click="dialogVisible = false"
      />
      <p class="title">输入房间密码</p>
      <div class="box">
        <input class="" v-model="pwd" placeholder="请输入房间密码" type="password" />
        <img
          src="../../assets/study/清除.png"
          class="clear"
          v-if="pwd != ''"
          @click="pwd = ''"
        />
      </div>
      <button
        class="button"
        :style="{
          'background-image': 'url(' + require('../../assets/study/login_btn.png') + ')',
        }"
        @click="openHtml"
      >
        确认
      </button>
    </div>
    <loading ref="loading" json_data_url="living" :errText="enterRoomStatusText" />
    <comAppraise ref="comAppraise" @submit="submit"/>
  </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
import comHidtext from "@/component/com-hidtext.vue";
import loading from "@/pages/living/diag/live-loading.vue";
import liveApi from "@/api/living";
import { Toast } from "mint-ui";
import comAppraise from "@/components/com-appraise"
export default {
  data() {
    return {
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 0,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      enterRoomStatusText: "正在进入房间，请稍等...",
      dialogVisible:false
    };
  },
  components: { swiper, swiperSlide, comHidtext, loading ,comAppraise},
  computed: {
    swiperCount() {
      let pageSize = 4;
      let count = this.list.length;
      return count % pageSize == 0 ? count / pageSize : parseInt(count / pageSize) + 1;
    },
  },
  watch: {
    "$route.query.roomid":{
      handler(val){
        if(val){
          this.roomId = this.$route.query.roomid;
          this.$refs.comAppraise.onShow();
        }
    }
  },
  "$route.query.currentIndex": {
    handler(val){
        if(val){
          this.$emit("getLiveList");
        }
    }
  }
  },
  methods: {
    async submit(info){
      let res = await liveApi.StudentEvaluationCollect({...info,room_id:this.roomId});
      console.log
      if(res.data.code === 0){
        Toast({
          message:"感谢您的评价！",
          duration:2000
        })
      }else{
        Toast({
          message:res.data.message,
          duration:2000
        })
      }
      this.$route.query.roomid = "";
    },
    toHtml(item) {
      this.enterRoomStatusText = "正在进入房间，请稍等...";
      this.$refs.loading.onShow();
      liveApi.StudentCourseStatus({ room_id: item.index }).then((res) => {
        if (!res.data.data.course_status && res.data.data.come_is) {
          this.$refs.loading.onHide();
          this.$router.push({
            path: "/living",
            query: {
              data: JSON.stringify(item),
              type: false,
            },
          });
        } else {
          this.$refs.loading.onHide();
          if (res.data.data.course_status) {
            Toast("直播已结束");
          } else {
            Toast("当前直播间未到直播时间");
          }
        }
      });
      // if(item.course_status != 3){
      //   console.log('sdfs')
      //   this.$router.push({
      //     path: "/living",
      //     query: {
      //       data:JSON.stringify(item),
      //       type:false
      //     }
      //   });
      // }

      // if (item.status > 2 || (item.status == 2 && item.can_enter)) {
      //   if (item.status == 4) {
      //     if (
      //       item.url_list.length > 0 &&
      //       item.url_list != null &&
      //       item.url_list[0] != ""
      //     ) {
      //       // 打开直播 嵌入aliyun
      //       window.localStorage.setItem(
      //         "ty_url_list",
      //         item.url_list.toString()
      //       );
      //       let winURL =
      //         process.env.NODE_ENV !== "production"
      //           ? `http://localhost:8080`
      //           : `app://./index.html`;
      //       ipcRenderer.send("openWindow", winURL + "#/livingCourseReplay");
      //     } else {
      //       Toast("暂无回放");
      //     }
      //   } else {
      //     if (item.type == 2) {
      //       this.dialogVisible = true;
      //       this.pwd = "";
      //       this.id = item.id;
      //       this.itemPwd = item.passwd;
      //     } else {
      //       this.getTicket(item.id);
      //     }
      //   }
      // }
    },
  },
  mounted(){
    this.$bus.emit("LiveToken");
  },
  props: {
    list: {
        type:Array,
        default:()=>[]
    },

  },
};
</script>
<style lang="less" scoped>
.swiper {
  width: 100vw;
}
.swiper-button-prev {
  width: 128px;
  height: 128px;
  left: 20px;
  top: 260px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 100;
}
.swiper-button-prev::after {
  content: "";
}
.swiper-button-next {
  width: 128px;
  height: 128px;
  right: 20px;
  top: 260px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 100;
}
.swiper-button-next::after {
  content: "";
}
.swiper-slide-active {
  margin-left: 80px;
}
// .swiper-button-prev {
//   width: 100px;
//   height: 100px;
// }
// .swiper-button-next {
//   width: 100px;
//   height: 100px;
// }
.swiper-slide {
  position: relative;
  .live_timer {
    width: 382px;
    height: 88px;
    opacity: 1;
    border-radius: 44px;
    background: rgba(0, 0, 0, 0.4);
    margin: 0 auto;
    margin-bottom: 20px;
    font-size: 22.33px;
    font-weight: bold;
    letter-spacing: 0px;
    // line-height: 131.48px;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    vertical-align: middle;
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      margin-right: 1px;
    }
  }
}
.living-course-pc {
  width: 520px;
  height: 524px;
  border-radius: 50px;
  background-color: #fff;
  box-shadow: 0 10px 50px 0 rgba(132, 184, 198, 0.25);
  padding: 10px;
  box-sizing: border-box;
  cursor: pointer;
  .live-bg {
    // position: absolute;
    // left: 12px;
    // top: 12px;
    width: 496px;
    height: 496px;
    opacity: 1;
    border-radius: 100px;
    background: rgba(255, 255, 255, 1);
    box-shadow: inset 0px -5px 8px rgba(224, 218, 211, 1),
      0px 4px 6px 2px rgba(108, 178, 27, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .course-pic {
    // position: absolute;
    // left: 24px;
    // top: 20px;
    width: 450.14px;
    height: 204px;
    object-fit: cover;
    margin-top: 20px;
    border-radius: 50px;
  }

  .title {
    font-size: 38px;
    color: #333333;
    font-weight: bold;
    margin-top: 32px;
    // position: absolute;
    // left: 32px;
    // top: 322px;
    width: 556px;
    height: 56px;
    opacity: 1;
    /** 文本1 */
    font-size: 34px;
    font-weight: bold;
    letter-spacing: 0px;
    line-height: 56px;
    color: rgba(51, 51, 51, 1);
    text-align: center;
    vertical-align: top;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .middle {
    margin-top: 16px;
  }

  .teacher {
    // position: absolute;
    // left: 212px;
    // top: 420px;
    // width: 556px;
    height: 40px;
    opacity: 1;
    /** 文本1 */
    font-size: 20px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 40px;
    color: rgba(153, 153, 153, 1);
    text-align: center;
    vertical-align: top;
    img {
      width: 38px;
      height: 38px;
      border-radius: 50%;
      margin-right: 16px;
    }
  }

  .name {
    font-size: 23px;
    color: #999999;
  }

  .time {
    font-weight: bold;
    color: #333333;
    font-size: 32px;

    .special {
      display: block;
    }
  }

  .button {
    width: 204px;
    height: 70px;
    border-radius: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
    opacity: 1;
    // border-radius: 44px;
    // background: linear-gradient(180deg, rgba(92, 214, 155, 1) 0%, rgba(84, 211, 146, 1) 100%);
    // box-shadow:inset 0px -6px 3px  rgba(54, 186, 116, 1), 0px 4px 8px 2px rgba(53, 174, 109, 0.4);
    span {
      font-size: 32px;
      color: #fff;
      font-weight: bold;
      margin-left: 14px;
    }
  }

  .bottom {
    margin-top: 32px;
  }

  .img1 {
    width: 32px;
    height: 30px;
  }

  .img2 {
    width: 40px;
    height: 40px;
  }
}
.live-go {
  opacity: 1;
  border-radius: 100px;
  background: linear-gradient(
    180deg,
    rgba(220, 242, 165, 1) 0%,
    rgba(185, 207, 111, 1) 100%
  );
  box-shadow: inset 0px -5px 8px rgba(157, 191, 61, 1),
    inset 0px 2px 6px rgba(249, 255, 243, 1), 0px 6px 20px rgba(0, 0, 0, 0.25);
}
.live-have {
  opacity: 1;
  border-radius: 100px;
  background: linear-gradient(
    180deg,
    rgba(207, 207, 214, 1) 0%,
    rgba(194, 194, 205, 1) 100%
  );
  box-shadow: inset 0px -5px 8px rgba(165, 166, 178, 1),
    inset 0px 2px 6px rgba(249, 255, 243, 1), 0px 6px 20px rgba(0, 0, 0, 0.25);
}
.dialog {
  width: 1152px;
  height: 744px;
  border-radius: 110px;
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  z-index: 20;

  .title {
    font-size: 56px;
    font-weight: bold;
    color: #333333;
    padding-top: 64px;
    text-align: center;
  }

  input {
    border: none;
    background: #f6f8fb;
    border-radius: 60px;
    height: 120px;

    margin-left: 176px;
    width: 740px;
    padding-left: 60px;
    font-size: 40px;
    color: #333333;

    &::after {
      border: none;
    }

    outline: none;
  }

  input:focus {
    border: none;
  }

  .button {
    width: 800px;
    height: 120px;
    border-radius: 60px;
    background-size: cover;
    background-repeat: no-repeat;
    margin-top: 80px;
    border: none;
    font-size: 40px;
    color: #ffffff;
    margin-left: 176px;
    outline: none;
    cursor: pointer;
  }

  .clear {
    position: absolute;
    right: 216px;
    top: 34px;
    height: 56px;
    width: 56px;
    cursor: pointer;
  }

  .box {
    position: relative;
    margin-top: 120px;
  }
}

.dialog-clear {
  position: absolute;
  right: -120px;
  width: 96px;
  height: 96px;
  z-index: 30;
  cursor: pointer;
}

.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}

.swiper-button-prev {
  width: 148px;
  height: 148px;

  left: 40px;
  margin-top: 54px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  pointer-events: auto !important;
}

.swiper-button-prev::after {
  content: "";
}

.swiper-button-next {
  width: 148px;
  height: 148px;

  right: 40px;
  bottom: 10px;
  margin-top: 54px;
  border-radius: 50%;

  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  pointer-events: auto !important;
}

.swiper-button-next::after {
  content: "";
}

.swiper-slide {
  width: 520px !important;
  margin-right: 60px;
  // height: 624px;
}
.empty {
  position: relative;
  left: 0px;
  top: 0px;
  width: 1120px;
  height: 736px;
  margin: 0 auto;
  opacity: 1;
  border-radius: 100px;
  background: linear-gradient(
    180deg,
    rgba(153, 207, 255, 1) 0%,
    rgba(103, 183, 255, 1) 100%
  );
  box-shadow: inset 0px -5px 8px rgba(46, 125, 255, 1),
    inset 0px 5px 10px rgba(151, 227, 255, 0.65), 0px 20px 50px rgba(24, 40, 95, 0.2);
  .empty-bg {
    position: absolute;
    left: 20px;
    top: 40px;
    width: 1080px;
    height: 656px;
    opacity: 1;
    border-radius: 80px;
    background: rgba(250, 255, 251, 1);
    box-shadow: inset 0px -5px 8px rgba(244, 225, 201, 1),
      0px 10px 30px 4px rgba(0, 96, 255, 0.5);
    img {
      position: absolute;
      left: 280px;
      top: 0px;
      width: 600px;
      height: 600px;
      opacity: 1;
      background: url("@/assets/living/zan.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      // background-repeat: repeat;
      // box-shadow: 0px 6px 20px  rgba(0, 90, 151, 0.5);
    }
    .text {
      position: absolute;
      left: 436px;
      top: 530px;
      width: 288px;
      height: 48px;
      opacity: 1;
      /** 文本1 */
      font-size: 36px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 48px;
      color: rgba(58, 59, 59, 1);
      text-align: center;
      vertical-align: middle;
    }
  }
}
</style>
