<template>
  <div class="player-box">
    <div id="player" class="player"></div>
  </div>
</template>
<script>
import "@/public/aliplayer-h5-min.js";
export default {
  data(){
    return {
      player: "",
      index: 1,
      isChange: false
    }
  },
  props: { urlList: [] },
  mounted(){
    this.initPlayer();
  },
  beforeDestroy(){
    if(this.timer){
      clearInterval(this.timer);
    }
  },
  methods: {
    close(){
      this.$emit("close");
    },
    initPlayer (){
      this.player = new Aliplayer({
          id: "player",
          source: this.urlList[this.index],
          width: "100%",
          height: "100%",
          controlBarVisibility: "click",
        }, (player) => {
          player.play();
      });
      this.player.on('ended', this.endedHandle);
      this.player.on('play', this.changeLoading);
    },
    endedHandle(){
      var lastIndex = this.index;
      this.index = this.index + 1;
      if(this.index == this.urlList.length) this.index = 1;
      if(lastIndex != this.index) {
        this.player.loadByUrl(this.urlList[this.index]);
      }
    },
    changeLoading(){
      this.isChange = false;
    },
    moveTo(index){
      if(!this.isChange){
        this.isChange = true;
        this.index = index;
        this.player.pause();
        this.player.stop();
        this.player.loadByUrl(this.urlList[this.index]);
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import url("https://g.alicdn.com/de/prismplayer/2.15.2/skins/default/aliplayer-min.css");
.player-box {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: #000;
  .close {
    height: 80px;
    width: 80px;
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 101;
  }
}
::v-deep .prism-setting-btn, ::v-deep .prism-cc-btn, ::v-deep .prism-fullscreen-btn, ::v-deep .prism-volume {
  display: none;
}
::v-deep .prism-player .prism-time-display {
  height: 40px;
  line-height: 40px;
  font-size: 24px;
}
::v-deep .prism-player .prism-play-btn {
  width: 30px;
  height: 30px;
}
::v-deep .prism-player .prism-controlbar .prism-controlbar-bg{
  height: 40px;
}
::v-deep .prism-player .prism-controlbar{
  height: 44px;
}
::v-deep .prism-play-btn {
  margin-top: 10px!important;
}
::v-deep .prism-time-display {
  margin-top: 5px!important;
}
::v-deep div.prism-player .prism-liveshift-progress, ::v-deep .prism-player .prism-progress {
  bottom: 64px!important;
  height: 10px!important;
}
::v-deep .prism-player .prism-liveshift-progress .prism-progress-cursor, ::v-deep .prism-player .prism-progress .prism-progress-cursor{
  top: -25px;
}
::v-deep .prism-player .prism-liveshift-progress .cursor-hover,::v-deep .prism-player .prism-progress .cursor-hover {
  top: -28px!important;
}
::v-deep .prism-player .prism-big-play-btn, ::v-deep .prism-player .prism-big-play-btn .outter {
  width: 64px;
  height: 64px;
  border-width: 7px;
}
::v-deep .prism-player .prism-big-play-btn {
  bottom: 100px!important;
  left: 30px!important;
}
</style>