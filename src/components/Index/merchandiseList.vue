<template>
  <div class="course-list swiper-no-swiping">
    <swiper class="swiper" :options="swiperOption" ref="Swiper">
      <swiper-slide v-for="(item1, index1) in list" :key="index1">
        <div class="swiper-item flex-row flex-wrap"  @click="toDetails(item1)">
          <div class="course-wrap flex-column">
            <img :src="item1.pad_header_image_url" />
            <div class="flex-column content-between">
              <div>
                <p class="title jcyt600">{{item1.title}} </p>
                <div class="top flex-row content-between items-center">
                  <p class="describle jcyt400">{{item1.description}}</p>
                  <p class="price flex-row">
                    <span class="symbol jcyt600">¥ </span>
                    <span class="jcyt600">{{parseInt( item1.current_price / 100 )}}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="btn jcyt600" @click="toDetails(item1)" :style="{
          'background-image': `url(${require('@/assets/index/buy_bg.png')})`,
          'background-size': 'contain'
        }">了解详情</div>
      </swiper-slide>
      <swiper-slide> </swiper-slide>
      <div
        class="swiper-button-prev"
        slot="button-prev"
        :style="{
          'background-image': `url(${require('@/assets/index/左箭头.png')})`,
          'background-size': 'contain'
        }"
      ></div>
      <div
        class="swiper-button-next"
        slot="button-next"
        :style="{
          'background-image': `url(${require('@/assets/index/右箭头.png')})`,
          'background-size': 'contain'
        }"
      ></div>
    </swiper>
  </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
export default {
  data(){
    return {
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 0,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        }
      },
    }
  },
  components: { swiper, swiperSlide },
  computed:{
    swiperCount(){
      let pageSize = 4;
      let count = this.list.length;
      return count % pageSize == 0 ? count / pageSize : parseInt(count / pageSize) + 1;
    }
  },
  methods: {
    toDetails(item){
      __bl.sum(this.tabs + "课程详情");
      this.$router.push({
          path: "/merchandiseDetail",
          query: {
            id: item.id,
          }
        });
    }
  },
  props: {
    list: Array,
    tabs: String
  }
}
</script>
<style lang="less" scoped>
.swiper {
  width: 100vw;
}
.swiper-button-prev {
  width: 128px;
  height: 128px;
  left: 20px;
  top: 260px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 100;
}
.swiper-button-prev::after {
  content: "";
}
.swiper-button-next {
  width: 128px;
  height: 128px;
  right: 20px;
  top: 260px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 100;
}
.swiper-button-next::after {
  content: "";
}
.swiper-slide-active {
  margin-left: 80px;
}
// .swiper-button-prev {
//   width: 100px;
//   height: 100px;
// }
// .swiper-button-next {
//   width: 100px;
//   height: 100px;
// }
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}

.content-between {
  justify-content: space-between;
}
.content-center {
  justify-content: center;
}
.swiper-slide {
  height: 672px;
  margin-right: 56px;
  width: 620px!important;
}
.swiper-item {
  height: 632px;
  width: 620px;
  padding: 12px 12px 24px 12px;
  border-radius: 80px 80px 100px 100px;
  box-sizing: border-box;
  background-image: linear-gradient(to bottom, #FFD264, #FFB64B);
  box-shadow: rgba(31, 29, 95, .25) 0 6px 20px 0, #FFA01A 0 -5px 8px 0 inset, #FFF8D4 0 2px 6px 0 inset;
}
.course-wrap {
  height: 596px;
  width: 100%;
  padding: 20px;
  margin: 0 auto;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: rgba(108, 178, 27, .2) 0 4p 6px 2px, #E0DAD3 0 -5px 8px 0 inset;
  cursor: pointer;
  border-radius: 80px 80px 100px 100px;
  img {
    width: 556px;
    height: 304px;
    border-radius: 60px;
  }
}
.price {
  span {
    color: #FF4C22;
    font-weight: bold;
    font-size: 44px;
    line-height: 48px;
  }
}
.title {
  margin-top: 24px;
  font-size: 44px;
  color: #333333;
  width: 556px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 56px;
}
.describle {
  font-size: 30px;
  color: #999999;
  width: 270px;
  line-height: 40px;
  word-break: break-all;
}
.top {
  margin-top: 12px;
}
.btn {
  width: 404px;
  height: 184px;
  position: absolute;
  bottom: -24px;
  margin: auto;
  left: 0;
  right: 0;
  line-height: 98px;
  font-size: 44px;
  color: #fff;
  padding-left: 81px;
  box-sizing: border-box;
  cursor: pointer;
}
</style>