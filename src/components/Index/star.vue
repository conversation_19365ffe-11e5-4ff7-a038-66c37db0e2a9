<template>
  <div
    class="star_wrap_cl"
    :style="{ width: 14 + (demo_width / 434) * 100 + '%' }"
  >
    <div
      class="star_item"
      v-for="(star, index) in star_arr"
      :key="index"
      :style="{ left: index * 1.7 + 'vw' }"
    >
      <img
      class="star_item_img"
        :src="
          star == true
            ? require('@/assets/lessonList/star.png')
            : require('@/assets/lessonList/dis_star.png')
        "
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "courseWrap",
  props: {
    total_num: {},
    finish_num: {}
  },
  data() {
    return {
      star_arr: []
    };
  },
  computed: {
    demo_width() {
      return (this.star_arr.length - 1) * 36;
    }
  },

  mounted() {
    if (this.finish_num > 0) {
      for (var i = 0; i < this.finish_num; i++) {
        this.star_arr.push(true);
      }
    }
    if (this.total_num - this.finish_num > 0) {
      for (var j = 0; j < this.total_num - this.finish_num; j++) {
        this.star_arr.push(false);
      }
    }
  }
};
</script>

<style lang="less">
.star_wrap_cl {
  height: 64px;
  position: relative;
  margin: 30px auto 0;
  .star_item {
    width: 64px;
    height: 64px;
    position: absolute;
    .star_item_img {
      width: 64px;
      height: 64px;
      position: absolute;
    }
  }
}
</style>
