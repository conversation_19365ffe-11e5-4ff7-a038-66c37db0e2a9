<template>
  <!-- <div class="header">
    <div v-if="JSON.stringify(userInfo) != '{}'" @click="ToMineDetail">
      <div
        class="name_n"
        :style="{
          'background-image': `url(${require('@/assets/index/name_bg.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <p class="name_w">{{ userInfo.name ?? "" }}</p>
      </div>
      <div
        class="header-img"
        :style="{
          'background-image': `url(${require('@/assets/index/header_bg.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <div
          class="avatar_bg"
          :style="{
            'background-image': `url(${require('@/assets/mine/默认头像.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <img v-if="userInfo.avatar != ''" :src="userInfo.avatar" />
        </div>
      </div> -->

      <!-- <div
        class="level"
        :style="{
          'background-image': `url(${require('@/assets/index/level_bg.png')})`,
          'background-size': '100% 100%',
        }"
      >
        N1
      </div> -->
    <!-- </div>
  </div> -->
  <div class="info-bg flex-row" @click="ToMineDetail" :style="{
    'background-image': `url(${require('@/assets/index/info.png')})`,
    'background-size': '100% 100%',
  }">
    <div class="avatar flex-row items-center content-center" :style="{
      'background-image': `url(${require('@/assets/index/avatar_bg.png')})`,
      'background-size': '100% 100%',
    }">
      <img v-if="userInfo.avatar != ''" :src="userInfo.avatar"/>
    </div>
    <div class="info">
      <div class="name jcyt600">
        <span>{{userInfo.name ?? ""}}</span>
      </div>
      <div class="level flex-row items-center">
        <img :src="rankInfo.level_signal"/>
        <span class="jcyt500">{{rankInfo.current_level_name}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import userApi from "@/api/user";
import mineApi from "@/api/mine";
export default {
  name: "index",
  data() {
    return {
      userInfo: {},
      rankInfo: {}
    };
  },
  methods: {
    ToMineDetail() {
      this.$router.push("/mine");
    },
    async getSeasonInfo(){
      let res = await mineApi.getSeasonInfo();
      this.rankInfo = res.data;
    }
  },
  mounted() {
    userApi.GetUserInfo().then((res) => {
      if (res.status == 200) {
        this.$storage.$setStroage("user_token", res.data.token);
        this.$storage.$setStroage("gender", res.data.gender);
        this.userInfo = res.data;
        if (res.data["has_user_info"] == false) {
          this.$router.push("/information");
        }
      }
    });
    this.getSeasonInfo();
  }
};
</script>

<style scoped lang="less">
// .header {
//   width: 440px;
//   height: 196px;
//   position: relative;
//   cursor: pointer;
//   .name_n {
//     position: absolute;
//     right: 0;
//     top: 40px;
//     width: 280px;
//     height: 120px;

//     .name_w {
//       margin-top: 37px;
//       margin-right: 30px;
//       float: right;
//       width: 170px;
//       font-size: 40px;
//       color: #ffffff;
//       font-weight: 700;
//       line-height: 48px;
//       text-align: center;
//       overflow: hidden;
//       white-space: nowrap;
//       text-overflow: ellipsis;
//     }
//   }
//   .header-img {
//     position: absolute;
//     left: 0;
//     top: 0px;
//     width: 244px;
//     height: 196px;

//     text-align: center;
//     .avatar_bg {
//       width: 148px;
//       height: 148px;
//       margin: 20px auto 0;
//       img {
//         width: 148px;
//         height: 148px;
//         border-radius: 50%;

//         // background-color:red;
//       }
//     }
//   }
//   .level {
//     position: absolute;
//     left: 29px;
//     bottom: 3px;
//     width: 194px;
//     height: 52px;
//     font-size: 32px;
//     color: #ff7a00;
//     line-height: 55px;
//     text-align: center;
//   }
// }
.info-bg {
  width: 536px;
  height: 163.5px;
  padding-top: 12px;
  box-sizing: border-box;
}
.avatar {
  width: 128px;
  height: 128px;
  margin-left: 24px;
  border-radius: 50%;
  box-shadow: rgba(0, 25, 53, .3) 0 3px 6px 0;
  position: relative;
  z-index: 2;
  img {
    width: 112px;
    height: 112px;
    border-radius: 50%;
  }
}
.name {
  height: 52px;
  width: 270px;
  background-color: rgba(0, 0, 0, .2);
  border-radius: 0 26px 26px 0;
  font-size: 30px;
  color: #fff;
  text-align: center;
  line-height: 52px;
  box-sizing: border-box;
  padding: 0 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.info {
  margin-top: 12px;
  margin-left: -30px;
}
.level {
  margin-left: 40px;
  img {
    width: 60px;
    height: 65px;
  }
  span {
    font-size: 26px;
    color: #fff;
    margin-left: 2px;
    margin-top: 8px;
  }
}
</style>
