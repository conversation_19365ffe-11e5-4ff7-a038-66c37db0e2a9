
<template>
  <div>

    <audio ref="audio"></audio>
  </div>
</template>

<script>
import fs from "fs";
import path from "path";
export default {
  data() {
    return {
      text: '111',
      audioUrl: '',
      
    };
  },
  methods: {
    getAudioPath(text) {
      // 获取语音文件路径
      const fileName = `${encodeURIComponent(text)}.mp3`;
      const filePath = path.join(__dirname, "audio", fileName);
      return filePath;
    },
    async play(text) {
      const fileName = this.getFileName(text);
      const filePath = `./public/${fileName}.mp3`;
    //   const filePath = this.getAudioPath(text);
      // 检查本地文件夹中是否已经存在相同的语音文件
      if (this.checkFileExists(filePath)) {
        this.playAudio(filePath);
        return;
      }

      // 调用百度语音合成 API 合成语音
      const audioData = await this.synthesizeAudio(this.text);

      // 将语音数据保存到本地文件夹中
      this.saveAudioToFile(audioData, filePath);

      // 播放语音
      this.playAudio(filePath);
    },
    async synthesizeAudio(text) {
      // 调用百度语音合成 API 合成语音
      const response = await fetch(`http://tsn.baidu.com/text2audio?tex=${encodeURIComponent(text)}&lan=zh&cuid=abcd&ctp=1&tok=24.6f6b6f7d797f5d5d5d5d5d5d5d5d5d5d.2592000.1623839477.282335-2412345`);
      const audioData = await response.arrayBuffer();
      return audioData;
    },
    saveAudioToFile(audioData, filePath) {
      // 将语音数据保存到本地文件夹中
      const fs = require('fs');
      const path = require('path');
      const dirPath = path.dirname(filePath);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      fs.writeFileSync(filePath, Buffer.from(audioData));
    },
    checkFileExists(filePath) {
      // 检查本地文件夹中是否已经存在相同的语音文件
      const fs = require('fs');
      return fs.existsSync(filePath);
    },
    playAudio(filePath) {
      // 播放语音
      const audio = this.$refs.audio;
      audio.src = filePath;
      audio.play();
    },
    getFileName(text) {
      // 根据文本内容生成文件名
      return text.replace(/[^\w]/g, '_');
    },
  },
};
</script>
