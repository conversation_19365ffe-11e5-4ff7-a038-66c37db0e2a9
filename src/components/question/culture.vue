<template>
  <div class="culture">
    <img :src="question.img" alt="" />
    <answer-right
      :enable_dialog="right_dialog"
      @close_dialog="right_dialog = false"
    />
    <answer-wrong
      :enable_dialog="wrong_dialog"
      @close_dialog="wrong_dialog = false"
    />
  </div>
</template>

<script>
import answerRight from "./answerRight.vue";
import answerWrong from "./answerWrong.vue";

export default {
  name: "cultureWrap",
  data() {
    return {
      right_dialog: false,
      wrong_dialog: false,
    };
  },
  components: { answerRight, answerWrong },
  props: {
    question: {
      type: Object,
    },
  },
  methods: {
    changeResultDialog(result) {
      result === "is_right"
        ? (this.right_dialog = true)
        : (this.wrong_dialog = true);
    },
  },
  mounted() {},
};
</script>

<style scoped lang="less">
.culture {
  width: 1000px;
  height: 1000px;
  position: relative;
  img {
    width: 970px;
    height: 970px;
    border-radius: 40px;
  }
}
</style>
