<template>
  <div class="chioce">
    <div id="board" class="board"></div>
    <answer-right
      :enable_dialog="right_dialog"
      @close_dialog="right_dialog = false"
    />
    <answer-wrong
      :enable_dialog="wrong_dialog"
      @close_dialog="wrong_dialog = false"
    />
  </div>
</template>

<script>
import tool from "../../public/tool";
import answerRight from "./answerRight.vue";
import answerWrong from "./answerWrong.vue";

export default {
  name: "choiceWrap",
  data() {
    return {
      player: "",
      last_mark: "",
      tsumego: [],
      board_click: false,
      right_dialog: false,
      wrong_dialog: false
    };
  },
  components: { answerRight, answerWrong },
  props: {
    question: {
      type: Object
    }
  },
  methods: {
    make_player: function () {
      // 创建棋盘
      let elem = document.getElementById("board");
      this.player = new WGo.BasicPlayer(elem, {
        sgf: this.question.sgf,
        layout: {
          left: "",
          bottom: ""
        },
        board: {
          size: this.size,
          theme: {
            gridLinesColor: "#B75200",
            starColor: "#B75200"
          },
          section: {
            top: this.question.section["top"],
            right: this.question.section["right"],
            bottom: this.question.section["bottom"],
            left: this.question.section["left"]
          }
        },
        enableWheel: false
      });
      this.player.board.removeEventListener("click", this.player.board._click);
      this.player.board.removeEventListener(
        "click",
        this.player.board._ev_click
      );
      tool.MoveToLast(this.player);
      if (this.question.move_first === "white") {
        this.player.kifuReader.node.appendChild(
          new WGo.KNode({
            move: {
              pass: true,
              c: this.player.kifuReader.game.turn
            }
          })
        );
        this.player.next(this.player.kifuReader.node.children.length - 1);
      }
    },
    changeResultDialog(result) {
      result == "is_right"
        ? (this.right_dialog = true)
        : (this.wrong_dialog = true);
    }
  },
  mounted() {
    this.make_player();
  }
};
</script>

<style scoped lang="less">
.chioce {
  width: 970px;
  height: 970px;
  position: relative;
  overflow: hidden;
}
</style>
