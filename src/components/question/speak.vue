<template>
  <div></div>
</template>

<script>
import fs from "fs";
import path from "path";
export default {
  name: "speakWrap",
  data() {
    return {
      ttsAudio: null
    };
  },

  methods: {
    cancle(){
//
    },
    play(text) {
      var param = {
        tex: text,
        tok: "25.5a58a905633e6c887629a78edc6c0938.315360000.1995256414.282335-23150696", //这个token要换成自己的
        spd: 5,
        pit: 5,
        vol: 15,
        per: 3
      };
      var url = "http://tsn.baidu.com/text2audio";
      var p = param || {};
      var that = this;
      var xhr = new XMLHttpRequest();
      xhr.open("POST", url); // 创建form参数
      var data = {};
      for (var p in param) {
        data[p] = param[p];
      } // 赋值预定义参数

      data.cuid = data.cuid || data.tok;
      data.ctp = 1;
      data.lan = data.lan || "zh"; //以上为封装请求的参数 // 序列化参数列表
      var fd = [];
      for (var k in data) {
        fd.push(k + "=" + encodeURIComponent(data[k]));
      }
      var frd = new FileReader();
      xhr.responseType = "blob";
      xhr.send(fd.join("&")); //请求返回结果处理
      var that = this;
      xhr.onreadystatechange = function () {
        if (xhr.readyState == 4) {
          if (xhr.status == 200) {
            if (xhr.response.type == "audio/mp3") {
              //创建audio对象进行播报
              if (!that.ttsAudio) {
                that.ttsAudio = new Audio();
              }
              that.ttsAudio.setAttribute(
                "src",
                URL.createObjectURL(xhr.response)
              );
              that.ttsAudio.play();
            } else {
              console.log("调用语音合成接口token已失效!");
            }
          } else {
            console.log("语音合成接口调用失败!");
          }
        }
      };
    }
  }
};
</script>

<style scoped lang="less"></style>
