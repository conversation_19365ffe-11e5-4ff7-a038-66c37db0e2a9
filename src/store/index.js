import Vue from 'vue'

import Vuex from 'vuex'

import ws from "@/store/module/ws";
import initWs from "@/store/module/initWs";
import loading from "@/store/module/loading";
import settings from "@/store/module/settings";
import trtc from "@/store/module/live/rtct";
import teduBoard from "@/store/module/live/teduBoard";


Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    ws,
    initWs,
    loading,
    settings,
    trtc,
    teduBoard
  }
});

