import ws from "@/public/ws";
import storage from "@/public/storage.js";
import config from "@/config";

let socket = ws.Socket;
const mutations = {
  initWebSocket() {
    let user_id = storage.$getStroage("userId");
    const os = require("os");
    let osSystem =
      os.platform() == "darwin"
        ? "mac"
        : os.platform() == "win32"
        ? "windows"
        : "";
    let version = os.platform() == "darwin" ? os.release() : os.version();
    let app_type = config.isAirSchool ? "airschool" : "higo";
    let url =
      "wss://" +
      config.wsLivingBaseUrl +
      `/api/public/ws-gateway-service/higo-app/ws?user_id=` +
      user_id +
      "&os=" +
      osSystem +
      "&version=" +
      version +
      "&app_type=" +
      app_type;
    if (user_id) {
      socket.init(url);
    }
  },
};

const actions = {
  initWebSocket(context, data) {
    context.commit("initWebSocket", data);
  },
};

export default {
  mutations,
  actions,
};
