// import seasonLevelApi from "../../../api/seasonLevel";
const state = {
    tim: null,
    isSignalReady: false,
    isTiwReady: false, // 白板是否已经Ready
    classInfo: {
      sdkAppId: 1600029524,
      userId: sessionStorage.getItem("TIW_CLASSINFO_USERID"),
      userSig: sessionStorage.getItem("TIW_CLASSINFO_USERSIG"),
      classId: sessionStorage.getItem("TIW_CLASSINFO_CLASSID"),
      nickname: sessionStorage.getItem("TIW_CLASSINFO_NICKNAME"),
      imGroupId: sessionStorage.getItem("TIW_CLASSINFO_IMGROUPID"),
      dowmGradeTimes: sessionStorage.getItem("TIW_CLASSINFO_DOWNGRADETIMES") || 0
    },
    current: {
      toolType: 0,
      fileInfo: {}
    },
    boardSetting: {},
    rightBarShow: false
  };
  const mutations = {
    SET_TIM(state, payload) {
      state.tim = payload;
    },
    classInfo(state, payload) {
      sessionStorage.setItem("TIW_CLASSINFO_USERID", payload.userId);
      sessionStorage.setItem("TIW_CLASSINFO_USERSIG", payload.userSig);
      sessionStorage.setItem("TIW_CLASSINFO_CLASSID", payload.classId);
      sessionStorage.setItem("TIW_CLASSINFO_NICKNAME", payload.nickname);
      sessionStorage.setItem("TIW_CLASSINFO_IMGROUPID", payload.imGroupId || "");
      sessionStorage.setItem(
        "TIW_CLASSINFO_DOWNGRADETIMES",
        payload.dowmGradeTimes || 0
      );
  
      state.classInfo.userId = payload.userId;
      state.classInfo.classId = payload.classId;
      state.classInfo.userSig = payload.userSig;
      state.classInfo.nickname = payload.nickname;
      state.classInfo.imGroupId = payload.imGroupId;
      state.classInfo.dowmGradeTimes = payload.dowmGradeTimes;
    },
  
    setSignalReady(state, payload) {
      state.isSignalReady = payload;
    },
  
    setTiwReady(state, payload) {
      state.isTiwReady = payload;
    },
  
    setRightBarShow(state, payload) {
      state.rightBarShow = payload;
    },
  
    setCurrentFile(state, payload) {
      state.current.fileInfo = payload;
    },
  
    updateBoardSetting(state, payload) {
      const t = state.current.boardSetting;
      state.current.boardSetting = {
        ...t,
        ...payload
      };
    }
  };
  const getters = {
    doneGetTim: (state) => {
      return state.tim;
    },
    doneGetIsSignalReady: (state) => {
      return state.isSignalReady;
    },
    doneGetIsTiwReady: (state) => {
      return state.isTiwReady;
    },
    doneGetClassInfo: (state) => {
      return state.classInfo;
    },
    doneGetRightBarShow: (state) => {
      return state.rightBarShow;
    }
  };
  const actions = {
    async SET_TIM(context, payload) {
      context.commit("SET_TIM", payload);
    },
    setSignalReady({ commit }, payload) {
      commit("setSignalReady", payload);
    },
  
    setTiwReady({ commit }, payload) {
      commit("setTiwReady", payload);
    },
  
    setRightBarShow({ commit }, payload) {
      commit("setRightBarShow", payload);
    },
  
    setCurrentFile({ commit }, payload) {
      commit("setCurrentFile", payload);
    },
  
    updateBoardSetting({ commit }, payload) {
      commit("updateBoardSetting", payload);
    }
  };
  
  export default {
    state,
    mutations,
    getters,
    actions
  };
  