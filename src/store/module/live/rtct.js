const getDefaultState = () => {
  return {
    // 是否在屏幕分享中
    isScreenSharing: false,
    // 使用的摄像头设备
    activeCamera: {},
    // 使用的麦克风设备
    activeMicrophone: {},
    // 麦克风采集音量大小
    audioVolume: 100,
    // 扬声器音量大小
    speakerVolume: 100,
    // 使用的扬声器设备
    activeSpeaker: {},
    // 本地流是否设置镜像
    isSetMirror: true,
    // 视频参数
    videoProfile: {
      width: 1280,
      height: 720,
      frameRate: 15,
      bitrate: 2000
    },
    // 屏幕分享参数
    screenProfile: {
      width: 1920,
      height: 1080,
      frameRate: 5,
      bitrate: 1600
    },
    // 音频采集状态
    isAudioMuted: true,
    // 视频采集状态
    isVideoMuted: true,
    // 音量大小
    audioLevel: 0,
    // 上行网络质量等级
    uplinkQualityLevel: 0,
    //降噪
    noiseReduction:false,
    // 音乐模式
    musicalPattern:false,
    //直播状态
    liveStage:"not_started",
    // 获取的奖杯数量
    getTreasureCount:0,
    // 画板权限
    isDraw:false,
    // 上台状态
    isOnStage:false,
    // 举手
    isRaiseHand:false,
    // 聊天框
    chatBox:false,
    // 棋盘
    isChessBoard:false,
    //棋盘参数
    chessBoardId:"0",
    // 上次的答案
    lastAnswer:"",
    // 开始答题
    isStartAnswer:false,
    // 答题数据
    answerOption:{},
    // 题目id
    questionId:0,
    // 老师是否介入权限
    isTeacher:true, // false 介入 true 未介入
    // 举手的人
    handUpUser:[],
    // 老师id
    teacherId:"",
    //老师的name
    teacherName:"",
    // 是否可以展示本地
    isShowLocal:true,
    isStandard:2,
    //麦克风是否可用
    isMicrophone:true,
    // 摄像头是否可用
    isCamera:true,
    // 禁言状态
    isMute:false,
    // 摄像头权限
    isCameraAuthority:true,
    // 奖杯
    treasureSta:false,
    // 白板尺寸
    boardSize:"",
    // 轮播状态
    isCarousel:false,
    // 全体是否禁言
    isAllMute:false,
    // 其他人查看自己摄像头
    isLookCamera:false,
    // 题库答题时间
    answerTime:0,
  };
}
  const state = getDefaultState();
  
  const mutations = {
    // 更新直播间ID
    SET_ROOM_ID(state, value) {
      state.roomId = value;
    },
    // 更新直播间昵称
    SET_ROOM_NAME(state, value) {
      state.roomName = value;
    },
    // 更新用户信息
    SET_USERSIG(state, data) {
      state.userSig = data;
    },
    // // 更新直播间阶段
    // UPDATE_LIVE_STAGE(state, value) {
    //   state.liveStage = value;
    // },
    // 更新是否在屏幕分享中
    UPDATE_IS_SCREEN_SHARING(state, value) {
      state.isScreenSharing = value;
    },
    // 更新使用的摄像头设备
    UPDATE_ACTIVE_CAMERA(state, device) {
      state.activeCamera = device;
    },
    // 更新使用的麦克风设备
    UPDATE_ACTIVE_MICROPHONE(state, device) {
      state.activeMicrophone = device;
    },
    UPDATE_AUDIOVOLUME(state, device) {
      state.audioVolume = device;
    },
    // 更新使用的扬声器设备
    UPDATE_ACTIVE_SPEAKER(state, device) {
      state.activeSpeaker = device;
    },
    // 更新是否设置本地流镜像
    UPDATE_SET_MIRROR(state, value) {
      state.isSetMirror = value;
    },
    // 更新视频参数
    UPDATE_VIDEO_PROFILE(state, data) {
      state.videoProfile = data;
    },
    // 更新美颜参数
    UPDATE_BEAUTY_PARAM(state, data) {
      state.beautyParam = data;
    },
    // 更新视频mute状态
    UPDATE_VIDEO_STATE(state, value) {
      state.isVideoMuted = value;
    },
    // 更新音频mute状态
    UPDATE_AUDIO_STATE(state, value) {
      state.isAudioMuted = value;
    },
    // 更新音量大小
    UPDATE_AUDIO_LEVEL(state, value) {
      state.audioLevel = value;
    },
    // 更新上行网络等级
    UPDATE_UPLINK_NETWORK_LEVEL(state, value) {
      state.uplinkQualityLevel = value;
    },
    // 设置扬声器音量大小
    SET_SPEAKER_VOLUME(state, value) {
      state.speakerVolume = value;
    },
    // 更新直播间状态
    UPDATE_LIVE_STAGE(state, value) {
      state.liveStage = value;
    },
    // 更新获取的奖杯数量
    UPDATE_GET_TREASURE_COUNT(state, value) {
      state.getTreasureCount = value;
    },
    // 更新画板权限
    UPDATE_DRAW_PERMISSION(state, value) {
      console.log( '画板权限', value)
      state.isDraw = value;
    },
    // 更新上台状态
    UPDATE_STAGE_STATUS(state, value) {
      state.isOnStage = value;
    },
    // 更新举手状态
    UPDATE_RAISE_HAND_STATUS(state, value) {
      state.isRaiseHand = value;
    },
    // 更新聊天框
    UPDATE_CHAT_BOX(state, value) {
      state.chatBox = value;
    },
    // 更新棋盘状态
    UPDATE_CHESS_BOARD_STATUS(state, value) {
      state.isChessBoard = value;
    },
    // 更新棋盘id
    UPDATE_CHESS_BOARD_PARAM(state, value) {
      state.chessBoardId = value;
    },
    // 更新答题器
    UPDATE_ANSWER_STATUS(state, value) {
      state.isStartAnswer = value;
    },
    // 更新答题选线
    UPDATE_ANSWER_OPTION(state, value) {
      state.answerOption = value;
    },
    // 更新题目
    UPDATE_QUESTION(state, value) {
      state.questionId = value;
    },
    // 更新老师权限
    UPDATE_TEACHER_PERMISSION(state, value) {
      state.isTeacher = value;
    },
    RESET_CARTSTATE (state) {
      Object.assign(state, getDefaultState())
    },
    // 更新举手的人
    UPDATE_HAND_UP_USER(state, value) {
      state.handUpUser = value;
    },
    // 更新老师
    UPDATE_TEACHER(state, value) {
      state.teacherId = value;
    },
    // 是否可以展示自己
    UPDATE_SHOW_SELF(state, value) {
      state.isShowLocal = value
    },
    // 当前直播间是否是标准
    UPDATE_IS_STANDARD(state, value) {
      state.isStandard = value;
    },
    // 更新老师名称
    UPDATE_TEACHER_NAME(state, value) {
      state.teacherName = value;
    },
    // 更新降噪
    UPDATE_NOISE_REDUCTION(state, value) {
      state.noiseReduction = value;
    },
    // 更新麦克风可用状态
    UPDATE_MICROPHONE_STATUS(state, value) {
      state.isMicrophone = value;
    },
    // 更新摄像头可用状态
    UPDATE_CAMERA_STATUS(state, value) {
      state.isCamera = value;
    },
    // 更新禁言状态
    UPDATE_MUTE_STATUS(state, value) {
      state.isMute = value;
    },
    // 更新摄像头权限
    UPDATE_CAMERA_PERMISSION(state, value) {
      state.isCameraAuthority = value;
    },
    // 更新将
    UPDATE_TREASURE_STATUS(state, value) {
      state.treasureSta = value;
    },
    // 更新上次答案
    UPDATE_LAST_ANSWER(state, value) {
      state.lastAnswer = value;
    },
    // 更新白板尺寸
    UPDATE_WHITE_BOARD_SIZE(state, value) {
      state.boardSize = value;
    },
    // 更新轮博状态
    UPDATE_ROTATION_STATUS(state, value) {
      state.isCarousel = value;
    },
    // 更新全体禁言
    UPDATE_ALL_MUTE_STATUS(state, value) {
      state.isAllMute = value;
    },
    // 更新其他人查看自己摄像头
    UPDATE_LOOK_CAMERA(state, value) {
      state.isLookCamera = value;
    },
    // 更新题库答题时间
    UPDATE_QUESTION_TIME(state, value) {
      state.answerTime = value;
    }
  };
  const getters = {
    // 当前使用的摄像头设备Id
    activeCameraId(state) {
      return state.activeCamera.deviceId;
    },
    // 当前使用的摄像头设备Id
    activeMicrophoneId(state) {
      return state.activeMicrophone.deviceId;
    },
    // 当前使用的扬声器设备Id
    activeSpeakerId(state) {
      return state.activeSpeaker.deviceId;
    },
    // 麦克风采集的音量大小
    doneAudioVolume(state) {
      return state.audioVolume;
    },
    // 采集音频的状态
    doneAudioState(state) {
      return state.isAudioMuted;
    },
    doneAudioLevel(state) {
      return state.audioLevel;
    },
    // 采集视频的状态
    doneVideoState(state) {
      return state.isVideoMuted;
    },
    doneAllMemberDrawingLimit(state) {
      return state.allMemberDrawingLimit;
    },
    doneAllMemberTrophy(state) {
      return state.allMemberTrophy;
    },
    doneSpeakerVolume(state) {
      return state.speakerVolume;
    },
    doneliveInstance(state) {
      return state.liveInstance;
    },
    doneShareScreen(state) {
      return state.isScreenSharing;
    },
    // 直播间状态
    doneLiveStage(state) {
      return state.liveStage;
    },
    doneTreasureCount(state) {
      return state.getTreasureCount;
    },
    // 画板权限
    doneDrawingAuthority(state) {
      return state.isDraw;
    },
    // 上台状态
    doneOnStage(state) {
      return state.isOnStage;
    },
    doneRaiseHand(state) {
      return state.isRaiseHand;
    },
    doneChat(state) {
      return state.chatBox;
    },
    doneChessBoard(state){
      return state.isChessBoard;
    },
    doneStartAnswer(state){
      return state.isStartAnswer;
    },
    doneAnswerOption(state){
      return state.answerOption;
    },
    doneChessBoardParam(state){
      return state.chessBoardId;
    },
    doneQuestionId(state){
      return state.questionId;
    },
    doneTeacher(state){
      return state.isTeacher;
    },
    doneHandUpUser(state){
      return state.handUpUser;
    },
    doneTeacherId(state){
      return state.teacherId;
    },
    doneShowLocal(state){
      return state.isShowLocal;
    },
    doneStandard(state){
      return state.isStandard;
    },
    doneTeacherName(state){
      return state.teacherName;
    },
    doneNoiseReduction(state){
      return state.noiseReduction;
    },
    doneMicrophone(state){
      return state.isMicrophone;
    },
    doneCamera(state){
      return state.isCamera;
    },
    doneMute(state){
      return state.isMute;
    },
    doneCameraAuthority(state){
      return state.isCameraAuthority;
    },
    doneTreasureSta(state){
      return state.treasureSta;
    },
    doneLastAnswer(state){
      return state.lastAnswer;
    },
    // 白板尺寸
    doneBoardSize(state){
      return state.boardSize;
    },
    doneUpdateRollCall(state){
      return state.isCarousel;
    },
    doneIsAllMute(state){
      return state.isAllMute;
    },
    doneTsLookCamera(state){
      return state.isLookCamera;
    },
    doneAnswerTime(state){
      return state.answerTime;
    }
  };
  const actions = {
    async SET_ROOM_ID(context, payload) {
      context.commit("SET_ROOM_ID", payload);
    },
    async SET_ROOM_NAME(context, payload) {
      context.commit("SET_ROOM_NAME", payload);
    },
    async SET_USERSIG(context, payload) {
      context.commit("SET_USERSIG", payload);
    },
  
    async UPDATE_ACTIVE_CAMERA(context, payload) {
      context.commit("UPDATE_ACTIVE_CAMERA", payload);
    },
    async UPDATE_ACTIVE_MICROPHONE(context, payload) {
      context.commit("UPDATE_ACTIVE_MICROPHONE", payload);
    },
    async UPDATE_ACTIVE_SPEAKER(context, payload) {
      context.commit("UPDATE_ACTIVE_SPEAKER", payload);
    },
    async UPDATE_SET_MIRROR(context, payload) {
      context.commit("UPDATE_SET_MIRROR", payload);
    },
    async UPDATE_LIVE_STAGE(context, payload) {
      context.commit("UPDATE_LIVE_STAGE", payload);
    },
    async UPDATE_AUDIOVOLUME(context, payload) {
      context.commit("UPDATE_AUDIOVOLUME", payload);
    },
    async UPDATE_AUDIO_STATE(context, payload) {
      context.commit("UPDATE_AUDIO_STATE", payload);
    },
    async UPDATE_VIDEO_STATE(context, payload) {
      context.commit("UPDATE_VIDEO_STATE", payload);
    },
    async UPDATE_ALL_MEMEBR_NOT_SPEAK(context, payload) {
      context.commit("UPDATE_ALL_MEMEBR_NOT_SPEAK", payload);
    },
    async UPDATE_ALL_MEMEBR_DRAWINGLIMIT(context, payload) {
      context.commit("UPDATE_ALL_MEMEBR_DRAWINGLIMIT", payload);
    },
    async UPDATE_AUDIO_LEVEL(context, payload) {
      context.commit("UPDATE_AUDIO_LEVEL", payload);
    },
    async SET_MUTE_REMOTE_AUDIO_VOLUME(context, payload) {
      context.commit("SET_MUTE_REMOTE_AUDIO_VOLUME", payload);
    },
    async SET_SPEAKER_VOLUME(context, payload) {
      context.commit("SET_SPEAKER_VOLUME", payload);
    },
    async UPDATE_GET_TREASURE_COUNT(context, payload) {
      context.commit("UPDATE_GET_TREASURE_COUNT", payload);
    },
    async UPDATE_DRAW_PERMISSION(context, payload) {
      context.commit("UPDATE_DRAW_PERMISSION", payload);
    },
    async UPDATE_STAGE_STATUS(context, payload) {
      context.commit("UPDATE_STAGE_STATUS", payload);
    },
    async UPDATE_RAISE_HAND_STATUS(context, payload) {
      context.commit("UPDATE_RAISE_HAND_STATUS", payload);
    },
    async UPDATE_CHAT_BOX(context, payload) {
      context.commit("UPDATE_CHAT_BOX", payload);
    },
    async UPDATE_CHESS_BOARD_STATUS(context, payload) {
      context.commit("UPDATE_CHESS_BOARD_STATUS", payload);
    },
    async UPDATE_ANSWER_STATUS(context, payload) {
      context.commit("UPDATE_ANSWER_STATUS", payload);
    },
    async UPDATE_CHESS_BOARD_PARAM(context,payload) {
      context.commit("UPDATE_CHESS_BOARD_PARAM",payload);
    },
    async UPDATE_QUESTION(context,payload) {
      context.commit("UPDATE_QUESTION",payload);
    },
    async UPDATE_TEACHER_PERMISSION(context,payload) {
      context.commit("UPDATE_TEACHER_PERMISSION",payload);
    },
    RESET_CARTSTATE ({ commit }) {
      commit('RESET_CARTSTATE')
    },
    async UPDATE_HAND_UP_USER(context, payload) {
      context.commit("UPDATE_HAND_UP_USER", payload);
    },
    async UPDATE_TEACHER(context, payload) {
      context.commit("UPDATE_TEACHER", payload);
    },
    async UPDATE_SHOW_SELF(context, payload) {
      context.commit("UPDATE_SHOW_SELF", payload);
    },
    async UPDATE_IS_STANDARD(context, payload) {
      context.commit("UPDATE_IS_STANDARD", payload);
    },
    async UPDATE_TEACHER_NAME(context, payload) {
      context.commit("UPDATE_TEACHER_NAME", payload);
    },
    async UPDATE_MICROPHONE_STATUS(context, payload) {
      context.commit("UPDATE_MICROPHONE_STATUS", payload);
    },
    async UPDATE_CAMERA_STATUS(context, payload) {
      context.commit("UPDATE_CAMERA_STATUS", payload);
    },
    async UPDATE_MUTE_STATUS(context, payload) {
      context.commit("UPDATE_MUTE_STATUS", payload);
    },
    async UPDATE_ANSWER_OPTION(context, payload){
      context.commit("UPDATE_ANSWER_OPTION", payload);
    },
    async UPDATE_CAMERA_PERMISSION(context, payload){
      context.commit("UPDATE_CAMERA_PERMISSION", payload);
    },
    async UPDATE_TREASURE_STATUS(context, payload){
      context.commit("UPDATE_TREASURE_STATUS", payload);
    },
    async UPDATE_LAST_ANSWER(context, payload){
      context.commit("UPDATE_LAST_ANSWER", payload);
    },
    async UPDATE_WHITE_BOARD_SIZE(context, payload){
      context.commit("UPDATE_WHITE_BOARD_SIZE", payload);
    },
    async UPDATE_ROTATION_STATUS(context, payload){
      context.commit("UPDATE_ROTATION_STATUS", payload);
    },
    async UPDATE_ALL_MUTE_STATUS(context, payload){
      context.commit("UPDATE_ALL_MUTE_STATUS", payload);
    },
    async UPDATE_LOOK_CAMERA(context, payload){
      context.commit("UPDATE_LOOK_CAMERA", payload);
    },
    async UPDATE_QUESTION_TIME(context, payload){
      context.commit("UPDATE_QUESTION_TIME", payload);
    }
  };
  
  export default {
    state,
    mutations,
    getters,
    actions,
    initState:Object.assign({}, state)
  };
  
