const state = {
  chessMoveSound: true,
};
const mutations = {
  setChessMoveSound(state, chessMoveSound) {
      state.chessMoveSound = chessMoveSound;
  },
};
const getters = {
  getChessMoveSound: (state) => state.chessMoveSound,
};
const actions = {
  // firstLoading(context,data){
  //   console.log(222);
  //   context.commit("setFirstLoading", data);
  // }
};

export default {
  state,
  mutations,
  getters,
  actions,
};
