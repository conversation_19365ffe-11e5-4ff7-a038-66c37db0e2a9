import zip from "../../public/zip";

const state = {
    // messageList: [],
    messageObj: {},
    wsReadyStats:0,
    network: 0
};
const mutations = {
    setMessage(state, msg) {
        let msg1 = msg.message ? JSON.parse(msg.message) : JSON.parse(msg);
        let data1 =
            msg1.message_type !== "client_info"
                ? JSON.parse(zip.unzipStr(msg1.message))
                : msg1;
        state.messageObj = {'index': msg1['index'], 'data': data1,'group':msg1['group_id']};
    },
    setWsReadyStats(state,msg) {
        state.wsReadyStats = msg;
    },
    setNetwork(state, msg){
      state.network = msg;
    }
};
const getters = {
    getMessage: (state) => state.messageObj,
    getWsReadyStats: (state) => state.wsReadyStats,
    getNetwork: (state) => state.network
};
const actions = {
    receive(context, data) {
        context.commit("setMessage", data);
    },
    wsReadyStats(context,data) {
        context.commit("setWsReadyStats", data);
    },
    network(context,data){
      context.commit("setNetwork", data);
    }
};

export default {
    state,
    mutations,
    getters,
    actions,
};
