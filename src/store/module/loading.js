const state = {
    firstLoading: true,
    imitateRoute: null,
    versionData: "",
    apiLoading: false
};
const mutations = {
    setFirstLoading(state, loading) {
        state.firstLoading = loading;
        console.log(loading);
    },
    setImitateRoute(state, imitateRoute) {
      state.imitateRoute = imitateRoute;
    },
    setVersionData(state, versionData){
      console.log(versionData)
      state.versionData = versionData;
    },
    setApiLoading(state, apiLoading){
      state.apiLoading = apiLoading;
    }
};
const getters = {
    getFirstLoading: (state) => state.firstLoading,
    getImitateRoute: (state) => state.imitateRoute,
    getVersionData: (state) => state.versionData,
    getApiLoading: (state) => state.apiLoading
};
const actions = {
    // firstLoading(context,data){
    //   console.log(222);
    //   context.commit("setFirstLoading", data);
    // }
};

export default {
    state,
    mutations,
    getters,
    actions,
};
