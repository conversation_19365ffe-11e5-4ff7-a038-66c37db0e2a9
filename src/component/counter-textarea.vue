<template>
  <div class="paper-textarea-warpper">
    <textarea
      class="paper-textarea jcyt400"
      :placeholder="placeholder"
      :value="textAreaValue"
      :maxlength="maxlength"
      @input="getTextAreaValue"
    />
    <div class="paper-length-maxlength jcyt400">
      <span>{{ length }}</span> / <span>{{ maxlength }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    placeholder: {
      type: String,
      default: "请输入内容",
    },
    value: {
      type: [String, undefined],
      default: undefined,
    },
    maxlength: {
      type: [Number],
      default: 300,
    },
  },
  data() {
    return {
      textAreaValue: this.value,
      length: this.value?.length || 0,
    };
  },
  methods: {
    getTextAreaValue(e) {
      const event = e || window.event;
      const target = event.srcElement || event.taget;
      this.length = target.value.length;
      if (length <= this.maxlength) {
        this.textAreaValue = target.value;
      }
      this.$emit("update:value", this.textAreaValue);
    },
  },
};
</script>

<style lang="less">
.paper-textarea-warpper {
  position: relative;
  width: 100%;
  height: 328px;
  padding: 40px;
  background-color: #f6f8fb;
  border-radius: 40px;
  box-sizing: border-box;
  .paper-length-maxlength {
    position: absolute;
    bottom: 24px;
    right: 40px;
    font-size: 28px;
    color: #bdbdbd;
  }
}
.paper-textarea {
  border: none;
  width: 100%;
  height: 100%;
  background-color: #f6f8fb;
  height: auto;
  outline: none;
  /** 禁止textarea拉伸 */
  resize: none;
  font-size:32px;
  /* WebKit, Blink, Edge */
  &::-webkit-input-placeholder {
    color: #bdbdbd;
    font-size:32px;
  }
  /* Mozilla Firefox 4 to 18 */
  &:-moz-placeholder {
    color: #bdbdbd;
    font-size:32px;
  }
  /* Mozilla Firefox 19+ */
  &::-moz-placeholder {
    color: #bdbdbd;
    font-size:32px;
  }
  /* Internet Explorer 10-11 */
  &:-ms-input-placeholder {
    color: #bdbdbd;
    font-size:32px;
  }
}
</style>