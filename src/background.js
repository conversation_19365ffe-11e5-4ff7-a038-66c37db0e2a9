"use strict";

import { app, protocol, BrowserWindow, screen, Menu ,systemPreferences,globalShortcut} from "electron";
import { createProtocol } from "vue-cli-plugin-electron-builder/lib";
import installExtension, { VUEJS_DEVTOOLS } from "electron-devtools-installer";
import { createMenu } from "../src/public/menu";
import  { useCapture } from '../src/lib/capture-main'
const isDevelopment = process.env.NODE_ENV !== "production";
let win2;
app.commandLine.appendSwitch('disable-renderer-backgrounding');
app.commandLine.appendSwitch("disable-site-isolation-trials");
protocol.registerSchemesAsPrivileged([
  {
    scheme: "app",
    privileges: { secure: true, standard: true, supportFetchAPI: true }
  }
]);

if (!app.isReady()) {
  app.disableHardwareAcceleration();
  app.on("ready", () => {
    // The app is now ready, enable GPU acceleration if available
    if (app.isReady()) {
      console.log("GPU acceleration is available");
    } else {
      app.disableHardwareAcceleration();
      console.log("GPU acceleration is not available");
    }
  });
} else {
  // The app is already ready, enable GPU acceleration if available
  if (app.isReady()) {
    console.log("GPU acceleration is available");
  } else {
    app.disableHardwareAcceleration();
    console.log("GPU acceleration is not available");
  }
}

async function createWindow() {
  const win = new BrowserWindow({
    width:
      (screen.getPrimaryDisplay().workAreaSize.width / 1920) * 1080 + 30 >
      screen.getPrimaryDisplay().workAreaSize.height
        ? parseInt(
            (screen.getPrimaryDisplay().workAreaSize.height * 1920) / 1080
          )
        : parseInt(screen.getPrimaryDisplay().workAreaSize.width),
    height:
      (screen.getPrimaryDisplay().workAreaSize.width / 1920) * 1080 + 30 >
      screen.getPrimaryDisplay().workAreaSize.height
        ? parseInt(screen.getPrimaryDisplay().workAreaSize.height)
        : parseInt(
            (screen.getPrimaryDisplay().workAreaSize.width / 1920) * 1080 + 30
          ),
    minWidth: 480,
    minHeight: 270,
    maxWidth:
      (screen.getPrimaryDisplay().workAreaSize.width / 1920) * 1080 + 30 >
      screen.getPrimaryDisplay().workAreaSize.height
        ? parseInt(
            (screen.getPrimaryDisplay().workAreaSize.height * 1920) / 1080
          )
        : parseInt(screen.getPrimaryDisplay().workAreaSize.width),
    maxHeight:
      (screen.getPrimaryDisplay().workAreaSize.width / 1920) * 1080 + 30 >
      screen.getPrimaryDisplay().workAreaSize.height
        ? parseInt(screen.getPrimaryDisplay().workAreaSize.height)
        : parseInt(
            (screen.getPrimaryDisplay().workAreaSize.width / 1920) * 1080 + 30
          ),
    autoHideMenuBar: true, // 隐藏菜单栏
    fullscreen: false,
    center: true,
    maximizable: false,
    show: false,
    webPreferences: {
      nodeIntegration: process.env.ELECTRON_NODE_INTEGRATION,
      contextIsolation: !process.env.ELECTRON_NODE_INTEGRATION,
      zoomFactor: 3.0
    }
  });

 
  let template = [
    {
      label: "文件",
      submenu: [
        {
          label: "聂卫平围棋",
          accelerator: "Ctrl+O",
         click: function () {
            app.emit("activate");
          }
        },
        {label:"退出", role: 'quit' }
      ]
    },
    {
      label: "查看",
      submenu: [
        {
          label: "重载",
          accelerator: "CmdOrCtrl+R",
          click: function (item, focusedWindow) {
            if (focusedWindow) {
              // 重载之后, 刷新并关闭所有的次要窗体
              if (focusedWindow.id === 1) {
                BrowserWindow.getAllWindows().forEach(function (win) {
                  if (win.id > 1) {
                    win.close();
                  }
                });
              }
              focusedWindow.reload();
            }
          }
        },
        // {
        //   label: "切换开发者工具",
        //   accelerator: (function () {
        //     if (process.platform === "darwin") {
        //       return "Alt+Command+I";
        //     } else {
        //       return "Ctrl+Shift+I";
        //     }
        //   })(),
        //   click: function (item, focusedWindow) {
        //     if (focusedWindow) {
        //       focusedWindow.toggleDevTools();
        //     }
        //   }
        // }
      ]
    },
    {
      label: "窗口",
      role: "window",
      submenu: [
        {
          label: "最小化",
          accelerator: "CmdOrCtrl+M",
          role: "minimize"
        },
        {
          label: "关闭",
          accelerator: "CmdOrCtrl+W",
          role: "close"
        },
        {
          type: "separator"
        },
        {
          label: "重新打开窗口",
          accelerator: "CmdOrCtrl+Shift+T",
          enabled: false,
          key: "reopenMenuItem",
          click: function () {
            app.emit("activate");
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
  win.maximize();
  win.show();

  win.setAspectRatio(1920 / 1080);
  win.center();
  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // Load the url of the dev server if in development mode
    await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL);
    if (!process.env.IS_TEST) win.webContents.openDevTools();
  } else {
    createProtocol("app");
    win.loadURL("app://./index.html");
  }
  win.on("close", () => {
    if (win2) {
      win2.destroy();
    }
  });
  require('electron').powerMonitor.on('unlock-screen', () => {
    try{
      win.webContents.send("close","调用退出");
    }catch(e){
      console.log(e)
    }
  })
  // 初始化截图
  useCapture();
}

// Quit when all windows are closed.
app.on("window-all-closed", () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on("ready", async () => {
  if (isDevelopment && !process.env.IS_TEST) {
    // Install Vue Devtools
    try {
      await installExtension(VUEJS_DEVTOOLS);
    } catch (e) {
      console.error("Vue Devtools failed to install:", e.toString());
    }
  }
  createWindow();
});

// Exit cleanly on request from parent process in development mode.
if (isDevelopment) {
  if (process.platform === "win32") {
    process.on("message", (data) => {
      if (data === "graceful-exit") {
        app.quit();
      }
    });
  } else {
    process.on("SIGTERM", () => {
      app.quit();
    });
  }
}

const ipcMain = require("electron").ipcMain;
const EMediaType = {
  microphone  : 'microphone', // 麦克风
  camera : 'camera', // 相机
}
/**
 * 访问状态
 *  'not-determined'：[未确定]表示用户尚未做出决定，或者系统尚未提示用户进行授权。
 *  'granted'：[已授权]表示用户已经明确授予了应用的权限。
 *  'denied'：[拒绝]表示用户拒绝了应用授权的请求。
 *  'restricted'：[受限]在某些情况下，可能是由于系统策略或其他安全限制导致应用无法获得改权限。
 *  'unknown'：[未知]在无法确定权限状态的情况下返回，可能是因为某种错误或其他不可预知的情况。
 */
let IAccessStatus = 'not-determined' | 'granted' | 'denied' | 'restricted' | 'unknown'
// 权限授权
const requestMediaAccess = async (mediaType) => {

  try {
    console.log('mac系统')
    // 获取当前媒体设备（在这里指麦克风或摄像头）的访问权限状态
    const privilege = systemPreferences.getMediaAccessStatus(mediaType)
    if(process.platform == 'darwin'){
      // macOS系统
      if (privilege !== 'granted') {
        // 未授权,则重新唤起系统弹框,等待用户点击授权
        await systemPreferences.askForMediaAccess(mediaType)
        // 请求权限后，再次获取媒体访问状态并返回
        return systemPreferences.getMediaAccessStatus(mediaType)
      }
    }else{
      // 非macOS系统
      console.log('windows系统')
      if (privilege !== 'granted') {
        // 未授权,则重新唤起系统弹框,等待用户点击授权
        if(mediaType == 'camera'){
          navigator.mediaDevices.getUserMedia({ video: true })
          .then(() => {
            console.log('摄像头用户已授权')
            return 'granted';
          }) // 用户已授权
          .catch(() => {
            console.log('摄像头用户未授权')
            return 'unknown';
          }); // 用户未授权
        }else if(mediaType == 'microphone'){
          navigator.mediaDevices.getUserMedia({ audio: true })
          .then(() => {
            return 'granted';
          }) // 用户已授权
          .catch(() => {
            return 'unknown';
          }); // 用户未授权
        }else if(mediaType == 'screen'){
          if (privilege !== 'granted') {
            // 未授权,则重新唤起系统弹框,等待用户点击授权
            await systemPreferences.askForMediaAccess(mediaType)
            // 请求权限后，再次获取媒体访问状态并返回
            return systemPreferences.getMediaAccessStatus(mediaType)
          }
        }
        // await navigator.permissions.request({ name: mediaType })
        // // 请求权限后，再次获取媒体访问状态并返回
        // return await navigator.permissions.query({ name: mediaType })
      }
    }
    // 已授权,则直接返回媒体访问状态
    return privilege
  } catch (e) {
    console.error('Failed to request media access:', e)
    return 'unknown'
  }

}
ipcMain.on('request-media-access', (event, message) => {
  console.log(`receive message from render: ${message}`) 
})
ipcMain.on('request-video-access', (event, message) => {
  console.log(`receive message from render: ${message}`) 
})
ipcMain.handle('request-media-access', async (event, mediaType = EMediaType.microphone) => {
  return requestMediaAccess(mediaType)
})
ipcMain.handle('request-video-access', async (event, mediaType = EMediaType.camera) => {
  return requestMediaAccess(mediaType)
})
ipcMain.on('get-screen-details', (event) => {
  event.reply('screen-details', screen.getPrimaryDisplay());
});
ipcMain.on('set-winTop', (event,args)=>{
  console.log(args)
  win.setAlwaysOnTop(args);
})
ipcMain.on("openWindow", function (event, url) {
  win2 =
    process.platform === "darwin"
      ? new BrowserWindow({
          fullscreen: true,
          maximizable: true,
          webPreferences: {
            nodeIntegration: true,
            enableRemoteModule: true,
            contextIsolation: false,
            nodeIntegration: process.env.ELECTRON_NODE_INTEGRATION,
            enableRemoteModule: process.env.ELECTRON_NODE_INTEGRATION,
            autoHideMenuBar: true // 隐藏菜单栏
          }
        })
      : new BrowserWindow({
          fullscreen: false,
          maximizable: true,
          width: parseInt(screen.getPrimaryDisplay().workAreaSize.width),
          height: parseInt(screen.getPrimaryDisplay().workAreaSize.height),
          minWidth: 480,
          minHeight: 270,
          maxHeight: parseInt(screen.getPrimaryDisplay().workAreaSize.height),
          maxWidth: parseInt(screen.getPrimaryDisplay().workAreaSize.width),
          webPreferences: {
            nodeIntegration: true,
            enableRemoteModule: true,
            contextIsolation: false,
            autoHideMenuBar: false // 隐藏菜单栏
          }
        });
  //createMenu(win2);
  if (url.indexOf("https:") == -1 && url.indexOf("http:") == -1) {
    createProtocol("app");
  }
  win2.loadURL(url);
  win2.on("close", () => {
    win2.destroy();
  });
});
