export default {
  install(Vue) {
    // 防止重复点击
    Vue.directive('preventReClick', {
      bind(el, binding, vnode) {
        let clicked = false;
        el.addEventListener('click', () => {
          if (!clicked) {
            clicked = true;
            binding.value(); // 执行传入指令的方法
            setTimeout(() => {
              clicked = false;
            }, 3000); // 设置 1 秒后可再次点击
          }
        });
      }
    });
  },
};
