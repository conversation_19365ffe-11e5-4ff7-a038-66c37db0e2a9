import Vue from "vue";
import App from "./App.vue";
import "./style/index.css";
import router from "./router";
import axios from "axios";
import storage from "@/public/storage.js";
import configApi from "./config.js";
import "mint-ui/lib/style.css";
import { Popover,Switch,Slider,Tooltip,Table,TableColumn,Rate,DatePicker,Progress} from 'element-ui';
import "./assets/font/font.css";
import VueBus from "./utils/bus.js";
Vue.use(VueBus);
import { Picker } from 'mint-ui';
import preventReClick from "@/utils/prevent"
Vue.use(preventReClick)
import VueClipboards from "vue-clipboards";
Vue.use(VueClipboards);

Vue.component(Picker.name, Picker);

Vue.prototype.$storage = storage;
Vue.prototype.axios = axios;
Vue.config.productionTip = false;

import electron from 'electron'
Vue.prototype.$electron = electron;

import store from './store/index.js'
Vue.prototype.$store = store

import ws from "./public/ws";
Vue.prototype.$socket = ws.Socket;

Vue.prototype.TEduBoard = window.TEduBoard;
import moment from "moment";
Vue.prototype.moment = moment;
const BrowserLogger = require("alife-logger");
const __bl = BrowserLogger.singleton({
  pid: "j711v9sbe7@7baa7850fa1b760",
  appType: "web",
  imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
  sendResource: true,
  enableLinkTrace: true,
  behavior: true,
  enableSPA: true,
  useFmp: true,
  enableConsole: true,
});
__bl.setConfig({
  environment: configApi.BaseApiUrl.includes("dev") ? "pre" : "prod",
});
Vue.component(Picker.name, Picker);
Vue.component(Switch.name, Switch);
Vue.component(Slider.name,Slider);
Vue.component(Tooltip.name,Tooltip);
Vue.component(Table.name,Table);
Vue.component(TableColumn.name,TableColumn);
Vue.component(Rate.name,Rate);
Vue.component(DatePicker.name,DatePicker);
Vue.component(Progress.name, Progress);
Vue.use(Popover,Switch,Slider,Table,TableColumn,Rate,DatePicker,Progress)

new Vue({
  render: (h) => h(App),
  router,
  store,
  __bl
}).$mount("#app");