/**
 * Created by xujian1 on 2018/10/8.
 */

const { remote, screen } = require('electron')


let currentWindow = remote.getCurrentWindow()
console.log(currentWindow);
exports.getCurrentScreen = () => {
    let { x, y } = currentWindow.getBounds();
    console.log(currentWindow.getBounds())
    console.log(remote.screen.getAllDisplays())
    return remote.screen.getAllDisplays().filter(d => d.bounds.x === x && d.bounds.y === y)[0]
}

exports.isCursorInCurrentWindow = () => {
    let { x, y } = remote.screen.getCursorScreenPoint()
    let {
        x: winX, y: winY, width, height,
    } = currentWindow.getBounds()
    return x >= winX && x <= winX + width && y >= winY && y <= winY + height
}
