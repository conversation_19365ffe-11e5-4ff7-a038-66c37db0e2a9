<template>
  <div>
    <div v-show="apiLoading" class="api-loading-wrap">
      <div class="api-loading">
        <div class="newCanvas">
          <canvas id="apiLoadingCanvas"></canvas>
        </div>
        <span class="jcyt400">加载中...</span>
      </div>
    </div>
    <div v-if="firstLoading" class="startup">
      <div class="newCanvas">
        <!-- <canvas id="startCanvas"></canvas> -->
        <img src="@/assets/login/logo1.png" class="app-logo"/>
      </div>
    </div>
    <template v-else>
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive"> </router-view>
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive"> </router-view>
      <v-versionDialog
        :version_dialog="version_dialog"
        :versionData="versionData"
        :is_force_updated="is_force_updated"
        @close_version_dialog="close_version_dialog"
      />
    </template>
  </div>
</template>

<script>
import userApi from "@/api/user.js";
import config from "./config.js";
import versionDialog from "./components/Index/versionDialog";
const tyVersion = require("../package.json");
import moment from "moment";
import { setWH, createRive } from "@/public/setCanvas";

export default {
  name: "App",
  data() {
    return {
      isAirSchool: "",
      version: "",
      showVersion: "",
      versionData: {},
      version_dialog: false,
      is_force_updated: false,
      timer: null,
      startRive: "",
      apiLoadingRive: ""
    };
  },
  components: {
    "v-versionDialog": versionDialog
  },
  created() {
    let firstLoading = window.sessionStorage.getItem("firstLoading");
    this.$store.commit("setFirstLoading", firstLoading == null ? true : firstLoading == "false" ? false : true);
    this.isAirSchool = config.isAirSchool;
    this.version = tyVersion.version;
    this.$nextTick(() => {
      this.$store.commit("initWebSocket");
    });
    this.geVersion();
    // this.setFirstLoading();
    // window.addEventListener("resize", this.onResize);
  },
  watch:{
    apiLoading: {
      handler(val) {
        if(val) {
          this.createApiLoading();
        }
      }
    }
  },
  beforeDestroy() {
		window.removeEventListener("resize", this.onResize);
	},
  mounted(){
    if(this.firstLoading) {
      setTimeout(() => {
        this.$nextTick(() => {
          this.$store.commit("setFirstLoading", false);
          window.sessionStorage.setItem("firstLoading", false);
        })
      }, 2500);
    }
    let chessMoveSound = window.localStorage.getItem("chessMoveSound");
    this.$store.commit("setChessMoveSound", typeof chessMoveSound == "undefined" || chessMoveSound == null ? true : chessMoveSound == "false" ? false : true);
  },
  computed:{
    wsReadyStats() {
      return this.$store.getters.getWsReadyStats;
    },
    firstLoading(){
      return this.$store.getters.getFirstLoading;
    },
    apiLoading(){
      return this.$store.getters.getApiLoading;
    }
  },
  methods: {
    close_version_dialog() {
      this.version_dialog = false;
    },
    setFirstLoading(){
      var firstTime = Number(this.$storage.$getStroage("firstComing"));
      if(typeof firstTime == "undefined") {
        return;
      }
      let nowTime = moment(new Date().getTime());
      let canOpenTime = moment(firstTime).add(10, "seconds");
      console.log(canOpenTime);
      console.log(nowTime);

      this.$store.commit("setFirstLoading",  nowTime.isAfter(canOpenTime));
    },
    async geVersion() {
      const os = require("os");
      let osSystem =
        os.platform() == "darwin"
          ? "macos"
          : os.platform() == "win32"
          ? "windows"
          : "pc";
      var res = await userApi.AppBrand({
        app_type: this.isAirSchool ? "airSchool" : "higo",
        band: osSystem
      });
      this.versionData = res.data;
      this.is_force_updated = res.data["is_force_updated"];
      this.$store.commit("setVersionData", res.data);
      if (
        parseInt(
          (parseInt(res.data["version"].replace(".", "")) * 1000)
            .toString()
            .substring(0, 5)
        ) >
        parseInt(
          (parseInt(this.version.replace(".", "")) * 1000)
            .toString()
            .substring(0, 5)
        )
      ) {
        if (this.is_force_updated) {
          this.version_dialog = true;
        }
      }
    },
    async createApiLoading(){
      this.$nextTick(async () => {
        await setWH("apiLoadingCanvas");
        this.apiLoadingRive = await createRive("apiLoadingCanvas", "apiLoading.riv");
      })
    },
    onResize() {
      this.apiLoadingRive.resizeToCanvas();
    }
  }
};
</script>

<style lang="scss">
.picker-slot {
  font-size: 40px !important;
}
.mt-picker-wrap {
  margin-top: -150px !important;
}
/* .mint-datetime-action {
  height: 100px !important;
  line-height: 100px !important;
} */
/* .picker-toolbar {
  height: 100px !important;
} */
.mint-datetime .picker-toolbar {
  border-bottom: none !important;
}
.mint-datetime-cancel {
  text-align: left !important;
}
.mint-datetime-confirm {
  text-align: right !important;
}
/* .picker-toolbar {
  padding: 0 40px !important;
} */
.mint-toast {
  padding: 24px !important;
}
.mint-toast-text {
  font-size: 40px !important;
}
/* .mint-popup {
  height: 1100px !important;
} */
/* .mint-datetime-picker {
  height: 1100px !important;
} */

button,
input,
input:focus {
  outline: none;
}

.jcyt300 {
  font-family: "jcyt300W";
}

.jcyt400 {
  font-family: "jcyt400W";
}

.jcyt500 {
  font-family: "jcyt500W";
}

.jcyt600 {
  font-family: "jcyt600W";
}

.jcyt700 {
  font-family: "jcyt700W";
}
.startup {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #fff;
  z-index: 1000;
}
.newCanvas {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
}
.firstLoading {
  width: 1580px;
  height: 1080px;
  right: 0;
  bottom: 0;
  margin: auto;
}
.newCanvas canvas {
  width: 100%;
  height: 100%;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.content-center {
  justify-content: center;
}
input {
  border: 4px solid transparent!important;
}
input:focus {
  outline: 4px solid transparent;
  border: 4px solid #16C3FF!important;
}

.app-logo {
  width: 738px;
  height: 176px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 10;
  animation: logoBounce 2.5s forwards ease-in;
}
@keyframes logoBounce {
    10% {
      transform: scale(1.2);
    }
    from, 20%, 53%, 80%, to {
      animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
      transform: scale(1)
    }

    40%, 43% {
      animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
      transform: scale(0.8);
    }

    70% {
      animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
      transform: scale(0.9)
    }

    90% {
      transform: scale(1)
    }
}
.infinite-status-prompt {
  font-family: jcyt500w;
}
.api-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 300px;
  height: 300px;
  border-radius: 20px;
  background: rgba(0, 0, 0, .7);
  z-index: 999;
}
.api-loading span {
  color: #fff;
  display: inline-block;
  width: 100%;
  text-align: center;
  font-size: 28px;
}
.api-loading .newCanvas {
  width: 225px;
  height: 250px;
  position: unset;
  margin: 0 auto;
}
.api-loading-wrap {
  width: 100vw;
  height: 100vh;
  position: absolute;
  z-index: 998;
}
.el-popover {
  width: 300px;
  height: auto;
  padding: 5px;
  min-width: 400px;
  .emojis {
    width: 100%;
    height: 300px;
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    padding: 10px;
    box-sizing: border-box;
    .emoji {
      width: 50px;
      height: 50px;
      text-align: center;
      display: flex;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
}
.el-tooltip__popper{
  border-radius: 4px;
  padding: 30px;
  z-index: 2000;
  font-size: 12px;
  line-height: 1.2;
  min-width: 30px;
  word-wrap: break-word;
  font-size: 20px !important;
  .popper__arrow{
    display: none;
  }
}
.el-popper{
  width: 688px !important;
  .el-date-picker__header{
    .el-icon-d-arrow-right,.el-icon-d-arrow-left{
      display: none;
    }
    .el-picker-panel__icon-btn:hover{
      border-radius: 50%;
      background: #08b9fc;
      color: aliceblue;
      width: 50px;
      height: 50px
    }
    // display:none;
    line-height: 80px;
    margin:12px;
    font-size:30px;
    .el-date-picker__header-label{
      font-size:30px;
      line-height: 10px;
    }
    .el-picker-panel__icon-btn{
      font-size: 40px;
      margin-top: 20px;
      font-weight: 700;
      width: 50px;
      height: 50px
      
    }
    
  }
  .el-picker-panel__content{
    width: 688px !important;
    .el-month-table,.el-year-table td{
      padding: 20px;
      line-height: 20px ;
      div:first-child{
       height: 100%;
      }
       .cell{
        width: 100px;
        height: 100px;
       }
    }
  }
  
.el-date-table{
  border-spacing:15px 20px;
  .current{
    background: #08b9fc !important;
    span{
      color: black !important;
    }
  }
}
  .el-date-table td{
    width: 12px;
    height: 30px;
    padding: 24px 0;
    border-radius: 12px;
    background: #dee4e7;
    // color: black;
  }
  .el-date-table td span{
    width: 24px;
    height: 24px;
    top: -10px
  }
  .el-date-table td div{
    height:30px;
  }
  .el-picker-panel__content{
    margin:0;
   
  }
  .el-date-table th{
      font-size: 30px;
    }
    th{
      font-size: 30px;
    }
    td{
      font-size: 30px;
    }
    .el-date-table tr:first-child {
    // display:none;
    line-height: 12px;
  }
}.el-date-table td.current:not(.disabled) span{
  color:red;
  background:center;
}

</style>
