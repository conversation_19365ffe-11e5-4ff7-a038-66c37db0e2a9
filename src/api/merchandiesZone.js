import axios from "../http";
function merchandiseZonesListAPI(data) {
  return axios
    .get("/api/v1/merchandise-zone/list", {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function merchandiseListAPI(data) {
  return axios
    .get("/api/v2/public-service/merchandise/list", {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function merchandiseCommodityListAPI(data) {
  return axios
    .get(`"/api/v1/merchandise/list/${data['merchand_id']}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getMerchandiseInfoAPI(data) {
  return axios
    .get(`/api/v1/merchandise/info/${data['merchandiseDetail_id']}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

export default {
  merchandiseZonesListAPI,
  merchandiseListAPI,
  merchandiseCommodityListAPI,
  getMerchandiseInfoAPI
}