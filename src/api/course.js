import axios from "../http";
import { Toast } from "mint-ui";
function courseListApi(data) {
  return axios
    .get("/api/v2/content-service/course/list", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function lessonListApi(data) {
  return axios
    .get(
      `/api/v2/content-service/lesson/list?course_id=${data["course_id"]}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}
function lessonInfoApi(data) {
  return axios
    .get(
      `/api/v2/content-service/lesson/info?course_id=${data["course_id"]}&lesson_id=${data["lesson_id"]}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

function checkGameOpenApi(data) {
  return axios
    .get("/api/v2/public-service/check-game-open", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

function createAiExercise(data) {
  return axios
    .post("/api/v2/content-service/ai-stage/make", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

function getExamId(data) {
  return axios
    .get("/api/unit-service/match/last-exam-id", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

function getCardInfo(data) {
  return axios
    .post("/api/v2/content-service/card/info", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

function updateCard(data) {
  return axios
    .post("/api/v2/content-service/card/update", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}
function homeworkInfo(data) {
  return axios
    .post("/api/v2/content-service/homework/info", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}
function updateHomework(data) {
  return axios
    .post("/api/v2/content-service/homework/update", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

function getVideoInfo(data) {
  return axios
    .get(`/api/v2/content-service/video/info?video_id=${data.video_id}&video_type=${data.video_type}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

function updateVideoHistory(data) {
  return axios
    .post("/api/v2/content-service/video/update", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}
//检查是否有对局未结束
function checkNotEndGame(data) {
  return axios
    .get("/api/v2/public-service/ai/check-not-end", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}

export default {
  CourseListApi(data) {
    return courseListApi(data);
  },
  LessonListApi(data) {
    return lessonListApi(data);
  },
  LessonInfoApi(data) {
    return lessonInfoApi(data);
  },
  CheckGameOpenApi(data) {
    return checkGameOpenApi(data);
  },
  CreateAiExercise(data) {
    return createAiExercise(data);
  },
  GetExamId(data) {
    return getExamId(data);
  },
  GetCardInfo(data) {
    return getCardInfo(data);
  },
  UpdateCard(data) {
    return updateCard(data);
  },
  HomeworkInfo(data) {
    return homeworkInfo(data)
  },
  UpdateHomework(data) {
    return updateHomework(data)
  },
  GetVideoInfo(data) {
    return getVideoInfo(data)
  },
  UpdateVideoHistory(data) {
    return updateVideoHistory(data)
  },
  CheckNotEndGame(data) {
    return checkNotEndGame(data)
  }
};
