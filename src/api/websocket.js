import axios from "axios";
import configApi from "@/config";
function getMissGroupWebSocketMsg(data) {
  return axios
    .post("https://" + configApi.wsLivingBaseUrl+ "/api/public/ws-gateway-service/group/msg/index", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

export default {
  GetMissGroupWebSocketMsg(data) {
    return getMissGroupWebSocketMsg(data);
  },
};
