import axios from "../http";

function studyReportListApi(data) {
  return axios
    .get("/api/v2/report-service/get-report/list?date_type=month", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getStudyReportDetailApi(data) {
  return axios
    .get(`/api/v2/report-service/get-report/report?id=${data.id}`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

export default {
  StudyReportListApi(data) {
    return studyReportListApi(data);
  },
  GetStudyReportDetailApi(data) {
    return getStudyReportDetailApi(data);
  }
};
