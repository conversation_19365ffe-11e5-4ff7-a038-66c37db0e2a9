import { Toast } from "mint-ui";
import axios from "../http";
import router from "../router";

function close(data) {
  return axios
    .post("/api/v2/public-service/ai/close", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function resign(data) {
  return axios
    .post("/api/v3/game-service/play/resign", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
        throw error;
    })
    .finally();
}

function getAiList(data) {
  return axios
    .get("/api/v2/public-service/ai/list", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getAiInfo(data) {
  return axios
    .get("/api/v2/public-service/ai/info", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getAiLeftTimes(data) {
  return axios
    .get("/api/v2/public-service/ai/left-times", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getAiAgent(data) {
  return axios
    .get("/api/v1/ai/agent", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function createMatch(data) {
  return axios
    .post("/api/v2/public-service/ai/make", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
        throw error;
    })
    .finally();
}

function getStatusData(data) {
  return axios
    .get(`/api/v2/game-service/player-game/status/${data["game_id"]}`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function captured(data) {
  return axios
    .post("/api/v2/game-service/player-game/captured", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function pass(data) {
  return axios
    .post("/api/v3/game-service/play/pass", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
        throw error;
    })
    .finally();
}

function humsMove(data) {
  return axios
    .post("/api/v3/game-service/play/move", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}

function areaScore(data) {
  return axios
    .post("/api/v3/game-service/play/score", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
        throw error;
    })
    .finally();
}

function clickTimes(data) {
  return axios
    .get(`/api/v2/game-service/player-game/click/${data["game_id"]}`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
        throw error;
    })
    .finally();
}

function gameResult(data) {
  return axios
    .get("/api/rank-service/pair/game-result", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
        throw error;
    })
    .finally();
}

function checkGameOpenApi(data) {
  return axios
    .get("/api/v2/public-service/check-game-open", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//申请数目
function applyAreaScore(data) {
  return axios
    .post("/api/v3/game-service/play/apply-score", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//同意数目
function agreeAreaScore(data) {
  return axios
    .post("/api/v3/game-service/play/agree-score", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//拒绝数目
function disagreeAreaScore(data) {
  return axios
    .post("/api/v3/game-service/play/reject-score", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//申请和棋
function applyDraw(data) {
  return axios
    .post("/api/v3/game-service/play/apply-summation", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//拒绝和棋
function rejectDraw(data) {
  return axios
    .post("/api/v3/game-service/play/reject-summation", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//同意和棋
function agreeDraw(data) {
  return axios
    .post("/api/v3/game-service/play/agree-summation", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//检查申请仲裁状态
function getArbitrationStatus(data) {
  return axios
    .get("/api/v1/tournament/arbitration/status", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function arbitrationCreate(data) {
  return axios
    .post("/api/v1/tournament/arbitration/apply", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

// 新对弈 ===============分界线
function getPlayInfo(data) {
  return axios
    .post("/api/v3/game-service/play/info", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      let e = error.error_code.indexOf("对弈不存在");
      if(e > -1) {
        Toast({message: "数据不存在", duration: 3000});
        router.go(-1);
      }
      throw error;
    })
    .finally();
}

function enterBoard(data) {
  return axios
    .post("/api/v3/game-service/play/enter", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function playSgf(data) {
  return axios
    .post("/api/v3/game-service/play/sgf", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function canPlay(data) {
  return axios
    .post("/api/v3/game-service/play/canplay", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getMaintain(data) {
  return axios
    .get("/api/v3/game-service/public/app/game/maintain", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

export default {
  Resign(data) {
    return resign(data);
  },
  Close(data) {
    return close(data);
  },
  GetAiList(data) {
    return getAiList(data);
  },
  GetAiInfo(data) {
    return getAiInfo(data);
  },
  GetAiLeftTimes(data) {
    return getAiLeftTimes(data);
  },
  GetAiAgent(data) {
    return getAiAgent(data);
  },
  CreateMatch(data) {
    return createMatch(data);
  },
  GetStatusData(data) {
    return getStatusData(data);
  },
  Captured(data) {
    return captured(data);
  },
  Pass(data) {
    return pass(data);
  },
  HumsMove(data) {
    return humsMove(data);
  },

  AreaScore(data) {
    return areaScore(data);
  },
  ClickTimes(data) {
    return clickTimes(data);
  },
  GameResult(data) {
    return gameResult(data);
  },
  CheckGameOpenApi(data) {
    return checkGameOpenApi(data);
  },
  ApplyAreaScore(data) {
    return applyAreaScore(data);
  },
  AgreeAreaScore(data) {
    return agreeAreaScore(data);
  },
  DisagreeAreaScore(data) {
    return disagreeAreaScore(data);
  },
  GetArbitrationStatus(data) {
    return getArbitrationStatus(data);
  },
  ArbitrationCreate(data) {
    return arbitrationCreate(data);
  },

  // 新对弈 ===============分界线
  GetPlayInfo(data) {
    return getPlayInfo(data);
  },
  EnterBoard(data) {
    return enterBoard(data);
  },
  PlaySgf(data) {
    return playSgf(data);
  },
  CanPlay(data) {
    return canPlay(data);
  },
  ApplyDraw(data) {
    return applyDraw(data);
  },
  RejectDraw(data) {
    return rejectDraw(data);
  },
  AgreeDraw(data) {
    return agreeDraw(data);
  },
  getMaintain
};
