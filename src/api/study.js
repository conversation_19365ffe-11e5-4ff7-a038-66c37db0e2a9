
import axios from "../http";

function getLivingCourseApi(data) {
  return axios
    .get("/api/v2/live-service/app/live/list")
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getLivingCourseTicketApi(data) {
  return axios
    .get("/api/v2/live-service/app/live/student/ticket",{ params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getMyLivingCourseApi(data) {
  return axios
    .get("/api/v2/live-service/app/live/my-list",{ params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}


export default {
  getLivingCourseApi,
  getLivingCourseTicketApi,
  getMyLivingCourseApi
};