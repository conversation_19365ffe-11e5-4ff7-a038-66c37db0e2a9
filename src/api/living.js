import axios from "../http";
import qs from "qs";
// 获取学员直播列表
function liveCourseList(data) {
  return axios
    .get("/api/live-go-service/third-public/student/course-list", {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 进入直播间获取直播签名等信息
function studentCome(data) {
  return axios
    .get("/api/live-go-service/third-public/student/come", {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 点击操作麦克风 摄像头
function studentControl(data) {
  return axios
    .post("/api/live-go-service/third-public/student/control", data, {
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 获取操作麦克风 摄像头
function studentRoomInfo(data) {
  return axios
    .get("/api/live-go-service/third-public/student/room-info", {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 学生进入直播间
function studentEnter(data) {
  return axios
    .post("/api/live-go-service/third-public/student/enter", data, {
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 学生退出直播间
function studentOuter(data) {
  return axios
    .post("/api/live-go-service/third-public/student/outer", data, {
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 获取学员信息
function studentsInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/live-go-service/third-public/student/students-info?${new_data}`,
      { type: "live" }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 获取天奕题库
function questionBank(data) {
  return axios
    .get(`/api/live-go-service/third-public/student/question-bank`, {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 获取直播间内上台顺序
function upStudentList(data) {
  return axios
    .get(`/api/live-go-service/third-public/student/up_student-list`, {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 获取回放列表

function playbackList(data) {
  return axios
    .get(`/api/live-go-service/third-public/student/playback-list`, {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 学生回放统计
function studentPlaybackCollect(data) {
  return axios
    .post("/api/live-go-service/third-public/student/playback-collect", data, {
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 学生进入直播间获取直播状态
function studentCourseStatus(data) {
  return axios
    .get(`/api/live-go-service/third-public/student/course-status`, {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 直播间是否允许展示
function permissionGet(data) {
  return axios
    .get("/api/live-go-service/third-public/permission/get", {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 敏感词汇过滤
function studentSensitive(data) {
  return axios
    .post("/api/live-go-service/third-public/student/sensitive", data, {
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 评分统计
function studentEvaluationCollect(data) {
  return axios
    .post(
      "/api/live-go-service/third-public/student/evaluation-collect",
      data,
      { type: "live" }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 获取评分信息
function studentEvaluationInfo(data) {
  return axios
    .get("/api/live-go-service/third-public/student/evaluation-info", {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

// 获取学生课堂报告
function studentLiveCourseReport(data) {
  return axios
    .get("/api/live-go-service/third-public/student/live-course-report", {
      params: data,
      type: "live",
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 获取当前天工所需的token
function liveToken(data) {
  return axios
    .post("/api/v1/student/live-token", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

export default {
  async LiveCourseList(data) {
    return liveCourseList(data);
  },
  async StudentCome(data) {
    return studentCome(data);
  },
  async StudentControl(data) {
    return studentControl(data);
  },
  async StudentRoomInfo(data) {
    return studentRoomInfo(data);
  },
  async StudentEnter(data) {
    return studentEnter(data);
  },
  async StudentOuter(data) {
    return studentOuter(data);
  },
  async StudentsInfo(data) {
    return studentsInfo(data);
  },
  async QuestionBank(data) {
    return questionBank(data);
  },
  async UpStudentList(data) {
    return upStudentList(data);
  },
  async PlaybackList(data) {
    return playbackList(data);
  },
  async StudentPlaybackCollect(data) {
    return studentPlaybackCollect(data);
  },
  async StudentCourseStatus(data) {
    return studentCourseStatus(data);
  },
  async permissionGet(data) {
    return permissionGet(data);
  },
  async StudentSensitive(data) {
    return studentSensitive(data);
  },
  async StudentEvaluationCollect(data) {
    return studentEvaluationCollect(data);
  },
  async StudentEvaluationInfo(data) {
    return studentEvaluationInfo(data);
  },
  async StudentLiveCourseReport(data) {
    return studentLiveCourseReport(data);
  },
  async LiveToken(data) {
    return liveToken(data);
  },
};
