import axios from "../http";


//排行榜 学员吃子排位赛校区榜列表
function rankingCaptureBranchListApi(data) {
  return axios
  .get("/api/rank-service/student-info/capture-branch-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员吃子排位赛班级榜列表
function rankingCaptureClassListApi(data) {
  return axios
  .get("/api/rank-service/student-info/capture-class-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员吃子排位赛总榜列表
function rankingCaptureListApi(data) {
  return axios
  .get("/api/rank-service/student-info/capture-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员吃子排位赛名次信息
function rankingCaptureRankApi(data) {
  return axios
  .get("/api/rank-service/student-info/capture-rank", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员围地排位赛校区榜列表
function rankingTerritoryBranchListApi(data) {
  return axios
  .get("/api/rank-service/student-info/territory-branch-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员围地排位赛班级榜列表
function rankingTerritoryClassListApi(data) {
  return axios
  .get("/api/rank-service/student-info/territory-class-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员围地排位赛总榜列表
function rankingTerritoryListApi(data) {
  return axios
  .get("/api/rank-service/student-info/territory-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员围地排位赛名次信息
function rankingTerritoryRankApi(data) {
  return axios
  .get("/api/rank-service/student-info/territory-rank", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}


export default {
  rankingCaptureBranchListApi,
  rankingCaptureClassListApi,
  rankingCaptureListApi,
  rankingCaptureRankApi,
  rankingTerritoryBranchListApi,
  rankingTerritoryClassListApi,
  rankingTerritoryListApi,
  rankingTerritoryRankApi
}