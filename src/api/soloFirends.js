import axios from "../http";
import { Toast } from "mint-ui";
function gameListAPi(data) {
    // 好友约战对局列表
    return axios
        .get("/api/v3/game-service/public/app/game/list", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
function creatRoomListApi() {
    //创建房间
    return axios
        .get("/api/v2/solo-room/list", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}

function creatRoomApi(data) {
    //创建房间
    return axios
        .post("/api/v2/solo-room/create", data)
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}

//等待对手页面接口
function roomStatusApi(data) {
    return axios
        .get("/api/v2/solo-room/status", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
//取消对局
function closeRoomApi(data) {
    return axios
        .get(`/api/v2/solo-room/disable/${data.room_id}`, { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
// 加入房间
function joinRoomApi(data) {
    return axios
        .get(`/api/v2/solo-room/join/${data.solo_room_numb}`, { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
// 开始对局
function startGameApi(data) {
    return axios
        .get(`/api/v2/solo-room/make/${data.room_id}`, { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
//获取好友约战规则列表
function friendFightRuleList(data) {
    return axios
        .get(`/api/v2/solo-room/friend-fight-rule/list`, { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}

export default {
    async GameListAPi(data) {
        return gameListAPi(data);
    },
    async CreatRoomListApi(data) {
        return creatRoomListApi(data);
    },
    async CreatRoomApi(data) {
        return creatRoomApi(data);
    },
    async RoomStatusApi(data) {
        return roomStatusApi(data);
    },
    async CloseRoomApi(data) {
        return closeRoomApi(data);
    },
    async JoinRoomApi(data) {
        return joinRoomApi(data);
    },
    async StartGameApi(data) {
        return startGameApi(data);
    },
    async FriendFightRuleList(data) {
        return friendFightRuleList(data);
    }
};
