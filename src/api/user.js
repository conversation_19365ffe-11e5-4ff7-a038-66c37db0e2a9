import axios from "../http";

function getUserInfo() {
  return axios
    .get("/api/v1/student/info", {params: {date: new Date().getTime()}})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
//解绑学习卡
function offBindStudyCard(data) {
  return axios
    .get("/api/v2/public-service/study-card/mobile-unbind-study-card", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//解绑手机号
function offBindMobile(data) {
  return axios
    .get("/api/v2/public-service/study-card/study-card-unbind-mobile", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
//获取app版本信息
function appVersion(data) {
  return axios
    .get("/api/v1/app-version", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//获取app版本信息
function appBrand(data) {
  return axios
    .get("/api/v1/app-version/band", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function branchSchoolList(data) {
  return axios
    .get("/api/v2/public-service/branch-school/list", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function meshPing(data) {
  return axios
    .post("/api/v1/ping", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}


export default {
  async GetUserInfo() {
    return getUserInfo();
  },
  async OffBindStudyCard() {
    return offBindStudyCard();
  },
  async OffBindMobile() {
    return offBindMobile();
  },
  async AppVersion() {
    return appVersion();
  },
  async AppBrand(data){
    return appBrand(data);
  },
  branchSchoolList,
  meshPing
};
