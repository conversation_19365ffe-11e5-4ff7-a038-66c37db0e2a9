import axios from "../http";
function getTopCategoryList(data) {
    // 题库
    return axios
      .get("/api/v2/content-service/question-library/top-category/list", data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function getCategoryList(data) {
    // 题库
    return axios
      .get("/api/v2/content-service/question-library/category/list", {params:data})
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function getQuestionList(data) {
    // 题库
    return axios
      .get("/api/v2/content-service/question/list", {params:data})
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      }) 
      .finally();
  }
  function getIdList(data) {
    return axios
      .get("/api/v2/content-service/question/id-list", {params:data})
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      }) 
      .finally();
  }
  function postQuestionAnswer(data) {
    return axios
      .post("/api/v2/content-service/question/answer", data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      }) 
      .finally();
  }
  function getQuestionInfo(data) {
    return axios
      .get("/api/v2/content-service/question/info", {params:data})
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      }) 
      .finally();
  }
  function getQuestionFavorite(data) {
    return axios
      .get("/api/v2/content-service/question/favorite", {params:data})
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      }) 
      .finally();
  }

  export default {
    GetTopCategoryList(data) {
      return getTopCategoryList(data);
    },
    GetCategoryList(data) {
      return getCategoryList(data);
    },
    GetQuestionList(data) {
      return getQuestionList(data);
    },
    GetQuestionInfo(data) {
      return getQuestionInfo(data);
    },
    GetIdList(data) {
      return getIdList(data);
    },
    PostQuestionAnswer(data) {
      return postQuestionAnswer(data);
    },
    GetQuestionFavorite(data) {
      return getQuestionFavorite(data);
    },
  };