import axios from "../http";

function getUnitTestRule(data) {
  // 题库
  return axios
    .get("/api/unit-service/match/match-info", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getExamId(data) {
  // 题库
  return axios
    .get("/api/unit-service/match/last-exam-id", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getReport(data) {
  // 题库
  return axios
    .get("/api/unit-service/match/report", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function question(data) {
  return axios
    .get("/api/unit-service/match/question", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function status(data) {
  return axios
    .get("/api/unit-service/match/status", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function getReportList(data) {
  return axios
    .get("/api/unit-service/match/report-list", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function PostSendAnswer(data) {
  return axios
    .post("/api/unit-service/match/answer", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function PostCreatExam(data) {
  return axios
    .post("/api/unit-service/match/create-exam", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

export default {
  GetUnitTestRule(data) {
    return getUnitTestRule(data);
  },
  GetExamId(data) {
    return getExamId(data);
  },
  GetReport(data) {
    return getReport(data);
  },
  Question(data) {
    return question(data);
  },
  Status(data) {
    return status(data);
  },
  GetReportList(data) {
    return getReportList(data);
  },
  PostSendAnswer(data) {
    return PostSendAnswer(data);
  },
  PostCreatExam(data) {
    return PostCreatExam(data);
  }
};