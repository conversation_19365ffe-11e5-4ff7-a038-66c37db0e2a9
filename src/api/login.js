import axios from "../http";
import { Toast } from "mint-ui";
function sendSMS(data) {
  // 登录获取验证码
  return axios
    .post("/api/v1/sms", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function login(data) {
  // 登录
  return axios
    .post("/api/v1/student/login", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}
function resetPasswordCode(data) {
  // 忘记密码获取验证码
  return axios
    .post("/api/v1/forgot-sms", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}
function resetPassword(data) {
  // 设置并登录
  return axios
    .post("/api/v1/student/forget-password", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Toast(error.error_code);
     throw error;
    })
    .finally();
}
export default {
  SendSMS(data) {
    return sendSMS(data);
  },
  Login(data) {
    return login(data);
  },
  ResetPasswordCode(data) {
    return resetPasswordCode(data);
  },
  ResetPassword(data) {
    return resetPassword(data);
  }
};
