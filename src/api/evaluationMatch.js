import axios from "../http";

function evaluationMatchListApi(data) {
  return axios
    .get("/api/v2/evaluation/list", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}


function evaluationMatchNotEndApi(data) {
  return axios
    .get("/api/v2/evaluation/check-not-end-match", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}


function evaluationMatchInfoApi(data) {
  return axios
    .get(`/api/v2/evaluation/info?match_id=${data['match_id']}`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}



function evaluationMatchStatusApi(data) {
  return axios
    .get(`/api/v2/evaluation/status?match_id=${data['match_id']}`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}


function gameStatus(data) {
  return axios
    .get("/api/v2/evaluation/status", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}


function question(data) {
  return axios
    .get("/api/v2/evaluation/question", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

function questionAnswer(data) {
  return axios
    .post("/api/v2/evaluation/question-answer", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

function questionPoint(data) {
  return axios
    .get("/api/v2/evaluation/history/point", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

function matchRating(data) {
  return axios
    .get("/api/v2/evaluation/history/rating", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

function historyGame(data) {
  return axios
    .get("/api/v2/evaluation/history/game", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

export default {
  evaluationMatchListApi,
  evaluationMatchNotEndApi,
  evaluationMatchInfoApi,
  evaluationMatchStatusApi,
  gameStatus,
  question,
  questionAnswer,
  questionPoint,
  matchRating,
  historyGame
}