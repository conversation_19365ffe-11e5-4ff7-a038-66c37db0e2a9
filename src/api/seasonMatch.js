
import axios from "../http";
import { Toast } from "mint-ui";
function avatarRankListApi(data) {
    // 头像列表
    return axios
        .get("/api/v1/avatar/rank-list", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
//用户数据
function SeasonInfoApi(data) {
    return axios
        .get("/api/rank-service/student-info/" + data + "-info", {})
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}


export default {
    async AvatarRankListApi(data) {
        return avatarRankListApi(data);
    },
    SeasonInfoApi

};