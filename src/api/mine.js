import axios from "../http";
import { Toast } from "mint-ui";

function getInfo(data) {
  return axios
    .get("/api/v1/student/info", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function sgfListAPi(data) {
  return axios
    .get("/api/v2/game-service/game/my-kifu", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function getAvatarListAPi(data) {
  return axios
    .get("/api/v1/avatar/list", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function changeAvatar(data) {
  return axios
    .post("/api/v1/avatar/update", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function updateInformation(data) {
  return axios
    .post("/api/v1/student/updateInfo", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function getSettingLogin(data) {
  return axios
    .get("/api/v2/public-service/setting/logoff", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
// 绑定手机号---发送验证码
function bindMobileSendSMS(data) {
  return axios
    .post("/api/v2/public-service/study-card/send-bind-mobile-sms", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function bindMobile(data) {
  return axios
    .post("/api/v2/public-service/study-card/bind-mobile-by-sms", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
//绑定学习卡
function bindStudyCard(data) {
  return axios
    .post("/api/v2/public-service/study-card/bind-study-card", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
//用户回馈
function feedBack(data) {
  return axios
    .post("/api/v2/public-service/feedback/create", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
//我的棋谱
function getSgfList(data) {
  return axios
    .get("/api/v3/game-service/public/app/battle/list", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//我的订单
function getOrderList(data) {
  return axios
    .get("/api/v1/order/list", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function studyReportList(data) {
  return axios
    .get("/api/v2/report-service/get-report/list", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
        Toast(error.error_code);
       throw error;
      })
    .finally();
}
function delNumberAPi(data) {
  return axios
    .post("/api/v1/student/logoff", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
        Toast(error.error_code);
       throw error;
      })
    .finally();
}

function delNumberDownAPi() {
  return axios
    .get("/api/v1/student/logoff-countdown")
    .then((response) => {
      return response;
    })
    .catch((error) => {
        Toast(error.error_code);
       throw error;
      })
    .finally();
}

function revokeAPi() {
  return axios
    .post("/api/v1/student/revoke")
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function gameCountAPi() {
  return axios
    .get("/api/v2/content-service/history/game-count")
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

function getSeasonInfo(){
  return axios
  .get("/api/rank-service/student-info/personal", {params: {date: new Date().getTime()}})
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

export default {
  async GetInfo(data) {
    return getInfo(data);
  },
  async SgfListAPi(data) {
    return sgfListAPi(data);
  },
  async GetAvatarListAPi(data) {
    return getAvatarListAPi(data);
  },
  async ChangeAvatar(data) {
    return changeAvatar(data);
  },
  async UpdateInformation(data) {
    return updateInformation(data);
  },
  async GetSettingLogin(data) {
    return getSettingLogin(data);
  },
  async BindMobileSendSMS(data) {
    return bindMobileSendSMS(data);
  },
  async BindMobile(data) {
    return bindMobile(data);
  },
  async BindStudyCard(data) {
    return bindStudyCard(data);
  },
  async FeedBack(data) {
    return feedBack(data);
  },
  async GetSgfList(data) {
    return getSgfList(data);
  },
  async GetOrderList(data) {
    return getOrderList(data);
  },
  async StudyReportList(data) {
    return studyReportList(data);
  },
  async DelNumberAPi(data) {
    return delNumberAPi(data);
  },
  async DelNumberDownAPi() {
    return delNumberDownAPi();
  },
  async RevokeAPi() {
    return revokeAPi();
  },
  async GameCountAPi() {
    return gameCountAPi();
  },
  getSeasonInfo
};
