import axios from "../http";

function getExerciseList(data) {
  return axios
    .post("/api/v2/content-service/homework/info", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    })
    .finally();
}
function sendAnswer(data) {
  return axios
    .post("/api/v2/content-service/question/answer", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    })
    .finally();
}
function getExerciseDetail(data) {
  return axios
    .get(
      `/api/v2/content-service/question/info?question_id=${data.question_id}&homework_id=${data.homework_id}&lesson_id=${data.lesson_id}&course_id=${data.course_id}&source=${data.source}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    })
    .finally();
}
function getExerciseHistoryList(data) {
  return axios
    .get(`/api/v1/content/homework-result?exercise_id=${data.exercise_id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    })
    .finally();
}
function updateHomework(data) {
  // 获取答题规则
  return axios
    .post("/api/v2/content-service/homework/update", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      /* eslint-disable */
      console.log(error);
      /* eslint-enable */
    })
    .finally();
}
function homeworkInfoApi(data) {
    // 获取答题规则
    return axios
      .post("/api/v2/content-service/homework/info", data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        /* eslint-disable */
        console.log(error);
        /* eslint-enable */
      })
      .finally();
  }

export default {
  GetExerciseList(data) {
    return getExerciseList(data);
  },
  GetExerciseDetail(data) {
    return getExerciseDetail(data);
  },
  SendAnswer(data) {
    return sendAnswer(data);
  },
  GetExerciseHistoryList(data) {
    return getExerciseHistoryList(data);
  },
  UpdateHomework(data) {
    return updateHomework(data);
  },
  HomeworkInfoApi(data) {
    return homeworkInfoApi(data);
  }
};
