import axios from "../http";
// import { Toast } from "mint-ui";
function seasonOpenApi(data) {
  return axios
    .get("/api/rank-service/student-info/" + data + "-open")
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function seasonInfoApi(data) {
  return axios
    .get("/api/rank-service/student-info/" + data + "-info", )
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function weekRecordInfoApi(data) {
  return axios
    .get(
      "/api/rank-service/week-record/" +
        data["type"] +
        "-info?season_id=" +
        data["season_id"]
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function weekRecordTotalApi(data) {
  return axios
    .get(
      "/api/rank-service/week-record/" +
        data["type"] +
        "-list?season_id=" +
        data["season_id"] +
        "&page=1"
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function weekRecordClassApi(data) {
  return axios
    .get(
      "/api/rank-service/week-record/" +
        data["type"] +
        "-class-list?season_id=" +
        data["season_id"] +
        "&page=1"
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function weekRecordBranchApi(data) {
  return axios
    .get(
      "/api/rank-service/week-record/" +
        data["type"] +
        "-branch-list?season_id=" +
        data["season_id"] +
        "&page=1"
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function seasonInfoListApi() {
  return axios
    .get("/api/rank-service/season-info/list")
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}
function territoryLevelListApi() {
    return axios
      .get("/api/rank-service/level/territory-list")
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function levelUpdateApi(data) {
    return axios
      .post("/api/rank-service/student-info/territory-info/update",data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function checkApi(data) {
    return axios
      .get("/api/rank-service/pair/check-not-end",{params:data})
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function checkNotEndApi(data) {
    return axios
      .get("/api/rank-service/pair/check-not-end?rank_type=" + data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function avatarRankListApi() {
    return axios
      .get("/api/v1/avatar/rank-list")
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function avatarAddApi(data) {
    return axios
      .post("/api/v1/student-avatar/add",data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function seasinPairApi(data) {
    return axios
      .post(`/api/rank-service/pair/make?rank_type=${data.rank_type}`,data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
  function seasinPairCancelApi(data) {
    return axios
      .post(`/api/rank-service/pair/cancel?rank_type=${data.rank_type}`,data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
       throw error;
      })
      .finally();
  }
export default {
  async SeasonOpenApi(data) {
    return seasonOpenApi(data);
  },
  async SeasonInfoApi(data) {
    return seasonInfoApi(data);
  },
  async WeekRecordInfoApi(data) {
    return weekRecordInfoApi(data);
  },
  async WeekRecordTotalApi(data) {
    return weekRecordTotalApi(data);
  },
  async WeekRecordClassApi(data) {
    return weekRecordClassApi(data);
  },
  async WeekRecordBranchApi(data) {
    return weekRecordBranchApi(data);
  },
  async SeasonInfoListApi() {
    return seasonInfoListApi();
  },
  async TerritoryLevelListApi() {
    return territoryLevelListApi();
  },
  async LevelUpdateApi(data) {
    return levelUpdateApi(data);
  },
  async CheckApi(data) {
    return checkApi(data);
  },
  async CheckNotEndApi(data) {
    return checkNotEndApi(data);
  },
  async AvatarRankListApi() {
    return avatarRankListApi();
  },
  async AvatarAddApi(data) {
    return avatarAddApi(data);
  },
  async SeasinPairApi(data) {
    return seasinPairApi(data);
  },
  async SeasinPairCancelApi(data) {
    return seasinPairCancelApi(data);
  },
};
