import axios from "../http";

//每周新星 上周学员吃子排位赛校区榜列表
function weekCaptureBranchListApi(data) {
  return axios
  .get("/api/rank-service/week-record/capture-branch-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员吃子排位赛班级榜列表
function weekCaptureClassListApi(data) {
  return axios
  .get("/api/rank-service/week-record/capture-class-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员吃子排位赛总榜列表
function weekCaptureListApi(data) {
  return axios
  .get("/api/rank-service/week-record/capture-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员吃子排位赛信息
function weekCaptureInfoApi(data) {
  return axios
  .get("/api/rank-service/week-record/capture-info", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//学员吃子排位赛名次信息
function weekCaptureRankApi(data) {
  return axios
  .get("/api/rank-service/student-info/capture-rank", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员围地排位赛校区榜列表
function weekTerritoryBranchListApi(data) {
  return axios
  .get("/api/rank-service/week-record/territory-branch-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员围地排位赛班级榜列表
function weekTerritoryClassListApi(data) {
  return axios
  .get("/api/rank-service/week-record/territory-class-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员围地排位赛信息
function weekTerritoryInfoApi(data) {
  return axios
  .get("/api/rank-service/week-record/territory-info", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员围地排位赛总榜列表
function weekTerritoryListApi(data) {
  return axios
  .get("/api/rank-service/week-record/territory-list", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

//上周学员围地排位赛
function weekTerritoryRankApi(data) {
  return axios
  .get("/api/rank-service/week-record/territory-rank", { params: data })
  .then((response) => {
    return response;
  })
  .catch((error) => {
   throw error;
  })
  .finally();
}

export default {
  weekCaptureBranchListApi,
  weekCaptureClassListApi,
  weekCaptureListApi,
  weekCaptureInfoApi,
  weekCaptureRankApi,
  weekTerritoryBranchListApi,
  weekTerritoryClassListApi,
  weekTerritoryInfoApi,
  weekTerritoryListApi,
  weekTerritoryRankApi
}