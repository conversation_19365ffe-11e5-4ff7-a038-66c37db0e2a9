import axios from "../http";

function nwpMatchResultTableApi(data) {
    return axios
        .get("/api/v1/tournament/resulttable", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}

function nwpMatchTableApi(data) {
    return axios
        .get("/api/v1/tournament/matchtable", data)
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}

function nwpMatchRoundTableApi(data) {
    return axios
        .get("/api/v1/tournament/matchtable-by-round", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}

function nwpMatchCurrentApi(data) {
    return axios
        .get("/api/v1/tournament/current", data)
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}

function nwpMatchRoundApi(data) {
    return axios
        .get("/api/v1/tournament/round", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}
export default {
    async NwpMatchResultTableApi(data) {
        return nwpMatchResultTableApi(data);
    },
    async NwpMatchTableApi(data) {
        return nwpMatchTableApi(data);
    },
    async NwpMatchRoundTableApi(data) {
        return nwpMatchRoundTableApi(data);
    },
    async NwpMatchCurrentApi(data) {
        return nwpMatchCurrentApi(data);
    },
    async NwpMatchRoundApi(data) {
        return nwpMatchRoundApi(data);
    },
}