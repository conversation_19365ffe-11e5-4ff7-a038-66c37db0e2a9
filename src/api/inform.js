import axios from "../http";
// import { Toast } from "mint-ui";
function getMessageList() {
    return axios
        .get("/api/v2/content-service/message/list")
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}
function getMessageInfo(data) {
    return axios
        .get("/api/v2/content-service/message/info", {
            params: data
        })
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}
function getMessageToast() {
    return axios
        .get("/api/v2/content-service/message/blue-toast")
        .then((response) => {
            return response;
        })
        .catch((error) => {
           throw error;
        })
        .finally();
}
export default {
    async GetMessageList(data) {
        return getMessageList(data);
    },
    async GetMessageInfo(data) {
        return getMessageInfo(data);
    },
    async GetMessageToast(data) {
        return getMessageToast(data);
    }
};
