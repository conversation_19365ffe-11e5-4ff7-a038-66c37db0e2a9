import axios from "../http";
import { Toast } from "mint-ui";
function getOrderDetail(data) {
    // 获取订单详情
    return axios
        .get(`/api/v1/order/info/${data['order_id']}`)
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
function getCancelOrder() {
    return axios
        .post(`/api/v1/order/close`, data)
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}

function getPersonalInformation(data) {
    // 获取订单详情
    return axios
        .get("/api/v1/student/info", { params: data })
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}
function updateAddress(data) {
    return axios
        .post("/api/v1/order/update-address", data)
        .then((response) => {
            return response;
        })
        .catch((error) => {
            Toast(error.error_code);
           throw error;
        })
        .finally();
}

export default {
    async GetOrderDetail(data) {
        return getOrderDetail(data);
    },
    async GetCancelOrder(data) {
        return getCancelOrder(data);
    },
    async GetPersonalInformation(data) {
        return getPersonalInformation(data);
    },
    async UpdateAddress(data) {
        return updateAddress(data);
    }
};
