import axios from "../http";

function gameListAPi(data) {
  return axios
    .get("/api/v2/game-service/game/public-list", {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

// 好友约战对局列表
function creatRoomListApi(data) {
  return axios
    .get("/api/v2/solo-room/list", {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//创建房间
function creatRoomApi(data) {
  return axios
    .post("/api/v2/solo-room/create", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//等待对手页面接口
function roomStatusApi(data) {
  return axios
    .get("/api/v2/solo-room/status", { params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//取消对局
function closeRoomApi(data) {
  return axios
    .get(`/api/v2/solo-room/disable/${data['room_id']}`, {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

// 加入房间
function joinRoomApi(data) {
  return axios
    .get(`/api/v2/solo-room/join/${data['solo_room_numb']}`, {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

// 开始对局
function startGameApi(data) {
  return axios
    .get(`/api/v2/solo-room/make/${data['room_id']}`, {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

//获取好友约战规则列表
function friendFightRuleList(data) {
  return axios
    .get("/api/v2/solo-room/friend-fight-rule/list", {params: data})
    .then((response) => {
      return response;
    })
    .catch((error) => {
     throw error;
    })
    .finally();
}

export default {
  gameListAPi,
  creatRoomListApi,
  creatRoomApi,
  roomStatusApi,
  closeRoomApi,
  joinRoomApi,
  startGameApi,
  friendFightRuleList
}