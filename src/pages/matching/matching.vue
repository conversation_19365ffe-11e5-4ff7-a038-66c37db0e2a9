<template>
  <div
    class="matching"
    :style="{
      'background-image': `url(${require('@/assets/season/排位赛背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="top"
      :style="{
        'background-image': `url(${require('@/assets/season/正在匹配背景.png')})`,
        'background-size': '100% 100%'
      }"
    >
      <div v-if="type == 1" style="display: flex; align-items: center">
        <div class="top-text-wrap">
          <div
            class="top_text"
            :style="{
              'background-image': `url(${require('@/assets/season/正在匹配.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
        </div>
        <div class="timer jcyt600">{{ countdown }}s</div>
      </div>
      <div
        v-else
        class="top_text"
        :style="{
          'background-image': `url(${require('@/assets/season/匹配成功.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
    </div>
    <div class="text_row">
      <div class="scorll_row">
        <div class="text_1 jcyt400" v-for="(item, i) in danmakList" :key="i">
          {{ item }}
        </div>
      </div>
    </div>
    <div
      class="arrar"
      :style="{
        'background-image': `url(${require('@/assets/season/light.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div
      class="arrar internal"
      :style="{
        'background-image': `url(${require('@/assets/season/light.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div
      class="arrar outter"
      :style="{
        'background-image': `url(${require('@/assets/season/light.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="opponent_row">
      <div
        class="opponent_left"
        :style="{
          'background-image': `url(${require('@/assets/season/left.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <div
          class="left_info"
          :style="{
            'background-image': `url(${require('@/assets/season/姓名等级.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <img
            class="head"
            :src="
              info.avatar
                ? info.avatar
                : require('@/assets/season/默认头像.png')
            "
          />
          <div class="name jcyt600">{{ info.name }}</div>
          <div class="level jcyt600">{{ info.nwp_level_name }}</div>
        </div>
      </div>
      <div class="center_box">
        <div class="opponent-shadow">
          <div
            class="opponent_center"
            :style="{
              'background-image': `url(${require('@/assets/season/内圈（转）.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
        </div>
        <div
          class="vs"
          :style="{
            'background-image': `url(${require('@/assets/season/vs.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
      </div>
      <div
        class="opponent_right"
        :style="{
          'background-image': `url(${require('@/assets/season/right.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <div
          class="left_info"
          :style="{
            'background-image': `url(${require('@/assets/season/姓名等级红.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <img
            class="head"
            :src="
              rivalInfo.vs_avatar
                ? rivalInfo.vs_avatar
                : require('@/assets/season/默认头像.png')
            "
          />
          <div class="name jcyt600">
            {{ rivalInfo.vs_name ? rivalInfo.vs_name : "等待棋友" }}
          </div>
          <div class="level jcyt600">{{ type == 2 ? "" : "请稍候..." }}</div>
        </div>
      </div>
    </div>
    <div class="count_down" v-if="type == 2">
      <img class="num" :src="require(`@/assets/season/${num}.png`)" />
    </div>
    <div class="match_close jcyt600" @click="matchClose" v-if="type != 2">取消</div>
    <tips
      v-if="notEndGameVisible"
      :isOpen="notEndGameVisible"
      msg="还有未结束的对局"
      cancelBtn="结束对局"
      reallyBtn="继续对局"
      @cancel="endGame"
      @really="goGame"
    >
    </tips>
    <!-- <mt-dialog
      :visible.sync="notEndGameVisible"
      :showClose="false"
      title="还有未结束的对局"
    >
      <template #footer>
        <div class="footer-container">
          <div class="footer-confirm-button" @click="endGame">结束对局</div>
          <div class="footer-cancel-button" @click="goGame">继续对局</div>
        </div>
      </template>
    </mt-dialog> -->
  </div>
</template>

<script>
import userApi from "@/api/user.js";
import seasonApi from "@/api/season.js";
import gameApi from "@/api/game";
import config from "@/config";
import storage from "@/public/storage.js";
import dialog from "../mine/dialog/publicDialog.vue";
import tool from "@/public/tool";
import AwaitLock from "await-lock";
import websocket from "@/api/websocket";
import tips from "@/components/tips/tips";

export default {
  data() {
    return {
      danmakList: [
        "不闻人声，时闻落子",
        "酌酒浅深需自度，围棋成败有傍观",
        "扫空百局无棋敌，倒尽千钟是酒仙",
        "日长来此消闲兴，一局楸枰对手敲",
        "北风吹人不可出，清坐且可与君棋",
        "战罢两奁分白黑，一枰何处有亏成",
        "成败系之人，吾当著棋史",
        "何处逢神仙，传此棋上旨"
      ],
      info: {},
      type: 1,
      countdown: 0,
      gameId: "",
      rivalInfo: {},
      num: 3,
      notEndGameVisible: false,
      rank_type: "capture",
      last_msg_index: 0,
      miss_msg_locker: new AwaitLock(),
      user_id: "",
      user_hash: ""
    };
  },
  components: {
    "mt-dialog": dialog,
    tips
  },
  computed: {
    wsMessage() {
      return this.$store.getters.getMessage;
    },
    wsReadyStats() {
      return this.$store.getters.getWsReadyStats;
    }
  },
  watch: {
    wsReadyStats(new_stats, old_stats) {
      if (new_stats === 0) {
        // alert("网络连接失败，请检查网络设置");
      }
    },
    wsMessage: async function (newValue, oldValue) {
      await this.onMessage(newValue);
    }
  },
  mounted() {
    this.user_id = storage.$getStroage("userId");
    this.user_hash = `${this.user_id}:1`;
    this.rank_type = this.$route.query.type;
    this.initCheck();
    userApi.GetUserInfo().then((res) => {
      this.$storage.$setStroage("user_token", res.data.token);
      this.info = res.data;
    });
  },
  methods: {
    async onMessage(val) {
      // console.log('onMessage')
      // console.log(val);
      let msg = val.data;
      let index = val.index;
      if (index) {
        if (this.miss_msg_locker.acquired) {
          this.queue.push(val);
          return;
        }
        if (this.last_msg_index === 0) {
          this.last_msg_index = index - 1;
        }
        if (this.last_msg_index + 1 < index) {
          if (this.miss_msg_locker.tryAcquire()) {
            let totalMissMsgCount = index - this.last_msg_index - 1;
            for (let i = 1; i <= totalMissMsgCount; i++) {
              await this.handlerMissMsg(this.last_msg_index + i);
            }
            this.handler(msg);
            this.last_msg_index = index;
            this.doMySelf();
            this.miss_msg_locker.release();
          }
        } else {
          this.handler(msg);
          this.last_msg_index = index;
        }
      }
    },
    async handlerMissMsg(msg_index) {
      await websocket
        .GetMissGroupWebSocketMsg({
          index: msg_index,
          group_id: `game:${this.game_id}`
        })
        .then((res) => {
          console.log(res);
          let data = res.data.data.message;
          console.log(res.data.data.index);
          let msg = zip.unzipStr(data);
          this.handler(JSON.parse(msg));
        }).catch(() => {
          window.location.reload();
        });
    },
    handler(msg) {
      console.log(msg.message_type);
      console.log(msg.data);
      console.log(msg.data.err);
      console.log(msg.data.wait);
      switch (msg.message_type) {
        case "rank:time":
          this.countdown = msg.data.wait;
          break;
        case "rank:paired":
          this.type = 2;
          this.gameId = msg.data.game_id;
          this.starGame(msg.data);
          // this.countdown = msg.data.wait;
          break;
      }
    },
    doMySelf() {
      let val = this.queue.shift();
      if (val) {
        let msg = val.data;
        let index = val.index;
        this.handler(msg);
        this.last_msg_index = index;
      }
      if (this.queue.length > 0) {
        this.doMySelf();
      }
    },
    initCheck() {
      seasonApi.CheckApi({ rank_type: "capture" }).then((res) => {
        if (res.data["has_not_end"] == false) {
          seasonApi
            .SeasinPairApi({ rank_type: this.rank_type })
            .catch((err) => {
              Toast(err.err);
            });
        } else if (res.data["has_not_end"] == true) {
          this.gameId = res.data["game_id"];
          this.notEndGameVisible = true;
        }
      });
    },
    matchClose() {
      this.cancel();
      this.$router.push(`/seasonIndex`);
    },

    starGame(mesData) {
      if (this.type == 2) {
        this.rivalInfo = mesData;
        var starTime = setInterval(() => {
          if (this.num == 1) {
            clearInterval(starTime);
            starTime = null;
            this.$router.push({
              path: "/season/game",
              query: { game_id: this.gameId, from: "qualifying" }
            });
          } else {
            this.num--;
          }
        }, 1000);
      }
    },
    endGame() {
      var send_data = {
        game_id: parseInt(this.gameId ),
        user_hash: this.user_hash,
        user_id: this.user_id
      };
      gameApi.Resign(send_data).then((res) => {
        this.notEndGameVisible = false;
        this.$router.push({
          path: "/seasonIndex"
        });
      });
    },
    goGame() {
      this.notEndGameVisible = false;
      this.$router.push({
        path: "/season/game",
        query: { game_id: this.gameId, from: "qualifying" }
      });
    },
   
    cancel() {
      seasonApi
        .SeasinPairCancelApi({ rank_type: this.rank_type })
        .catch((err) => {
          Toast(err.err);
        });
    }
  },
};
</script>

<style lang="less">
.matching {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .top {
    width: 624px;
    height: 96px;
    margin: 72px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    .top-text-wrap {
      padding-top: 6px;
    }
    .top_text {
      width: 206px;
      height: 50px;
      margin-right: 20px;
    }
    .timer {
      font-size: 48px;
      color: #fffde2;
      line-height: 40px;
    }
  }
  .text_row {
    overflow-x: scroll;
    height: 80px;
    white-space: nowrap;
    // position: absolute;
    .scorll_row {
      animation: 20s wordsloop linear infinite normal;
      padding: 1px;
    }
    .text_1 {
      height: 64px;
      padding: 0 40px;
      background: rgba(255, 255, 255, 0.16);
      border-radius: 32px;
      font-size: 32px;
      color: #ffffff;
      letter-spacing: 0;
      line-height: 64px;
      display: inline-block;
      margin-right: 128px;
    }
  }
  .text_row::-webkit-scrollbar {
    display: none;
  }
  .arrar {
    width: 440px;
    height: 440px;
    margin: 0 auto;
    border-radius: 50%;
    position: absolute;
    left: 740px;
    top: 404px;
    animation-duration: 2.4s;
    animation-name: state1;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }
  .internal {
    animation-delay: 0.9s;
  }
  .outter {
    animation-delay: 1.6s;
  }
  .opponent_row {
    display: flex;
    position: relative;
    margin-top: 144px;
    justify-content: space-between;
    align-items: center;
    .opponent_left {
      width: 638px;
      height: 304px;
      margin-left: 195px;
      .left_info {
        width: 342px;
        height: 152px;
        margin: 76px 0 76px 60px;
        position: relative;
        padding-right: 109px;
        box-sizing: border-box;
        .head {
          width: 160px;
          height: 160px;
          border-radius: 50%;
          border: 6px solid rgba(255, 255, 255, 0.4);
          position: absolute;
          top: -8px;
          right: -85px;
        }
        .name {
          font-size: 40px;
          color: #ffffff;
          text-align: right;
          line-height: 88px;
        }
        .level {
          font-size: 32px;
          color: #ffffff;
          text-align: right;
          line-height: 64px;
        }
      }
    }
    .center_box {
      width: 440px;
      height: 440px;
      position: absolute;
      left: 740px;
      display: flex;
      align-items: center;
      justify-content: center;
      .vs {
        width: 327px;
        height: 272px;
        position: absolute;
      }
    }
    .opponent-shadow {
      width: 440px;
      height: 440px;
      position: absolute;
      box-shadow: rgba(66, 26, 65, 0.71) 0 23px 30px 0;
      border-radius: 50%;
    }
    .opponent_center {
      width: 440px;
      height: 440px;
      // position: absolute;
      border-radius: 50%;
      animation: orbit 2s infinite linear;
    }
    .opponent_right {
      width: 638px;
      height: 304px;
      margin-right: 185px;
      .left_info {
        width: 342px;
        height: 152px;
        margin: 76px 0 76px 215px;
        position: relative;
        padding-left: 109px;
        box-sizing: border-box;
        // margin-left: 288px;
        .head {
          width: 160px;
          height: 160px;
          border-radius: 50%;
          border: 6px solid rgba(255, 255, 255, 0.4);
          position: absolute;
          top: -8px;
          left: -85px;
        }
        .name {
          font-size: 40px;
          color: #ffffff;
          text-align: left;
          line-height: 88px;
        }
        .level {
          font-size: 32px;
          color: #ffffff;
          text-align: left;
          line-height: 64px;
        }
      }
    }
  }
  .count_down {
    width: 100vw;
    height: 304px;
    position: absolute;
    top: 464px;
    left: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    .num {
      width: auto;
      height: 404px;
    }
  }
  .match_close {
    width: 272px;
    height: 80px;
    background: rgba(0, 0, 0, 0.2);
    border: 2.25px solid rgba(255, 255, 255, 0.5);
    border-radius: 40px;
    font-size: 36px;
    color: #ffffff;
    text-align: center;
    line-height: 80px;
    margin: 140px auto 0;
    position: relative;
    z-index: 3;
    cursor: pointer;
  }
  .footer-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    > div {
      width: 312px;
      height: 112px;
      line-height: 112px;
      text-align: center;
      font-size: 40px;
      border-radius: 60px;
      font-weight: 500;
    }
    .footer-confirm-button {
      border: 6px solid #00bdff;
      color: #00bdff;
    }
    .footer-cancel-button {
      background: #08ccfd;
      color: #fff;
    }
  }
}
@keyframes wordsloop {
  0% {
    transform: translateX(10%);
  }
  100% {
    transform: translateX(-100%);
  }
}
@keyframes orbit {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes state1 {
  0% {
    opacity: 0.5;
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  100% {
    opacity: 0;
    -webkit-transform: scale(4.5);
    transform: scale(4.5);
  }
}
</style>
