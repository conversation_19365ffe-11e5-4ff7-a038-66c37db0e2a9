<template>
  <div
    class="login"
  >
  <!-- :style="{
      'background-image': `url(${require('@/assets/login/login_bg.png')})`,
      'background-size': '100% 100%',
    }" -->
    <div class="newCanvas">
      <canvas id="canvas"></canvas>
    </div>
    <div
      class="login_box"
      :style="{
        'background-image': `url(${require('@/assets/login/login_box.png')})`,
        'background-size': '100% 100%',
      }"
    >
      <div
        class="logo"
        :class="{ 'logo-old': !isAirSchool }"
        :style="{
          'background-image': `url(${
            isAirSchool
              ? require('@/assets/login/logo.png')
              : require('@/assets/login/logo1.png')
          })`,
          'background-size': '100% 100%',
        }"
      ></div>
      <div class="nav_row">
        <div
          :class="nav_active == 'sms' ? 'nav_active' : 'nav'"
          @click="navActive('sms')"
          class="jcyt600"
        >
          短信登录
        </div>
        <div
          :class="nav_active == 'password' ? 'nav_active' : 'nav'"
          @click="navActive('password')"
          class="jcyt600"
        >
          密码登录
        </div>
      </div>
      <div
        class="input_box"
        :style="{
          'border-radius':
            nav_active == 'sms' ? '0 4vw 4vw 4vw' : '4vw 0 4vw 4vw',
        }"
        v-if="nav_active == 'sms'"
      >
        <div class="input_item input_top">
          <input
            type="text"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            class="input jcyt500"
            placeholder="请输入手机号"
            v-model="username"
            maxlength="11"
          />
          <i class="close" :style="{
            'background-image': `url(${require('@/assets/login/清除.png')})`,
            'background-size': '100% 100%',
          }" v-if="username !=''" @click="username = ''"></i>
        </div>
        <div class="sms_row">
          <div class="input_item">
            <input
              type="text"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              class="input_sms jcyt500"
              placeholder="请输入验证码"
              v-model="sms_code"
              maxlength="6"
            />
            <i class="close" :style="{
              'background-image': `url(${require('@/assets/login/清除.png')})`,
              'background-size': '100% 100%',
            }" v-if="sms_code !=''" @click="sms_code = ''"></i>
          </div>
          <div
            class="sms_btn jcyt500"
            :style="{ color: isEnabledGetSms ? '#00BDFF' : '#bfc1c5' }"
            @click="getSms"
          >
            {{ codeCountdownStr }}
          </div>
        </div>
        <div
          class="submit jcyt600"
          :style="{
            opacity: isEnabledSubmit ? '1' : '0.5',
            'background-image': `url(${require('@/assets/login/login_btn.png')})`,
            'background-size': '100% 100%',
          }"
          @click="submit"
        >
          登录/注册
        </div>
        <div class="footer">
          <div class="pie" @click="affirmType">
            <div class="pieRight" v-if="affirm_type"></div>
          </div>
          <div class="footer_text jcyt400">
            <span>我已阅读并且同意</span>
            <i @click="goPrivateAgreement">《隐私政策》</i>
            <span>和</span>
            <i @click="goUserAgreement">《用户协议》</i>
          </div>
        </div>
      </div>
      <!-- 密码登录 -->
      <div
        class="input_box"
        :style="{
          'border-radius':
            nav_active == 'sms' ? '0 4vw 4vw 4vw' : '4vw 0 4vw 4vw',
        }"
        v-else
      >
        <div class="input_item input_top">
          <input
            type="text"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            class="input jcyt500"
            placeholder="请输入手机号"
            v-model="username"
          />
          <i class="close" :style="{
            'background-image': `url(${require('@/assets/login/清除.png')})`,
            'background-size': '100% 100%',
          }" v-if="username !=''" @click="username = ''"></i>
        </div>
        <div class="sms_row">
          <div class="input_item">
            <input
              :type="isSee ? 'text' : 'password'"
              class="input jcyt500"
              placeholder="请输入密码"
              style="margin: 0"
              v-model="password"
            />
            <!-- <i class="close close-right" :style="{
              'background-image': `url(${require('@/assets/login/清除.png')})`,
              'background-size': '100% 100%',
            }" v-if="password !=''" @click="password = ''"></i> -->
          </div>
          <i
            :class="isSee ? 'not_icon' : 'input_icon'"
            @click="
              () => {
                isSee = !isSee;
              }
            "
          ></i>
        </div>
        <div
          class="forget_pass jcyt500"
        >
          <span @click="phoneReset = true;">
          忘记密码
          </span>
        </div>
        <div
          class="submit marginTop jcyt600"
          :style="{
            opacity: isEnabledSubmit2 ? '1' : '0.5',
            'background-image': `url(${require('@/assets/login/login_btn.png')})`,
            'background-size': '100% 100%',
          }"
          @click="submit"
        >
          登录
        </div>
        <div class="footer">
          <div class="pie" @click="affirmType">
            <div class="pieRight" v-if="affirm_type"></div>
          </div>
          <div class="footer_text jcyt400">
            <span>我已阅读并且同意</span>
            <i @click="goPrivateAgreement">《隐私政策》</i>
            <span>和</span>
            <i @click="goUserAgreement">《用户协议》</i>
          </div>
        </div>
      </div>
    </div>
    <!-- 忘记密码弹窗 -->
    <div class="dialog_box" v-if="phoneReset">
      <div
        class="phone_reset"
        :style="{
          'background-image': `url(${require('@/assets/login/忘记密码弹窗.png')})`,
          'background-size': '100% 100%',
        }"
      >
        <div class="dialog_title jcyt600">忘记密码</div>
        <div style="display: flex; justify-content: center">
          <input
            type="text"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            class="input_phone jcyt500"
            placeholder="请输入手机号"
            v-model="username"
            maxlength="11"
          />
        </div>
        <div
          class="get_sms jcyt500"
          :style="{
            opacity: isEnabledGetSms ? '1' : '0.5',
            'background-image': `url(${require('@/assets/login/login_btn.png')})`,
            'background-size': '100% 100%',
          }"
          @click="settingPassword"
        >
          发送验证码
        </div>
      </div>
      <div
        class="dialog_close"
        @click="dialogClose"
        :style="{
          'background-image': `url(${require('@/assets/login/关闭按钮.png')})`,
          'background-size': '100% 100%',
        }"
      ></div>
    </div>
    <resetPassword
      v-if="passwordReset"
      :username="username"
      @close="settingDialogClose"
    ></resetPassword>
  </div>
</template>

<script>
import loginApi from "@/api/login";
import { sha256 } from "js-sha256";
import { Toast } from "mint-ui";
import resetPassword from "./dialog/resetPassword.vue";
import config from "@/config";
import { setWH, createRive } from "@/public/setCanvas";

export default {
  name: "loginW",
  data() {
    return {
      nav_active: "password",
      username: "",
      input_sms_code: "",
      affirm_type: false,
      codeCountdownStr: "获取验证码",
      isEnabledSubmit: false,
      isEnabledSubmit2: false,
      isEnabledGetSms: false,
      sms_code: "",
      password: "",
      timer: "",
      expireTime: 60,
      isSee: false,
      phoneReset: false,
      passwordReset: false,
      isAirSchool: "",
      rive: "",
    };
  },
  components: {
    resetPassword,
  },
  watch: {
    username(new_str) {
      console.log(this.isPhoneExp(new_str));
      if (this.isPhoneExp(new_str)) {
        this.isEnabledGetSms = true;
      } else {
        this.isEnabledGetSms = false;
      }
      if (
        (this.isPhoneExp(new_str) && this.sms_code) ||
        (this.isPhoneExp(new_str) && this.isPasswordExp(this.password))
      ) {
        this.isEnabledSubmit = true;
      } else {
        this.isEnabledSubmit = false;
      }
    },
    sms_code(new_str) {
      console.log(new_str + "--sms_code");
      if (this.isPhoneExp(this.username) && new_str) {
        this.isEnabledSubmit = true;
      } else {
        this.isEnabledSubmit = false;
      }
    },
    password(new_str) {
      console.log(this.isPasswordExp(new_str));
      if (this.isPhoneExp(this.username) && this.isPasswordExp(new_str)) {
        this.isEnabledSubmit2 = true;
      } else {
        this.isEnabledSubmit2 = false;
      }
    },
  },
  created(){
    window.addEventListener("resize", this.onResize);
  },
  beforeDestroy() {
		window.removeEventListener("resize", this.onResize);
    this.rive.pause();
	},
  mounted() {
    this.isAirSchool = config.isAirSchool;
    this.createLogin();
  },
  methods: {
    createLogin(){
      this.$nextTick(async () => {
        await setWH("canvas");
        this.rive = await createRive("canvas", "login.riv");
      })
    },
    onResize() {
      this.rive.resizeToCanvas();
		},
    getSms() {
      if (this.isEnabledGetSms) {
        loginApi.SendSMS({ username: this.username }).then((res) => {
          Toast("验证码已发送");
          this.reGetCountdown();
          this.isEnabledGetSms = false;
          console.log(res);
        });
      }
    },
    reGetCountdown() {
      if (this.timer) {
        clearInterval(this.timer);
      } else {
        this.timer = setInterval(() => {
          if (this.expireTime > 0) {
            this.expireTime--;
            this.codeCountdownStr = this.expireTime + "s";
          } else {
            this.codeCountdownStr = "重新获取";
            clearInterval(this.timer);
            this.isEnabledGetSms = true;
          }
        }, 1000);
      }
    },
    navActive(val) {
      this.nav_active = val;
    },
    affirmType() {
      this.affirm_type = !this.affirm_type;
    },
    submit() {
      if (this.affirm_type) {
        if (this.nav_active == "sms") {
          loginApi
            .Login({
              username: this.username,
              sms_code: sha256(`${this.sms_code}_eStarGo2019`),
              operation: "sms_login",
              sign_from: this.isAirSchool ? "airschool" : "higo",
            })
            .then((res) => {
              this.$storage.$setStroage("user_token", res.data.token);
              this.$storage.$setStroage("username", res.data.actual_name);
              this.$storage.$setStroage("userId", res.data.id);
              setTimeout(() => {
                this.$router.push({
                  path: "/",
                });
              }, 500);
              this.$store.commit("initWebSocket");
            });
        } else {
          loginApi
            .Login({
              sign_from: this.isAirSchool ? "airschool" : "higo",
              username: this.username,
              password: this.password,
              operation: this.isPhoneExp(this.username)
                ? "password"
                : "study_card",
            })
            .then((res) => {
              this.$storage.$setStroage("user_token", res.data.token);
              this.$storage.$setStroage("username", res.data.actual_name);
              this.$storage.$setStroage("userId", res.data.id);
              setTimeout(() => {
                this.$router.push({
                  path: "/",
                });
              }, 500);
              this.$store.commit("initWebSocket");
            });
        }
      } else {
        Toast("请勾选同意下方协议");
      }
    },
    dialogClose() {
      this.phoneReset = false;
    },
    settingDialogClose(val) {
      this.passwordReset = val;
    },
    settingPassword() {
      loginApi.ResetPasswordCode({ username: this.username }).then(() => {
        Toast("验证码已发送");
        this.phoneReset = false;
        this.passwordReset = true;
      });
    },
    goPrivateAgreement() {
      this.$router.push({
        path: "/privateAgreement",
      });
    },
    goUserAgreement() {
      this.$router.push({
        path: "/userAgreement",
      });
    },
    isPhoneExp(str) {
      let pattern = /^1([0-9]{10})$/;
      return pattern.test(str);
    },

    isNumber(str) {
      let pattern = /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9]{6,8}$/;
      return pattern.test(str);
    },

    isPasswordExp(str) {
      let pattern = /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9]{6,10}$/;
      return pattern.test(str);
    },
  },
};
</script>

<style lang="less" scoped>
.login {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  input::-webkit-input-placeholder {
    color: #bfc1c5;
    font-size: 40px;
  }
  input {
    border: 4px solid transparent!important;
  }
  input:focus {
    outline: 4px solid transparent;
    border: 4px solid #16C3FF!important;
  }

  .login_box {
    width: 1000px;
    height: 868px;
    padding: 24px;
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    box-shadow: rgba(0, 56, 79, .15) 0 10px 30px 0;
    border-radius: 90px;
    .logo {
      width: 369px;
      height: 88px;
      margin: 24px auto 36px;
    }
    .logo-old {
      width: 378px;
    }
    .nav_row {
      display: flex;
      width: 960px;
      .nav {
        width: 480px;
        height: 96px;
        color: #ffffff;
        font-size: 40px;
        text-align: center;
        line-height: 96px;
      }
      .nav_active {
        width: 480px;
        height: 96px;
        color: #00BDFF;
        background: #ffffff;
        border-radius: 48px 48px 0 0;
        font-size: 40px;
        text-align: center;
        line-height: 96px;
      }
    }
    .input_box {
      width: 960px;
      height: 584px;
      background: #ffffff;
      border-radius: 0 80px 80px 80px;
      overflow: hidden;
      padding: 0 56px;
      box-sizing: border-box;
      .input {
        width: 848px;
        height: 104px;
        background: #f6f8fb;
        border-radius: 56px;
        border: none;
        padding-left: 56px;
        box-sizing: border-box;
        font-size: 40px;
      }
      .input_top {
        margin: 48px auto 32px;
      }
      .input_item {
        position: relative;
      }
      .sms_row {
        display: flex;
        justify-content: space-between;
        position: relative;
        .input_icon {
          width: 56px;
          height: 56px;
          background: url("@/assets/login/闭眼.png") no-repeat;
          background-size: 100% 100%;
          position: absolute;
          right: 48px !important;
          top: 28px;
          cursor: pointer;
        }
        .not_icon {
          width: 56px;
          height: 56px;
          background: url("@/assets/login/睁眼.png") no-repeat;
          background-size: 100% 100%;
          position: absolute;
          right: 48px !important;
          top: 28px;
          cursor: pointer;
        }
        .input_sms {
          width: 560px;
          height: 104px;
          background: #f6f8fb;
          border-radius: 56px;
          border: none;
          padding-left: 56px;
          box-sizing: border-box;
          font-size: 40px;
          border: 4px solid #f6f8fb;
        }
        .sms_btn {
          width: 264px;
          height: 104px;
          background: #f6f8fb;
          border-radius: 56px;
          color: #bfc1c5;
          font-size: 40px;
          text-align: center;
          line-height: 104px;
          cursor: pointer;
        }
      }
      .submit {
        width: 848px;
        height: 104px;
        margin-top: 72px;
        font-size: 48px;
        color: #ffffff;
        opacity: 0.5;
        text-align: center;
        line-height: 104px;
        cursor: pointer;
        border-radius: 56px;
        box-shadow: rgba(0, 56, 79, .1) 0 4px 7px 0;
      }
    }
    .marginTop {
      margin-top: 16px !important;
    }

    .forget_pass {
      font-size: 34px;
      color: #00bdff;
      line-height: 40px;
      margin-top: 16px;
      margin-right: 16px;
      text-align: right;
      cursor: pointer;
    }
    .footer {
      display: flex;
      margin-top: 32px;
      justify-content: center;
      align-items: center;

      .pie {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        box-sizing: border-box;
        border: 4px solid #e1e1e1;
        margin-right: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .pieRight {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: url("@/assets/login/椭圆形.png") no-repeat;
          background-size: 100% 100%;
          box-shadow: #D4FAFF 0 0 3px 0;
        }
      }
      .footer_text {
        font-size: 34px;
        color: #999999;
        i {
          color: #00bdff;
          font-style: normal;
          cursor: pointer;
        }
      }
    }
  }
  .dialog_box {
    width: 100vw;
    height: 100vh;
    position: fixed;
    background: rgba(0, 0, 0, 0.6);
    z-index: 400;
    .dialog_title {
      text-align: center;
      font-size: 52px;
      color: #333333;
      margin: 80px 0 72px;
    }
    .phone_reset {
      width: 1080px;
      height: 648px;
      margin: auto;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;

      .input_phone {
        width: 864px;
        height: 104px;
        background: #f6f8fb;
        border-radius: 54px;
        border: none;
        padding-left: 48px;
        box-sizing: border-box;
        font-size: 36px;
      }
    }
    .get_sms {
      width: 864px;
      height: 104px;
      margin: 64px auto 0;
      font-size: 38px;
      color: #ffffff;
      opacity: 0.5;
      text-align: center;
      line-height: 104px;
      cursor: pointer;
    }
    .dialog_close {
      width: 96px;
      height: 96px;
      position: absolute;
      left: 80%;
      top: 22.8%;
      transform: translate(-30%, -9%);
      cursor: pointer;
    }
  }
  .close {
    width: 48px;
    height: 48px;
    display: inline-block;
    position: absolute;
    right: 56px;
    top: 28px;
    cursor: pointer;
  }
  .close-right {
    right: 136px;
  }
}
</style>
