<template>
  <!-- 设置密码弹窗 -->
  <div class="dialog_box">
    <div class="password_reset" :style="{
          'background-image': `url(${require('@/assets/login/忘记密码弹窗.png')})`,
          'background-size': '100% 100%'
        }">
      <div class="dialog_title jcyt600">设置密码</div>
      <div class="sms_row">
        <input
          type="text"
          onkeyup="value=value.replace(/[^\d]/g,'')"
          class="input_sms jcyt500"
          placeholder="请输入验证码"
          v-model="sms_code"
          maxlength="6"
          @input="changeHandle"
        />
        <div
          class="sms_btn jcyt500"
          :style="{'color':isEnabledGetSms?'#00BDFF':'#bfc1c5'}"
          @click="getSms"
        >{{ codeCountdownStr }}</div>
      </div>
      <div class="sms_row">
        <input
          :type="isSee?'text':'password'"
          class="pass_input jcyt500"
          v-model="password"
          placeholder="请输入新密码，不少于8位"
          @input="changeHandle"
          @change="passwordChange(twoPassword)"
        />
        <i :class="isSee?'not_icon':'input_icon'" @click="()=>{isSee=!isSee}"></i>
      </div>
      <div class="sms_row">
        <input
          :type="isSee2?'text':'password'"
          class="pass_input jcyt500"
          v-model="twoPassword"
          placeholder="请再次输入密码"
          @input="changeHandle"
          @change="passwordChange(password)"
        />
        <i :class="isSee2?'not_icon':'input_icon'" @click="()=>{isSee2=!isSee2}"></i>
      </div>
      <div class="pop_text jcyt400">
        <span v-if="password_type">两次密码输入不一致，请重新输入</span>
      </div>
      <div
        class="get_sms jcyt500"
        :style="{'opacity':isEnabledSubmit?'1':'0.5','background-image': `url(${require('@/assets/login/login_btn.png')})`,
          'background-size': '100% 100%'}"
        @click="settingLogin"
      >设置并登录</div>
    </div>
    <div class="dialog_close2" @click="dialogClose"  :style="{
          'background-image': `url(${require('@/assets/login/关闭按钮.png')})`,
          'background-size': '100% 100%'
        }"></div>
  </div>
</template>
<script>
import loginApi from "@/api/login";
import { sha256 } from "js-sha256";
export default {
  name: "resetPassword",
  props: {
    username: { default: "" }
  },
  data() {
    return {
      sms_code: "",
      password: "",
      twoPassword: "",
      isEnabledGetSms: false,
      codeCountdownStr: "获取验证码",
      expireTime: 60,
      timer: "",
      isEnabledSubmit: false,
      password_type: false,
      isSee: false,
      isSee2: false
    };
  },
  mounted() {
    console.log(this.username);
    this.reGetCountdown();
  },
  methods: {
    getSms() {
      if (this.isEnabledGetSms) {
        loginApi.ResetPasswordCode({ username: this.username }).then(() => {
          Toast("验证码已发送");
          this.phoneReset = false;
          this.passwordReset = true;
          this.reGetCountdown()
        });
      }
    },
    reGetCountdown() {
      if (this.timer) {
        clearInterval(this.timer);
      } else {
        this.timer = setInterval(() => {
          if (this.expireTime > 0) {
            this.expireTime--;
            console.log();
            this.codeCountdownStr = this.expireTime + "s";
          } else {
            this.codeCountdownStr = "重新获取";
            clearInterval(this.timer);
            this.isEnabledGetSms = true;
          }
        }, 1000);
      }
    },
    passwordChange(val) {
      if (val) {
        if (this.password != this.twoPassword) {
          this.password_type = true;
        } else {
          this.password_type = false;
        }
      }
    },
    changeHandle() {
      if (
        this.sms_code &&
        this.isPasswordExp(this.password) &&
        this.isPasswordExp(this.twoPassword)
      ) {
        this.isEnabledSubmit = true;
      } else {
        this.isEnabledSubmit = false;
      }
    },
    settingLogin() {
      var obj = {
        username: this.username,
        sms_code: sha256(`${this.sms_code}_eStarGo2019`),
        password: this.password
      };
      loginApi.ResetPassword(obj).then(res=>{
        this.$storage.$setStroage("user_token", res.data.token);
        this.$storage.$setStroage("userId", res.data.id);
        this.$storage.$setStroage("userPhone", res.data.username);
        this.$storage.$setStroage("username", res.data.actual_name);
        setTimeout(() => {
                this.$router.push({
                  path: "/"
                });
              }, 500);
      });
    },
    dialogClose() {
      this.$emit("close", false);
    },
    isPasswordExp(str) {
      let pattern = /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9]{6,10}$/;
      return pattern.test(str);
    }
  }
};
</script>
<style lang="less" scoped>
.dialog_box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  background: rgba(0, 0, 0, 0.6);
  input::-webkit-input-placeholder {
    color: #bfc1c5;
    font-size: 40px;
  }
  .dialog_title {
    text-align: center;
    font-size: 52px;
    color: #333333;
    margin: 90px 0 56px;
  }
  .get_sms {
    width: 864px;
    height: 104px;
    margin: 22px auto 0;
    font-size: 38px;
    color: #ffffff;
    opacity: 0.5;
    text-align: center;
    line-height: 104px;
    cursor: pointer;
  }
  .password_reset {
    width: 1080px;
    height: 874px;
    margin: auto;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    padding: 0 108px;
    box-sizing: border-box;
    .sms_row {
      display: flex;
      justify-content: space-between;
      position: relative;
      .input_icon {
        width: 56px;
        height: 56px;
        background: url("@/assets/login/闭眼.png") no-repeat;
        background-size: 100% 100%;
        position: absolute;
        right: 48px !important;
        top: 60px;
        cursor: pointer;
      }
      .not_icon {
        width: 56px;
        height: 56px;
        background: url("@/assets/login/睁眼.png") no-repeat;
        background-size: 100% 100%;
        position: absolute;
        right: 48px !important;
        top: 60px;
        cursor: pointer;
      }
      .input_sms {
        width: 552px;
        height: 104px;
        background: #f6f8fb;
        border-radius: 54px;
        border: none;
        padding-left: 48px;
        box-sizing: border-box;
        font-size: 36px;
      }

      .sms_btn {
        width: 288px;
        height: 104px;
        background: #f6f8fb;
        border-radius: 54px;
        color: #bfc1c5;
        font-size: 36px;
        text-align: center;
        line-height: 104px;
      }
    }
    .pass_input {
      width: 864px;
      height: 104px;
      line-height: 104px;
      background: #f6f8fb;
      border-radius: 54px;
      margin-top: 32px;
      border: none;
      padding-left: 48px;
      box-sizing: border-box;
      font-size: 36px;
    }
    .pop_text {
      font-size: 26px;
      color: red;
      margin: 20px 0 0 30px;
      height: 40px;
    }
  }
  .dialog_close2 {
    width: 96px;
    height: 96px;
    position: absolute;
    left: 80%;
    top: 19%;
    transform: translate(-40%, -80%);
    cursor: pointer;
  }
}
</style>