<template>
  <div class="join" :style="{'background-image': `url(${require('@/assets/soloFriend/好友约战背景图.png')})`}">
    <img src="@/assets/index/back.png" class="back" @click="back"/>
    <div class="join-wrap">
      <img src="@/assets/soloFriend/棋子pad.png" class="piece"/>
      <div class="content flex-column items-center">
        <p class="title jcyt600">获得好友的房间号，进入对局</p>
        <input type="number" class="room-num jcyt500" placeholder="请输入房间号" v-model="roomNum"/>
        <div class="btn jcyt600" :style="{'background-image': `url(${require('@/assets/soloFriend/创建对局背景.png')})`}" @click="join">加入对局</div>
      </div>
    </div>
    <tips
      v-if="tipsVisible"
      :isOpen="tipsVisible"
      :msg="`是否加入${roomStatus['own_student_name']}的房间`"
      cancelBtn="取消"
      reallyBtn="确定"
      @cancel="cancel"
      @really="confirm"
    >
    </tips>
    <!-- <div class="modal" v-if="tipsVisible"></div>
    <div class="tips flex-column items-center justify-between" v-if="tipsVisible">
      <p>是否加入{{roomStatus['own_student_name']}}的房间</p>
      <div class="btn flex-row justify-around">
        <div class="btn-1" @click="cancel">
          <span>取消</span>
        </div>
        <div class="btn-1 btn-2" @click="confirm">
          <span>确定</span>
        </div>
      </div>
    </div> -->
  </div>
</template>
<script>
import { Toast } from 'mint-ui';
import soloApi from "@/api/solofriends";
import tips from "@/components/tips/tips";
export default {
  data(){
    return {
      roomNum: "",
      tipsVisible: false,
      roomStatus: {}
    }
  },
  components: {
    tips
  },
  methods: {
    back(){
      this.$router.push("/rule");
    },
    async join(){
      if (this.roomNum != "") {
        try {
          var params = {"solo_room_numb": this.roomNum};
          let res = await soloApi.roomStatusApi(params);
          if(typeof res.error_code == 'undefined'){
            this.roomStatus = res.data;
            if (this.roomStatus != {}) {
              this.tipsVisible = true;
            }
          }else {
            Toast(res.error_code);
          }
        }catch(e) {
          Toast(e);
        }
      }
    },
    cancel(){
      this.tipsVisible = false;
    },
    async confirm(){
      try {
        __bl.sum("好友约战加入对局");
        this.tipsVisible = false;
        let res = await soloApi.joinRoomApi({"solo_room_numb": this.roomNum.toString()});
        this.$router.push({
          path: "/rival",
          query: {
            'room_id': res.data["room_id"]
          }
        });
      }catch(e) {
        this.tipsVisible = false;
        Toast(e);
      }
    }
  }
}
</script>
<style lang="less" scoped>
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.join {
  background-size: cover;
  width: 100vw;
  height: 100vh;
  .join-wrap {
    width: 1264px;
    height: 704px;
    margin: 32px auto 0;
    background-image: linear-gradient(to bottom, #FCD24C, #F6BF26, #F9C93D);
    box-shadow: 0 8px 15px 0 rgba(168, 93, 27, 0.2), 0 8px 15px 0 #FFEBAF inset, 0 -8px 15px 0 #E39D1E inset;
    background-size: 100% 100%;
    border-radius: 37.5px;
    position: relative;
    padding-top: 16px;
    box-sizing: border-box;
  }
  .piece {
    position: absolute;
    z-index: 1;
    right: 0;
    top: 0;
    width: 292.5px;
  }
  .back {
    width: 120px;
    height: 120px;
    margin-left: 56px;
    margin-top: 40px;
    cursor: pointer;
  }
  .content {
    height: 544px;
    width: 1200px;
    background:#FEF8E8;
    border: 16px solid #FFFCF5;
    border-radius: 50px;
    box-shadow: 0 0 15px 0 rgba(255,158,0,.5);
    position: relative;
    z-index: 10;
    margin: 96px auto 0;
  }
  .title {
    font-size: 40px;
    line-height: 28px;
    color: #A85D1B;
    margin-top: 112px;
    text-align: center;
  }
  .room-num {
    width: 560px;
    height: 100px;
    font-size: 40px;
    color: #999;
    background: rgba( 255,255,255,1);
    padding-left: 48px;
    box-sizing: border-box;
    border: 2px solid #EAD7C6;
    border-radius: 50px;
    margin-top: 56px;
    outline: none;
  }
  .btn {
    width: 432px;
    height: 96px;
    color: #fff;
    font-size: 32px;
    box-sizing: border-box;
    background-size: 100% 100%;
    text-align: center;
    line-height: 88px;
    margin-top: 56px;
    cursor: pointer;
  }
}
input::-webkit-input-placeholder {
/* WebKit browsers，webkit内核浏览器 */
  color: rgba(168,93,27,.25);
  font-size: 40px;
}
input:-moz-placeholder {
/* Mozilla Firefox 4 to 18 */
  color: rgba(168,93,27,.25);
  font-size: 40px;
}
input::-moz-placeholder {
/* Mozilla Firefox 19+ */
  color: rgba(168,93,27,.25);
  font-size: 40px;
}
input:-ms-input-placeholder {
/* Internet Explorer 10+ */
  color: rgba(168,93,27,.25);
  font-size: 40px;
}
/* Chrome浏览器 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
/* Firefox浏览器 */
input[type="number"]{
    -moz-appearance: textfield;
}
.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}

.tips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 12;
  width: 820px;
  height: 480px;
  border-radius: 50px;
  box-shadow: 0 0 20px 0 rgba(48, 48, 48, 0.4);
  background-color: #fff;
  p {
    color: #333;
    font-size: 48px;
    line-height: 60px;
    width: 600px;
    text-align: center;
    word-break: break-all;
    font-weight: bold;
    padding: 72px 0 50px 0;
  }
  .btn {
    .btn-1 {
      border-radius: 60px;
      border: 4px solid #00BDFF;
      width: 312px;
      height: 112px;
      cursor: pointer;
      font-size: 40px;
      text-align: center;
      line-height: 112px;
      color:#00BDFF;
      margin-bottom: 60px;
    }
    .btn-2 {
      background: #31BFFF;
      color: #fff;
      margin-left: 40px;
    }
  }
}
</style>