<template>
  <div
    class="solo-rule"
    :style="{
      'background-image': `url(${require('@/assets/soloFriend/好友约战背景图.png')})`
    }"
  >
    <div class="flex-row justify-between items-center">
      <img src="@/assets/index/back.png" class="back" @click="back" />
      <div class="select flex-row" @click="join">
        <div class="select-left">
          <div class="select_icon" :style="{
            'background-image': `url(${require('@/assets/soloFriend/加入房间.png')})`,
            'background-size': 'cover'
          }" ></div>
        </div>
        <span class="jcyt600 label">加入对局</span>
      </div>
    </div>
    <div
      class="content"
      :style="{
        'background-image': `url(${
          gameTypeIndex == 0
            ? require('@/assets/soloFriend/好友约战吃子背景.png')
            : require('@/assets/soloFriend/好友约战围地背景.png')
        })`
      }"
    >
      <div class="title flex-row">
        <div class="title-1 jcyt600" @click="changeTab(0)">吃子</div>
        <div class="title-1 jcyt600" @click="changeTab(1)">围地</div>
      </div>
      <div class="form" v-if="gameType[gameTypeIndex]">
        <div class="form-item flex-row justify-between items-center">
          <span class="form-item-label jcyt500">棋盘路数</span>
          <div
            class="form-item-select flex-row justify-between items-center"
            @click="gameContentChange('board_size')"
          >
            <span class="jcyt500">{{
              gameType[gameTypeIndex].board_size[boardSizeIndex].name
            }}</span>
            <img src="@/assets/soloFriend/箭头.png" />
          </div>
        </div>
        <div class="line flex-row">
          <img src="@/assets/soloFriend/分割线.png" />
        </div>
        <div class="form-item flex-row justify-between items-center">
          <span class="form-item-label jcyt500">每方时间</span>
          <div
            class="form-item-select flex-row justify-between items-center"
            @click="gameContentChange('total_time')"
          >
            <span class="jcyt500">{{
              gameType[gameTypeIndex].total_time[totalTimeIndex].name
            }}</span>
            <img src="@/assets/soloFriend/箭头.png" />
          </div>
        </div>
        <div class="line flex-row">
          <img src="@/assets/soloFriend/分割线.png" />
        </div>
        <div class="form-item flex-row justify-between items-center">
          <span class="form-item-label jcyt500">{{
            gameTypeIndex == 0 ? "对局规则" : "读秒次数"
          }}</span>
          <div
            class="form-item-select flex-row justify-between items-center"
            @click="
              gameTypeIndex == 0
                ? gameContentChange('win_capture')
                : gameContentChange('frequency')
            "
          >
            <span class="jcyt500">
              {{
                gameTypeIndex == 0
                  ? gameType[gameTypeIndex].win_capture[winCaptureIndex].name
                  : gameType[gameTypeIndex].frequency[frequencyIndex].name
              }}</span
            >
            <img src="@/assets/soloFriend/箭头.png" />
          </div>
        </div>
        <div class="line flex-row">
          <img src="@/assets/soloFriend/分割线.png" />
        </div>
      </div>
      <div
        class="create"
        :style="{
          'background-image': `url(${require('@/assets/soloFriend/创建对局背景.png')})`
        }"
        @click="submitCreate"
      >
        <span class="jcyt600">创建对局</span>
      </div>
    </div>
    <div
      class="modal"
      @click="cancel"
      v-if="pickerVisible"
    ></div> <!--gameVisible-->
    <div class="picker-wrap" v-if="pickerVisible">
      <div class="title flex-row justify-between">
        <span class="jcyt500" @click="cancel">取消</span>
        <span class="jcyt500 sure" @click="confirm">确认</span>
      </div>
      <mt-picker
        :slots="slots"
        @change="onValuesChange"
        class="mt-picker-wrap"
        :itemHeight="80 / 1920 * w"
      ></mt-picker>
    </div>

    <tips
      v-if="gameVisible"
      :isOpen="gameVisible"
      :msg="toastMessage"
      cancelBtn="结束对局"
      reallyBtn="继续对局"
      @cancel="overGame"
      @really="continueGame"
    >
    </tips>

    <!-- <div
      class="tips flex-column items-center justify-between"
      v-if="gameVisible"
    >
      <p>{{ toastMessage }}</p>
      <div class="btn flex-row justify-around">
        <div class="btn-1" @click="overGame">
          <span>结束对局</span>
        </div>
        <div class="btn-1 btn-2" @click="continueGame">
          <span>继续对局</span>
        </div>
      </div>
    </div> -->
  </div>
</template>
<script>
import gameApi from "@/api/game";
import soloApi from "@/api/solofriends";
import { Toast } from "mint-ui";
import storage from "@/public/storage.js";
import tips from "@/components/tips/tips";

export default {
  data() {
    return {
      gameTypeIndex: 0,
      boardSizeIndex: 0,
      totalTimeIndex: 0,
      winCaptureIndex: 0,
      frequencyIndex: 0,
      slots: [
        {
          flex: 1,
          values: [],
          className: "slot1",
          textAlign: "center",
          defaultIndex: 0
        }
      ],
      pickerVisible: false,
      enabled: true,
      gameVisible: false,
      toastMessage: "",
      creatRoomListStatus: {},
      gameType: [],
      type: "",
      index: "",
      columnArr: [],
      w: 1
    };
  },
  components: {
    tips
  },
  mounted() {
    this.checkGameOpen();
    this.getFriendFightRule();
    this.creatRoomList();
    this.user_id = storage.$getStroage("userId");
    this.w = document.body.clientWidth;
  },
  methods: {
    onValuesChange(picker, values) {
      this.index = this.columnArr.indexOf(values[0]);
    },
    back() {
      this.$router.push({
        path: "/",
      });
    },
    cancel() {
      this.pickerVisible = false;
    },
    confirm() {
      this.pickerVisible = false;
      if (this.type == "board_size") {
        this.boardSizeIndex = this.index;
      } else if (this.type == "total_time") {
        this.totalTimeIndex = this.index;
      } else if (this.type == "win_capture") {
        this.winCaptureIndex = this.index;
      } else if (this.type == "frequency") {
        this.frequencyIndex = this.index;
      }
    },
    changeTab(index) {
      this.gameTypeIndex = index;
      this.boardSizeIndex = 0;
      this.totalTimeIndex = 0;
      this.winCaptureIndex = 0;
      this.frequencyIndex = 0;
    },
    gameContentChange(type) {
      this.type = type;
      this.pickerVisible = true;
      this.columnArr = this.gameType[this.gameTypeIndex][type].map(
        (item) => item.name
      );
      let obj = JSON.parse(JSON.stringify(this.slots[0].values));
      obj["values"] = this.columnArr;
      obj["defaultIndex"] =
        this.type == "board_size"
          ? this.boardSizeIndex
          : this.type == "total_time"
          ? this.totalTimeIndex
          : this.type == "win_capture"
          ? this.winCaptureIndex
          : this.type == "frequency"
          ? this.frequencyIndex
          : 0;
      this.$set(this.slots, 0, obj);
    },
    overGame() {
      this.gameClose();
      this.gameVisible = false;
    },
    continueGame() {
      this.gameVisible = false;
      if (this.creatRoomListStatus["has_not_end_room"]) {
        this.$router.push({
          path: "/rival",
          query: {
            room_id: this.creatRoomListStatus["not_end_room_id"]
          }
        });
      } else if (this.creatRoomListStatus["has_not_end_game"]) {
        this.$router.push({
          path: "/soloGames",
          query: {
            game_id: this.creatRoomListStatus["not_end_game_id"],
          }
        });
        // Routes.goBack(
        //     context,
        //     GameView(
        //       gameId:
        //           creatRoomListStatus['not_end_game_id']
        //               .toString(),
        //       from: 'solo',
        //     ));
      }
    },
    async checkGameOpen() {
      let res = await gameApi.CheckGameOpenApi();
      this.enabled = res.data["enabled"] == null ? true : res.data["enabled"];
    },
    async getFriendFightRule() {
      let res = await soloApi.friendFightRuleList();
      this.gameType =
        res.data[1]["game_type"] == "capture"
          ? [res.data[1], res.data[0]]
          : res.data == null
          ? []
          : res.data;
    },
    async creatRoomList() {
      try {
        let res = await soloApi.creatRoomListApi();
        this.creatRoomListStatus = res.data;
        console.log(this.creatRoomListStatus)
        this.send_data = {
          game_id: this.creatRoomListStatus["not_end_game_id"],
          user_hash: `${this.user_id}:1`,
          user_id: this.user_id,
        };
        if (res.data["has_not_end_game"] == true) {
          this.toastMessage = "你有正在进行中的对弈，" + "\n" + "需要继续比赛";
          this.gameVisible = true;
        } else if (res.data["has_not_end_room"] == true) {
          this.toastMessage =
            "你有正在进行中的房间，" + "\n" + "需要继续比赛吗？";
          this.gameVisible = true;
        }
      } catch (e) {
        Toast(e);
      }
    },
    async gameClose() {
      if (this.creatRoomListStatus["has_not_end_room"] == true) {
        // 结束/关闭房间
        await soloApi.closeRoomApi({
          room_id: this.creatRoomListStatus["not_end_room_id"]
        });
      } else if (this.creatRoomListStatus["has_not_end_game"] == true) {
        //结束对弈/对局
        await gameApi.Resign(this.send_data);
      }
    },
    async submitCreate() {
      if (this.enabled == true) {
        try {
          __bl.sum("好友约战创建对局");
          let res = await soloApi.creatRoomApi({
            game_type: this.gameType[this.gameTypeIndex]["game_type"],
            total_time:
              this.gameType[this.gameTypeIndex]["total_time"][
                this.totalTimeIndex
              ]["total_time"],
            win_capture:
              this.gameType[this.gameTypeIndex]["game_type"] == "capture"
                ? this.gameType[this.gameTypeIndex]["win_capture"][
                    this.winCaptureIndex
                  ]["win_capture"]
                : 0,
            byoyomi:
              this.gameType[this.gameTypeIndex]["game_type"] == "capture"
                ? 0
                : this.gameType[this.gameTypeIndex]["frequency"][
                    this.frequencyIndex
                  ]["byoyomi"],
            byoyomi_time:
              this.gameType[this.gameTypeIndex]["game_type"] == "capture"
                ? 0
                : this.gameType[this.gameTypeIndex]["frequency"][
                    this.frequencyIndex
                  ]["byoyomi_time"],
            board_size:
              this.gameType[this.gameTypeIndex]["board_size"][
                this.boardSizeIndex
              ]["board_size"]
          });

          this.$router.push({
            path: "/rival",
            query: {
              room_id: res.data["room_id"].toString()
            }
          });
        } catch (e) {
          Toast(e);
        }
      } else {
        Toast("对弈服务关闭");
      }
    },
    join() {
      this.$router.push({
        path: "/join"
      });
    }
  }
};
</script>
<style lang="less" scoped>
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.solo-rule {
  height: 100vh;
  width: 100vw;
  background-size: cover;
  .back {
    width: 120px;
    height: 120px;
    margin-left: 56px;
    margin-top: 40px;
    cursor: pointer;
  }
  .select {
    height: 80px;
    width: 280px;
    margin-left: 40px;
    background-image: linear-gradient(to bottom, #FFFFFF, #DAF4FF);
    border-radius: 44px;
    box-shadow: #8BB6E1 0 -3px 3px 0 inset, 0 4px 8px 0 rgba(0, 33, 135, .5);
    box-sizing: border-box;
    text-align: center;
    font-size: 36px;
    cursor: pointer;
    display: flex;
    position: absolute;
    right: 48px;
    top: 60px;
    .label {
      margin-left: 22px;
      line-height: 80px;
      color: #294584;
      padding-right: 30px;
    }
    .select_icon {
      width: 49px;
      height: 52px;
      margin-top: 18px;
      margin-left: 28px;
    }
    .select-left {
      width: 84px;
      height: 80px;
      border-radius: 44px 0 0 44px;
      background: #3168E7;
      position: relative;
      box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      &::after {
        content:"";
        width: 0; height: 0;
        border-color: transparent #3168E7; /*上下颜色 左右颜色*/
        border-width: 0 0 80px 20px;
        border-style: solid;
        position: absolute;
        top: 0;
        left: 84px;
        box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      }
    }
  }
  .content {
    background-size: cover;
    width: 1264px;
    height: 720px;
    margin: 19px auto;
    .title-1 {
      width: 621px;
      height: 100px;
      margin-top: 19px;
      font-size: 40px;
      color: #8C3202;
      text-align: center;
      line-height: 100px;
    }
  }
}
.form {
  padding-top: 48px;
}
.form-item {
  width: 880px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 23.5px;
  .form-item-label {
    font-size: 32px;
    color: #333333;
  }
  .form-item-select {
    width: 728px;
    height: 80px;
    cursor: pointer;
    padding: 0 36px 0 28px;
    box-sizing: border-box;
    border: 1.5px solid #ead7c6;
    background: #fff;
    border-radius: 50px;
    span {
      color: #a85d1b;
      font-size: 32px;
    }
    img {
      width: 40px;
      height: 40px;
    }
  }
}
.line {
  width: 880px;
  height: 4px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 32px;
  img {
    width: 880px;
    height: 1.5px;
  }
}
.create {
  background-size: cover;
  width: 416px;
  height: 90px;
  text-align: center;
  margin: 0 auto;
  border: none;
  background-color: transparent;
  box-sizing: border-box;
  line-height: 90px;
  cursor: pointer;
  span {
    color: #fff;
    font-size: 32px;
  }
}
.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}
.picker-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 416px;
  z-index: 10;
  background: #fff;
  border-radius: 32px 32px 0 0;
  overflow: hidden;
  .title {
    color: #666666;
    font-size: 32px;
    padding: 45px 56px 43px;
    cursor: pointer;
    z-index: 1;
    position: relative;
    background-color: #fff;
    .sure {
      color: #00BAFF;
    }
  }
  .mt-picker-wrap {
    margin-top: 100px;
    overflow: visible;
  }
  ::v-deep .picker-slot {
    overflow: visible;
    font-size:40px;
  }
  ::v-deep .picker-center-highlight {
    background-color: #EEEEF0;
    border-radius: 14.04px;
    margin: 0 calc((100vw - 640px) / 2);
    width: 640px;
    border: none;
    z-index: -1;
  }
  ::v-deep .picker-item {
    font-size: 38px;
    font-family: jcyt500w;
  }
  ::v-deep .picker-center-highlight:before,
  ::v-deep .picker-center-highlight:after {
    height: 0px;
  }
}
.tips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 12;
  width: 820px;
  height: 450px;
  border-radius: 50px;
  box-shadow: 0 0 20px 0 rgba(48, 48, 48, 0.4);
  background-color: #fff;
  p {
    color: #333;
    font-size: 48px;
    line-height: 60px;
    width: 600px;
    text-align: center;
    word-break: break-all;
    font-weight: bold;
    padding: 72px 0 50px 0;
  }
  .btn {
    .btn-1 {
      border-radius: 60px;
      border: 4px solid #00bdff;
      width: 312px;
      height: 112px;
      cursor: pointer;
      font-size: 40px;
      text-align: center;
      line-height: 112px;
      color: #00bdff;
      margin-bottom: 60px;
    }
    .btn-2 {
      background: #31bfff;
      color: #fff;
      margin-left: 40px;
    }
  }
}
</style>
