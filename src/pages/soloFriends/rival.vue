<template>
  <div class="rival" :style="{'background-image': `url(${require('@/assets/soloFriend/好友约战背景图.png')})`}">
    <template v-if="roomStatus['left_time'] != null">
      <div class="flex-row items-center justify-center header">
        <img src="@/assets/index/back.png" class="back" @click="back"/>
        <div class="countdown">
          <span class="jcyt500">倒计时：{{ countDown }}</span>
        </div>
      </div>
      <div class="content">
        <img src="@/assets/soloFriend/棋子pad.png" class="piece"/>
        <div class="room-num flex-row items-center justify-between">
          <span class="jcyt500">房间号：{{roomStatus['room_numb']}}</span>
          <div class="copy jcyt600" v-clipboard="roomStatus['room_numb']" @success="copy('success')" @error="copy('error')">复制</div>
        </div>
        <div class="wrap flex-row">
          <div class="left-content">
            <div class="vs flex-row justify-around" :style="{'background-image': `url(${require('@/assets/soloFriend/vs.png')})`}">
              <div class="flex-column justify-between info">
                <img :src="roomStatus['own_student_avatar']" />
                <span class="jcyt500">{{roomStatus['own_student_name']}}</span>
              </div>
              <div class="flex-column justify-between info">
                <img :src="roomStatus['guest_student_avatar'] ==''?require('@/assets/soloFriend/默认头像.png') : roomStatus['guest_student_avatar']" />
                <span class="jcyt500">{{roomStatus['guest_student_name'] == "" ? '等待对手' : roomStatus['guest_student_name']}}</span>
              </div>
            </div>
            <div class="line" :style="{'background-image': `url(${require('@/assets/soloFriend/虚线.png')})`}"></div>
            <div class="btn flex-row items-center justify-center">
              <div class="btn-1" :style="{'background-image': `url(${require('@/assets/soloFriend/取消对局.png')})`}" @click="cancelGame"></div>
              <div class="btn-1" :style="{'background-image': `url(${require('@/assets/soloFriend/开始对局.png')})`}" @click="begin" v-if='userId == roomStatus["guest_student_id"]'></div>
            </div>
          </div>
          <div class="rule flex-column">
            <div class="rule-title jcyt600">{{`${roomStatus['board_size']}路${roomStatus['game_type'] == "territory" ? "围地" : "吃子"}`}}</div>
            <div class="flex-row form-items">
              <span class="item-label jcyt400">每方时间</span>
              <span class="item-value jcyt500">{{ parseInt(roomStatus['total_time'] / 60) + '分钟'}}</span>
            </div>
            <div class="flex-row form-items">
              <span class="item-label jcyt400">读秒次数</span>
              <span class="item-value jcyt500">{{ roomStatus['byoyomi'] == 0 && roomStatus['byoyomi_time'] == 0 ? '无读秒' : roomStatus['byoyomi'] + '次' + roomStatus['byoyomi_time'] + '秒'}}</span>
            </div>
            <div class="flex-row form-items">
              <span class="item-label jcyt400">对局规则</span>
              <span class="item-value jcyt500">{{roomStatus['game_type'] === 'territory' ? '黑贴7.5目' : '吃' + roomStatus['win_capture'] + '子获胜'}}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else></template>

    <tips
      v-if="cancelVisible"
      :isOpen="cancelVisible"
      msg="是否解散房间？"
      cancelBtn="取消"
      reallyBtn="确认"
      @cancel="cancelRoom"
      @really="confirmRoom"
    >
    </tips>
    <tips
      v-if="tipsVisible"
      :isOpen="tipsVisible"
      :msg="toastMessage"
      :hasReally="false"
      cancelBtn="确定"
      @cancel="tipsConfirm"
    >
    </tips>

    <!-- <div class="modal" v-if="cancelVisible || tipsVisible"></div> -->
    <!-- <div class="tips flex-column items-center justify-between" v-if="cancelVisible">
      <p>是否解散房间？</p>
      <div class="btn flex-row justify-around">
        <div class="btn-1" @click="confirmRoom">
          <span>确认</span>
        </div>
        <div class="btn-1 btn-2" @click="cancelRoom">
          <span>取消</span>
        </div>
      </div>
    </div> -->
    <!-- <div class="tips tips-confirm flex-column items-center justify-between" v-if="tipsVisible">
      <p>{{toastMessage}}</p>
      <div class="btn flex-row justify-around">
        <div class="btn-1 btn-2" @click="tipsConfirm">
          <span>确定</span>
        </div>
      </div>
    </div> -->
  </div>
</template>
<script>
import soloApi from "@/api/solofriends";
import { Toast } from 'mint-ui';
import tips from "@/components/tips/tips";

export default {
  data(){
    return {
      userId: "",
      room_id: "",
      from: "",
      toastMessage: "",
      cancelVisible: false,
      tipsVisible: false,
      roomStatus: {},
      timer: null,
      isStatus: false,
      countDown: "",
      isEnabled: true
    }
  },
  components: {
    tips
  },
  mounted(){
    this.room_id = this.$route.query.room_id;
    this.from = this.$route.query.from;
    this.userId = this.$storage.$getStroage("userId");
    this.roomNum = this.$route.query.roomNum;
    this.getRoomStatus();
  },
  methods: {
    copy(type){
      Toast(type == 'success' ? "复制成功" : "复制失败");
    },
    tipsConfirm(){
      this.$router.go(-1);
    },
    async back(){
      this.cancelTimer();
      try {
        await soloApi.closeRoomApi({'room_id': this.room_id})
        this.$router.go(-1);
      }catch(e) {
        this.$router.go(-1);
      };
    },
    async getRoomStatus(){
      if (this.from == 'share') {
        let res = await soloApi.roomStatusApi();
        if (res.data['guest_student_name'] == '' ||
            res.data['guest_student_name'] == null ||
            res.data['guest_student_id'] == int.parse(this.userId)) {
          await soloApi.joinRoomApi({"solo_room_numb": (this.roomNum).toString()});
          this.getData();
        } else if (res.data['own_student_id'] == int.parse(userId)) {
          this.getData();

        } else {
          this.toastMessage = "该房间已有其他人进入";
          this.tipsVisible = true;
          this.cancelTimer();
        }
      }else {
        this.getData();
      }
    },
    getTime(){
      var s;
      if (this.roomStatus['left_time'] == '') {
        s = 0;
      }
      var h;
      h = parseInt((this.roomStatus['left_time'] / 60));
      s = (this.roomStatus['left_time'] % 60);
      h = h.toString().length == 1 ? "0" + h.toString() : h;
      s = s.toString().length == 1 ? "0" + s.toString() : s;
      return h.toString() + ":" + s.toString();
    },
    async getData(){
      try {
        this.timer = setTimeout( () => {
          soloApi.roomStatusApi({'room_id': this.room_id}).then(res=> {
            this.roomStatus = res.data;
            this.countDown = this.getTime();
            this.isStatus = true;
            if (this.roomStatus['is_start']) {
              this.cancelTimer();
              this.$router.push({
                path: "/soloGames",
                query: {
                  game_id: this.roomStatus['game_id'],
                  from: 'solo',
                  from_url: 'rule',
                }
              });
              // Routes.goBack(context,
              //     GameView(from: 'solo', gameId: roomStatus['game_id'].toString()));
            }
            if (this.roomStatus['guest_close'] == true && this.roomStatus['own_close'] == true) {
              this.toastMessage = "房间超时";
              this.tipsVisible = true;
              this.cancelTimer();
              return;
            } else if (this.roomStatus['guest_close'] == true && this.roomStatus['own_close'] == false) {
              this.toastMessage = "客人关闭房间";
              this.tipsVisible = true;
              this.cancelTimer();
              return;
            } else if (this.roomStatus['guest_close'] == false && this.roomStatus['own_close'] == true) {
              this.toastMessage = "房主关闭房间";
              this.tipsVisible = true;
              this.cancelTimer();
              return;
            }
            this.getData();
          });
        }, 1000);
      }catch(e) {
        if(e.indexOf(ERR_NETWORK_CHANGED)> -1) {
          this.getData();
        }
      }
    },
    cancelTimer(){
      clearTimeout(this.timer);
      this.timer = null;
    },
    async confirmRoom(){
      __bl.sum("好友约战取消对局");
      this.cancelVisible = false;
      await soloApi.closeRoomApi({'room_id': this.room_id})
    },
    cancelRoom(){
      this.cancelVisible = false;
    },
    cancelGame(){
      this.cancelVisible = true;
    },
    async begin(){
      if (this.isEnabled == true && this.roomStatus['guest_student_name'] != "") {
        __bl.sum("好友约战开始对局");
        this.isEnabled = false;
        await soloApi.startGameApi({ 'room_id': this.room_id });
      }
    },
  },
  beforeDestroy(){
    this.cancelTimer();
  }
}
</script>
<style lang="less" scoped>
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.justify-around {
  justify-content: space-around;
}
.rival {
  width: 100vw;
  height: 100vh;
  background-size: cover;
  .header{
    padding-top: 56px;
  }
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    left: 56px;
    top: 40px;
    cursor: pointer;
  }
  .countdown {
    width: 360px;
    height: 88px;
    // margin-top: 120px;
    // margin-bottom: 96px;
    background: rgba(0, 0, 0, 0.25);
    border-radius: 44px;
    text-align: center;
    line-height: 88px;
    color: #fff;
    font-size: 40px;
    margin: 0 auto;
  }
  .content {
    width: 1264px;
    height: 704px;
    margin: 48px auto 0;
    background-image: linear-gradient(to bottom, #FCD24C, #F6BF26, #F9C93D);
    box-shadow: 0 8px 15px 0 rgba(168, 93, 27, 0.2), 0 8px 15px 0 #FFEBAF inset, 0 -8px 15px 0 #E39D1E inset;
    background-size: 100% 100%;
    border-radius: 37.5px;
    position: relative;
    padding-top: 16px;
    box-sizing: border-box;
  }
  .piece {
    position: absolute;
    z-index: 1;
    right: 0;
    width: 292.5px;
  }
  .room-num {
    height: 80px;
    margin-left: 32px;
    width: 424px;
    padding-left: 36px;
    padding-right: 12px;
    background: #FEF8E8;
    border-radius: 40px;
    box-sizing: border-box;
    span {
      color: #8C3202;
      font-size: 32px;
    }
    .copy {
      width: 104px;
      height: 56px;
      background: #FFE4C0;
      border-radius: 40px;
      color: #FD5722;
      font-size: 28px;
      text-align: center;
      line-height: 56px;
      cursor: pointer;
    }
  }
  .wrap {
    width: 1232px;
    height: 576px;
    background: #FFFCF5;
    border-radius: 37.5px;
    box-shadow:0 0 15px 0 rgba(255,158,0,.5);
    margin: 0 auto;
    position: relative;
    z-index: 10;
    margin-top: 16px;
    padding-top: 16px;
    box-sizing: border-box;
  }
  .left-content {
    width: 819px;
    height: 544px;
    margin-left: 16px;
    background: #FEF8E8;
    border-radius: 30px;
    padding-top: 64px;
    box-sizing: border-box;

  }
  .vs {
    width: 682px;
    height: 260px;
    background-size: cover;
    margin: 0 auto;
    .info {
      width: 160px;
      height: 176px;
      margin-top: 37px;
      img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        margin: 0 20px;
      }
      span {
        font-size: 32px;
        width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #fff;
        text-align: center;
        display: inline-block;
        word-break: keep-all;
        line-height: 40px;
      }
    }
  }
  .line {
    width: 672px;
    height: 2px;
    background-size: cover;
    margin: 35.5px auto 0;
  }
  .btn {
    margin-top: 55.5px;
  }
  .btn-1 {
    width: 280px;
    background-size: cover;
    height: 96px;
    cursor: pointer;
  }
  .btn-2 {
    margin-left: 20px;
    cursor: pointer;
  }
  .rule {
    height: 540px;
    width: 369px;
    background-color: #FFEFC3;
    border-radius: 30px;
    margin-left: 16px;
  }
  .rule-title {
    width: 240px;
    height: 64px;
    background: #FFE59C;
    border-bottom-left-radius: 22.5px;
    border-bottom-right-radius: 22.5px;
    color: #FD5722;
    font-size: 32px;
    text-align: center;
    margin: 0 auto;
    line-height: 64px;
  }
}
.form-items {
  padding-left: 35px;
  padding-top: 48px;
  .item-label {
    margin-right: 32px;
    color: #A85D1B;
    font-size: 28px;
    line-height: 40px;
  }
  .item-value {
    color: #A85D1B;
    font-size: 28px;
    line-height: 40px;
  }
}
.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}

.tips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 12;
  width: 820px;
  height: 450px;
  border-radius: 50px;
  box-shadow: 0 0 20px 0 rgba(48, 48, 48, 0.4);
  background-color: #fff;
  p {
    color: #333;
    font-size: 48px;
    line-height: 60px;
    width: 600px;
    text-align: center;
    word-break: break-all;
    font-weight: bold;
    padding: 72px 0 50px 0;
  }
  .btn {
    .btn-1 {
      border-radius: 60px;
      border: 4px solid #00BDFF;
      width: 312px;
      height: 112px;
      cursor: pointer;
      font-size: 40px;
      text-align: center;
      line-height: 112px;
      color:#00BDFF;
      margin-bottom: 60px;
    }
    .btn-2 {
      background: #31BFFF;
      color: #fff;
      margin-left: 40px;
    }
  }
}
.tips-confirm {
  width: 650px;
  height: 420px;
}
</style>