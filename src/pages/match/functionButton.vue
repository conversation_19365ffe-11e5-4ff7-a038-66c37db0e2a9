<template>
  <div class="functionButtonWrap">
    <div
      class="button_wrap"
      :class="game_end ? 'is_over' : 'not_over'"
      :style="{
        'background-image':
          active == 1
              ? `url(${require('@/assets/game/功能键-结束tab.png')})`
              : `url(${require('@/assets/game/对局-结束tab.png')})`,
        'background-size': '100% 100%',
      }"
    >
      <div class="wrap">
        <div class="top_tab">
          <p
            v-for="(item, index) in  2"
            :key="index"
            class="tab_item"
            :style="{ width: '50%'}"
            @click="$emit('check_tab', index + 1)"
          ></p>
        </div>
        <div class="tab_content">
          <!-- <div class="" v-if="status.game_status == 'is_end'"></div> -->
          <div class="">
            <div
              class="buttons"
              v-if="
                ((game_end || user_player == false) && active == 1)
              "
            >
              <img
                src="@/assets/game/手数-btn.png"
                alt
                @click="$emit('show_step')"
              />
              <slide
                :min="0"
                :max="parseInt(end_step)"
                v-model="newStep"
                :step="setup"
                v-if="game_end"
              ></slide>

              <div class="pre_buttons" v-if="game_end">
                <img
                  :src="
                    setup > 0
                      ? require('@/assets/game/1.png')
                      : require('@/assets/game/1_dis.png')
                  "
                  class="image_24"
                  @click="changeStepStyle(0)"
                />
                <img
                  :src="
                    setup >= 5
                      ? require('@/assets/game/2.png')
                      : require('@/assets/game/2_dis.png')
                  "
                  class="image_23"
                  @click="changeStepStyle(-5)"
                />
                <img
                  :src="
                    setup >= 1
                      ? require('@/assets/game/3.png')
                      : require('@/assets/game/3_dis.png')
                  "
                  class="image_22"
                  @click="changeStepStyle(-1)"
                />

                <img
                  :src="
                    end_step >= setup + 1
                      ? require('@/assets/game/4.png')
                      : require('@/assets/game/4_dis.png')
                  "
                  class="image_22"
                  @click="changeStepStyle(1)"
                />
                <img
                  :src="
                    end_step >= setup + 5
                      ? require('@/assets/game/5.png')
                      : require('@/assets/game/5_dis.png')
                  "
                  class="image_23"
                  @click="changeStepStyle(5)"
                />
                <img
                  :src="
                    end_step !== setup
                      ? require('@/assets/game/6.png')
                      : require('@/assets/game/6_dis.png')
                  "
                  class="image_24"
                  @click="changeStepStyle(361)"
                />
              </div>
            </div>
            <div
              class="start"
              v-if="
                ((game_end || user_player === false) && active === 2)
              "
            >
              <div v-if="game_end">
                <p class="title jcyt600">对局结果</p>
                <div class="result_wrap">
                  <div class="black_wrap">
                    <div class="img"></div>
                    <div class="jcyt500"
                      v-if="lose_reason === 'captured' || lose_reason === 'Draw'"
                    >
                      提{{ black_captured }}子
                    </div>
                    <div v-else-if="lose_reason == 'resign'" class="jcyt500">
                      {{ win_side === "black" ? "中盘胜" : "中盘负" }}
                    </div>
                    <div v-else-if="lose_reason == 'time_out'" class="jcyt500">
                      {{ win_side === "black" ? "胜利" : "超时负" }}
                    </div>
                    <div v-else-if="lose_reason === 'area_score'" class="jcyt500">
                      {{ black_score }}子
                    </div>
                    <div v-else-if="lose_reason === 'withdraw' || lose_reason === 'Abstain'" class="jcyt500">
                      {{ win_side === "black" ? "胜利" : "弃权负" }}
                    </div>
                  </div>
                  <div class="white_wrap">
                    <div class="img"></div>
                    <div
                      class="jcyt500"
                      v-if="lose_reason === 'captured' || lose_reason === 'Draw'"
                    >
                      提{{ white_captured }}子
                    </div>
                    <div v-else-if="lose_reason === 'resign'" class="jcyt500">
                      {{ win_side === "white" ? "中盘胜" : "中盘负" }}
                    </div>
                    <div v-else-if="lose_reason === 'time_out'" class="jcyt500">
                      {{ win_side === "white" ? "胜利" : "超时负" }}
                    </div>
                    <div v-else-if="lose_reason === 'area_score'" class="jcyt500">
                      {{ white_score }}子
                    </div>
                    <div v-else-if="lose_reason === 'withdraw' || lose_reason === 'Abstain'" class="jcyt500">
                      {{ win_side === "white" ? "胜利" : "弃权负" }}
                    </div>
                  </div>
                </div>
              </div>
              <div v-else>
                <p class="pop jcyt500">
                  <span v-if="game_index"
                    >第{{ game_index }}局/共{{ game_length }}局</span
                  >
                </p>
                <p
                  class="now jcyt600"
                  :style="{
                    'line-height': game_index ? '' : '3vw',
                  }"
                >
                  对局开始
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import slide from "@/components/game/slide.vue";

export default {
  name: "functionButtonWrap",
  data() {
    return {
      userInfo: {},
      newStep: "",
    };
  },
  props: {
    game_end: {},
    active: {},
    confirm_status: {},
    status: {},
    setup: {},
    game_index: {},
    game_length: {},
    user_player: {},
    win_side: {},
    lose_reason: {},
    white_captured: {},
    black_captured: {},
    end_step: {},
    black_score: {},
    white_score: {},
  },
  components: { slide },
  watch: {
    newStep: {
      handler(e) {
        this.$emit("goToStep", e);
      },
      deep: true,
    },
  },
  methods: {
    calcWinAreaScore(isBlack, areaScore, boardSize) {
      areaScore = Math.abs(areaScore);
      var result =
        boardSize == 19
          ? 180.5 + areaScore / 2
          : boardSize == 9
          ? 40.5 + areaScore / 2
          : boardSize == 13
          ? 84.5 + areaScore / 2
          : 0.0;
      if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
      } else {
        // 白方向下取整
        return Math.floor(result);
      }
    },
    calcLoseAreaScore(isBlack, areaScore, boardSize) {
      areaScore = Math.abs(areaScore);
      var result =
        boardSize == 19
          ? 361 - (180.5 + areaScore / 2)
          : boardSize == 9
          ? 81 - (40.5 + areaScore / 2)
          : boardSize == 13
          ? 169 - (84.5 + areaScore / 2)
          : 0.0;
      if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
      } else {
        // 白方向下取整
        return Math.floor(result);
      }
    },
    changeStepStyle(num) {
      this.$emit("changeStepStyle", num);
    },
  },
};
</script>

<style scoped lang="less">
.functionButtonWrap {
  margin-left: -2px;
  position: absolute;
  bottom: 50px;
  .is_over {
    margin-top: 168px;
  }

  .not_over {
    margin-top: 90px;
  }

  .button_wrap {
    width: 488px;
    height: 416px;

    padding: 20px 16px 20px 10px;
    box-sizing: border-box;

    .wrap {
      // width: 616px;
      // height: 514px;

      .top_tab {
        width: 100%;
        height: 74px;
        display: flex;
        justify-content: space-between;

        .tab_item {
          height: 74px;
          cursor: pointer;
        }
      }

      .tab_content {
        // width: 564px;
        height: 272px;
        // background-color: pink;
        // margin: 20px 25px;

        .confirm_button {
          width: 416px;
          height: 272px;
          cursor: pointer;
          margin-top: 16px;
          margin-left: 20px;
        }

        .confirm_dis {
          width: 416px;
          height: 272px;
          pointer-events: none;
          margin-top: 16px;
          margin-left: 20px;
        }

        .buttons {
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap;
          padding: 28px 36px;
          box-sizing: border-box;
          gap: 32px 48px;

          img {
            width: 96px;
            cursor: pointer;
          }

          .pre_buttons {
            width: 377px;
            height: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: -13px auto 0;

            .image_24 {
              width: 25px;
              height: 22px;
              cursor: pointer;
            }

            .image_23 {
              width: 34px;
              height: 22px;
              cursor: pointer;
            }

            .image_22 {
              width: 19px;
              height: 28px;
              cursor: pointer;
            }
          }
        }

        .start {
          font-size: 48px;
          text-align: center;
          color: #ff6e30;

          box-sizing: border-box;

          .arbitration_icon {
            width: 120px;
            height: 120px;
            margin-top: 30px;
          }
          .text_1 {
            font-size: 32px;
            color: #ff6e30;
            text-align: center;
            margin-top: 8px;
          }
          .text_2 {
            font-size: 24px;
            color: #777777;
            text-align: center;
            margin-top: 7px;
          }

          .pop {
            margin-bottom: 24px;
            margin-top: 100px;
            font-size: 24px;
            color: #777777;
          }

          .now {
            // line-height: 372px;
          }

          .title {
            font-size: 32px;
            color: #ff6e30;
            text-align: center;
            margin-top: 96px;
            line-height: 40px;
          }
        }

        .result_wrap {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 16px auto;

          .black_wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 24px;
            color: #1f242e;

            .img {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: url("@/assets/game/black.png") no-repeat;
              background-size: cover;
              margin-right: 20px;
              box-shadow: rgba(0, 0, 0, .5) 0 2px 3px 0;
            }
          }

          .white_wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 24px;
            color: #1f242e;
            margin-left: 50px;

            .img {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: url("@/assets/game/white.png") no-repeat;
              background-size: cover;
              margin-right: 20px;
              box-shadow: rgba(0, 0, 0, .5) 0 2px 3px 0;
            }
          }
        }
      }
    }
  }
}
</style>
