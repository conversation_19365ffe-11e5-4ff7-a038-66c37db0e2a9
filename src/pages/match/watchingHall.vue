<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/index/对弈大厅背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
    class="back"
    @click="goBack"
    :style="{
      'background-image': `url(${require('@/assets/index/back.png')})`,
      'background-size': '100% 100%'
    }"
  ></div>
    <div class="tv-btn-left"></div>
    <div class="tv-btn-left tv-btn-right"></div>
    <img src="@/assets/index/watch_hall_cord.png" class="tv-cord"/>
    <div class="orange-box"></div>
    <div class="main-box">
      <div class="title jcyt600" :style="{
        'background-image': `url(${require('@/assets/index/watch_hall_title.png')})`,
        'background-size': '100% 100%'
      }">观战大厅</div>
      <img src="@/assets/index/watch_hall_left.png" class="tv-hand-left"/>
      <img src="@/assets/index/watch_hall_left.png" class="tv-hand-left tv-hand-right"/>
      <img src="@/assets/index/watch_hall_bottom.png" class="tv-bottom"/>
      <div class="center-box">
          <div class="center-container">
            <div class="chessboard-list-container">
              <div class="no-Message-container" v-if="gameList.length == 0">
                <img class="no-Message" src="@/assets/nothing/暂无订单.png" />
                <p class="no-message-content jcyt500">暂无内容</p>
              </div>
              <div v-else class="chessboard-list-box">
                <div
                  v-for="(item, index) in gameList"
                  v-bind:key="index"
                  class="chessboard-container"
                  @click="goDetail(item?.id?.toString())"
                >
                  <div class="left-box">
                    <p class="game-type jcyt500">
                      {{
                        getBussinessTypeLabel(item.business_type) +
                        "·" +
                        (item.board_size || 19) +
                        "路" +
                        (item.win_captured === 0 ? "围地" : "吃子")
                      }}
                    </p>
                    <!-- <p class="game-time">观战人数：{{ item.view_count }}</p> -->
                  </div>
                  <div class="match-result-container">
                    <div class="user-info">
                      <div class="user-avatar">
                        <img :src="item.black_user_avatar" />
                        <div
                          class="user-color current-user-color"
                          :style="{
                            'background-image':
                              'url(' +
                              require('../../assets/mine/black.png') +
                              ')',
                            'background-repeat': 'no-repeat',
                            'background-size': '100% 100%'
                          }"
                        ></div>
                      </div>
                      <div class="user-name-container">
                        <div class="user-name jcyt500">
                          {{ shortToLongName(item.black_user_nick_name) }}
                        </div>
                      </div>
                    </div>
                    <div
                      class="result-container jcyt500"
                      :class="
                        item.business_type == '智能棋盘'
                          ? 'new-vs-wrap'
                          : item.win == 4
                          ? 'waiver'
                          : item.win == 1 || item.win == 2 || item.win == 3
                          ? 'normal'
                          : 'new-vs-wrap'
                      "
                    >
                      <img
                        v-if="
                          item.business_type == '智能棋盘' ||
                          (item.win != 1 &&
                            item.win != 2 &&
                            item.win != 3 &&
                            item.win != 4)
                        "
                        :src="require('../../assets/mine/new-vs.png')"
                        alt=""
                      />
                      <span v-else class="jcyt500">{{
                        getLabel(item.win, item.win_result)
                      }}</span>
                    </div>
                    <div class="user-info">
                      <div class="other-user-name-container">
                        <div class="user-name jcyt500">
                          {{ shortToLongName(item.white_user_nick_name) }}
                        </div>
                      </div>
                      <div class="user-avatar">
                        <img :src="item.white_user_avatar" />
                        <div
                          class="user-color other-user-color"
                          :style="{
                            'background-image':
                              'url(' +
                              require('../../assets/mine/white.png') +
                              ')',
                            'background-repeat': 'no-repeat',
                            'background-size': '100% 100%'
                          }"
                        ></div>
                      </div>
                    </div>
                  </div>
                  <!-- <div
                    class="navigate-button"
                    @click="goDetail(item?.id?.toString())"
                    :style="{
                      'background-image': `url(${require('@/assets/mine/我的棋谱按钮.png')})`,
                      'background-size': '100% 100%'
                    }"
                  >
                    观战
                  </div> -->
                </div>
                <infinite-loading
                  spinner="spiral"
                  @infinite="infiniteHandler"
                  :distance="200"
                  class="infinite-loading-wrap"
                >
                  <div slot="spinner">加载中...</div>
                  <div slot="no-more">到底啦...</div>
                  <div slot="no-results">No more Data</div>
                </infinite-loading>
              </div>
            </div>
          </div>
      </div>
    </div>
  </div>
</template>

<script>
import soloApi from "@/api/soloFirends";
import InfiniteLoading from "vue-infinite-loading";
import storage from "@/public/storage.js";
import { format_win_result } from "@/public/result";

export default {
  name: "mineIndex",
  components: { InfiniteLoading },
  data() {
    return {
      informDetail: "",
      apiDone: false,
      selecting: false,
      label: "全部赛事",
      selectNum: 0,
      dataId: "",
      gameList: [],
      totalCount: 0,
      totalPage: 0,
      matchTypeList: [
        { name: "全部赛事", source: "all" },
        { name: "聂道赛事", source: "tournament" },
        { name: "AI对弈", source: "ai-tournament" },
        { name: "课后对弈", source: "ai-stage" },
        { name: "评测赛事", source: "evaluation" },
        { name: "好友约战", source: "room_solo" },
        { name: "排位赛", source: "season-rank" },
        { name: "定级赛", source: "rank_level" },
        { name: "智能棋盘", source: "smart_board" },
        { name: "单元测", source: "unit" }
      ],
      changeType: "all",
      page: 1,
      user_id: ""
    };
  },
  created() {
    // if (this.$storage.$getStroage("mySgfPage")) {
    this.user_id = storage.$getStroage("userId");
    this.$store.commit("setApiLoading", true);
    this.init(this.changeType);
    // }
  },
  computed: {
    url_from() {
      return this.$route.query.from_url;
    }
  },
  methods: {
    getLabel(win, winResult) {
      return win == 3
        ? "和棋"
        : win == 4
        ? "弃权"
        : format_win_result(winResult);
    },
    getBussinessTypeLabel(source) {
      var matchTypeList = [
        ...this.matchTypeList,
        { name: "排位赛", source: "auto_pair" }
      ];
      let item = matchTypeList.find((item) => item.source == source);
      let name = typeof item == "undefined" ? "未知" : item.name;
      return name;
    },
    init(type, $state) {
      if(this.page > 5) {
        $state.complete();
        return false;
      }
      let data = {
        page: this.page,
        page_size: 15
      };
      if (type != "all") {
        data = { ...data, business_type: type };
      }
      soloApi.GameListAPi(data).then((res) => {
        if (res.data.results && res.data.results.length) {
          this.page += 1;
          let arr = res.data.results;
          this.totalCount = res.data.count || 0;

          this.gameList = [...this.gameList, ...arr];
          this.apiDone = true;
          if ($state) {
            {
              $state.loaded();
            }
          }
        } else {
          if ($state) {
            $state.complete();
          }
        }
        this.$store.commit("setApiLoading", false);
      });
    },
    infiniteHandler($state) {
      setTimeout(() => {
        this.init(this.changeType, $state);
      }, 1000);
    },
    goBack() {
      if (this.url_from == "nwpMatch") {
        this.$router.push('/nwpMatch');
      } else {
        this.$router.push({
          path: "/",
          // query: {
          //   defaultIndex: 2
          // }
        });
      }
    },
    shortToLongName(name) {
      if (name.length > 6) {
        return name.substring(0, 6) + "...";
      } else {
        return name;
      }
    },
    // goGame(item) {
    //   __bl.sum("观战");
    //   if (item.game_type == "聂道赛事") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "watchingHall",
    //         from: "playing"
    //       }
    //     });
    //   } else if (item.game_type == "AI对弈" || item.game_type == "课后对弈") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "watchingHall",
    //         from: "Ai"
    //       }
    //     });
    //   } else if (item.game_type == "排位赛") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "watchingHall",
    //         from: "qualifying"
    //       }
    //     });
    //   } else if (item.game_type == "定级赛") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "watchingHall",
    //         from: "Ai"
    //       }
    //     });
    //   } else if (item.game_type == "智能棋盘") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "watchingHall",
    //         from: "smartBoard"
    //       }
    //     });
    //   } else if (item.game_type == "好友约战") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "watchingHall",
    //         from: "solo"
    //       }
    //     });
    //   }
    // },
    goDetail(id) {
      __bl.sum("观战");
      let arr = [
        { name: "聂道赛事", source: "tournament" },
        { name: "AI对弈", source: "ai-tournament" },
        { name: "课后对弈", source: "ai-stage" },
        { name: "评测赛事", source: "evaluation" },
        { name: "好友约战", source: "room_solo" },
        { name: "排位赛", source: "season-rank" },
        { name: "定级赛", source: "rank_level" },
        { name: "智能棋盘", source: "smart_board" },
        { name: "单元测", source: "unit" },
        { name: "排位赛", source: "auto_pair" }
      ];
      this.$router.push({
        path: "/watchingGame",
        query: {
          game_id: id,
          from_url: "/watchingHall"
        }
      });
    },
    searchValueHandle(item, index) {
      this.gameList = [];
      this.label = item.name;
      this.changeType = item.source;
      this.selectNum = index;
      this.selecting = false;
      this.page = 1;
      this.$store.commit("setApiLoading", true);
      this.init(item.source);
    }
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    position: absolute;
    left: 56px;
    top: 40px;
  }
}
.tv-btn-left {
  background: #FB6440;
  border-radius: 7.45px 0 0 7.45px;
  width: 56.1px;
  height: 89.23px;
  box-shadow: #FF8B6F 2px 0 7px 0 inset, 0 4px 8px 0 #FF9C84 inset,0 -4px 8px 0 #BE391A inset;
  position: absolute;
  left: 140px;
  top: 332px;
  z-index: 2;
}
.tv-btn-right {
  border-radius: 0 7.45px 7.45px 0;
  left: unset;
  right: 140px;
}
.tv-cord {
  width: 269.3px;
  height: 267.69px;
  position: absolute;
  right: 52px;
  top: 354.31px;
  z-index: 1;
}
.main-box {
  box-sizing: border-box;
  width: 1600px;
  height: 880px;
  border-radius: 120px;
  background-image: linear-gradient(15deg, #FAB165 10%, #FFD261);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 160px auto 0;
  position: relative;
  z-index: 3;
  .tv-hand-left {
    width: 72px;
    // height: 320.04px;
    position: absolute;
    left: -20px;
    top: 353px;
    z-index: 10;
  }
  .tv-hand-right {
    transform: rotate(180deg);
    right: -20px;
    left: unset;
  }
  .tv-bottom {
    width: 882.99px;
    height: 162.22px;
    position: absolute;
    bottom: -11.22px;
    z-index: 1;
  }
  .title {
    width: 512px;
    height: 110px;
    font-size: 48px;
    color: #fff;
    text-align: center;
    line-height: 110px;
    position: absolute;
    z-index: 10;
    top: -70px;
  }
  .center-box {
    width: 1520px;
    position: relative;
    z-index: 8;
    height: 800px;
    border-radius: 80px;
    background: #FBF4CE;
    margin: 40px;
    box-shadow: rgba(199, 83, 26, .1) 0 -6px 10px 0, rgba(199, 83, 26, .25) 0 6px 10px 0,
    rgba(255, 247, 167, 0.36) 6px 0 10px 0 inset, #FFE9A7 -6px 0 13px 0 inset,
    rgba(255, 215, 59, 0.54) 0 8px 11px 0 inset,rgba(255, 250, 241, 0.22) 0 -6px 13px 0 inset;
  }
  .center-container {
    width: 1480px;
    height: 760px;
    border-radius: 64px;
    background-image: linear-gradient(to bottom, #634CEC, #634CEC);
    background-color: #5042AE;
    flex: 1;
    display: flex;
    justify-content: center;
    margin: 20px;
      .no-Message-container {
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
      }
      .no-Message {
        width: auto;
        height: 442px;
        margin-top: 60px;
        margin-bottom: 31px;
        margin-left: 70px;
      }
      .no-message-content {
        font-size: 36px;
        color: #3A3B3B;
      }
      .chessboard-list-container {
        width: 100%;
        height: 100%;
        overflow: auto;
        padding: 24px;
        box-sizing: border-box;
        .chessboard-list-box {
          width: 1432px;
          height: auto;
          .chessboard-container {
            width: 100%;
            height: 160px;
            border-radius: 40px;
            background-image: linear-gradient(to bottom, #E6FAFF,#AEE1FF);
            margin-bottom: 24px;
            box-sizing: border-box;
            padding-left: 40px;
            padding-right: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .left-box {
              width: 360px;
            }
            .game-type {
              font-size: 40px;
              line-height: 48px;
              color: #435588;
            }
            .game-time {
              font-size: 28px;
              line-height: 32px;
              color: #6e95b5;
              font-weight: 400;
            }
            .match-result-container {
              width: 960px;
              height: 120px;
              border-radius: 60px;
              background-color: rgba(255, 255, 255, 0.9);
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 32px;
              box-sizing: border-box;
              .user-info {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                .user-avatar {
                  width: 80px;
                  height: 80px;
                  border-radius: 50%;
                  box-sizing: border-box;
                  position: relative;
                  img {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                  }
                  .user-color {
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    position: absolute;
                    bottom: -5px;
                  }
                  .current-user-color {
                    right: -5px;
                  }
                  .other-user-color {
                    left: -5px;
                  }
                }
                .user-name-container {
                  width: 200px;
                  margin-left: 24px;
                  text-align: left;
                }
                .other-user-name-container {
                  width: 200px;
                  margin-right: 24px;
                  text-align: end;
                }
                .user-name {
                  font-size: 40px;
                  color: #435588;
                }
              }
              .result-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                img {
                  width: 64px;
                  height: 36px;
                }
              }
            }
            .navigate-button {
              width: 232px;
              height: 88px;
              border-radius: 44px;
              line-height: 88px;
              font-size: 40px;
              color: #fff;
              text-align: center;
            }
          }
        }
      }
      .chessboard-list-container::-webkit-scrollbar {
        width: 0px;
      }
      // .paging-container {
      //   width: 100%;
      //   height: 120px;
      //   background-color: #ffe8c8;
      //   position: absolute;
      //   left: 0;
      //   bottom: 0;
      //   display: flex;
      //   align-items: center;
      //   justify-content: space-between;
      //   box-sizing: border-box;
      //   padding: 20px 80px;
      //   .page-button-container {
      //     width: 480px;
      //     display: flex;
      //     align-items: center;
      //     justify-content: space-between;
      //   }
      //   .count-container {
      //     font-size: 32px;
      //     color: #b65600;
      //   }
      //   .page-button {
      //     width: 224px;
      //     height: 80px;
      //     border-radius: 40px;
      //     line-height: 80px;
      //     text-align: center;
      //     font-size: 32px;
      //     color: #b65600;
      //   }
      //   .disabledButton {
      //     color: rgba(182, 86, 0, 0.5);
      //   }
      // }
  }
  .select-box {
    width: 128px;
    position: relative;
    .select-label {
      font-weight: 600;
    }
  }
  .select-box .show-box {
    width: 286px;
    height: 88px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 44px;
    background: #ffffff;
    padding: 20px 40px;
    box-sizing: border-box;
    cursor: pointer;
    font-size: 40px;
    line-height: 48px;
    color: #333;
    font-weight: 400;
  }
  .select-box .show-box.selecting {
    border: 1px solid #00d9ffff;
  }
  .select-box .show-box .arrow-down {
    width: 30px;
    height: 32px;
  }
  .select-box .dropdown-box {
    list-style: none;
    position: absolute;
    border-radius: 40px;
    z-index: 99;
    top: 104px;
    right: 0;
    width: 336px;
    padding: 24px 40px;
    box-sizing: border-box;
    height: auto;
    min-height: 616px;
    overflow-y: auto;
    overflow-x: hidden;
    background: #fdfdfd;
    box-shadow: 0px 6px 30px 0px rgba(0, 0, 0, 0.08);
  }
  .select-box .dropdown-box .dropdown-item {
    position: relative;
    height: 88px;
    width: 100%;
    color: #666;
    border-radius: 44px;
    padding: 0 8px;
    line-height: 88px;
    text-align: center;
    font-size: 36px;
    box-sizing: border-box;
  }
  .select-box .dropdown-box .dropdown-item:hover {
    color: #00bdff;
    background: rgba(22, 195, 255, 0.09);
    cursor: pointer;
  }
  .selectItem {
    color: #00bdff !important;
    background: rgba(22, 195, 255, 0.09);
    cursor: pointer;
  }
}
.infinite-loading-wrap {
  font-size: 36px;
  color: #00bdff;
}

.fading-circle {
  color: rgba(100, 49, 191, 255);
}
.new-vs-wrap {
  background: linear-gradient(to bottom, #fffffe, #fffbec);
  height: 72px;
  width: 240px;
  border-radius: 36px;
  border: 3.4px solid #fae652;
  box-sizing: border-box;
}

.waiver {
  height: 88px;
  width: 286px;
  background: #ebf0f5;
  border-radius: 44px;
  span {
    color: #536786;
    font-size: 40px;
    font-weight: bold;
    text-align: center;
  }
}

.normal {
  height: 72px;
  width: 240px;
  background: #fcf3e4;
  border-radius: 36px;
  span {
    color: #a85d1b;
    font-size: 40px;
    text-align: center;
  }
}
.orange-box {
  width: 1600px;
  height: 800px;
  background: #FF873A;
  border-radius: 140px;
  position: absolute;
  top: 136px;
  left: 160px;
}
</style>
