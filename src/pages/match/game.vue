<template>
  <div
    class="gameWrap"
    :style="{
      'background-image': `url(${require('@/assets/exerciseDetail/下棋页面背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="content">
      <div class="left">
        <board
          ref="board_play"
          id="board"
          :sgf="sgf"
          :board_click="board_click"
          @setup="change_setup"
          @captured="change_captured"
          @chess_move="chess_move"
        />
      </div>
      <div class="right">
        <div class="game_type_wrap">
          <gameRule
            :status="status"
            ref="is_rule_show"
            :showPeaceAndAcrose="false"
          ></gameRule>
          <div class="game_index jcyt600">第 {{ setup }} 手</div>

          <div class="fuc_wrap">
            <div
              class="reload"
              @click="goReload()"
              :style="{
                'background-image': `url(${require('@/assets/game/刷新.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="rule"
              @click="changeRuleShow()"
              :style="{
                'background-image': is_rule_show
                  ? `url(${require('@/assets/game/查看规则.png')})`
                  : `url(${require('@/assets/game/规则.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="back"
              @click="back"
              :style="{
                'background-image': `url(${require('@/assets/exerciseDetail/返回.png')})`,
                'background-size': 'cover'
              }"
            ></div>
          </div>
        </div>
        <gamePeople
          :game_end="game_end"
          :status="status"
          :black_enter="black_enter"
          :white_enter="white_enter"
          :ws_left_time="ws_left_time"
          :black_left_time="black_left_time"
          :white_left_time="white_left_time"
          :hum_captured="hum_captured"
          :ai_captured="ai_captured"
          :lose_reason="lose_reason"
          :time_c="time_c"
          :win_side="win_side"
        ></gamePeople>
        <div
          class="drop_countdown jcyt500"
          v-if="game_end === false && status.enable_move_time === 1"
        >
          落子倒计时：{{ s_to_hs(drop_left_time) }}
        </div>
        <functionButton
          :game_end="game_end"
          :active="active"
          :confirm_status="confirm_status"
          :status="status"
          :setup="setup"
          :user_player="user_player"
          :win_side="win_side"
          :lose_reason="lose_reason"
          :white_captured="ai_captured"
          :black_captured="hum_captured"
          :end_step="end_step"
          :black_score="black_score"
          :white_score="white_score"
          @check_tab="check_tab"
          @check_area_score="check_area_score"
          @changeStepStyle="changeStepStyle"
          @goToStep="goToStep"
          @show_step="show_step"
        ></functionButton>
      </div>
    </div>
  </div>
</template>

<script>
import functionButton from "@/pages/match/functionButton.vue";
import gameRule from "@/components/game/gameRule";
import gamePeople from "@/components/game/gamePeople";
import gameApi from "@/api/game";
import board from "@/components/game/board";
import { Toast } from "mint-ui";
import storage from "@/public/storage.js";
import zip from "../../public/zip";
import "@/public/wgo/wgo.min";
import "@/public/wgo/wgo.player.min";
import AwaitLock from "await-lock";
import tool from "@/public/tool";

export default {
  name: "gameWrap",
  data() {
    return {
      active: 1,
      is_rule_show: false,
      move_audio: "",
      board_click: false,
      status: {},
      hum_captured: 0,
      ai_captured: 0,
      last_mark: "",
      last_move: {
        x: "",
        y: "",
        turn: ""
      },
      confirm_status: false,
      apply_score_number: "",
      areaScoreing: false,
      showMoveNumber: false,
      user_id: "",
      user_hash: "",
      send_data: {},
      setup: 0,
      sgf: "",
      timer: "",
      timer1: "",
      ws_left_time: {},
      turn: 1,
      user_player: false,
      time_c: 1,
      move_c: 1,
      win_side: "",
      lose_reason: "",
      game_end: false,
      black_left_time: "",
      white_left_time: "",
      drop_left_time: "",
      end_step: 0,
      black_enter: false,
      white_enter: false,
      black_score: 0,
      white_score: 0,
      last_msg_index: 0,
      miss_msg_locker: new AwaitLock(),
      handler_locker: new AwaitLock(),
      ownership: [], //数目结束标志位
      queue: [],
      dis_game_id: ""
    };
  },
  components: {
    functionButton,
    board,
    gameRule,
    gamePeople
  },

  computed: {
    wsMessage() {
      return this.$store.getters.getMessage;
    },
    game_id() {
      return this.$route.query.game_id;
    },
    wsReadyStats() {
      return this.$store.getters.getWsReadyStats;
    },
    from_url() {
      return this.$route.query.from_url;
    },
    course_id() {
      return this.$route.query.course_id;
    },
    lesson_id() {
      return this.$route.query.lesson_id;
    },
    type() {
      return this.$route.query.type;
    },
    matchId() {
      return this.$route.query.matchId;
    }
  },
  watch: {
    wsReadyStats: {
      handler(new_stats, old_stats) {
        if (new_stats === 1) {
          this.$nextTick(() => {
            this.$socket.send({
              message_type: "bind_group",
              data: { group_id: "game:" + this.game_id }
            });
          });
        }
        if (new_stats === 0) {
          // alert("网络连接失败，请检查网络设置");
        }
      },
    },
    wsMessage: async function (newValue, oldValue) {
      await this.onMessage(newValue);
    },
    status: {
      handler(new_object) {
        gameApi.PlaySgf(this.send_data).then((res) => {
          var data = JSON.parse(zip.unzipStr(res.data));
          this.sgf = data.sgf;
        });

        if (new_object.is_end === false) {
          this.$nextTick(() => {
            if (this.$socket.conn.ws.readyState === 1) {
              this.$socket.send({
                message_type: "bind_group",
                data: { group_id: "game:" + this.game_id }
              });
            }
          });
          this.drop_left_time = new_object.now_move_time;
          if (this.timer1) {
            clearInterval(this.timer1);
          }
          this.dropTimeReGetCountdown(this.drop_left_time);
        } else {
          this.game_end = true;
          this.end_step = new_object.step;
          this.win_side = new_object.win === 1 ? "black" : "white";
          this.drop_left_time = new_object.now_move_time;
          this.black_score = new_object.black_score;
          this.white_score = new_object.white_score;
          this.check_result(new_object);
       
        }
        this.black_left_time =
          new_object.now_black_time > 0
            ? new_object.now_black_time
            : new_object.black_time;
        this.white_left_time =
          new_object.now_white_time > 0
            ? new_object.now_white_time
            : new_object.white_time;

        this.apply_score_number = new_object.territory_step;
        this.black_enter = new_object.is_end ? true : new_object.black_enter;
        this.white_enter = new_object.is_end ? true : new_object.white_enter;
        console.log('this.white_enter')
        console.log(this.white_enter)
        this.check_user_player();
      },
      deep: true
    }
  },
  methods: {
    async onMessage(val) {
      let msg = val.data;
      let index = val.index;
      switch (val["group"]) {
        case `game:${this.game_id}`:
          if (this.miss_msg_locker.acquired) {
            this.queue.push(val);
            return;
          }
          if (this.last_msg_index === 0) {
            this.last_msg_index = index - 1;
          }
          if (this.last_msg_index + 1 < index) {
            if (this.miss_msg_locker.tryAcquire()) {
              let totalMissMsgCount = index - this.last_msg_index - 1;
              for (let i = 1; i <= totalMissMsgCount; i++) {
                await this.handlerMissMsg(this.last_msg_index + i);
              }
              this.handler(msg);
              this.last_msg_index = index;
              this.doMySelf();
              this.miss_msg_locker.release();
            }
          } else {
            this.handler(msg);
            this.last_msg_index = index;
          }
          break;
        default:
          console.log("onMessage default", val);
          break;
      }
    },
    async handlerMissMsg(msg_index) {
      await websocket
        .GetMissGroupWebSocketMsg({
          index: msg_index,
          group_id: `game:${this.game_id}`
        })
        .then((res) => {
          let data = res.data.data.message;
          console.log(res.data.data.index);
          let msg = zip.unzipStr(data);
          this.handler(JSON.parse(msg));
        }).catch(() => {
          window.location.reload();
        });
    },
    handler(msg) {
      console.log(msg);
      switch (msg.message_type) {
        case "time":
          let data = msg.data;
          this.time_c = data.c;
          this.ws_left_time = data;
          this.time_down();
          break;
        case "move_time":
          this.move_c = msg.data.color;
          if (this.timer1) {
            clearInterval(this.timer1);
          }
          if (this.status.enable_move_time == 1) {
            this.drop_left_time = msg.data.left_time;
            this.dropTimeReGetCountdown(this.drop_left_time);
          }
          break;
        case "user_status":
          console.log(msg.data, this.status);
          if (msg.data.user_hash === this.status.black_user_hash) {
            if (msg.data.enter_status && msg.data.online_status) {
              this.black_enter = true;
            }
          } else if (msg.data.user_hash === this.status.white_user_hash) {
            if (msg.data.enter_status && msg.data.online_status) {
              this.white_enter = true;
            }
          }
          break;
        case "move":
          this.$refs.board_play.update_board({
            x: msg.data.x,
            y: msg.data.y,
            turn: msg.data.c === 2 ? -1 : 1
          });
          break;
        case "pass":
          this.$refs.board_play.update_pass();
          this.turn = msg.data.c == 1 ? 2 : 1;
          Toast(this.turn === 1 ? "白方停一手" : "黑方停一手");
          this.can_play();
          break;
        case "end":
          this.game_end = true;
          this.end_step = msg.data.step;
          this.black_score = msg.data.b_score;
          this.white_score = msg.data.w_score;
          this.check_result(msg.data);
          if (this.timer) {
            clearInterval(this.timer);
          }
          if (this.timer1) {
            clearInterval(this.timer1);
          }
          if (!this.status["is_end"]) {
            this.$socket.send({
              message_type: "unbind_group",
              data: { group_id: "game:" + this.game_id }
            });
          }

          break;
        case "force_reload":
          if (this.game_id == msg.data.game_id) {
            this.$refs.board_play.loadSgf(msg.data.sgf);
          }
          break;
      }
    },
    doMySelf() {
      let val = this.queue.shift();
      if (val) {
        let msg = val.data;
        let index = val.index;
        this.handler(msg);
        this.last_msg_index = index;
      }
      if (this.queue.length > 0) {
        this.doMySelf();
      }
    },
    owner_ship() {
      tool.OwnerShip(this.ownership, this.$refs.board_play.player);
    },
    remove_owner_ship() {
      if (this.ownership.length > 0) {
        tool.RemoveOwnerShip(this.ownership, this.$refs.board_play.player);
        this.ownership = [];
      }
    },
    check_user_player() {
      this.user_player = false;
    },
    check_result(data) {
      console.log("check_result" + data);
      this.win_side =
        data.win === 1 ? "black" : data.win === 2 ? "white" : data.win;
      if (this.win_side === 3) {
        this.lose_reason = data.win_result;
      } else {
        if (data.win_result === "Abstain") {
          
          this.lose_reason = "Abstain";
          console.log( this.lose_reason);
          return;
        }
        var str = data.win_result.substring(
          data.win_result.indexOf("+") + 1,
          data.win_result.length
        );
        // console.log(structuredClone);
        if (str.indexOf("C")) {
          var num = str.substring(1, str.length);
          data.win === 1
            ? (this.black_captured = num)
            : (this.white_captured = num);
        }
        this.lose_reason =
          str.indexOf("C") > -1 || str == "O"
            ? "captured"
            : str === "R"
            ? "resign"
            : str === "T"
            ? "time_out"
            : parseInt(str) > 0
            ? "area_score"
            : str === "L"
            ? "withdraw"
            : "Draw";
        console.log(this.lose_reason + 'lose_reason');
      }
    },
    reGetCountdown(new_time) {
      this.timer = setInterval(() => {
        if (new_time > 0) {
          new_time--;
          this.time_c === 2
            ? (this.white_left_time = new_time)
            : (this.black_left_time = new_time);
        }
      }, 1000);
    },
    dropTimeReGetCountdown(new_time) {
      this.timer1 = setInterval(() => {
        if (new_time > 0) {
          new_time--;
          this.drop_left_time = new_time;
        }
      }, 1000);
    },
    changeRuleShow() {
      this.is_rule_show = !this.is_rule_show;
      this.is_rule_show
        ? this.$refs.is_rule_show.open()
        : this.$refs.is_rule_show.close();
    },
    time_down() {
      if (this.time_c === 1) {
        if (
          this.ws_left_time.black_main_time === 0 &&
          this.ws_left_time.black_byo_yomi > 0
        ) {
          if (this.timer) {
            clearInterval(this.timer);
            this.black_left_time = this.ws_left_time.black_byo_yomi_time;
          }
          this.reGetCountdown(this.ws_left_time.black_byo_yomi_time);
        } else {
          if (this.timer) {
            clearInterval(this.timer);
            this.black_left_time = this.ws_left_time.black_main_time;
          }
          this.reGetCountdown(this.ws_left_time.black_main_time);
        }
      } else if (this.time_c == 2) {
        if (
          this.ws_left_time.white_main_time == 0 &&
          this.ws_left_time.white_byo_yomi > 0
        ) {
          if (this.timer) {
            clearInterval(this.timer);
            this.white_left_time = this.ws_left_time.white_byo_yomi_time;
          }
          this.reGetCountdown(this.ws_left_time.white_byo_yomi_time);
        } else {
          if (this.timer) {
            clearInterval(this.timer);
            this.white_left_time = this.ws_left_time.white_main_time;
          }
          this.reGetCountdown(this.ws_left_time.white_main_time);
        }
      }
    },
    goReload() {
      window.location.reload();
    },
    s_to_hs: function (s) {
      if (!s) {
        s = 0;
      }
      var h;
      h = Math.floor(s / 60);
      s = s % 60;
      h += "";
      s += "";
      h = h.length == 1 ? "0" + h : h;
      s = s.length == 1 ? "0" + s : s;
      return h + ":" + s;
    },
    change_captured: function (event) {
      this.hum_captured = event.B;
      this.ai_captured = event.W;
    },
    change_setup: function (event) {
      this.setup = event;
    },
    chess_move: function (ob) {
      if (this.last_mark) {
        this.$refs.board_play.remove_chess_mark();
      }
      this.last_mark = ob;
      this.confirm_status = true;
      this.$refs.board_play.add_chess_mark(this.last_mark);
    },
    check_sgf_same(status_sgf, board_sgf) {
      // if (status_sgf !== board_sgf) {
      this.$refs.board_play.loadSgf(status_sgf);

      // }
    },
    peace() {
      //和棋
      Toast("耐心等待");
    },
    check_tab(active_index) {
      this.active = active_index;
    },
    back() {
      if (this.from_url == "/unitTestReport") {
        this.$router.push({
          path: "/unitTestReport",
          query: {
            course_id: this.course_id,
            lesson_id: this.lesson_id,
            matchId: this.matchId,
            type: this.type
          }
        });
      } else if (this.from_url) {
        this.$router.push({
          path: this.from_url
        });
      } else {
        this.$router.push({
          path: "/watchingHall"
        });
      }
    },

    getStatus() {
      gameApi
        .GetPlayInfo(this.send_data)
        .then((res) => {
          this.status = JSON.parse(zip.unzipStr(res.data));
        })
        .catch(() => {});
    },
    check_area_score() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }

      if (this.game_end == false) {
        if (this.areaScoreing == false) {
          this.areaScoreing = true;
          if (
            this.setup >= this.apply_score_number &&
            this.board_click === true
          ) {
            this.handler_locker.tryAcquire();
            this.board_click = false;
            gameApi
              .AreaScore(this.send_data)
              .then((res) => {
                if (this.last_mark) {
                  this.$refs.board_play.remove_chess_mark();
                }
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
              })
              .catch((error) => {
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
              });
          } else if (
            this.setup < this.apply_score_number &&
            this.board_click === true
          ) {
            Toast(`请在${this.apply_score_number}手后申请数子`);
          } else if (this.board_click === false) {
            Toast("请等待对手落子后再数目");
          }
          this.areaScoreing = false;
        }
      }
    },
    changeStepStyle(num) {
      this.remove_owner_ship();
      if (num == 0) {
        if (this.setup > 0) {
          this.$refs.board_play.move_to_first();
        }
      } else if (num == -5) {
        if (this.setup >= 5) {
          this.$refs.board_play.goTo(-5);
        }
      } else if (num == -1) {
        if (this.setup >= 1) {
          this.$refs.board_play.move_to_previous();
        }
      } else if (num == 1) {
        if (this.end_step >= this.setup + 1) {
          this.$refs.board_play.move_to_next();
        }
      } else if (num == 5) {
        if (this.end_step >= this.setup + 5) {
          this.$refs.board_play.goTo(5);
        }
      } else if (num == 361) {
        if (this.end_step != this.setup) {
          this.$refs.board_play.move_to_last();
        }
      }
    },
    goToStep(e) {
      this.$refs.board_play.goToStep(e);
    },
    show_step() {
      if (!this.showMoveNumber) {
        this.$refs.board_play.show_move_number();
      } else {
        this.$refs.board_play.close_move_number();
      }
      this.showMoveNumber = !this.showMoveNumber;
    },
    cancel() {
      if (!this.status.is_end) {
        this.$socket.send({
          message_type: "unbind_group",
          data: { group_id: "game:" + this.dis_game_id }
        });
      }
    }
  },
  mounted() {
    this.handler_locker.tryAcquire();
    this.handler_locker.release();
    this.dis_game_id = this.game_id;
    this.user_id = storage.$getStroage("userId");
    this.user_hash = `${this.user_id}:1`;
    this.send_data = {
      game_id: parseInt(this.game_id),
      user_hash: this.user_hash,
      user_id: this.user_id
    };
    this.move_audio = document.getElementById("move-audio");
    this.getStatus();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.timer1) {
      clearInterval(this.timer1);
    }
    this.cancel();
  }
};
</script>

<style scoped lang="less">
.gameWrap {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  vertical-align: middle;
  .wifi {
    position: absolute;
    right: 16px;
    top: 40px;
    display: flex;
    z-index: 10;
    flex-direction: row-reverse;
  }

  .content {
    // width: 2016px;
    // height: 1344px;
    padding-top: 40px;
    display: flex;
    justify-content: center;
    margin: 0 auto;
    .left {
      width: 1000px;
      height: 1000px;
      background: #f7a448;
      border: 16px solid #fee194;
      box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.1);
      border-radius: 40px;
      position: relative;
      box-sizing: border-box;
    }

    .right {
      width: 488px;
      height: 1000px;
      margin-left: 16px;
      background: rgba(255, 255, 255, 0.5);
      border: 2px solid rgba(255, 255, 255, 0.5);
      box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.1);
      border-radius: 30px;
      box-sizing: border-box;

      .game_type_wrap {
        width: 488px;
        height: 104px;
        padding: 16px 20px 16px 16px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        background-image: linear-gradient(180deg, #ffffff 6%, #c6ecff 87%);
        box-shadow: 0 4px 15px 0 #5bb9e7, inset 0 -3px6px 0 #e1f1fc;
        border-radius: 30px 30px 0 0;
        position: relative;
        align-items: center;

        .game_index {
          width: 144px;
          height: 72px;
          line-height: 72px;
          text-align: center;
          background: rgba(198, 236, 255, 0.5);
          border: 1.49px solid rgba(108, 206, 255, 0.3);
          box-shadow: inset 0 0 9px 0 #6cceff;
          border-radius: 37.2px;
          font-size: 26.79px;
          color: #344770;
        }

        .fuc_wrap {
          // width: 304px;
          // height: 92px;
          display: flex;

          .back {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
            margin-left: 12px;
          }

          .reload {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
          }

          .rule {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
            margin-left: 12px;
          }
        }
      }

      .drop_countdown {
        width: calc(100% - 32px);
        height: 64px;
        line-height: 64px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 32px;
        font-size: 32px;
        color: #ffffff;
        text-align: center;

        margin: 16px auto 0;
      }
    }
  }
}
</style>
