<template>
  <div
    class="lessonList"
    :style="{
      'background-image': `url(${require('@/assets/index/首页背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="title_wrap">
      <div
        class="back"
        @click="back"
        :style="{
          'background-image': `url(${require('@/assets/index/back.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
      <p class="title jcyt600" v-if="reload">{{ title }}</p>
      <div class="zhanwei"></div>
    </div>
    <div class="content swiper-no-swiping" v-if="reload">
      <swiper class="swiper" :options="swiperOption">
        <swiper-slide v-for="(i, index) in page" :key="index"
          ><div class="swiper-item">
            <div
              class="item"
              v-for="(item, index) in lessonList.slice(4 * (i - 1), i * 4)"
              :key="index"
              @click="goLessonInfo(item)"
            >
              <div class="left_img_wrap">
                <div
                  :style="{
                    background:
                      item['type'] == 'final'
                        ? 'url(' + item['avatar_pad'] + ') '
                        : `url(${require('@/assets/lessonList/课程封面小.png')})`,
                    'background-size': '100% 100%'
                  }"
                  class="left_img"
                >
                  <div class="evaluation jcyt600" v-if="item['lesson_unit_test_id'] != 0">
                    单元测评
                  </div>
                </div>
                <div class="mask" v-if="!item['unlocked']">
                  <img src="@/assets/lessonList/lock.png" alt="" />
                </div>
              </div>
              <div class="info" :style="{
                'background-image': `url(${require('@/assets/index/lesson_bottom_bg.png')})`,
                'background-size': '100% 100%'
              }">
                <p class="lesson_index jcyt500">
                  {{
                    item["type"] == "final" &&
                    item["final_exam_status"] == "is_pass"
                      ? "已通过"
                      : item["type"] == "final" &&
                        item["final_exam_status"] == "not_pass"
                      ? "未通过"
                      : item["type"] == "final" &&
                        item["final_exam_status"] != "not_pass" &&
                        item["final_exam_status"] != "ist_pass"
                      ? "测一测你的学习成果"
                      : `第 ${index + 1 + 4 * (i - 1)} 课`
                  }}
                </p>
                <p class="lesson_name jcyt600">{{ item["name"] }}</p>
                <div
                  class="is_pass"
                  v-if="
                    item['type'] == 'final' &&
                    item['final_exam_status'] == 'is_pass'
                  "
                >
                  <img src="@/assets/lessonList/正确.png" alt="" />
                </div>
                <div
                  class="is_pass"
                  v-else-if="
                    item['type'] == 'final' &&
                    item['final_exam_status'] == 'not_pass'
                  "
                >
                  <img src="@/assets/lessonList/警告.png" alt="" />
                </div>
                <star
                  v-else
                  :total_num="item['total_module'] ?? 0"
                  :finish_num="item['finish_model'] ?? 0"
                ></star>
              </div>
            </div></div
        ></swiper-slide>
      </swiper>
      <div
        class="swiper-button-prev"
        slot="button-prev"
        v-show="lessonList.length > 0"
        :style="{
          'background-image': `url(${require('@/assets/index/左箭头.png')})`,
          'background-size': 'contain'
        }"
      ></div>
      <div
        class="swiper-button-next"
        slot="button-next"
        v-show="lessonList.length > 0"
        :style="{
          'background-image': `url(${require('@/assets/index/右箭头.png')})`,
          'background-size': 'contain'
        }"
      ></div>
    </div>
    <div class="bigToast" v-if="show_dialog">
      <div class="mask"></div>
      <div class="toastWrap">
        <div class="cha"></div>
        <div class="toastContent" v-html="lessonListInfo.diag_text"></div>
        <div
          @click="close"
          class="cha"
          :style="{
            'background-image': `url(${require('@/assets/login/关闭按钮.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
import star from "@/components/Index/star";
import courseListApi from "@/api/course.js";
import { Toast } from "mint-ui";
export default {
  name: "lessonList",
  data() {
    return {
      page: 0,
      lessonList: [],
      title: "",
      swiperOption: {
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        }
      },
      reload: false,
      diag_text: "",
      lessonListInfo: {},
      show_dialog: false
    };
  },
  computed: {
    course_id() {
      return this.$route.query.course_id;
    }
  },
  components: { swiper, swiperSlide, star },
  methods: {
    back() {
      this.$router.push({ path: "/" });
    },
    goLessonInfo(item) {
      if (item["unlocked"]) {
        if (item["type"] == "final") {
          if (
            item["final_exam_status"] == "is_pass" ||
            item["final_exam_status"] == "not_pass"
          ) {
            this.$router.push({
              path: "/unitTestReport",
              query: {
                course_id: this.course_id,
                lesson_id: item.lesson_id,
                matchId: item["final_exam_id"],
                type: "final"
                // from: "final"
              }
            });
          } else {
            //期末测开始页
            this.$router.push({
              path: "/unitTest",
              query: {
                course_id: this.course_id,
                lesson_id: item.lesson_id,
                matchId: item["final_exam_id"],
                from: "lessonList"
              }
            });
          }
        } else {
          this.$router.push({
            path: "/lessonInfo",
            query: {
              lesson_id: item.lesson_id,
              course_id: this.course_id
            }
          });
        }
      } else {
        if (this.lessonListInfo.notify_type == 1) {
          Toast(this.lessonListInfo.toast_text);
        } else if (this.lessonListInfo.notify_type == 2) {
          this.show_dialog = true;
        }
      }
    },
    close() {
      this.show_dialog = false;
    }
  },
  mounted() {
    courseListApi
      .LessonListApi({ course_id: this.course_id })
      .then((res) => {
        if (res.status == 200) {
          this.lessonListInfo = res?.data ?? {};
          this.lessonList = res?.data?.data ?? [];
          this.title = res?.data["name"] ?? "";
          this.page = Math.ceil(this.lessonList.length / 4);
          console.log(this.lessonList);
          console.log(this.page, "page");
          this.reload = true;
        }
      })
      .catch((e) => {
        console.log(e);
      });
  }
};
</script>

<style scoped lang="less">
.lessonList {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  .title_wrap {
    height: 120px;
    line-height: 120px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40px 56px 0;
    .back {
      width: 120px;
      height: 120px;
      cursor: pointer;
    }
    .title {
      height: 120px;
      line-height: 120px;
      font-size: 56px;
      color: #ffffff;
      text-align: center;
    }
    .zhanwei {
      width: 120px;
      height: 120px;
    }
  }
  .content {
    // width: 1856px;
    // height: 1004px;
    margin: 188px auto;
    position: relative;

    .swiper-item {
      width: 1824px;
      height: 494px;

      margin: 0 auto;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      // align-content: space-between;
      .item {
        width: 426px;
        height: 482px;
        background-color: #F8FDFF;
        border-radius: 90px;
        margin-right: 40px;
        position: relative;
        box-shadow: 0 6px 12px 0 rgba(71, 97, 121, 0.5),0 -6px 0 0 #CACDE7 inset;
        cursor: pointer;
        .info {
          height: 327px;
          width: 463px;
          position: absolute;
          top: 172.5px;
          left: -18.5px;
        }
        .left_img_wrap {
          width: 386px;
          height: 206px;
          // border-radius: 50px;
          margin: 20px;
          position: relative;
          .left_img {
            width: 386px;
            height: 210px;
            border-radius: 80px 80px 0 0;
            overflow: hidden;
          }
          .evaluation {
            width: 179px;
            height: 64px;
            line-height: 64px;
            background-image: linear-gradient(90deg, #ff848a 0%, #ff5e65 100%);
            border-radius: 80px 0 40px 0;
            font-size: 28px;
            color: #ffffff;
            text-align: center;
            padding-left: 10px;
            box-sizing: border-box;
          }
          .mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 386px;
            height: 210px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 80px 80px 0 0;
            text-align: center;
            img {
              width: 82px;
              height: 88px;
              margin-top: 59px;
            }
          }
        }
        .lesson_index {
          font-size: 28px;
          color: #999999;
          text-align: center;
          padding-top: 86px;
          line-height: 36px;
        }
        .lesson_name {
          font-size: 40px;
          color: #333333;
          text-align: center;
          margin: 10px auto 0;
          width: 386px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: 48px;
        }
        .is_pass {
          width: 48px;
          height: 48px;
          margin: 36px auto;
          img {
            width: 48px;
            height: 48px;
          }
        }
      }
      .item:nth-of-type(4n + 0) {
        margin-right: 0;
      }
    }
  }
}

.swiper-button-next {
  width: 128px;
  height: 128px;

  right: 20px;
  top: 260px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 100;
}
.swiper-button-next::after {
  content: "";
}
.swiper-button-prev {
  width: 128px;
  height: 128px;

  left: 20px;
  top: 260px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 100;
}
.swiper-button-prev::after {
  content: "";
}
.bigToast {
  .mask {
    width: 2480px;
    height: 1536px;
    background-color: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
  }
}
.toastWrap {
  display: flex;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 11;
  // padding-top: 1000px;

  .toastContent {
    background: #ffffff;
    box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 30px 0 #ccfaff;
    border-radius: 50px;
    padding: 50px;
    width: 800px;
    height: 500px;
    margin: 0 20px;
    font-size: 56px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-y: scroll;
    box-sizing: border-box;
  }
  .cha {
    width: 96px;
    height: 96px;
    cursor: pointer;
  }
}
</style>

