<template>
  <div
    class="lessonInfo"
    :style="{
      'background-image': `url(${require('@/assets/index/首页背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="title_wrap">
      <div
        class="back"
        @click="back"
        :style="{
          'background-image': `url(${require('@/assets/index/back.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
    </div>
    <div class="content">
      <div class="lesson_left">
        <img src="@/assets/lessonInfo/课程封面大.png" alt class="left_img" />
        <p class="lesson_index jcyt400">第 {{ lesson_info["index"] }} 课</p>
        <p class="lesson_name jcyt600">{{ lesson_info["name"] }}</p>
      </div>
      <div class="lesson_right">
        <div
          v-for="(moduleItem, index) in moduleList"
          :key="index"
          class="lesson_item_wrap"
        >
          <div
            class="lesson_item"
            v-for="(contentItem, index) in moduleItem.data"
            :key="index"
            @click="goNext(contentItem)"
          >
            <img
              :src="
                contentItem['module_type'] == 'video'
                  ? require('@/assets/lessonInfo/课程视频.png')
                  : contentItem['module_type'] == 'homework'
                  ? require('@/assets/lessonInfo/课后练习.png')
                  : contentItem['module_type'] == 'unittest'
                  ? require('@/assets/lessonInfo/单元测评.png')
                  : contentItem['module_type'] == 'card'
                  ? require('@/assets/lessonInfo/今日卡片.png')
                  : require('@/assets/lessonInfo/AI对弈.png')
              "
              alt
              class="right_img"
            />
            <p class="right_title jcyt500">{{ contentItem.name }}</p>
            <div class="star" v-if="contentItem['module_type'] == 'unittest'">
              <lessonInfoStar
                :total_num="5"
                :finish_num="contentItem['star']"
              ></lessonInfoStar>
            </div>

            <p class="status jcyt400" v-if="contentItem['module_type'] != 'unittest'">
              {{
                contentItem["unlocked"] == false
                  ? "待解锁"
                  : contentItem["module_type"] == "card"
                  ? contentItem["is_finished"] == true
                    ? "已领取"
                    : "待领取"
                  : contentItem["is_finished"] == true
                  ? "已完成"
                  : "未完成"
              }}
            </p>
            <img
              v-if="contentItem['module_type'] != 'unittest'"
              :src="
                contentItem['unlocked'] == false
                  ? require('@/assets/lessonInfo/未解锁.png')
                  : contentItem['is_finished'] == true
                  ? require('@/assets/lessonInfo/完成.png')
                  : require('@/assets/lessonInfo/未完成.png')
              "
              alt
              class="status_img"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import courseListApi from "@/api/course.js";
import lessonInfoStar from "@/components/Index/lessonInfoStar";
import { Toast } from "mint-ui";
export default {
  name: "lessonInfo",
  data() {
    return {
      lesson_info: {},
      moduleList: []
    };
  },
  components: { lessonInfoStar },
  computed: {
    course_id() {
      return this.$route.query.course_id;
    },
    lesson_id() {
      return this.$route.query.lesson_id;
    }
  },
  methods: {
    back() {
      this.$router.push({
        path: "/lessonList",
        query: {
          course_id: this.course_id
        }
      });
    },

    getStatus(id) {
      courseListApi
        .getExamId({ group_id: id })
        .then((res) => {
          // Routes.goBack(context, UnitTestReportView(matchID: res.toString()));
        })
        .catchError((err) => {
          var _errText = err.toString();
          _errText = _errText.replaceAll("Exception: ", "");
          Toast(_errText);
        });
    },
    goNext(contentItem) {
      if (contentItem["unlocked"]) {
        if (contentItem["module_type"] == "video") {
          __bl.sum("课程视频");
          this.$router.push({
            path: "/video",
            query: {
              course_id: this.course_id,
              lesson_id: this.lesson_id,
              video_id: contentItem["content_id"].toString(),
              video_type: contentItem["content_type"]
            }
          });
          // Routes.navigateTo(context, Routes.lessonVideo, params: {
          //   "course_id": courseId,
          //   "lesson_id": lessonId,
          //   "video_id": contentItem['content_id'].toString(),
          //   "video_type": contentItem['content_type']
          // });
        } else if (contentItem["module_type"] == "homework") {
          __bl.sum("闯关答题");
          console.log("0000");
          if (contentItem["is_finished"] == false) {
            this.$router.push({
              path: "/exerciseDetail",
              query: {
                course_id: this.course_id,
                lesson_id: this.lesson_id,
                homework_id: contentItem["content_id"].toString(),
                question_index: "1"
              }
            });
          } else {
            this.$router.push({
              path: "/homeworkFinish",
              query: {
                course_id: this.course_id,
                lesson_id: this.lesson_id,
                homework_id: contentItem["content_id"].toString()
              }
            });
          }
        } else if (contentItem["module_type"] == "ai") {
          __bl.sum("课程中ai对弈");
          courseListApi.CheckGameOpenApi().then((res) => {
            if (res.data["enabled"] == true) {
              courseListApi
                .CreateAiExercise({
                  ai_stage_id: contentItem["content_id"],
                  lesson_id: parseInt(this.lesson_id),
                  course_id: parseInt(this.course_id)
                })
                .then((res) => {
                  this.$router.push({
                    path: "/AIgame",
                    query: {
                      game_id: res.data["game_id"],
                      course_id: this.course_id,
                      lesson_id: this.lesson_id,
                      ai_stage_id: contentItem["content_id"],
                      ai_type: contentItem["content_type"]
                    }
                  });
                });
            } else {
              Toast("对弈服务已关闭");
            }
          });
        } else if (contentItem["module_type"] == "card") {
          __bl.sum("今日卡片");
          // var params = {
          //   "course_id": courseId,
          //   "lesson_id": lessonId,
          //   'card_id': contentItem['content_id'].toString(),
          //   'index': lessonInfo['index'].toString()
          // };
          // Routes.navigateTo(context, Routes.card, params: params);
          this.$router.push({
            path: "/card",
            query: {
              course_id: this.course_id,
              lesson_id: this.lesson_id,
              card_id: contentItem["content_id"].toString(),
              lesson_index: this.lesson_info["index"].toString()
            }
          });
        } else if (contentItem["module_type"] == "unittest") {
          __bl.sum("单元测试");
          if (contentItem["is_finished"] == false) {
            // var params = {
            //   "course_id": courseId,
            //   "lesson_id": lessonId,
            //   'test_id': contentItem['content_id'].toString(),
            // };
            // Routes.navigateTo(context, Routes.unitTestStart, params: params);
            this.$router.push({
              path: "/unitTest",
              query: {
                course_id: this.course_id,
                lesson_id: this.lesson_id,
                matchId: contentItem["content_id"],
                from: "lessonInfo",
                type: "unittest"
              }
            });
          } else {
            this.$router.push({
              path: "/unitTestReport",
              query: {
                course_id: this.course_id,
                lesson_id: this.lesson_id,
                matchId: contentItem["content_id"],
                type: "unittest",
                from: "lessonInfo"
              }
            });
            // this.getStatus(contentItem["content_id"].toString());
          }
        }
      }
    }
  },
  mounted() {
    courseListApi
      .LessonInfoApi({
        course_id: this.course_id,
        lesson_id: this.lesson_id
      })
      .then((res) => {
        this.lesson_info = res.data ?? {};
        this.moduleList = res.data["data"] ?? [];
      });
  }
};
</script>

<style scoped lang="less">
.lessonInfo {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .title_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .back {
      width: 120px;
      height: 120px;
      cursor: pointer;
      position: absolute;
      top: 40px;
      left: 56px;
      z-index: 10;
    }
  }
  .content {
    // height: 828px;
    padding-top: 142px;
    box-sizing: border-box;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    .lesson_left {
      width: 740px;
      height: 828px;
      background-color: #fff;
      border-radius: 90px;
      box-shadow: rgba(0, 85, 136, .3) 0 5px 18px 0, #9ADDFF 0 -5px 7px 0 inset;
      .left_img {
        width: 668px;
        height: 360px;
        border-radius: 80px;
        margin: 36px 36px 0 36px;
      }
      .lesson_index {
        font-size: 36px;
        color: #999999;
        text-align: center;
        margin-top: 48px;
        line-height: 40px;
      }
      .lesson_name {
        font-size: 52px;
        color: #333333;
        text-align: center;
        margin-top: 24px;
        line-height: 60px;
      }
    }
    .lesson_right {
      height: 828px;
      overflow-y: scroll;
      margin-left: 22px;
      padding-bottom: 18px;
      .lesson_item_wrap {
        .lesson_item {
          margin: 0 18px;
          width: 832px;
          height: 180px;
          background-color: #fff;
          border-radius: 56px;
          box-shadow: rgba(0, 85, 136, .3) 0 5px 18px 0, #9ADDFF 0 -5px 7px 0 inset;
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-bottom: 36px;
          position: relative;
          cursor: pointer;
          .right_img {
            width: 108px;
            height: 108px;
            border-radius: 50%;
            margin: 36px 30px 36px 36px;
          }
          .right_title {
            width: 400px;
            font-size: 44px;
            color: #333333;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .status {
            font-size: 36px;
            color: #999999;
            margin-right: 24px;
          }
          .status_img {
            width: 64px;
            height: 64px;
          }

          .star {
            width: 232px;
            height: 40px;
          }
        }

        .lesson_item:after {
          content: "";
          position: absolute;
          display: block;
          bottom: -26px;
          left: 84px;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background-color: #ffffff;
        }
      }
      .lesson_item_wrap:nth-last-child(1) .lesson_item {
        // margin-bottom: 0;
        position: static;
      }
    }
    .lesson_right::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
