<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/course/星星背景.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div
      class="back"
      @click="goBack()"
      :style="{
        'background-image': `url(${require('@/assets/back/返回_白.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div class="main-box">
      <div class="center-box">
        <div class="center-container">
          <div v-if="cardDetail?.image_url" class="image-container">
            <img :src="cardDetail?.image_url" alt="" />
          </div>
          
        </div>
      </div>
      <div
        class="ribbon-container"
        :style="{
          'background-image': `url(${require('@/assets/course/彩带.png')})`,
          'background-size': '100% 100%',
        }"
      ></div>
    </div>
    <div class="dialog" v-if="visible">
      <div
        class="dialog-container"
        :style="{
          'background-image': `url(${require('@/assets/course/卡片弹窗2.png')})`,
          'background-size': '100% 100%',
        }"
      >
        <!-- <div class="close-icon" @click="close()"></div> -->
        <!-- <div class="dialog-title">{{ title }}</div> -->
        <div class="content">
          <p class="content-top-box jcyt600">恭喜你完成了今日学习！</p>
          <p class="content-center-box jcyt400">积土而为山，积水而为海</p>
          <div class="confirm-button" @click="confirmBtn">
            <img :src="require('@/assets/game/领取卡片按钮.png')" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
import courseApi from "@/api/course";
import { Indicator } from "mint-ui";
export default {
  name: "mineIndex",
  components: {},
  data() {
    return {
      apiDone: false,
      cardDetail: {},
      visible: false,
    };
  },
  created() {
    this.init();

    Indicator.open({
      //   text: "加载中...",
      //文字
      spinnerType: "fading-circle",
      //样式
    });
  },
  destroyed() {
    Indicator.close();
  },
  computed: {
    course_id() {
      return parseInt(this.$route.query.course_id);
    },
    lesson_id() {
      return parseInt(this.$route.query.lesson_id);
    },
    card_id() {
      return parseInt(this.$route.query.card_id);
    },
    lesson_index() {
      return parseInt(this.$route.query.lesson_index);
    },
  },
  methods: {
    init(query) {
      courseApi
        .GetCardInfo({
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          card_id: this.card_id,
        })
        .then((res) => {
          //   this.apiDone = true;
          if (res.data.image_url) {
            Indicator.close();
          }
          this.cardDetail = res.data;
          this.visible = true;
        });
    },
    goBack() {
      this.$router.push({
        path: "/lessonInfo",
        query: {
          lesson_id: this.lesson_id,
          course_id: this.course_id,
        },
      });
    },
    confirmBtn() {
      courseApi
        .UpdateCard({
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          card_id: this.card_id,
        })
        .then((res) => {
          this.visible = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 40px;
    left: 56px;
    border-radius: 50%;
    z-index: 9;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .center-box {
    width: 100%;
    height: calc(100vh - 142px);
    // height: 100%;
    // flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 142px;
    overflow-y: scroll;
    .center-container {
      margin-top: 142px;
      width: 873px;
      display: flex;
      box-sizing: border-box;
      align-items: center;
      justify-content: center;
      border-radius: 56px;
      background: #ffffff;
      box-shadow: 0 0px 50px 0px rgba(124, 143, 166, 0.08);
      padding: 17px;
      position: relative;
      z-index: 12;
      .image-container {
        width: 100%;
        height: 100%;
        border-radius: 42px;
        overflow: hidden;
      }
      img {
        width: 100%;
        height: auto;
      }
      //   padding: 80px;
    }
  }
  .ribbon-container {
    width: 1341px;
    height: 662px;
    position: absolute;
    top: 55px;
    z-index: 13;
  }
}
.fading-circle {
  color: rgba(100, 49, 191, 255);
}
.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 99;
  .dialog-container {
    width: 968px;
    height: 818px;
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 80px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    padding: 0 32px;
    .content {
      width: 100%;
      height: 400px;
      margin-top: 336px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .content-top-box {
        font-size: 48px;
        line-height: 56px;
        color: #333;
        margin-bottom: 20px;
      }
      .content-center-box {
        font-size: 32px;
        line-height: 40px;
        color: #999;
      }
    }
    .confirm-button {
      width: 400px;
      height: 116px;
      margin-top: 72px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
