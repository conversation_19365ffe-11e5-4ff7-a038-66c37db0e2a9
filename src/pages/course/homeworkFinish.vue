<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/game/下棋页面背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="main-box">
      <div class="center-box">
        <div
          class="center-container"
          :style="{
            'background-image': `url(${require('@/assets/course/答题完成背景PAD.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <div class="top-box">
            <div class="top-box-left">
              <div class="right-num-container jcyt500">
                答对：<span>{{ homeworkDetail?.right_numb || 0 }}</span
                >题
              </div>
              <span class="num-line"></span>
              <div class="wrong-num-container jcyt500">
                答错：<span>{{ homeworkDetail?.wrong_numb || 0 }}</span
                >题
              </div>
            </div>
            <div class="jcyt500">请点击题目查看解析</div>
          </div>
          <div class="list-container">
            <div class="list-box">
              <div
                class="status-box"
                v-for="(item, index) in homeworkDetail?.answer_data"
                v-bind:key="index"
                @click="selectHomework(index + 1)"
              >
                <img
                  :src="
                    item.status == 'is_right'
                      ? require('@/assets/course/勾.png')
                      : require('@/assets/course/哭.png')
                  "
                  alt=""
                />
                <span class="jcyt500">{{ index + 1 }}</span>
              </div>
            </div>
          </div>
          <div
            class="bottom-button-box"
            @click="confirmBtn"
            :style="{
              'background-image': `url(${require('@/assets/course/再试一次.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
          <div
            class="decorate-container"
            :style="{
              'background-image': `url(${require('@/assets/course/和服小喵.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
import courseApi from "@/api/course";
import { Indicator } from "mint-ui";
export default {
  name: "mineIndex",
  components: {},
  data() {
    return {
      apiDone: false,
      homeworkDetail: {}
    };
  },
  created() {
    // if (this.$route.query.length > 0) {
    //   let { courseID, lessonID, homeworkID } = this.$route.query;
    //   this.courseID = courseID;
    //   this.lessonID = lessonID;
    //   this.homeworkID = homeworkID;
    this.init();
    // }
    Indicator.open({
      //   text: "加载中...",
      //文字
      spinnerType: "fading-circle"
      //样式
    });
  },
  destroyed() {
    Indicator.close();
  },
  computed: {
    course_id() {
      return parseInt(this.$route.query.course_id);
    },
    lesson_id() {
      return parseInt(this.$route.query.lesson_id);
    },
    homework_id() {
      return parseInt(this.$route.query.homework_id);
    }
  },
  methods: {
    init() {
      courseApi
        .HomeworkInfo({
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          homework_id: this.homework_id
        })
        .then((res) => {
          this.apiDone = true;
          Indicator.close();

          this.homeworkDetail = res.data;
        });
    },
    goBack() {
      this.$router.push({
        path: "/lessonInfo",
        query: {
          lesson_id: this.lesson_id,
          course_id: this.course_id
        }
      });
    },
    selectHomework(questionIndex) {
      this.$router.push({
        path: "/exerciseDetail",
        query: {
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          homework_id: this.homework_id,
          question_index: questionIndex.toString(),
          from: "homework"
        }
      });
    },
    confirmBtn() {
      courseApi
        .UpdateHomework({
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          homework_id: this.homework_id
        })
        .then((res) => {});
      this.$router.push({
        path: "/exerciseDetail",
        query: {
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          homework_id: this.homework_id,
          question_index: "1",
          type: "try_again"
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 128px;
    height: 128px;
    position: absolute;
    top: 40px;
    left: 56px;
    border-radius: 50%;
    z-index: 9;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .center-box {
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .decorate-container {
      width: 272px;
      height: 384px;
      right: -82px;
      bottom: -25px;
      position: absolute;
    }

    .center-container {
      width: 1728px;
      height: 1020px;
      display: flex;
      position: relative;
      box-sizing: border-box;
      flex-direction: column;
      align-items: center;
      border-radius: 100px;
      box-shadow: 0 0px 50px 0px rgba(124, 143, 166, 0.08);
      padding: 200px 136px 40px;
      position: relative;
      .top-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 32px;
        color: #999;
        margin-bottom: 64px;
        .top-box-left {
          min-width: 356px;
          height: 40px;
          line-height: 40px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .right-num-container {
            span {
              color: #00c26e;
            }
          }
          .num-line {
            height: 30px;
            width: 2px;
            background: #919996;
            display: inline-block;
            margin: 0 15.5px;
          }
          .wrong-num-container {
            text-align: end;
            span {
              color: #ff6461;
            }
          }
        }
      }
      .list-container {
        width: 100%;
        height: 408px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        .list-box {
          width: 100%;
          // overflow: hidden;
          display: flex;
          flex-wrap: wrap;
          // float: left;
          .status-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            width: 120px;
            height: 184px;
            margin-right: 65px;
            margin-bottom: 40px;
            img {
              width: 100%;
              height: auto;
              height: 120px;
            }
            span {
              color: #666666;
              font-size: 40px;
              line-height: 48px;
            }
          }
          .status-box:nth-of-type(8n) {
            margin-right: 0;
          }
        }
      }
      .list-container::-webkit-scrollbar {
        width: 0px;
      }
      .bottom-button-box {
        width: 432px;
        height: 152px;
        line-height: 120px;
        border-radius: 60px;
        margin-top: 56px;
      }
    }
  }
}
.fading-circle {
  color: rgba(100, 49, 191, 255);
}
</style>
