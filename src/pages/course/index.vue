<template>
  <div class="courseWrap">
    <div class="test"></div>
    <img src="@/assets/logo.png" />
    <p>首页课程 姓名：{{ userInfo.name }}</p>
    <button @click="goQuestion">题库</button>
    <button @click="goUnit">单元测评</button>
    <button @click="goLogin">去登录</button>
  </div>
</template>

<script>
import userApi from "@/api/user";
export default {
  name: "courseWrap",
  data() {
    return {
      userInfo: {}
    };
  },
  created() {
    userApi.GetUserInfo().then((res) => {
      this.$storage.$setStroage("user_token", res.data.token);
      this.userInfo = res.data;
    });
  },

  methods: {
    goQuestion() {
      this.$router.push({
        path: "/questionBank"
      });
    },
    goUnit() {
      this.$router.push({
        path: "/unitTest"
      });
    },
    goLogin() {
      this.$router.push({
        path: "/smsLogin"
      });
    }
  }
};
</script>

<style scoped lang="less">
.test {
  width: 500px;
  height: 500px;
}
img {
  width: 200px;
  height: 200px;
}
p {
  font-size: 30px;
}
</style>
