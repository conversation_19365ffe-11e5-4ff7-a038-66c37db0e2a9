<template>
  <div id="video">
    <link
      v-if="is_https"
      rel="stylesheet"
      href="https://g.alicdn.com/de/prismplayer/2.8.2/skins/default/aliplayer-min.css"
    />
    <div class="stages-video">
      <div class="head-main">
        <div class="back" @click="go_back"></div>
        <div class="title jcyt600">
          <p>
            {{ videoInfo.name }}
          </p>
        </div>
      </div>

      <div class="stages-video-main">
        <div class="video-box">
          <div class="video-content">
            <div class="video">
              <!-- <ali-player
                v-if="reload"
                :source="videoInfo.video_url"
                id="id"
                @ready="ready"
                @play="onPlayerPlay($event)"
                @pause="onPlayerPause($event)"
                @ended="ended"
                @timeupdate="onTimeupdate($event)"
                controlBarVisibility="hover"
                height="100%"
              /> -->
              <vue-aliplayer-v2
                :source="videoInfo.video_url"
                ref="VueAliplayerV2"
                :options="options"
                style="height: 100%"
                @ready="ready"
                @play="onPlayerPlay($event)"
                @pause="onPlayerPause($event)"
                @ended="ended"
                @timeupdate="onTimeupdate($event)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import courseApi from "@/api/course";
import { Base64 } from "base64-js";
import VueAliplayerV2 from "vue-aliplayer-v2";

export default {
  name: "video",
  components: {
    VueAliplayerV2,
  },
  data() {
    return {
      video_time: "",
      is_https: false,
      content_detail: [],
      webSocket: "",
      options: {
        currentPage: 0,
        tracking: true,
        thresholdDistance: 100,
        thresholdTime: 300,
        infinite: 1,
        slidesToScroll: 1,
        loop: false,
      },
      reload: false,
      current_time: "",
      token: "",
      videoInfo: [],
      apiDone: true,
    };
  },
  created() {
    this.init();
    this.reload = false;
  },

  computed: {
    lesson_index() {
      return parseInt(this.$route.query.lesson_index);
    },
    lesson_id() {
      return parseInt(this.$route.query.lesson_id);
    },
    course_id() {
      return parseInt(this.$route.query.course_id);
    },
    video_id() {
      return parseInt(this.$route.query.video_id);
    },
    video_type() {
      return this.$route.query.video_type;
    },
  },
  watch: {
    source: {
      handler(new_obj) {
        if (new_obj instanceof Object && JSON.stringify(new_obj) !== "{}") {
          this.reload = false;
          this.$nextTick(() => {
            this.reload = true;
            // this.$loading.close();
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    init() {
      courseApi
        .GetVideoInfo({ video_id: this.video_id, video_type: this.video_type })
        .then((res) => {
          this.videoInfo = res.data || [];
          this.apiDone = true;
          courseApi
            .UpdateVideoHistory({
              course_id: this.course_id,
              lesson_id: this.lesson_id,
              duration: 0,
              video_id: this.video_id,
              video_type: this.video_type,
            })
            .then(() => {});
        });
    },
    go_back() {
      // if (this.$route.params.type === "lesson") {
      this.$router.push({
        path: `/lessonInfo`,
        query: this.$route.query,
      });
      // } else {
      //   this.$router.push({
      //     path: `/stages/${this.commodity_id}/${this.course_id}/${this.lesson_id}/${this.lesson_index}`,
      //     query: this.$route.query,
      //   });
      // }
    },
    con_base64_to_array(exercise_list_params) {
      return JSON.parse(btoa(encodeURI(exercise_list_params)));
    },
    ended() {
      // var status_obj = {
      //   duration: this.video_time,
      // };
      // this.content_detail.push(status_obj);
    },
    onPlayerPlay(player) {
      this.video_time = player._duration;
      // this.initWebSocket();
    },
    // 暂停
    onPlayerPause() {
      // this.webSocket.close();
    },
    change_video(key) {
      this.onPlayerPause();
      this.$router.push({
        path: `/cwc/video/${this.commodity_id}/${this.course_id}/${this.videoInfo}/${this.lesson_id}/${this.lesson_index}/${key}`,
        query: this.$route.query,
      });
      this.reload = false;
      // this.$store.dispatch("getVideoList", {
      //   lesson: this.lesson_id,
      //   video_list: Base64.decode(this.videoInfo),
      // });
    },
    initWebSocket() {
      let wsProtocol = "";
      if (document.location.protocol === "https:") {
        wsProtocol = "wss://" + document.location.host;
      } else {
        wsProtocol = "ws://" + document.location.host;
      }
      if (document.domain === "localhost") {
        wsProtocol =
          "ws://nwp-test.cca5f1676eabb4dfcbb3079b6f2491f5b.cn-zhangjiakou.alicontainer.com";
      }
      let ws_uri = `${wsProtocol}/ws/video/playvideo/?${this.lesson_id}&&${
        this.con_base64_to_array(this.videoInfo)[this.video_id]
      }&&${this.token}`;
      this.webSocket = new WebSocket(ws_uri);
      this.webSocket.onopen = this.webSocketOnOpen;
      this.webSocket.onmessage = this.webSocketOnMessage;
      this.webSocket.onerror = this.webSocketOnError;
    },
    webSocketOnOpen: function () {},
    webSocketOnError: function () {
      //连接建立失败重连
      // tool.Sleep(5000).then(() => {
      //   this.initWebSocket();
      // });
    },
    webSocketOnMessage: function () {},
    webSocketSend: function () {
      //数据发送
    },
    // 获取播放时间
    onTimeupdate(e) {
      this.current_time = e._TimeUpdateStamp;
    },
    ready(e) {
      // e.setVolume(0.5);
      // e.pause();
    },
    // 关闭浏览器时获取当前时间
  },
  mounted() {
    this.token = this.$storage.$getStroage("user_token");
    // this.$loading.show();
    // 监听浏览器关闭执行 和 关闭标签页
    this.is_https = document.location.protocol === "https:";

    this.is_https = true;
  },
  beforeDestroy() {
    // this.$loading.close();
  },
  beforeRouteLeave(to, from, next) {
    if (this.webSocket.readyState === 1) {
      // this.webSocket.close();
      next();
    } else {
      next();
    }
  },
};
</script>
<style scoped lang="less">
#video {
  width: 100vw;
  height: 100vh;
  .stages-video {
    height: 100vh;
    background-color: #1d1d1d;
    .head-main {
      width: 100%;
      height: 192px;
      overflow: auto;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .back {
        width: 112px;
        height: 112px;
        margin-left: 50px;
        margin-right: 48px;
        border-radius: 50%;
        background: url("@/assets/back/视频返回.png") center no-repeat;
        background-size: cover;
      }
      .title {
        font-size: 48px;
        color: #fff;
      }
    }
    .stages-video-main {
      width: 100%;
      height: calc(100vh - 192px);
      display: flex;
      justify-content: center;
      .video-box {
        width: 100%;
        height: calc(100vh - 192px);
        display: flex;
        justify-content: center;
        align-content: center;
        .video-content {
          width: 100%;
          height: auto;
          background: #98c6c7;
          box-shadow: 0 0 50px 0 rgba(0, 0, 0, 0.15);
          border-radius: 20px;
          z-index: 9;
          .video {
            width: 100%;
            height: 100%;
            .prism-player {
              border-radius: 13px;
            }
            /deep/.prism-big-play-btn {
              left: 5vw !important;
              bottom: 5vw !important;
              width: 13vw;
              height: 13vw;
              .outter {
                width: 13vw;
                height: 13vw;
              }
            }
          }
        }
      }
    }
  }
}
</style>
