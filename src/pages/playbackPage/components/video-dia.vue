<template>
  <div class="bg" v-if="show">
    <div class="sz">
      <div class="video">
        <video
          :src="url"
          controls="controls"
          controlslist="nodownload noremoteplayback"
          :disablePictureInPicture="true"
          id="videos"
        ></video>
        <!-- <vue-aliplayer-v2
          :source="url"
          ref="VueAliplayerV2"
          :options="options"
          style="height: 100%" -->
        <!-- /> -->
        <!-- @ready="ready"
                @play="onPlayerPlay($event)"
                @pause="onPlayerPause($event)"
                @ended="ended"
                @timeupdate="onTimeupdate($event)" -->
      </div>
    </div>
    <div
      class="close"
      :style="{
        'background-image': `url(${require('@/assets/living/close.png')})`,
      }"
      @click="getClose"
    ></div>
  </div>
</template>

<script>
import moment from "moment";
import storage from "@/public/storage.js";
import VueAliplayerV2 from "vue-aliplayer-v2";
import { Toast } from "mint-ui";
export default {
  name: "videoDia",
  props: {},
  components: {
    VueAliplayerV2,
  },
  data() {
    return {
      url: "",
      show: false,
      room_id: "",
      end_time: "",
      start_time: "",
      options: {
        currentPage: 0,
        tracking: true,
        thresholdDistance: 100,
        thresholdTime: 300,
        infinite: 1,
        slidesToScroll: 1,
        loop: false,
      },
    };
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    onShow(url, room_id) {
      this.url = url;
      this.room_id = room_id;
      this.show = true;
      this.start_time = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      this.$nextTick(()=>{
      var video = document.getElementById('videos');
    
    video.addEventListener('error', function(event) {
      console.log(event)
      var error = event.target.error;
      let errorDiv;
      console.log(error.code)
      switch (error.code) {
        case error.MEDIA_ERR_ABORTED:
          errorDiv = '视频加载被中止！';
          break;
        case error.MEDIA_ERR_NETWORK:
          errorDiv = '网络错误导致视频加载失败！';
          break;
        case error.MEDIA_ERR_DECODE:
          errorDiv = '视频解码失败！';
          break;
        case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorDiv = '无法加载视频';
          break;
        default:
          errorDiv = '视频编码异常';
          break;
      }
      Toast(errorDiv+'(' + error.code + ')');
    });
    })
    },
    getClose() {
      this.show = false;
      this.end_time = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      this.$emit("getPlaybackStatistics", {
        start_time: this.start_time,
        end_time: this.end_time,
        room_id: this.room_id,
        hi_go_id: storage.$getStroage("userId"),
      });
    }
  },
  created() {},
  mounted() {
    
  },
};
</script> 

<style lang='scss' scoped>
.bg {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  .sz {
    width: 80.28125vw;
    height: 37.707031vw;
    position: relative;
    top: 14.648438vw;
    left: 8.734375vw;
    border-radius: 0.732422vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    justify-content: space-evenly;
    .video {
      width: 100%;
      height: 100%;
      ::v-deep .prism-player {
        border-radius: 13px;
      }

      video {
        width: 100%;
        height: 100%;
      }
    }
  }
  .close {
    width: 4.882813vw;
    height: 4.882813vw;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: absolute;
    top: 15%;
    left: 91%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
::v-deep video::-internal-media-controls-overflow-button {
  display: none !important;
}
video::-webkit-media-controls {
  overflow: hidden !important;
}
video::-webkit-media-controls-enclosure {
  width: calc(100% + 32px);
  margin-left: auto;
}
.prism-big-play-btn {
  left: 5vw !important;
  bottom: 5vw !important;
  width: 13vw;
  height: 13vw;
  .outter {
    width: 13vw;
    height: 13vw;
  }
}
::v-deep .prism-big-play-btn {
  display: none !important;
}
</style>
