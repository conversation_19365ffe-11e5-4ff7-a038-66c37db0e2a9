<template>
  <div
    class="playback"
    :style="{
      'background-image': `url(${require('@/assets/living/wecom-temp-923039-5f08c79db245781a6543dd1225158871.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="title_row">
      <div
        class="back"
        :style="{
          'background-image': `url(${require('@/assets/living/closebule.png')})`,
          'background-size': '100% 100%',
        }"
        @click="goBack"
      ></div>
    </div>
    <div
      class="room"
      :style="{
        'background-image': `url(${require('@/assets/living/box.png')})`,
        'background-size': '100% 100%',
      }"
    >
      <div class="stuentInfo">
        <div class="info">
          <div class="avatar">
            <img :src="userInfo.avatar" alt="" />
          </div>
          <div class="title_info">
            <div class="name">{{ userInfo.name }}</div>
            <div class="score">等级：{{ userInfo.nwp_level_name }}</div>
          </div>
        </div>
        <div class="level">
          <img
            src="@/assets/living/xx.png"
            alt=""
            v-for="(item, index) in classInfo.student_level"
            :key="index"
          />
        </div>
      </div>
      <!-- <img src="@/assets/living/go.png" alt="" class="go" />
      <img src="@/assets/living/go.png" alt="" class="go" style="left: 79.48828vw" /> -->
      <div class="reports">
        <div class="active">
          <img src="@/assets/living/action.png" alt="" class="activeImg" />
          <div class="left">
            <div class="progress">
              <div
                class="pros"
                :style="{
                  'background-image': `url(${require('@/assets/living/bingbug.png')})`,
                  'background-size': '100% 100%',
                }"
              >
                <el-progress
                  type="circle"
                  :percentage="percentage(classInfo.achievement.question_right_ratio)"
                  stroke-linecap="square"
                  :show-text="false"
                  :stroke-width="10"
                  define-back-color="#2EA7FF"
                  color="#5DD04C"
                  class="prog"
                >
                </el-progress>
                <div class="circleCenter">
                  <div>{{ percentage(classInfo.achievement.question_right_ratio) }}%</div>
                  <span> 答题正确率 </span>
                </div>
              </div>
            </div>
            <div class="teacherInfo">
              <div class="numInfo">
                <!-- 教师发题数量 -->
                <div class="img yle" style="background: #ffb11734">
                  <img src="@/assets/living/yle.png" alt="" />
                </div>

                <div class="num">
                  <span>{{ classInfo.achievement.question_total }}</span>
                  个
                  <div class="title">教师发题数量</div>
                </div>
              </div>
              <div class="numInfo">
                <!-- 学生答题数量 -->
                <div class="img bul" style="background: #2ea7ff33">
                  <img src="@/assets/living/bul.png" alt="" />
                </div>

                <div class="num">
                  <span>{{ classInfo.achievement.question_join }}</span
                  >个
                  <div class="title">学生答题数量</div>
                </div>
              </div>
              <div class="numInfo">
                <!-- 学生答题正确率 -->
                <div class="img gre" style="background: #5dd04c33">
                  <img src="@/assets/living/gree.png" alt="" />
                </div>

                <div class="num">
                  <span>{{ classInfo.achievement.question_right }}</span
                  >个
                  <div class="title">学生答题正确数量</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="active Attendance">
          <img src="@/assets/living/apptiv.png" alt="" class="activeImg" />
          <div class="progress">
            <div class="progress_item">
              <el-progress
                type="circle"
                :percentage="percentage(classInfo.attendance.total_ratio)"
                stroke-linecap="square"
                :show-text="false"
                :stroke-width="10"
                define-back-color="#D7D7D7"
                color="#5DD04C"
                class="prog"
              >
              </el-progress>
              <div class="circleCenter">
                <div>{{ percentage(classInfo.attendance.total_ratio) }}%</div>
              </div>
              <div class="progress_title">出勤{{ classInfo.attendance.total }}次</div>
            </div>
            <div class="progress_item">
              <el-progress
                type="circle"
                :percentage="percentage(classInfo.attendance.leave_early_ratio)"
                stroke-linecap="square"
                :show-text="false"
                :stroke-width="10"
                define-back-color="#D7D7D7"
                color="#FFBD39"
                class="prog"
              >
              </el-progress>
              <div class="circleCenter">
                <div>{{ percentage(classInfo.attendance.leave_early_ratio) }}%</div>
              </div>
              <div class="progress_title">
                早退{{ classInfo.attendance.leave_early }}次
              </div>
            </div>
            <div class="progress_item">
              <el-progress
                type="circle"
                :percentage="percentage(classInfo.attendance.be_late_ratio)"
                stroke-linecap="square"
                :show-text="false"
                :stroke-width="10"
                define-back-color="#D7D7D7"
                color="#FF6939"
                class="prog"
              >
              </el-progress>
              <div class="circleCenter">
                <div>{{ percentage(classInfo.attendance.be_late_ratio) }}%</div>
              </div>
              <div class="progress_title">迟到{{ classInfo.attendance.be_late }}次</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import liveApi from "@/api/living.js";
import userApi from "@/api/user";
export default {
  name: "appraise",
  props: {},
  components: {},
  data() {
    return {
      classInfo: {
        student_level: 3,
        achievement: {
          question_total: 0,
          question_join: 0,
          question_right: 0,
          question_right_ratio: 0,
        },
        attendance: {
          total: 0,
          total_ratio: 0,
          leave_early: 0,
          leave_early_ratio: 0,
          be_late: 0,
          be_late_ratio: 0,
        },
      },
      userInfo: {},
    };
  },
  computed: {
    // 转换百分比 不保留小数 四舍五入

    percentage() {
      return (num) => {
        // 转换为百分比
        if (num === 0) {
          return 0;
        }
        return Number((num * 100).toFixed(0));
      };
    },
  },
  // 侦听器
  watch: {},
  methods: {
    goBack() {
      this.$router.push({
        path: "/",
        query: {
          defaultIndex: 1,
          currentIndex: 0,
        },
      });
    },
    getUserInfo() {
      userApi.GetUserInfo().then((res) => {
        if (res.status == 200) {
          this.$storage.$setStroage("user_token", res.data.token);
          this.userInfo = res.data;
          if (res.data["has_user_info"] == false) {
            this.$router.push("/information");
          }
        }
      });
    },
  },
  async mounted() {
    this.getUserInfo();
    let res = await liveApi.StudentLiveCourseReport({
      room_id: Number(this.$route.query.room_id),
    });
    this.classInfo = res.data.data ?? {};
  },
};
</script>

<style lang="scss" scoped>
.playback {
  width: 100vw;
  height: 100vh;
  //   overflow: hidden;
  .title_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 50px;
    // margin-top: 80px;

    .back {
      width: 128px;
      height: 128px;
      margin: 0 0 0 50px;
      cursor: pointer;
    }
  }
  .room {
    // display: flex;
    // flex-direction: column;
    // justify-content: center;
    // align-items: center;
    width: 1648px;
    // height: 100%;
    margin: 0 auto;
    padding: 130px 80px 40px 80px;
    position: relative;
    p {
      font-size: 60px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 50px;
    }
    .stuentInfo {
      // width: 1408px;
      height: 125px;
      display: flex;
      // align-items: center;
      background: #ffffff;
      border-radius: 60px;
      align-items: center;
      padding: 20px;
      justify-content: space-between;

      .info {
        display: flex;
        border-right: 0.5px solid #e7ebef;
        width: 65%;
        align-items: center;
        .avatar {
          width: 130px;
          height: 120px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 60px;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .title_info {
          .name {
            font-size: 30px;
            font-family: PingFang SC;
            font-weight: bold;
            color: #333333;
            margin-top: 20px;
          }
          .score {
            background: #e4f6ff;
            width: 180px;
            height: 44px;
            border-radius: 50px;
            font-size: 20px;
            text-align: center;
            color: #00b0ff;
            font-weight: 500;
            margin-top: 5px;
            line-height: 2.5vw;
          }
        }
      }
      .level {
        width: 50%;
        display: flex;
        justify-content: center;
        img {
          width: 106px;
          height: 106px;
          margin-left: 50px;
        }
      }
    }
    .go {
      position: absolute;
      top: 25.976563vw;
      left: 8.488281vw;
      width: 28px;
      height: 104px;
      z-index: 999;
    }
    .reports {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .active {
      height: 388px;
      background: #ffffff;
      width: 848px;
      // display: flex;
      // align-items: center;
      background: #ffffff;
      border-radius: 60px;
      align-items: center;
      padding: 50px;
      justify-content: space-between;
      margin-top: 30px;
      position: relative;
      margin-bottom: 30px;
      display: inline-block;
      .activeImg {
        width: 332px;
        height: 85px;
        position: absolute;
        top: -0.6vw;
        left: 13vw;
      }
      .left {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        margin-top: 90px;
        .progress {
          width: 50%;
        }
      }
      ::v-deep .progress {
        height: 300px;
        // margin-bottom: 20px;
        position: relative;
        .pros {
          width: 280px !important;
          height: 280px !important;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .prog {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .el-progress-circle {
          width: 180px !important;
          height: 180px !important;
        }
        .circleCenter {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          div {
            font-size: 35px;
            color: #4e4e4e;
            font-weight: 600;
            margin-bottom: 5px;
          }
          span {
            font-size: 20px;
            color: #999999;
            text-align: center;
            font-weight: 600;
            display: inline-block;
            width: 100%;
          }
        }
      }
      .teacherInfo {
        // display: flex;
        // justify-content: space-around;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .numInfo {
          text-align: center;
          display: flex;
          flex-direction: row;
          .img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: #2ea7ff33;
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin: 0 auto;
            img {
              width: 90px;
              height: 90px;
            }
          }
          .num {
            font-size: 20px;
            width: 200px;
          }
        }
      }
      .teacherInfo {
        // display: flex;
        // justify-content: space-around;
        .numInfo {
          text-align: center;
          margin-top: 30px;
          .img {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #2ea7ff33;
            display: flex;
            align-items: center;
            justify-content: space-around;

            img {
              width: 50px;
              height: 50px;
            }
          }
          .num {
            font-size: 20px;
            span {
              font-size: 30px;
              font-weight: 500;
            }
          }
          .title {
            font-size: 20px;
            color: #999999;
            font-weight: 400;
          }
        }
      }
    }
    .Attendance {
      // height: 688px;
      width: 504px;
      .activeImg {
        left: 5vw;
      }
      .progress {
        height: 100%;
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        align-items: center;
        ::v-deep .progress_item {
          position: relative;
          width: 180px;
          height: 180px;
          .el-progress-circle {
            width: 140px !important;
            height: 140px !important;
          }
          .circleCenter {
            div {
              font-size: 36px !important;
              font-weight: 700 !important;
            }
          }
          .progress_title {
            position: absolute;
            bottom: -53px;
            width: 100%;
            text-align: center;
            font-size: 28px;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
