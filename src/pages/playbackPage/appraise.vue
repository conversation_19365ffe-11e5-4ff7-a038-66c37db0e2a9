<template>
  <div
    class="playback"
    :style="{
      'background-image': `url(${require('@/assets/living/lu.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="title_row">
      <div
        class="back"
        :style="{
          'background-image': `url(${require('@/assets/living/closebule.png')})`,
          'background-size': '100% 100%',
        }"
        @click="goBack"
      ></div>
    </div>
    <div class="com-appraise" :style="{
      'background-image': `url(${require('@/assets/living/appraise.png')})`,
      'background-size': '100% 100%',
    }">
    <div class="header">
      <!-- <span>评价</span> -->
      <!-- <div class="close">×</div> -->
    </div>
    <div class="content">
      <div class="appraise">
        <p class="star" style="padding-top: 0; padding-bottom: 0; height: 10px">
          请对教师授课做出评价
        </p>
        <div class="star" style="padding-top: 0">
          <span>评分</span>
          <span class="left_content">
            <el-rate v-model="classInfo.level" disabled></el-rate>
          </span>
        </div>
        <div class="star text" style="padding-top: 0">
          <span>评语</span>
          <!-- <textarea
            runat="server"
            id="textarea"
            placeholder=""
            class="left_content"
            v-model="text"
           maxlength="300"
          ></textarea> -->
          <ty-textarea
            :value.sync="classInfo.memo"
            placeholder="请在此处输入内容"
            class="textarea left_content"
            :maxlength="300"
            v-if="classShow"
            :disabled="true"
          ></ty-textarea>
        </div>
      </div>
    </div>
  </div>
    <!-- <div class="room">
      <p>
        <span>评分：</span>
        <el-rate
          disabled
          v-model="classInfo.level"
          style="display: inline-block"
        ></el-rate>
      </p>
      <p class="content"><span>评语：</span>{{ classInfo.memo ?? 暂无 }}</p>
    </div> -->
  </div>
</template>

<script>
import textarea from "@/component/counter-textarea";
import liveApi from "@/api/living.js";
export default {
  name: "appraise",
  props: {},
  components: {
    "ty-textarea": textarea,
  },
  data() {
    return {
      classInfo: {},
      classShow:false
    };
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    goBack() {
      this.$router.go(-1);
    },
  },
  async mounted() {
    let res = await liveApi.StudentEvaluationInfo({ room_id: this.$route.query.room_id });
    this.classInfo = res.data.data ?? {};
    this.classShow = true;
  },
};
</script>

<style lang="scss" scoped>
.playback {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .title_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 80px;

    .back {
      width: 128px;
      height: 128px;
      margin: 0 0 0 50px;
      cursor: pointer;
    }
  }
  .room {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 200px;
    width: 100%;
    p {
      font-size: 60px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 50px;
      width: 1200px;
    }
    .content {
      height: 500px;
      overflow: auto;
    }
  }
}
.com-appraise {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
    height: 700px;
//   background: #fff;
  color: #435588;
  font-size: 30px;
  font-weight: 800;
  z-index: 99;
  border-radius: 20px;
  font-family: "JiangChengYuanTi";
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px;
    height: 40px;
    // border-bottom: 0.2px solid #ccc;
    .close {
      color: #999;
      cursor: pointer;
    }
  }
  .content {
    padding: 80px;
    .appraise {
      .star {
        display: flex;
        // justify-content: flex-start;
        // align-items: center;
        padding: 40px;
        // height: 130px;
      }
      .left_content {
        margin-left: 20px;
      }
      .text {
        // height: 400px;
        .textarea {
          width: 80%;
          height: 180px;
          border: 1px solid #435588;
          border-radius: 24px;
          padding:10px;
          //   border: none;
          //   outline: none;
          //   resize: none;
          //   background: aliceblue;
        }
      }
    }
    .but {
      display: flex;
    //   justify-content: flex-end;
    justify-content: center;
      /* line-height: 36px; */
    //   margin-top: 52px;
      margin-bottom: 52px;
      .submit {
        margin-top: 10px;
        // height: 60px;
        // width: 100px;
        line-height: 40px;
        text-align: center;
        // background: rgb(99, 117, 223);
        color: #fff;
        border-radius: 5px;
        img{
            width: 264px;
            height: 98px;
        }
      }
      .clo {
        // background: #ccc;
        margin-left: 20px;
      }
    }
  }
}
::v-deep .el-rate {
  height: 50px;
  .el-rate__icon {
    font-size: 50px;
  }
}
</style>
