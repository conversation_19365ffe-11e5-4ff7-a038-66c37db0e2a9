<template>
  <div
    class="playback"
    :style="{
      'background-image': `url(${require('@/assets/living/lu.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="title_row">
      <div
        class="back"
        :style="{
          'background-image': `url(${require('@/assets/living/closebule.png')})`,
          'background-size': '100% 100%',
        }"
        @click="goBack"
      ></div>
    </div>
    <div class="playback_body">
      <div class="playback_now">
        <div
          class="search"
          :style="{
            'background-image': `url(${require('@/assets/living/bgseach.png')})`,
            'background-size': '100% 100%',
          }"
        >
          <div class="search_input">
            <div class="search_title">直播名称：</div>
            <!-- <input type="text" placeholder="请输入直播名称" /> -->
            <Search v-model="pageInatin.live_course_name"></Search>
          </div>
          <div class="search_input">
            <div class="search_title">直播时间：</div>
            <!-- <input type="text" placeholder="请输入直播名称" /> -->
            <el-date-picker
              v-model="pageInatin.live_start_time"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
            >
            </el-date-picker>
          </div>
          <div class="search_btn" @click="getList">
            <img src="@/assets/living/Group 326.png" alt="" />
          </div>
          <div class="search_btn" @click="reset">
            <img src="@/assets/living/Group 325.png" alt="" />
          </div>
        </div>
        <div class="playback_list" v-if="playbackList.length > 0">
          <div
            class="playback_item"
            v-for="(item, index) in playbackList"
            :key="index"
            :style="{
              'background-image': `url(${require('@/assets/living/listbg.png')})`,
              'background-size': '100% 100%',
            }"
          >
            <div class="playback_body">
              <div class="playback_type">
                <div class="playback_title">直播名称</div>
                <div class="playback_data">
                  {{ item.name }}
                </div>
              </div>
              <div class="playback_type">
                <div class="playback_title">直播日期</div>
                <div class="playback_data">
                  {{ item.start_time }}
                </div>
              </div>
              <div class="playback_type">
                <div class="playback_title">任课老师</div>
                <div class="playback_data">
                  {{ item.teacher_name }}
                </div>
              </div>
              <div class="playback_type">
                <div class="playback_title">点击次数</div>
                <div class="playback_data">
                  {{ item.playback_num }}
                </div>
              </div>
            </div>
            <div class="but">
              <img
                src="@/assets/living/back.png"
                alt=""
                @click="handPlayback(item.url, item.room_id)"
              />
              <!-- v-if="item.is_evaluate" -->
              <img
                src="@/assets/living/classroom.png"
                alt=""
                @click="handClassroom(item)"
              />
              <img
                src="@/assets/living/kehoued.png"
                alt=""
                @click="getAppraise(item)"
                v-if="item.can_evaluate"
              />
              <img
                src="@/assets/living/kehou.png"
                alt=""
                v-else
              />
             
            </div>
          </div>
        </div>
        <div class="playback_now now" style="top:56%" v-else>
        <img src="@/assets/living/wu.png" alt="" />
      </div>
      </div>
      
    </div>
    <videoDia ref="videoDia" @getPlaybackStatistics="getPlaybackStatistics" />
    <comAppraise ref="comAppraise" @submit="submit" />
  </div>
</template>

<script>
import liveApi from "@/api/living.js";
import moment from "moment";
import { Toast, Search, Indicator } from "mint-ui";
import videoDia from "./components/video-dia.vue";
import comAppraise from "@/components/com-appraise";
export default {
  name: "playbackPage",
  props: {},
  components: {
    videoDia,
    comAppraise,
    Search,
  },
  data() {
    return {
      playbackList: [],
      pageInatin: {
        live_start_time: "",
        live_course_name: "",
      },
      room_id: "",
    };
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    goBack() {
      this.$router.push({
        path: "/",
        query: {
          defaultIndex: 1,
          currentIndex: 0,
        },
      });
    },
    handPlayback(url, room_id) {
      if (!url) {
        Toast("暂无回放");
        return;
      }
      this.$refs.videoDia.onShow(url, room_id);
    },
    getList() {
      // const today = moment().format("YYYY-MM-DD");
      // this.pageInatin.start_time = today;
      // this.pageInatin.end_time = today;
      Indicator.open();
      liveApi
        .PlaybackList({
          ...this.pageInatin,
        })
        .then((res) => {
          if (res.data.code == 0) {
            this.playbackList = res.data.data.results;
            Indicator.close();
          } else {
            Toast(res.data.message);
          }
        });
    },
    // 回放统计
    getPlaybackStatistics(data) {
      liveApi.StudentPlaybackCollect({
        ...data,
      });
      this.getList();
    },
    async submit(info) {
      let res = await liveApi.StudentEvaluationCollect({
        ...info,
        room_id: this.room_id,
      });
      if (res.data.code === 0) {
        Toast({
          message: "感谢您的评价！",
          duration: 2000,
        });
        this.getList();
      } else {
        Toast({
          message: res.data.message,
          duration: 2000,
        });
      }
    },
    handAppraise(item) {
      this.$refs.comAppraise.onShow();
    },
    handClassroom(item) {
      if (!item.is_report) {
        Toast("课堂报告生成中，请稍后查看");
        return;
      }
      this.$router.push({
        path: "/classReports",
        query: {
          room_id: item.room_id,
        },
      });
    },
    async getAppraise(item) {
      if(item.is_evaluate){
        this.$router.push({
        path: "/appraise",
        query: {
          room_id: item.room_id,
        },
      });
      }else{
        this.$refs.comAppraise.onShow(item.room_id);
        this.room_id = item.room_id
      }
    },
    reset() {
      (this.pageInatin = {
        live_start_time: "",
        live_course_name: "",
      }),
        this.getList();
    },
  },
  created() {},
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.playback {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .title_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 80px;

    .back {
      width: 128px;
      height: 128px;
      margin: 0 0 0 50px;
      cursor: pointer;
    }
  }
  .playback_body {
    width: 100%;
    height: 100%;
    position: relative;
    margin-top: 30px;
    .now {
      height: 900px !important;
      width: 1300px !important;
    }
    .playback_now {
      position: absolute;
      left: 50%;
      top: 40%;
      transform: translate(-50%, -50%);
      width: 1400px;
      height: 1260px;
      img {
        width: 100%;
        height: 100%;
      }
      .search {
        width: 92%;
        height: 100px;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        // align-items: flex-end;
        padding: 50px;

        .search_input {
          display: flex;
          flex-direction: row;
          widows: 464px;
          height: 64px;
          align-items: center;
          ::v-deep .mint-search {
            height: 100% !important;
          }
          ::v-deep .el-input__icon {
            position: absolute;
            line-height: 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          ::v-deep .el-date-editor {
            width: 288px;
            height: 64px;
            // input::-webkit-input-placeholder {
            //   position: relative;
            //   left: 60px;
            // }
            .el-input__inner {
              width: 100%;
              height: 100%;
              padding-left: 30px;
              padding-right: 30px;
              font-size: 20px;
              position: absolute;
              border: none;
              border-radius: 6px;
              text-align: center;
            }
            .el-input__prefix {
              left: -20px;
              // top: -100px;
            }
            .el-input__suffix {
              right: -20px;
              top: -90px;
              display: none;
            }
            .el-icon-date:before {
              font-size: 30px;
            }
          }
          ::v-deep .mint-searchbar {
            padding: 0;
            .mint-searchbar-inner {
              height: 30px;
            }
            .mintui-search {
              font-size: 30px;
            }
            .mintui-search:before {
              font-size: 30px;
            }
            .mint-searchbar-core {
              font-size: 20px;
              margin-left: 10px;
            }
            .mint-searchbar-cancel {
              display: none !important;
            }
          }
          .search_title {
            font-size: 32px;
            font-weight: 500;
          }
        }
        .search_btn {
          width: 140px;
          height: 80px;
          cursor: pointer;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .playback_list {
        height: 72vh;
        overflow: auto;
        .playback_item {
          height: 640px;
          padding: 50px;
          width: 92%;
          padding-bottom: 0;
          .playback_body {
            height: 332px;
            width: 92%;
            margin: 30px;
            background: rgba(255, 255, 255, 1);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 20px;
            .playback_type {
              display: flex;
              // padding: 0 30px;
              height: 60px;
              .playback_title {
                width: 220px;
                height: 100%;
                font-size: 22px;
                font-weight: 500;
                background: #31bfff;
                color: #fff;
                height: 60px;
                line-height: 60px;
                text-align: center;
                border-top-left-radius: 10px;
                border-bottom-left-radius: 10px;
              }
              .playback_data {
                width: 100%;
                height: 100%;
                background: #effaff;
                color: black;
                display: flex;
                align-items: center;
                padding-left: 20px;
                font-size: 22px;
                font-weight: 500;
              }
            }
          }
          .but {
            img {
              width: 180px;
              height: 70px;
              margin-right: 10px;
              cursor: pointer;
            }
            display: flex;
            justify-content: flex-end;
            margin-right: 32px;
          }
        }
      }
    }
  }
}
::v-deep .mint-search-list {
  display: none;
}
::v-deep input, input:focus{
  outline: none !important;
  border: 0px !important;
}
</style>
