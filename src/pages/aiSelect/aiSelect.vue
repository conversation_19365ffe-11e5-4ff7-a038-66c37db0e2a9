<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/aiSelet/对弈背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="main-box">
      <div class="header-box">
        <div
          class="back"
          @click="goBack"
          :style="{
            'background-image': `url(${require('@/assets/index/back.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
        <div class="title jcyt600">AI对弈</div>
        <div class="ai-select-box">
          <div class="surplus-count-container jcyt400">
            今日剩余次数：<span class="jcyt500">{{ aiLeftTimes?.count || 0 }}</span>
          </div>
          <div class="select" :tabindex="1000" @blur="selecting = false">
            <div
              @click="selecting = !selecting"
              class="select-w flex-row"
              :class="['show-box', selecting ? 'selecting' : '']"
            >
              <!-- <div class="select-label">{{ label }}</div> -->
              <div class="select-left">
                <div class="select_icon" :style="{
                  'background-image': `url(${require('@/assets/questionBank/Fill.png')})`,
                  'background-size': 'cover'
                }" ></div>
              </div>
              <span class="jcyt600 label">{{ label }}</span>
                <!-- <div
                class="arrow-down"
                :style="{
                  'background-image': `url(${require('@/assets/questionBank/Fill.png')})`,
                  'background-size': 'cover'
                }"
              ></div> -->
            </div>
            <ul v-show="selecting" class="dropdown-box">
              <li
                class="dropdown-item jcyt500"
                :class="{
                  selectItem: selectNum == index,
                  jcyt600: selectNum == index,
                }"
                v-for="(item, index) in checkList1"
                v-bind:key="index"
                @click="searchValueHandle(item, index)"
              >
                {{ item.tag }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="content-container" ref="container" @scroll="scrollTo">
        <div
          class="content"
          v-if="apiDone"
          :style="{
            width:
              checkList.length * 380 * 0.******** -
              (checkList.length - 1) * 26 * 0.******** +
              'vw'
          }"
        >
          <div
            class="levev_item jcyt600"
            :class="item.isTitle === true ? 'levev_item1' : ''"
            v-for="(item, index) in this.checkList"
            :key="index"
            :style="{
              'background-image': `url(${
                item.isTitle === false
                  ? require('@/assets/aiSelet/等级图.png')
                  : require('@/assets/aiSelet/n级图.png')
              })`,
              'background-size': '100% 100%',
              cursor: item.isTitle ? 'none' : 'pointer',
              left: index * 410 * 0.******** + 'vw'
            }"
            @click="show_dialog(item)"
            ref="levelList"
          >
            <p
              :style="{
                color:
                  item.isTitle === true
                    ? '#ffffff'
                    : item.hard_level === 'K'
                    ? '#3B62CF'
                    : item.hard_level === 'D'
                    ? '#FF6700'
                    : '#3B62CF',
                'font-size':
                  item.isTitle === true
                    ? 46 * 0.******** + 'vw'
                    : 40 * 0.******** + 'vw',
                'margin-top':
                  item.isTitle === true
                    ? 310 * 0.******** + 'vw'
                    : 190 * 0.******** + 'vw',
              }"
            >
              {{ item.tag }}
            </p>
            <div
              class="spacer-container"
              v-if="
                item.isTitle == false && checkList[index - 1].isTitle == false
              "
              :style="{
                bottom:
                  (index - 3) % 4 == 0 || (index - 2) % 4 == 0
                    ? 308 * 0.******** + 'vw'
                    : 78 * 0.******** + 'vw'
              }"
            >
              <div class="spacer"></div>
            </div>
          </div>
        </div>
        <div
          v-if="showToStart"
          class="to-start-icon"
          @click="toStart"
          :style="{
            'background-image': `url(${require('@/assets/aiSelet/返回最前.png')})`,
            'background-size': 'cover'
          }"
        ></div>
      </div>
    </div>
    <mt-dialog
      :visible.sync="beginChessVisible"
      :isCustomPadding="true"
      @confirm="beginChessConfirm"
      title="对局规则"
      confirmText="开始对弈"
    >
      <template>
        <div class="chess-container">
          <div class="choice-chess">
            <div
              v-for="(item, index) in chessList"
              v-bind:key="index"
              class="chess-box"
              @click="choice(index)"
              :style="{
                'background-image':
                  'url(' +
                  (selectChessNum == index
                    ? require('@/assets/aiSelet/选中.png')
                    : require('@/assets/aiSelet/未选中.png')) +
                  ')',
                'background-repeat': 'no-repeat',
                'background-size': '100% 100%',
              }"
            >
              <div
                class="chess-piece"
                :style="{
                  'background-image':
                    'url(' +
                    (index == 0
                      ? require('@/assets/board/black.png')
                      : require('@/assets/board/white.png')) +
                    ')',
                  'background-repeat': 'no-repeat',
                  'background-size': '100% 100%'
                }"
              ></div>
              <span class="jcyt500" :style="{'color': selectChessNum == index ? '#00BDFF' : '#666666'}">{{ item?.label }}</span>
            </div>
          </div>
          <div class="rule-content">
            <div class="jcyt500">
              <span class="rule-content-label jcyt500">对局类型：</span
              >{{ chessData.game_type == "capture" ? "吃子" : "围地"
              }}<span></span>
            </div>
            <div class="jcyt500">
              <span class="rule-content-label jcyt500">棋盘尺寸：</span
              >{{ chessData.board_size + "路" }}<span></span>
            </div>
            <div class="jcyt500">
              <span class="rule-content-label jcyt500">每方时间：</span
              ><span class="jcyt500">{{
                Math.trunc(chessData.total_time / 60).toString() + "分钟"
              }}</span>
              <span class="byo-container jcyt500">{{ Byo }}</span>
            </div>
            <div class="jcyt500">
              <span class="rule-content-label jcyt500">获胜条件：</span
              >{{
                chessData.game_type == "capture"
                  ? "吃" + chessData.win_capture.toString() + "子获胜"
                  : "黑贴7.5目"
              }}<span></span>
            </div>
          </div>
        </div> </template
    ></mt-dialog>
    <tips
      v-if="notEndGameVisible"
      :isOpen="notEndGameVisible"
      msg="还有未结束的对局"
      cancelBtn="结束对局"
      reallyBtn="继续对局"
      @cancel="endGame"
      @really="goGame"
    >
    </tips>
    <!-- <mt-dialog
      :visible.sync="notEndGameVisible"
      :showClose="false"
      title="还有未结束的对局"
      :customStyleWidth="840"
      :isCustomPadding="true"
    >
      <template #footer>
        <div class="footer-container">
          <div class="footer-confirm-button" @click="endGame">结束对局</div>
          <div class="footer-cancel-button" @click="goGame">继续对局</div>
        </div>
      </template>
    </mt-dialog> -->
  </div>
</template>

<script>
import courseApi from "@/api/course";
import gameApi from "@/api/game";
import dialog from "../mine/dialog/publicDialog.vue";
import { Toast } from "mint-ui";
import storage from "@/public/storage.js";
import tips from "@/components/tips/tips";

export default {
  name: "mineIndex",
  components: {
    "mt-dialog": dialog,
    tips
  },
  data() {
    return {
      aiLeftTimes: {},
      isEnabled: false,
      apiDone: false,
      selecting: false,
      label: "切换等级",
      Ailist: [],
      checkList1: [],
      checkList: [],
      selectNum: 0,
      selectChessNum: 0,
      countdownTimer: null,
      beginChessVisible: false,
      chessList: [{ label: "执黑" }, { label: "执白" }],
      chessData: {},
      Byo: "",
      checkBlack: true,
      canGame: true,
      ai_id: "",
      enabled: true,
      showToStart: false,
      checkNotEndGameStatus: {}, //检查是否有对局未结束
      notEndGameVisible: false,
      notEndGameId: 0
    };
  },
  created() {
    this.checkNotEndGame();
    gameApi.GetAiLeftTimes().then((res) => {
      this.aiLeftTimes = res.data;
      this.isEnabled = this.aiLeftTimes.can_play;
      gameApi.GetAiList().then((res) => {
        this.Ailist = res.data;
        for (var i = 0; i < this.Ailist.length; i++) {
          this.checkList1.push({
            tag: this.Ailist[i].level_tag,
            isTitle: true,
            index: i + 1
          });
        }

        for (var i = 0; i < this.Ailist.length; i++) {
          this.checkList.push({
            tag: this.Ailist[i].level_tag,
            isTitle: true,
            index: i + 1
          });
          for (var j = 0; j < this.Ailist[i].data.length; j++) {
            this.checkList.push({
              tag: this.Ailist[i].data[j].name,
              isTitle: false,
              id: this.Ailist[i].data[j].id,
              hard_level: this.Ailist[i].data[j].hard_level
            });
          }
        }
        this.apiDone = true;
      });
    });
  },
  mounted() {
    var that = this;

    window.addEventListener("scroll", this.handleScroll);
  },
  computed: {},
  methods: {
    checkNotEndGame() {
      courseApi
        .CheckNotEndGame({ game_source: "ai-tournament" })
        .then((res) => {
          this.checkNotEndGameStatus = res.data;
          if (res.data.has_not_end_game) {
            this.notEndGameVisible = true;
            this.notEndGameId = res.data.game_id;
          }
        });
    },
    goGame() {
      this.notEndGameVisible = false;
      this.$router.push({
        path: "/AIgame",
        query: { game_id: this.notEndGameId, from: "Ai" }
      });
    },
    endGame() {
      var user_id = storage.$getStroage("userId");
      gameApi
        .Close({ game_id: parseInt(this.checkNotEndGameStatus.game_id) })
        .then((res) => {
          this.notEndGameVisible = false;
        });
    },
    scrollTo() {
      this.showToStart = this.$refs.container.scrollLeft > 1000;
    },
    show_dialog(item) {
      if (!this.apiDone) {
        return;
      }

      if (item.isTitle == false) {
        this.ai_id = item.id;
        gameApi.GetAiInfo({ ai_id: item.id }).then((res) => {
          this.beginChessVisible = true;
          this.chessData = res.data;
          if (this.chessData.byoyomi == 0 && this.chessData.byoyomi_time == 0) {
            this.Byo = "无读秒";
          } else {
            this.Byo =
              this.chessData.byoyomi +
              "次" +
              this.chessData.byoyomi_time +
              "秒";
          }
        });
      }
    },
    choice(index) {
      this.selectChessNum = index;
      this.checkBlack = index == 0;
    },
    goBack() {
      this.$router.push("/");
    },
    shortToLongName(name) {
      if (name.length > 4) {
        return name.substring(0, 4) + "...";
      } else {
        return name;
      }
    },
    checkGameOpen() {
      gameApi.CheckGameOpenApi().then((res) => {
        this.enabled = res.data.enabled || true;
      });
    },
    beginChessConfirm() {
      if (!this.canGame) {
        Toast("请稍后");
        return;
      } else if (this.enabled == false) {
        __bl.sum("ai对弈服务关闭");
        Toast("对弈服务已关闭");
      } else if (!this.isEnabled) {
        __bl.sum("ai对弈今日次数用光");
        Toast("今日次数已用光");
      } else {
        __bl.sum("ai对弈开始对局");
        this.canGame = false;
        gameApi
          .CreateMatch({
            ai_id: parseInt(this.ai_id),
            is_black: this.checkBlack
          })
          .then((res) => {
            var params = {
              game_id: res.data.game_id.toString(),
              from: "Ai"
            };
            this.$router.push({ path: "/AIgame", query: params });
          })
          .catch((error) => {
            this.canGame = true;
          });
      }
    },
    searchValueHandle(item, index) {
      __bl.sum("ai对弈切换等级" + item.tag);
      this.label = item.tag;
      this.selectNum = index;
      this.selecting = false;
      let tagName;
      for (var i = 0; i < this.checkList1.length; i++) {
        if (this.checkList1[i].index - 1 == index) {
          tagName = this.checkList1[i].tag;
        }
      }
      for (var i = 0; i < this.checkList.length; i++) {
        if (this.checkList[i].tag == tagName) {
          let doc =
            this.$refs.levelList[
              i + 5 > this.checkList.length - 1
                ? this.checkList.length - 1
                : i + 5
            ];
          if (this.$refs.container.scrollLeft > doc.offsetLeft || i == 0) {
            doc = this.$refs.levelList[i];
          }
          doc.scrollIntoView({ behavior: "smooth" });
        }
      }
    },
    toStart() {
      let doc = this.$refs.levelList[0];
      doc.scrollIntoView({ behavior: "smooth" });
    },
    scrollToStart(distance) {
      if (this.countdownTimer != null) {
        return;
      } else {
        this.countdownTimer = setTimeout(() => {
          var step = 5;
          // 目标位置
          var target = distance;
          // 获取当前位置
          var scrollTop = scrollController.offset;
          var current = scrollTop;
          if (current > target) {
            step = Math.abs(-step);
          }
          // 判断当前是否到达目标位置
          if (Math.abs(current - target) <= Math.abs(step)) {
            clearTimeout(this.countdownTimer);
            return;
          }
          current += step;
          scrollController.jumpTo(current);
        }, 500);
      }
    },
    goDetail() {}
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    position: absolute;
    top: 24px;
    left: 40px;
    z-index: 30;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .header-box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 56px 50px 0 50px;
    box-sizing: border-box;
    .ai-select-box {
      display: flex;
      align-items: center;
      position: absolute;
      right: 48px;
      top: 48px;
      z-index: 30;
    }
  }
  .title {
    font-size: 56px;
    line-height: 64px;
    color: #fff;
    width: 100%;
    z-index: 10;
    text-align: center;
  }
  .surplus-count-container {
    width: 315px;
    height: 80px;
    border-radius: 40px;
    box-shadow: rgba(0, 56, 79, 0.05) 0 4px 4px 0;
    background: rgba(0, 0, 0, 0.25);
    line-height: 80px;
    text-align: center;
    font-size: 32px;
    color: #ffffff;
    span {
      color: #ffcd00;
    }
  }
  .content-container {
    width: 100vw;
    overflow-x: scroll;
    position: relative;
    flex: 1;
    .to-start-icon {
      width: 138px;
      height: 120px;
      position: fixed;
      left: 10px;
      bottom: 200px;
    }
  }
  .content-container::-webkit-scrollbar {
    width: 0px;
  }
  .content {
    height: 840px;
    position: absolute;
    // margin-top: 840px/2;
    left: 104px * 0.39;
    .levev_item {
      width: 272px;
      height: 424px;
      position: absolute;

      &:nth-child(2n + 1) {
        top: 222px;
      }
      &:nth-child(4n + 2) {
        top: 0;
      }
      &:nth-child(4n) {
        top: 440px;
      }
      p {
        font-size: 32px * 0.39;
        color: #ffcd00;
        text-align: center;
        margin-top: 87px * 0.39;
      }
    }
    .levev_item1 {
      &:nth-child(2n) {
        animation: float1 3s ease-in-out infinite;
      }
      &:nth-child(2n + 1) {
        animation: float2 4s ease-in-out infinite;
      }
      &:nth-child(3n) {
        animation: float3 4s ease-in-out infinite;
      }
    }
    @keyframes float1 {
      0%,
      100% {
        transform: translateY(-6%);
      }
      50% {
        transform: translateY(5%);
      }
    }
    @keyframes float2 {
      0%,
      100% {
        transform: translateY(-7%);
      }
      50% {
        transform: translateY(6%);
      }
    }
    @keyframes float3 {
      0%,
      100% {
        transform: translateY(-4%);
      }
      50% {
        transform: translateY(9%);
      }
    }
    .spacer-container {
      position: absolute;
      left: -24px;
      .spacer {
        width: 19.2px;
        height: 19.2px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
      }
    }
  }
  // .select-box {
  //   position: relative;
  //   margin-left: 24px;
  //   .select-label {
  //     font-size: 40px;
  //     color: #fff;
  //   }
  // }
  .select {
    height: 80px;
    width: 280px;
    margin-left: 40px;
    background-image: linear-gradient(to bottom, #FFFFFF, #DAF4FF);
    // background: #ffffff;
    border-radius: 44px;
    box-shadow: #8BB6E1 0 -3px 3px 0 inset, 0 4px 8px 0 rgba(0, 33, 135, .5);
    box-sizing: border-box;
    text-align: center;
    font-size: 36px;
    cursor: pointer;
    display: flex;
    .select-w {
      width: 100%;
    }
    .label {
      margin-left: 22px;
      line-height: 80px;
      color: #294584;
      padding-right: 30px;
      width: 144px;
      text-align: center;
    }
    .select_icon {
      width: 49px;
      height: 52px;
      margin-top: 18px;
      margin-left: 28px;
    }
    .select-left {
      width: 84px;
      height: 80px;
      border-radius: 44px 0 0 44px;
      background: #3168E7;
      position: relative;
      box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      &::after {
        content:"";
        width: 0; height: 0;
        border-color: transparent #3168E7; /*上下颜色 左右颜色*/
        border-width: 0 0 80px 20px;
        border-style: solid;
        position: absolute;
        top: 0;
        left: 84px;
        box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      }
    }

  }
  // .select-box .show-box {
  //   width: 286px;
  //   height: 88px;
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  //   border-radius: 44px;
  //   background: rgba(0, 0, 0, 0.25);
  //   padding: 20px 40px;
  //   box-sizing: border-box;
  //   cursor: pointer;
  //   font-size: 40px;
  //   line-height: 48px;
  //   color: #333;
  //   font-weight: 400;
  //   border: none;
  // }
  // .select-box .show-box.selecting {
  //   border: 1px solid #00d9ffff;
  // }
  // .select-box .show-box .arrow-down {
  //   width: 30px;
  //   height: 32px;
  // }
  .dropdown-box {
    list-style: none;
    min-width: 320px;
    padding: 32px 40px;
    background: #ffffff;
    box-shadow: rgba(18, 103, 180, .5) 0 6px 29px 0, rgba(154, 221, 255, .75) 0 -5px 8px 0 inset;
    border-radius: 40px;
    margin-top: 16px;
    position: absolute;
    top: 88px;
    right: 0;
    z-index: 19;
    box-sizing: border-box;
    overflow: hidden;
  }
  .dropdown-box .dropdown-item {
    text-align: center;
    height: 80px;
    line-height: 80px;
    font-size: 32px;
    cursor: pointer;
    color: #666666;
  }
  .dropdown-box .dropdown-item:hover {
      background: rgba(0, 150, 255, 0.1);
      border-radius: 41.9px;
      color: #0096FF;
      letter-spacing: 0;
      text-align: center;
  }
  .selectItem {
    background: rgba(0, 150, 255, 0.1);
    border-radius: 41.9px;
    color: #0096FF!important;
    letter-spacing: 0;
    text-align: center;
  }
}

::v-deep .dialog .dialog-container {
  width: 1152px!important;
  height: 872px;
  border-radius: 50px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 12px 40px 0, #CCFAFF 0 -8px 30px 0 inset;
  padding: 63px 80px 56px 80px;
  .dialog-title {
    font-family: jcyt600w;
    font-size: 56px;
    line-height: 64px;
  }
  .confirm-button {
    font-size: 48px;
    font-family: jcyt600w;
    height: 120px;
    width: 716px;
    font-weight: normal;
  }
}

.chess-container {
  width: 100%;
  height: 100%;
  // height: 600px;
  .choice-chess {
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .chess-box {
      width: 456px;
      height: 120px;
      line-height: 120px;
      text-align: center;
      border-radius: 60px;
      background: #f6f8fb;
      display: flex;
      align-items: center;
      justify-content: center;
      .chess-piece {
        width: 48px;
        height: 48px;
        margin-right: 32px;
      }
      span {
        font-size: 40px;
        color: #666;
      }
    }
  }
  .rule-content {
    width: 100%;
    height: 304px;
    background: #f6f8fb;
    border-radius: 50px;
    padding: 40px 64px;
    box-sizing: border-box;
    margin: 40px 0;
    font-size: 40px;
    line-height: 48px;
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    .rule-content-label {
      color: #999;
      margin-right: 64px;
    }
    .byo-container {
      margin-left: 10px;
    }
  }
}
.footer-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    width: 312px;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    font-weight: 500;
  }
  .footer-confirm-button {
    border: 6px solid #00bdff;
    color: #00bdff;
  }
  .footer-cancel-button {
    background: #08ccfd;
    color: #fff;
  }
}
.fading-circle {
  color: rgba(100, 49, 191, 255);
}
</style>
