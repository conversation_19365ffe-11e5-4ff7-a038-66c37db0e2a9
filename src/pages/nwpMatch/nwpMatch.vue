<template>
  <div
    class="nwp_match"
    :style="{
      'background-image': `url(${require('@/assets/nwpMatch/match_bg.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="title_row">
      <div
        class="back"
        :style="{
          'background-image': `url(${require('@/assets/nwpMatch/返回_橙.png')})`,
          'background-size': '100% 100%'
        }"
        @click="goBack"
      ></div>
      <div class="title jcyt600">聂道赛事</div>
      <div class="icon">
        <div
          class="match_rule"
          :style="{
            'background-image': `url(${require('@/assets/nwpMatch/赛事规则.png')})`,
            'background-size': '100% 100%'
          }"
          @click="openRule"
        ></div>
        <div
          class="match_rule"
          :style="{
            'background-image': `url(${require('@/assets/nwpMatch/排行榜.png')})`,
            'background-size': '100% 100%'
          }"
          @click="goList"
        ></div>
      </div>
    </div>
    <div v-if="!hasTournament" class="not_match">
      <div
        class="not_match_icon"
        :style="{
          'background-image': `url(${require('@/assets/nwpMatch/暂无赛事.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
    </div>
    <div v-else class="match_info">
      <div class="nwp_left">
        <img
          src="@/assets/nwpMatch/刷新.png"
          alt
          class="reload"
          @click="onRefresh"
        />
        <div
          class="macth_img"
          :style="{
            'background-image': `url(${require('@/assets/nwpMatch/聂道赛.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <div
            class="cut"
            @click="dialogShow()"
            :style="{
              'background-image': `url(${require('@/assets/nwpMatch/切换赛事.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
        </div>
        <div class="macth_title jcyt500">{{ tournamentName }}</div>
        <div class="macth_time jcyt500">
          {{
            currentRoundTime != "0001-01-01T00:00:00Z"
              ? `本轮比赛：${moment(currentRoundTime).format("MM月DD日 HH:mm")}`
              : "当前赛事轮次暂未开始"
          }}
        </div>
        <div
          class="star_btn jcyt600"
          :class="canStartMatch ? 'text_star' : 'text_dis'"
          :style="{
            'background-image': canStartMatch
              ? `url(${require('@/assets/nwpMatch/star_btn.png')})`
              : `url(${require('@/assets/nwpMatch/btn_dis.png')})`,
            'background-size': '100% 100%'
          }"
          @click="goGame(gameID)"
        >
          {{
            canStartMatch
              ? "开始比赛"
              : showTimeDown
              ? `开始比赛(${durationTransform(remainingTime)})`
              : "开始比赛"
          }}
        </div>
      </div>
      <div class="nwp_right">
        <div class="nav_row">
          <div class="direction" @click="leftUp">
            <img
              class="arrows"
              :src="
                nav_scrollLeft > 0
                  ? `${require('@/assets/nwpMatch/left.png')}`
                  : `${require('@/assets/nwpMatch/left_dis.png')}`
              "
            />
          </div>
          <div class="center" ref="nav">
            <div
              class="nav"
              ref="navOption"
              v-for="(item, index) in roundList"
              :key="index"
              @click="selectNav(index)"
              :class="active == index ? 'nav_active' : ''"
            >
              <div class="num jcyt500">第{{ item.round }}轮</div>
              <div class="time jcyt400">
                {{ moment(item.starting_time).format("MM.DD HH:mm") }}
              </div>
            </div>
          </div>
          <div class="direction" @click="rightUp">
            <img
              class="arrows"
              :src="
                roundList.length > 4 &&
                nav_scrollLeft != (roundList.length - 4) * nav_width
                  ? `${require('@/assets/nwpMatch/right.png')}`
                  : `${require('@/assets/nwpMatch/right_dis.png')}`
              "
            />
          </div>
        </div>
        <div
          class="title_num jcyt500"
          :style="{
            'background-image': `url(${require('@/assets/nwpMatch/轮次背景.png')})`,
            'background-size': '100% 100%'
          }"
        >
          第 {{ active + 1 }} 轮
        </div>
        <div class="game_list">
          <div v-if="roundTable == null" class="empty">
            <div
              class="empty_img"
              :style="{
                'background-image': `url(${require('@/assets/nwpMatch/暂无对阵表.png')})`,
                'background-size': '100% 100%'
              }"
            ></div>
          </div>
          <div
            class="game_info"
            v-else
            v-for="(item, index) in roundTable"
            :key="index"
            @click="goGames(item)"
          >
            <div class="game_type jcyt500">聂道赛·{{ gameType }}</div>
            <div class="game_detail">
              <div class="left">
                <div
                  class="head"
                  :style="{
                    'background-image': `url(${item.black_player_avatar})`,
                    'background-size': '100% 100%'
                  }"
                >
                  <img src="@/assets/game/black.png" class="chess black-chess" />
                </div>
                <div class="name_info">
                  <div class="name jcyt500">{{ item.black_player_name }}</div>
                  <div class="name_type jcyt500">参赛选手</div>
                </div>
              </div>
              <div
                class="match_type"
                :style="{
                  'background-image':
                    item.result === 'blackWin'
                      ? `url(${require('@/assets/mine/black_win.png')})`
                      : item.result === 'blackLose'
                      ? `url(${require('@/assets/mine/back-fail.png')})`
                      : item.result === 'withdraw'
                      ? `url(${require('@/assets/mine/弃权.png')})`
                      : item.result === 'draw'
                      ? `url(${require('@/assets/mine/和棋.png')})`
                      : `url(${require('@/assets/mine/vs.png')})`,
                  'background-size': '100% 100%'
                }"
              ></div>
              <div class="right">
                <div class="name_info">
                  <div class="name jcyt500">{{ item.white_player_name }}</div>
                  <div class="name_type jcyt500">参赛选手</div>
                </div>
                <div
                  class="head"
                  :style="{
                    'background-image': `url(${item.white_player_avatar})`,
                    'background-size': '100% 100%'
                  }"
                >
                  <img src="@/assets/game/white.png" class="chess" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <mt-dialog
      :visible.sync="matchRuleVisible"
      :isCustomPadding="false"
      :title="tournamentName"
      class="match-rule"
    >
      <template>
        <div class="match-rule-container">
          <div
            v-if="startTime !== '0001-01-01T00:00:00Z'"
            class="match-rule-date jcyt500"
          >
            比赛时间：{{ moment(startTime).format("YYYY/MM/DD") }}-
            {{ moment(endTime).format("YYYY/MM/DD") }}
          </div>
          <div class="jcyt500">{{ tournamentRule }}</div>
        </div>
      </template>
      <template #footer>
        <div></div>
      </template>
    </mt-dialog>
    <div class="dialog" v-if="isDialogOn">
      <div class="dialog_row">
        <div class="dialog_box">
          <div class="dialog_title jcyt600">切换赛事</div>
          <div class="ul">
            <div
              class="li"
              v-for="(item, i) in tournamentList"
              :key="i"
              :class="order_index == i ? 'li_active' : ''"
              @click="orderHandle(item, i)"
            >
              <div>
                <div class="li_title jcyt500">{{ item.name }}</div>
                <div class="li_time jcyt400">
                  比赛时间：{{
                    moment(item.tournament_starting_date).format("YYYY/MM/DD")
                  }}-{{ moment(item.tournament_end_date).format("YYYY/MM/DD") }}
                </div>
              </div>
              <div class="pitch_on"></div>
            </div>
          </div>
        </div>
        <div
          class="dialog_close"
          @click="dialogClose"
          :style="{
            'background-image': `url(${require('@/assets/login/关闭按钮.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
      </div>
    </div>
    <tips
      v-if="notEndGameVisible"
      :isOpen="notEndGameVisible"
      msg="还有未结束的对局"
      :hasCancel="false"
      reallyBtn="继续对局"
      @really="goGame"
    >
    </tips>
    <!-- <mt-dialog
      :visible.sync="notEndGameVisible"
      :showClose="false"
      title="还有未结束的对局"
      :customStyleWidth="840"
      :isCustomPadding="true"
    >
      <template #footer>
        <div class="footer-container">
          //<div class="footer-confirm-button" @click="endGame">结束对局</div>
          <div class="footer-cancel-button" @click="continueGame">继续对局</div>
        </div>
      </template>
    </mt-dialog> -->
  </div>
</template>

<script>
import nwpMatchApi from "@/api/nwpMatch";
import config from "@/config";
import storage from "@/public/storage.js";
import dialog from "../mine/dialog/publicDialog";

import moment from "moment";
import gameApi from "@/api/game";
import zip from "@/public/zip";
import tips from "@/components/tips/tips";


export default {
  components: {
    "mt-dialog": dialog,
    tips
  },
  data() {
    return {
      hasTournament: true,
      tournamentList: [],
      showTimeDown: false,
      tournamentID: "",
      tournamentName: "",
      currentRoundTime: "",
      studentID: "",
      startTime: "",
      endTime: "",
      tournamentRule: "",
      gameID: "",
      canEnterRoom: "",
      active: 0,
      roundTable: [],
      gameType: "",
      matchRuleVisible: false,
      roundList: [
        { time: "03.18 17:00" },
        { time: "03.18 17:00" },
        { time: "03.18 17:00" },
        { time: "03.18 17:00" },
        { time: "03.18 17:00" },
        { time: "03.18 17:00" }
      ],
      roundId: "",
      nav_scrollLeft: 0,
      match_active: 0,
      remainingTime: 0,
      canStartMatch: false,
      nav_width: "",
      isDialogOn: false,
      order_index: 0,
      timer_ticker: null,
      user_id: "",
      notEndGameVisible: false
    };
  },
  computed: {
    wsMessage() {
      return this.$store.getters.getMessage;
    },
    wsReadyStats() {
      return this.$store.getters.getWsReadyStats;
    }
  },
  watch: {
    wsReadyStats: {
      handler(new_stats, old_stats){
        if (new_stats === 1) {
          this.$nextTick(() => {
            this.$socket.send({
              message_type: "bind_group",
              data: {
                group_id: `tournament:${this.tournamentID}:round:timeDown`
              }
            });
            this.$socket.send({
              message_type: "bind_group",
              data: { group_id: `tournament:${this.tournamentID}:round:create` }
            });
          });
        }
        if (new_stats === 0) {
          // alert("网络连接失败，请检查网络设置");
        }
      },
    },
    wsMessage: {
      handler(val) {
        let msg = val.data;
        switch (msg.message_type) {
          case "round_time_down":
            this.onWsMessage(msg.data);
            break;
          case "round_create":
            this.onRefresh();
            break;
        }
      },
      deep: true
    }
  },
  mounted() {
    this.user_id = storage.$getStroage("userId");
    this.getData();
    this.nav_width = this.$refs.navOption[0].offsetWidth;
    this.$refs.nav.addEventListener("scroll", () => {
      this.nav_scrollLeft = this.$refs.nav.scrollLeft;
    });
  },
  destroyed() {
    this.unBindWsGroup();
  },
  methods: {
    openRule() {
      __bl.sum("聂道赛事规则");
      this.matchRuleVisible = true;
    },
    onRefresh() {
      this.canStartMatch = false;
      this.roundTableDone = false;
      this.hasTournament = false;
      this.showTimeDown = false;
      this.canEnterRoom = false;
      this.getData();
    },
    orderHandle(item, i) {
      this.match_active = i;
      this.active = 0;
      this.roundId = "";
      this.choiceTournament(i);
      this.dialogClose();
    },
    dialogClose() {
      this.isDialogOn = false;
    },
    dialogShow() {
      this.isDialogOn = true;
    },
    getData() {
      nwpMatchApi.NwpMatchCurrentApi().then((res) => {
        this.tournamentList = res.data.tournament_list;
        this.hasTournament = res.data.has_current_tournament;
        if (res.data.has_current_tournament) {
          if (this.match_active != null) {
            this.choiceTournament(this.match_active);
          } else {
            this.choiceTournament(0);
          }
        }
      });
    },
    choiceTournament(index) {
      this.unBindWsGroup();
      this.showTimeDown = false;
      this.tournamentID = this.tournamentList[index]["tournament_id"];
      this.tournamentName = this.tournamentList[index]["name"];
      this.currentRoundTime = this.tournamentList[index]["current_round"];
      this.studentID = this.tournamentList[index]["student_id"];
      this.startTime = this.tournamentList[index]["tournament_starting_date"];
      this.endTime = this.tournamentList[index]["tournament_end_date"];
      this.tournamentRule = this.tournamentList[index]["rule"];
      this.gameID = this.tournamentList[index]["game_id"];
      this.canEnterRoom = this.tournamentList[index]["can_enter_room"];
      this.order_index = this.tournamentList.length - this.tournamentList[index]["index"];
      nwpMatchApi
        .NwpMatchRoundApi({ tournament_id: this.tournamentID })
        .then((res) => {
          this.roundList = res.data;
          if (res.data !== []) {
            for (let i = 0; i < this.roundList.length; i++) {
              if (this.roundList[i]["is_current_round"] === true) {
                this.active = i;
                this.roundId = this.roundList[i]["round"];
                if (i > 3) {
                  setTimeout(() => {
                    this.$refs.nav.scrollLeft =
                      (this.roundList.length - 4) * this.nav_width;
                    //   this.nav_scrollLeft = this.$refs.nav.scrollLeft
                  }, 100);
                } else {
                  this.$refs.nav.scrollLeft = 0;
                }
              }
            }
            this.getRoundOrScoreData();
            this.initWebSocket();
          }
        });
    },
    getRoundOrScoreData() {
      nwpMatchApi
        .NwpMatchRoundTableApi({
          tournament_id: this.tournamentID,
          round: this.roundId
        })
        .then((res) => {
          this.roundTable = res.data.line_ups;
          for (var i = 0; i < this.roundTable?.length - 1; i++) {
            if (
              (this.user_id == this.roundTable[i].black_player_id ||
                this.user_id == this.roundTable[i].white_player_id) &&
              this.roundTable[i].match_status == "InGame"
            ) {
              this.notEndGameVisible = true;
            }
          }
          this.gameType = res.data.game_type_name;
        });
    },
    onWsMessage(msg) {
      if (this.timer_ticker) {
        clearInterval(this.timer_ticker);
        this.timer_ticker = null;
      }
      var isRoundStart = msg["is_round_started"] ?? false;
      this.remainingTime = msg["remaining_time"] ?? 999;
      if (this.remainingTime <= 3600 && this.remainingTime > 0) {
        this.showTimeDown = true;
        this.timer_ticker = setInterval(() => {
          this.remainingTime--;
          if (this.remainingTime === 0) {
            clearInterval(this.timer_ticker);
            this.timer_ticker = null;
          }
        }, 1000);
      }
      if (isRoundStart && this.canEnterRoom && this.remainingTime === 0) {
        this.canStartMatch = true;
      }
      if (this.canStartMatch) {
        this.unBindWsGroup();
      }
    },
    unBindWsGroup() {
      if (this.tournamentID === 0) {
        return;
      }
      if (this.timer_ticker) {
        clearInterval(this.timer_ticker);
        this.timer_ticker = null;
      }
      if (this.$socket.conn.ws.readyState === 1) {
        this.$socket.send({
          message_type: "unbind_group",
          data: { group_id: `tournament:${this.tournamentID}:round:timeDown` }
        });
        this.$socket.send({
          message_type: "unbind_group",
          data: { group_id: `tournament:${this.tournamentID}:round:create` }
        });
      }
    },
    //初始化Websocket--sys_info
    initWebSocket() {
      if (this.$socket.conn.ws.readyState === 1) {
        this.$socket.send({
          message_type: "bind_group",
          data: { group_id: `tournament:${this.tournamentID}:round:timeDown` }
        });
        this.$socket.send({
          message_type: "bind_group",
          data: { group_id: `tournament:${this.tournamentID}:round:create` }
        });
      }
    },
    durationTransform(val) {
      // convert seconds to  hours, minutes and seconds
      let sec_num = parseInt(val, 10); // don't forget the second param
      let hours = Math.floor(sec_num / 3600);
      let minutes = Math.floor((sec_num - hours * 3600) / 60);
      let seconds = sec_num - hours * 3600 - minutes * 60;
      if (hours < 10) {
        hours = "";
      }
      if (minutes < 10) {
        minutes = "0" + minutes;
      }
      if (seconds < 10) {
        seconds = "0" + seconds;
      }
      if (hours === "") {
        return minutes + ":" + seconds;
      }
      return hours + ":" + minutes + ":" + seconds;
    },
    goBack() {
      this.$router.push({
        path: "/",
      });
    },
    selectNav(val) {
      this.active = val;
      this.roundId = this.roundList[val].round;
      this.getRoundOrScoreData();
    },
    leftUp() {
      this.$refs.nav.scrollLeft = 0;
    },
    rightUp() {
      console.log("00");
      this.$refs.nav.scrollLeft = (this.roundList.length - 4) * this.nav_width;
      console.log("00");
    },
    goList() {
      __bl.sum("聂道赛事排行榜");
      this.$router.push({
        path: "nwpMatchList",
        query: {
          tournament_id: this.tournamentID,
          match_active: this.match_active
        }
      });
    },
    continueGame() {
      this.$router.push({
        path: "/tournament/game",
        query: { game_id: this.gameID, from: "playing" }
      });
    },
    goGames(item) {
      this.notEndGameVisible = false;
      if (
        item["match_status"] != "NotStart" &&
        item["game_id"] != 0 &&
        item["black_player_id"] != 0 &&
        item["white_player_id"] != 0
      ) {
        if (
          this.user_id == item["black_player_id"] ||
          this.user_id == item["white_player_id"]
        ) {
          this.$router.push({
            path: "/tournament/game",
            query: { game_id: item.game_id, from: "playing" }
          });
        } else {
          this.$router.push({
            path: "/watchingGame",
            query: {
              game_id: item.game_id,
              from_url: "/nwpMatch"
            }
          });
        }
      }
    },
    goGame(val) {
      if (this.canStartMatch) {
        this.$router.push({
          path: "/tournament/game",
          query: { game_id: val, from: "playing" }
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.nwp_match {
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .title_row {
    display: flex;
    justify-content: space-between;
    // align-items: center;
    margin-top: 40px;

    .back {
      width: 120px;
      height: 120px;
      margin: 0 0 0 56px;
      cursor: pointer;
    }

    .title {
      font-size: 48px;
      color: #ff680e;
      text-align: center;
      margin-left: 300px;
      padding-top: 24px;
      line-height: 56px;
    }

    .icon {
      display: flex;
      margin-right: 96px;

      .match_rule {
        width: 128px;
        height: 120px;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }

  .not_match {
    width: 1800px;
    height: 76.29vh;
    background: #ffffff;
    box-shadow: 0 9px 23px 0 rgba(255, 108, 28, 0.2),
      inset 0 -12px 12px 0 #fff3dc;
    border-radius: 72px;
    margin: 40px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;

    .not_match_icon {
      width: 488px;
      height: 460px;
    }
  }

  .match_info {
    display: flex;
    margin: 32px 24px 0;
    align-items: flex-start;

    .nwp_left {
      width: 592px;
      // height: 720px;
      display: flex;
      flex-direction: column;
      background: #ffffff;
      box-shadow: 0 10px 20px 0 rgba(255, 108, 28, 0.2),
        inset 0 -8px 8px 0 #fff3dc;
      border-radius: 50px;
      position: relative;
      padding-bottom: 16px;

      .reload {
        width: 60px;
        height: 60px;
        position: absolute;
        right: 40px;
        top: 30px;
        cursor: pointer;
      }

      .macth_img {
        width: 192px;
        height: 192px;
        border-radius: 50%;
        margin: 46px auto 39px;
        position: relative;
        box-shadow: rgba(199, 103, 0, 0.1) 0 12px 20px 0;
        .cut {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          position: absolute;
          bottom: -8px;
          right: 0;
          cursor: pointer;
        }
      }

      .macth_title {
        width: 512px;
        // height: 120px;
        font-size: 48px;
        color: #333333;
        text-align: center;
        line-height: 60px;
        margin: 0 40px 33px;
        overflow: hidden;

        /*2. 文字用省略号替代超出的部分 */
        text-overflow: ellipsis;

        /* 3. 弹性伸缩盒子模型显示 */
        display: -webkit-box;

        /* 4. 限制在一个块元素显示的文本的行数 */
        -webkit-line-clamp: 2;

        /* 5. 设置或检索伸缩盒对象的子元素的排列方式 */
        -webkit-box-orient: vertical;
      }

      .macth_time {
        font-size: 32px;
        color: #999999;
        text-align: center;
      }

      .star_btn {
        width: 512px;
        height: 122px;
        margin: 32px auto 0;
        text-align: center;
        font-size: 40px;
        line-height: 110px;
        cursor: pointer;
      }

      .text_dis {
        color: #8c8c8c;
        text-shadow: 0 4px 4px rgba(255, 255, 255, 0.15);
      }

      .text_star {
        color: #ffffff;
        text-shadow: 0 4px 4px rgba(255, 155, 8, 0.85);
      }
    }

    .nwp_right {
      width: 1264px;
      height: 840px;
      background: #ffffff;
      box-shadow: 0 10px 20px 0 rgba(255, 108, 28, 0.2),
        inset 0 -8px 8px 0 #fff3dc;
      border-radius: 50px;
      margin-left: 16px;

      .nav_row {
        width: 1200px;
        height: 152px;
        background: #fff3dc;
        border-radius: 84px;
        margin: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        .direction {
          width: 112px;
          height: 152px;
          display: flex;
          align-items: center;
          justify-content: center;

          .arrows {
            width: 40px;
            height: 72px;
          }
        }

        .center::-webkit-scrollbar {
          display: none;
        }

        .center {
          width: 976px;
          height: 152px;
          overflow-x: auto;
          white-space: nowrap;

          .nav {
            width: 244px;
            height: 152px;
            display: inline-block;
            text-align: center;
            cursor: pointer;

            .num {
              font-size: 40px;
              color: #7e4825;
              margin-top: 31px;
              line-height: 48px;
            }

            .time {
              font-size: 28px;
              color: #7e4825;
              line-height: 32px;
              margin-top: 8px;
            }
          }

          .nav_active {
            background: #ffce3a;
            border-radius: 50px;
          }
        }
      }

      .title_num {
        width: 379px;
        height: 48px;
        margin: 23px auto 25px;
        font-size: 40px;
        color: #333333;
        line-height: 48px;
        text-align: center;
      }

      .game_list {
        width: 1200px;
        height: 544px;
        padding: 0 32px;
        overflow-y: auto;
        .empty {
          .empty_img {
            width: 488px;
            height: 460px;
            margin: 30px auto;
          }
        }

        .game_info {
          width: 1200px;
          height: 168px;
          background: #e3f5fd;
          border-radius: 84px;
          margin-bottom: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          .game_type {
            font-size: 40px;
            color: #333333;
          }

          .game_detail {
            width: 746px;
            height: 128px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 64px;
            margin-left: 56px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            box-sizing: border-box;

            .left,
            .right {
              display: flex;
              align-items: center;
            }

            .head {
              width: 96px;
              height: 96px;
              border-radius: 48px;
              background: #ccc;
              position: relative;

              .chess {
                width: 36px;
                height: 36px;
                position: absolute;
                bottom: 0;
                right: 0;
                border-radius: 50%;
              }
            }
            .black-chess {
              box-shadow: rgba(0, 0, 0, 0.5) 0 2px 5px 0;
            }
            .right {
              .chess {
                width: 36px;
                height: 36px;
                position: absolute;
                bottom: 0;
                left: 0;
                border-radius: 50%;
                box-shadow: rgba(0, 0, 0, 0.5) 0 1px 5px 0;
              }

              .name_info {
                margin-left: 0;
                margin-right: 24px;
                text-align: right;
              }
            }

            .name_info {
              margin-left: 24px;

              .name {
                width: 160px;
                font-size: 40px;
                color: #333333;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 48px;
              }

              .name_type {
                font-size: 28px;
                color: #fe4f37;
                line-height: 32px;
              }
            }

            .match_type {
              width: 96px;
              height: 96px;
              border-radius: 48px;
              position: relative;
            }
          }
        }
      }

      .game_list::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .dialog {
    width: 100vw;
    height: 100vh;
    position: fixed;
    background: rgba(0, 0, 0, 0.5);
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog_row {
      display: flex;
      justify-content: center;
    }

    .dialog_box {
      width: 1152px;
      height: 888px;
      border-radius: 50px;
      background: #ffffff;
      box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 30px 0 #ccfaff;
      margin-left: 112px;

      .dialog_title {
        font-size: 48px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        margin: 64px 0;
      }

      .ul {
        height: 648px;
        overflow-y: auto;

        .li_active {
          border: 4px solid #ffa132;
          box-sizing: border-box;
          position: relative;

          .pitch_on {
            width: 57px;
            height: 52px;
            background: url("@/assets/unitTest/选中.png") no-repeat;
            background-size: 100% 100%;
            position: absolute;
            bottom: 0;
            right: 0;
          }
        }

        .li {
          width: 976px;
          height: 200px;
          background: #f7f9fb;
          border-radius: 40px;
          margin: 0 auto 24px;
          overflow: hidden;
          display: flex;
          justify-content: space-between;

          .li_title {
            font-size: 40px;
            color: #333333;
            margin: 47px 0 17px 48px;
          }

          .li_time {
            font-size: 32px;
            color: #999999;
            margin-left: 48px;
          }

          // .star_row {
          //   display: flex;
          //   justify-content: center;
          //   margin-bottom: 32px;
          //   margin-top: 52px;
          //   margin: 52px 48px 32px 0;

          //   .star {
          //     width: 40px;
          //     height: 40px;
          //     background: url("@/assets/unitTest/star.png") no-repeat;
          //     background-size: 100% 100%;
          //     margin-right: 8px;
          //   }

          //   .star_dis {
          //     width: 40px;
          //     height: 40px;
          //     background: url("@/assets/unitTest/star_dis.png") no-repeat;
          //     background-size: 100% 100%;
          //     margin-right: 8px;
          //   }
          // }

          // .pass_type {
          //   width: 152px;
          //   height: 152px;
          //   margin: 24px 48px 0 0;

          //   img {
          //     width: 100%;
          //     height: 100%;
          //   }
          // }
        }
      }
    }

    .dialog_close {
      width: 96px;
      height: 96px;
      margin-left: 16px;
    }
  }
}
::v-deep .match-rule .dialog-container{
  width: 1152px;
  height: 888px;
  padding: 63px 64px 56px 64px;
  border-radius: 50px;
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, .1), inset #CCFAFF 0 -8px 30px 0;
  .dialog-title {
    margin-bottom: 49px;
  }
}

.match-rule-container {
  width: 1024px;
  background: #EEF9FE;
  border-radius: 40px;
  padding: 48px 48px 24px 48px;
  height: 664px;
  font-size: 32px;
  color: #3D5066;
  line-height: 48px;
  box-sizing: border-box;

  .match-rule-date {
    margin-bottom: 40px;
  }
}

.footer-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  > div {
    width: 312px;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    font-weight: 500;
  }
  .footer-confirm-button {
    border: 6px solid #00bdff;
    color: #00bdff;
  }
  .footer-cancel-button {
    background: #08ccfd;
    color: #fff;
  }
}
</style>
