<template>
  <div
    class="nwp_match"
    :style="{
      'background-image': `url(${require('@/assets/nwpMatch/match_bg.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="title_row">
      <div
        class="back"
        :style="{
          'background-image': `url(${require('@/assets/nwpMatch/返回_橙.png')})`,
          'background-size': '100% 100%',
        }"
        @click="goBack"
      ></div>
      <div class="title jcyt600">排行榜</div>
    </div>
    <div class="main-container">
      <div
        class="main-box"
      >
        <div class="table-header">
          <div class="header-rank jcyt500">排名</div>
          <div class="header-rank-right jcyt500"></div>
          <div class="header-name jcyt500">姓名</div>
          <div class="header-integral jcyt500">积分</div>
          <div class="header-score jcyt500">小分</div>
        </div>
        <div class="list-container">
          <div class="list-scroll">
            <div
              v-for="(item, index) in resultTable?.result_table"
              v-bind:key="index"
              class="score-container"
            >
              <div class="rank-content">
                <img
                  v-if="index < 3"
                  :src="
                    index == 0
                      ? require('@/assets/nwpMatch/one.png')
                      : index == 1
                      ? require('@/assets/nwpMatch/two.png')
                      : require('@/assets/nwpMatch/three.png')
                  "
                />
                <span v-else class="jcyt500">{{ index + 1 }}</span>
              </div>
              <div class="rank-content-right">
                <img :src="item.student_avatar" />
              </div>
              <div class="name-content jcyt500">{{ item.name }}</div>
              <div class="integral-content jcyt500">{{ item.score }}</div>
              <div class="score-content jcyt500">{{ item.sos }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import nwpMatchApi from "@/api/nwpMatch";
export default {
  data() {
    return {
      resultTable: {},
      resultDone: false,
    };
  },
  computed: {
    tournament_id() {
      return this.$route.query.tournament_id;
    },
    match_active() {
      return this.$route.query.match_active;
    },
  },
  mounted() {
    this.getData();
  },
  destroyed() {},
  methods: {
    getData() {
      nwpMatchApi
        .NwpMatchResultTableApi({ tournament_id: this.tournament_id })
        .then((res) => {
          this.resultTable = res.data;
          this.resultDone = true;
        });
    },
    goBack() {
      this.$router.push({
        path: "nwpMatch",
        query: {
          match_active: this.match_active,
        },
      });
    },
  },
};
</script>

<style scoped lang="less">
.nwp_match {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .title_row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 64px;
    margin-bottom: 64px;
    width: 100vw;
    .back {
      width: 120px;
      height: 120px;
      left: 56px;
      cursor: pointer;
      position: absolute;
      top: 40px;
    }
    .title {
      font-size: 48px;
      color: #ff680e;
      text-align: center;
      line-height: 56px;
    }
    .icon {
      display: flex;
      margin-right: 80px;
      .match_rule {
        width: 128px;
        height: 128px;
        margin-left: 32px;
      }
    }
  }
  .main-container {
    display: flex;
    align-items: center;
    justify-content: center;
    .main-box {
      width: 1760px;
      height: 840px;
      display: flex;
      background-image: linear-gradient(to bottom, #FCD24C, #F6BF26, #F9C93D);
      box-shadow: #FFF3CF 0 8px 17px 0 inset, 0 0 25px 0 rgba(165, 58, 0, .2), #F09B00 0 -8px 17px 0  inset;
      border-radius: 60px;
      flex-direction: column;
      align-items: center;
      .table-header {
        // margin-left: 26px;
        width: 1680px;
        height: 104px;
        display: flex;
        align-items: center;
        color: #9f5816;
        font-size: 40px;

        > div {
          height: 100%;
          text-align: center;
          line-height: 104px;
        }
        .header-rank {
          width: 284px;
        }
        .header-rank-right {
          width: 180px;
        }
        .header-name {
          width: 456px;
          text-align: left;
          padding-left: 40px;
          box-sizing: border-box;
        }
        .header-integral {
          width: 240px;
        }
        .header-score {
          width: 480px;
        }
      }

      .list-container {
        // flex: 1;
        width: 1680px;
        height: 704px;
        background: #FFBC12;
        box-shadow: #F79400 0 0 25px 0 inset;
        border-radius: 56px;
        .list-scroll {
          width: 1632px;
          height: 656px; //544px
          overflow-y: auto;
          margin: 24px 24px 0 24px;
          border-radius: 40px;
          background: #FFFBF3;
          &::-webkit-scrollbar {
            width: 0!important;
          }
          .score-container {
            width: calc(100% - 64px);
            margin: 0 32px;
            display: flex;
            height: 136px;
            align-items: center;
            color: #9f5816;
            font-size: 40px;
            text-align: center;
            border-bottom: 2px dashed #f4e8da;
            .rank-content {
              width: 88px;
              height: 88px;
              margin-left: 44px;
              margin-right: 139px;
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .rank-content-right {
              width: 88px;
              height: 88px;
              margin-right: 49px;
              img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
              }
            }
            .name-content {
              width: 456px;
              text-align: left;
              color: #333333;
            }
            .integral-content {
              width: 240px;
            }
            .score-content {
              width: 480px;
            }
          }
        }
      }
      .list-container::-webkit-scrollbar {
        width: 0px;
      }
    }
  }
}
</style>