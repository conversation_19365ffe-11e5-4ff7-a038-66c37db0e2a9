<template>
  <div
    class="gameWrap"
    :style="{
      'background-image': `url(${require('@/assets/exerciseDetail/下棋页面背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="wifi">
      <wifiWrap :game_end="game_end"></wifiWrap>
    </div>
    <div class="content">
      <div class="left">
        <board
          ref="board_play"
          id="board"
          :sgf="sgf"
          :board_click="board_click"
          :userTurn="user_turn"
          @turn="change_turn"
          @setup="change_setup"
          @captured="change_captured"
          @chess_move="chess_move"
        />
      </div>
      <div class="right">
        <div class="game_type_wrap">
          <gameRule
            :status="status"
            :type="'people'"
            ref="is_rule_show"
          ></gameRule>
          <div class="game_index jcyt600">第 {{ setup }} 手</div>

          <div class="fuc_wrap">
            <div
              class="reload"
              @click="goReload()"
              :style="{
                'background-image': `url(${require('@/assets/game/刷新.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="rule"
              @click="changeRuleShow()"
              :style="{
                'background-image': is_rule_show
                  ? `url(${require('@/assets/game/查看规则.png')})`
                  : `url(${require('@/assets/game/规则.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="back"
              @click="back"
              :style="{
                'background-image': `url(${require('@/assets/exerciseDetail/返回.png')})`,
                'background-size': 'cover'
              }"
            ></div>
          </div>
        </div>
        <gamePeople
          :game_end="game_end"
          :status="status"
          :black_enter="black_enter"
          :white_enter="white_enter"
          :ws_left_time="ws_left_time"
          :black_left_time="black_left_time"
          :white_left_time="white_left_time"
          :hum_captured="hum_captured"
          :ai_captured="ai_captured"
          :lose_reason="lose_reason"
          :time_c="time_c"
          :win_side="win_side"
          :user_hash="user_hash"
          :start_byomi="start_byomi"
        ></gamePeople>
        <div
          class="drop_countdown jcyt500"
          v-if="game_end === false && status.enable_move_time === 1"
        >
          落子倒计时：{{ s_to_hs(drop_left_time) }}
        </div>
        <functionButton
          :game_end="game_end"
          :active="active"
          :confirm_status="confirm_status"
          :status="status"
          :setup="setup"
          :user_player="user_player"
          :win_side="win_side"
          :lose_reason="lose_reason"
          :white_captured="ai_captured"
          :black_captured="hum_captured"
          :end_step="end_step"
          :black_score="black_score"
          :white_score="white_score"
          :type="'people'"
          :toolsGroup="toolsGroup"
          @check_tab="check_tab"
          @confirm_move="confirm_move"
          @update_pass="update_pass"
          @resign="game_end === false && $refs.confirm_dialog.open()"
          @check_area_score="check_area_score"
          @changeStepStyle="changeStepStyle"
          @goToStep="goToStep"
          @show_step="show_step"
          @check_apply_draw="check_apply_draw"
          @arbitration="arbitration"
        ></functionButton>
      </div>
    </div>
    <confirmResignDialog
      ref="confirm_dialog"
      @confirm_resign="confirm_resign"
      @cancel_resign="cancel_resign"
    ></confirmResignDialog>
    <winDialog
      :status="status"
      ref="gameSuccessDialog"
      :win_side="win_side"
      :lose_reason="lose_reason"
      :white_captured="ai_captured"
      :black_captured="hum_captured"
      :black_score="black_score"
      :white_score="white_score"
    ></winDialog>
    <loseDialog
      :status="status"
      ref="gameFailDialog"
      :win_side="win_side"
      :lose_reason="lose_reason"
      :white_captured="ai_captured"
      :black_captured="hum_captured"
      :black_score="black_score"
      :white_score="white_score"
    ></loseDialog>
    <peaceDialog
      :status="status"
      ref="peaceDialog"
      :win_side="win_side"
      :lose_reason="lose_reason"
      :white_captured="ai_captured"
      :black_captured="hum_captured"
      :black_score="black_score"
      :white_score="white_score"
    ></peaceDialog>
    <tips
      v-if="showAgreeAreaScoreDialog"
      :isOpen="showAgreeAreaScoreDialog"
      :msg="applyPeaceing ? '对方申请和棋' : '对方申请数目'"
      :cancelBtn="`不同意${applyPeaceing
          ? s_to_hs(apply_draw_time_left)
          : s_to_hs(area_score_time_left)}
      `"
      reallyBtn="同意"
      @cancel="notConsent"
      @really="consent"
      footerText="已暂停双方倒计时"
    >
    </tips>
    <tips
      v-if="showApplyAreaScoreTotast == 'show'"
      :isOpen="showApplyAreaScoreTotast == 'show'"
      :msg="`等待对手同意${
        applyPeaceing
          ? s_to_hs(apply_draw_time_left)
          : s_to_hs(area_score_time_left)
      }`"
      :hasCancel="false"
      :hasReally="false"

    >
    </tips>
    <tips
      v-if="changeApplyArbitrationDialogShow"
      :isOpen="changeApplyArbitrationDialogShow"
      msg="请选择裁判事项"
      cancelBtn="数子结果有误"
      reallyBtn="取消申请"
      @cancel="arbitrationChange('area_score')"
      @really="arbitrationChange('')"
    >
      <template #footerBtn>
        <div class="btn cancel jcyt600" data-content="对手违规" :style="{
            'background-image': `url(${require('@/assets/tips/btn_blue.png')})`,
            'background-size': '100% 100%',
          }" @click="arbitrationChange('opponent_foul')">对手违规</div>
      </template>
    </tips>
    <!-- <mt-dialog
      :visible.sync="showAgreeAreaScoreDialog"
      :showClose="false"
      :customStyleWidth="800"
      :customStylePadding="50"
      :customStylefontSize="48"
      :title="applyPeaceing ? '对方申请和棋' : '对方申请数目'"
    >
      <template #footer>
        <div class="footer-container">
          <div class="footer-confirm-button" @click="consent">同意</div>
          <div class="footer-cancel-button" @click="notConsent">
            不同意
            {{
              applyPeaceing
                ? s_to_hs(apply_draw_time_left)
                : s_to_hs(area_score_time_left)
            }}
          </div>
        </div>
        <div class="footer_text">已暂停双方倒计时</div>
      </template>
    </mt-dialog>
    <div class="toast_loading" v-if="showApplyAreaScoreTotast == 'show'">
      等待对手同意{{
        applyPeaceing
          ? s_to_hs(apply_draw_time_left)
          : s_to_hs(area_score_time_left)
      }}
    </div>
    <mt-dialog
      :visible.sync="changeApplyArbitrationDialogShow"
      :showClose="false"
      :customStyleWidth="800"
      :customStylePadding="50"
      :customStylefontSize="48"
      title="请选择裁判事项"
      class="arbitration_dialog"
    >
      <template #footer>
        <div class="arbitration_container">
          <div class="arbitration_btn" @click="arbitrationChange('area_score')">
            数子结果有误
          </div>
          <div
            class="arbitration_btn"
            @click="arbitrationChange('opponent_foul')"
          >
            对手违规
          </div>
          <div class="arbitration_close" @click="arbitrationChange('')">
            取消申请
          </div>
        </div>
      </template>
    </mt-dialog> -->

  </div>
</template>

<script>
import functionButton from "@/components/game/functionButton";
import confirmResignDialog from "@/components/game/confirmResignDialog";
import winDialog from "@/components/game/winDialog";
import loseDialog from "@/components/game/loseDialog";
import peaceDialog from "@/components/game/peaceDialog";
import gameRule from "@/components/game/gameRule";
import gamePeople from "@/components/game/gamePeople";
import wifiWrap from "@/components/game/gameWifi";
import gameApi from "@/api/game";
import board from "@/components/game/board";
import { Toast } from "mint-ui";
import storage from "@/public/storage.js";
import zip from "../../public/zip";
import "@/public/wgo/wgo.min";
import "@/public/wgo/wgo.player.min";
import AwaitLock from "await-lock";
import tool from "@/public/tool";
import dialog from "../mine/dialog/publicDialog.vue";
import websocket from "@/api/websocket";
import tips from "@/components/tips/tips";


export default {
  name: "gameWrap",
  data() {
    return {
      active: 1,
      is_rule_show: false,
      move_audio: "",
      board_click: false,
      status: {},
      hum_captured: 0,
      ai_captured: 0,
      last_mark: "",
      last_move: {
        x: "",
        y: "",
        turn: ""
      },
      confirm_status: false,
      apply_score_number: "",

      showMoveNumber: false,
      user_id: "",
      user_hash: "",
      send_data: {},
      setup: 0,
      sgf: "",
      timer: "",
      timer1: "",
      ws_left_time: {},
      turn: 1,
      user_player: false,
      time_c: 1,
      move_c: 1,
      win_side: "",
      lose_reason: "",
      game_end: false,
      black_left_time: "",
      white_left_time: "",
      drop_left_time: "",
      end_step: 0,
      black_enter: false,
      white_enter: false,
      black_score: 0,
      white_score: 0,
      user_turn: 0,
      last_msg_index: 0,
      miss_msg_locker: new AwaitLock(),
      handler_locker: new AwaitLock(),
      ownership: [], //数目结束标志位
      queue: [],
      showApplyAreaScoreTotast: "",
      showAgreeAreaScoreDialog: false,
      area_score_time_left: 0,
      areaScoreing: false,
      apply_draw_time_left: 0,
      applyPeaceing: false,
      dis_game_id: 0,
      toolsGroup: {
        arbitrationStatus: "",
        waitArbitration: "",
        changeResultArbitration: "",
        isFinished: false
      },
      changeApplyArbitrationDialogShow: false,
      start_byomi: ""
    };
  },
  components: {
    functionButton,
    board,
    confirmResignDialog,
    winDialog,
    loseDialog,
    peaceDialog,
    gameRule,
    gamePeople,
    "mt-dialog": dialog,
    wifiWrap,
    tips
  },

  computed: {
    wsMessage() {
      return this.$store.getters.getMessage;
    },
    game_id() {
      return this.$route.query.game_id;
    },
    wsReadyStats() {
      return this.$store.getters.getWsReadyStats;
    }
  },
  watch: {
    wsReadyStats: {
      handler(new_stats, old_stats) {
        if (new_stats === 1) {
          this.$nextTick(() => {
            this.$socket.send({
              message_type: "bind_group",
              data: { group_id: "game:" + this.game_id }
            });
          });
        }
        if (new_stats === 0) {
          // alert("网络连接失败，请检查网络设置");
        }
      },
    },
    wsMessage: async function (newValue, oldValue) {
      await this.onMessage(newValue);
    },
    status: {
      handler(new_object) {
        gameApi.PlaySgf(this.send_data).then((res) => {
          var data = JSON.parse(zip.unzipStr(res.data));
          this.sgf = data.sgf;
          gameApi
            .CanPlay(this.send_data)
            .then((res) => {
              if (res.status == 200) {
                this.turn = res.data.next_color ?? 0;
                this.can_play();
              }
            })
            .catch((err) => {});
        });
        this.turn = this.time_c = new_object.turn;
        if (new_object.black_user_hash === this.user_hash) {
          this.user_turn = 1;
        }
        if (new_object.white_user_hash === this.user_hash) {
          this.user_turn = 2;
        }
        if (new_object.is_end === false) {
          this.$nextTick(() => {
            if (this.$socket.conn.ws.readyState === 1) {
              this.$socket.send({
                message_type: "bind_group",
                data: { group_id: "game:" + this.game_id }
              });
            }
          });
          if (this.user_turn == 1) {
            if (!new_object.black_enter) {
              gameApi.EnterBoard(this.send_data);
            }
          }
          if (this.user_turn == 2) {
            if (!new_object.white_enter) {
              gameApi.EnterBoard(this.send_data);
            }
          }
          // this.can_play();
          this.drop_left_time = new_object.now_move_time;
          this.black_left_time = new_object.now_black_time;
          this.white_left_time = new_object.now_white_time;
          if (this.timer1) {
            clearInterval(this.timer1);
          }
          if (this.timer) {
            clearInterval(this.timer);
          }
          if (new_object.turn == 1) {
            this.reGetCountdown(this.black_left_time);
          } else if (new_object.turn == 2) {
            this.reGetCountdown(this.white_left_time);
          }
          this.dropTimeReGetCountdown(this.drop_left_time);
        } else {
          this.game_end = true;
          this.end_step = new_object.step;
          this.win_side = new_object.win === 1 ? "black" : "white";
          this.drop_left_time = new_object.now_move_time;
          this.black_score = new_object.black_score;
          this.white_score = new_object.white_score;
          this.black_enter = true;
          this.white_enter = true;
          this.check_result(new_object);
        }
        this.black_left_time =
          new_object.now_black_time > 0
            ? new_object.now_black_time
            : new_object.black_time;
        this.white_left_time =
          new_object.now_white_time > 0
            ? new_object.now_white_time
            : new_object.white_time;

        this.apply_score_number = new_object.territory_step;
        this.check_user_player();
        if (this.user_player) {
          this.watchArbitrationStatus();
        }
      },
      deep: true
    }
  },
  methods: {
    async onMessage(val) {
      console.log("onMessage");
      console.log(val);
      let msg = val.data;
      let index = val.index;
      switch (val["group"]) {
        case `game:${this.game_id}`:
          if (this.miss_msg_locker.acquired) {
            this.queue.push(val);
            return;
          }
          if (this.last_msg_index === 0) {
            this.last_msg_index = index - 1;
          }
          if (this.last_msg_index + 1 < index) {
            if (this.miss_msg_locker.tryAcquire()) {
              let totalMissMsgCount = index - this.last_msg_index - 1;
              for (let i = 1; i <= totalMissMsgCount; i++) {
                await this.handlerMissMsg(this.last_msg_index + i);
              }
              await this.handler(msg);
              this.last_msg_index = index;
              this.doMySelf();
              this.miss_msg_locker.release();
            }
          } else {
            await this.handler(msg);
            this.last_msg_index = index;
          }
          break;
        default:
          if (val.data.message_type === "lines_up_updated") {
            this.handler(msg);
            return;
          }
          break;
      }
    },
    async handlerMissMsg(msg_index) {
      await websocket
        .GetMissGroupWebSocketMsg({
          index: msg_index,
          group_id: `game:${this.game_id}`
        })
        .then((res) => {
          let data = res.data.data.message;
          console.log(res.data.data.index);
          let msg = zip.unzipStr(data);
          this.handler(JSON.parse(msg));
        }).catch(() => {
          window.location.reload();
        });
    },
    handler(msg) {
      console.log(msg);
      console.log(
        "%cMyProject%cline:466%cmsg.message_type",
        "color:#fff;background:#ee6f57;padding:3px;border-radius:2px",
        "color:#fff;background:#1f3c88;padding:3px;border-radius:2px",
        "color:#fff;background:rgb(131, 175, 155);padding:3px;border-radius:2px",
        msg.message_type
      );

      switch (msg.message_type) {
        case "time":
          let data = msg.data;
          this.time_c = data.c;
          this.ws_left_time = data;
          this.time_down();
          break;
        case "move_time":
          this.move_c = msg.data.color;
          if (this.timer1) {
            clearInterval(this.timer1);
          }
          if (this.status.enable_move_time == 1) {
            this.drop_left_time = msg.data.left_time;
            this.dropTimeReGetCountdown(this.drop_left_time);
          }
          this.can_play();
          break;
        case "user_status":
          if (msg.data.user_hash === this.status.black_user_hash) {
            if (
              (msg.data.enter_status && msg.data.online_status) ||
              this.status.is_end
            ) {
              this.black_enter = true;
            } else {
              this.black_enter = false;
            }
          } else if (msg.data.user_hash === this.status.white_user_hash) {
            if (
              (msg.data.enter_status && msg.data.online_status) ||
              this.status.is_end
            ) {
              this.white_enter = true;
            } else {
              this.white_enter = false;
            }
          }
          break;
        case "move":
          if (msg.data.c !== this.user_turn) {
            this.$refs.board_play.update_board({
              x: msg.data.x,
              y: msg.data.y,
              turn: msg.data.c === 2 ? -1 : 1
            });
          }
          this.turn = msg.data.turn === 1 ? 2 : 1;
          this.can_play();
          break;
        case "pass":
          this.$refs.board_play.update_pass();
          this.turn = msg.data.c == 1 ? 2 : 1;
          this.can_play();
          Toast(this.turn === 1 ? "白方停一手" : "黑方停一手");
          break;
        case "area_score_time":
          this.applyAreaPopup(msg.data);
          break;
        case "summation_time":
          this.applyDraw(msg.data);
          break;
        case "lines_up_updated":
          this.watchArbitrationStatus(); //人人对弈查看是否可以申请仲裁
          break;
        case "start_byomi":
          this.start_byomi = msg.data.c === 1 ? "black" : "white";
          break;
        case "end":
          this.game_end = true;
          this.end_step = msg.data.step;
          this.black_score = msg.data.b_score;
          this.white_score = msg.data.w_score;
          this.active = 1;
          this.check_result(msg.data);
          this.end_game_popup(msg.data);
          if (msg.data.owner_ship) {
            this.ownership = msg.data.owner_ship;
            this.owner_ship();
          }
          if (this.timer) {
            clearInterval(this.timer);
          }
          if (this.timer1) {
            clearInterval(this.timer1);
          }
          this.$socket.send({
            message_type: "unbind_group",
            data: { group_id: "game:" + this.game_id }
          });

          break;
      }
    },
    doMySelf() {
      let val = this.queue.shift();
      if (val) {
        let msg = val.data;
        let index = val.index;
        this.handler(msg);
        this.last_msg_index = index;
      }
      if (this.queue.length > 0) {
        this.doMySelf();
      }
    },
    owner_ship() {
      tool.OwnerShip(this.ownership, this.$refs.board_play.player);
    },
    remove_owner_ship() {
      if (this.ownership.length > 0) {
        tool.RemoveOwnerShip(this.ownership, this.$refs.board_play.player);
        this.ownership = [];
      }
    },
    check_user_player() {
      if (this.status.black_user_hash === this.user_hash) {
        this.user_player = true;
        return;
      }
      if (this.status.white_user_hash === this.user_hash) {
        this.user_player = true;
        return;
      }
      this.user_player = false;
    },
    goReload() {
      window.location.reload();
    },
    check_result(data) {
      console.log("check_result" + data);
      this.win_side =
        data.win === 1 ? "black" : data.win === 2 ? "white" : data.win;
      if (this.win_side === 3) {
        this.lose_reason = data.win_result;
      } else {
        if (data.win_result === "Abstain") {
          this.lose_reason = "Abstain";
          return;
        }
        var str = data.win_result.substring(
          data.win_result.indexOf("+") + 1,
          data.win_result.length
        );
        // console.log(structuredClone);
        if (str.indexOf("C")) {
          var num = str.substring(1, str.length);
          data.win === 1
            ? (this.black_captured = num)
            : (this.white_captured = num);
        }
        this.lose_reason =
          str.indexOf("C") > -1 || str == "O"
            ? "captured"
            : str === "R"
            ? "resign"
            : str === "T"
            ? "time_out"
            : parseInt(str) > 0
            ? "area_score"
            : str === "L"
            ? "withdraw"
            : "Draw";
        console.log(this.lose_reason);
      }
    },
    reGetCountdown(new_time) {
      this.timer = setInterval(() => {
        if (new_time > 0) {
          new_time--;
          this.time_c === 2
            ? (this.white_left_time = new_time)
            : (this.black_left_time = new_time);
        }
      }, 1000);
    },
    dropTimeReGetCountdown(new_time) {
      this.timer1 = setInterval(() => {
        if (new_time > 0) {
          new_time--;
          this.drop_left_time = new_time;
        }
      }, 1000);
    },
    changeRuleShow() {
      this.is_rule_show = !this.is_rule_show;
      this.is_rule_show
        ? this.$refs.is_rule_show.open()
        : this.$refs.is_rule_show.close();
    },
    time_down() {
      if (this.time_c === 1) {
        if (
          this.ws_left_time.black_main_time === 0 &&
          this.ws_left_time.black_byo_yomi > 0
        ) {
          if (this.timer) {
            clearInterval(this.timer);
            this.black_left_time = this.ws_left_time.black_byo_yomi_time;
          }
          this.reGetCountdown(this.ws_left_time.black_byo_yomi_time);
        } else {
          if (this.timer) {
            clearInterval(this.timer);
            this.black_left_time = this.ws_left_time.black_main_time;
          }
          this.reGetCountdown(this.ws_left_time.black_main_time);
        }
      } else if (this.time_c == 2) {
        if (
          this.ws_left_time.white_main_time == 0 &&
          this.ws_left_time.white_byo_yomi > 0
        ) {
          if (this.timer) {
            clearInterval(this.timer);
            this.white_left_time = this.ws_left_time.white_byo_yomi_time;
          }
          this.reGetCountdown(this.ws_left_time.white_byo_yomi_time);
        } else {
          if (this.timer) {
            clearInterval(this.timer);
            this.white_left_time = this.ws_left_time.white_main_time;
          }
          this.reGetCountdown(this.ws_left_time.white_main_time);
        }
      }
    },
    update_pass: function () {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }

      if (this.board_click === true) {
        this.handler_locker.tryAcquire();
        if (this.last_mark) {
          this.$refs.board_play.remove_chess_mark();
          this.last_mark = null;
        }
        this.confirm_status = false;
        this.board_click = false;

        gameApi
          .Pass(Object.assign(this.send_data, { c: this.turn }))
          .then((res) => {
            if (res.status == 200) {
              // Toast(this.turn === 1 ? "黑方停一手" : "白方停一手");
              this.turn = res.data.next_color;
            } else {
              this.board_click = true;
              Toast("停一手失败");
            }
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          })
          .catch(() => {
            this.board_click = true;
            Toast("停一手失败");
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          });
      } else {
        Toast("还没轮到你哦～");
      }
    },

    s_to_hs: function (s) {
      if (!s) {
        s = 0;
      }
      var h;
      h = Math.floor(s / 60);
      s = s % 60;
      h += "";
      s += "";
      h = h.length == 1 ? "0" + h : h;
      s = s.length == 1 ? "0" + s : s;
      return h + ":" + s;
    },
    change_captured: function (event) {
      this.hum_captured = event.B;
      this.ai_captured = event.W;
    },
    change_setup: function (event) {
      this.setup = event;
    },
    change_turn: function (event) {
      this.turn = event === -1 ? 2 : 1;
      console.log("turn" + this.turn);
    },

    can_play: function () {
      if (this.user_turn === this.turn) {
        this.board_click = true;
      }
    },
    chess_move: function (ob) {
      if (this.last_mark) {
        this.$refs.board_play.remove_chess_mark();
      }
      this.last_mark = ob;
      this.confirm_status = true;
      this.$refs.board_play.add_chess_mark(this.last_mark);
    },
    confirm_move() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }
      this.handler_locker.tryAcquire();
      this.board_click = false;
      this.confirm_status = false;
      if (this.last_mark) {
        this.play_move(this.last_mark);
      }
    },
    check_sgf_same(status_sgf, board_sgf) {
      this.$refs.board_play.loadSgf(status_sgf);
    },
    consent() {
      if (this.applyPeaceing) {
        gameApi.AgreeDraw(this.send_data);
      } else {
        gameApi.AgreeAreaScore(this.send_data);
      }
      this.showAgreeAreaScoreDialog = false;
    },
    notConsent() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }
      this.handler_locker.tryAcquire();
      if (this.applyPeaceing) {
        gameApi.RejectDraw(this.send_data);
      } else {
        gameApi.DisagreeAreaScore(this.send_data);
      }
      this.showAgreeAreaScoreDialog = false;
      if (this.handler_locker.acquired) {
        this.handler_locker.release();
      }
    },
    play_move(ob) {
      if ("x" in ob && "y" in ob) {
        gameApi
          .HumsMove(
            Object.assign(this.send_data, { x: ob.x, y: ob.y, c: this.turn })
          )
          .then((res) => {
            this.$refs.board_play.repain_chess_mark(this.last_mark);
            this.$refs.board_play.update_board(this.last_mark);
            //this.board_click = false;
            //this.confirm_status = false;
            this.last_move = this.last_mark;
            this.last_mark = null;
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          })
          .catch((error) => {
            this.board_click = true;
            this.confirm_status = false;
            this.last_mark = null;
            console.log("落子失败");
            Toast(error.err);
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          });
      }
    },

    check_tab(active_index) {
      this.active = active_index;
    },
    back() {
      this.$router.push("/nwpMatch");
    },

    getStatus() {
      gameApi
        .GetPlayInfo(this.send_data)
        .then((res) => {
          this.status = JSON.parse(zip.unzipStr(res.data));
        })
        .catch(() => {});
    },

    confirm_resign() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }
      this.handler_locker.tryAcquire();
      gameApi
        .Resign(this.send_data)
        .then((res) => {
          if (this.handler_locker.acquired) {
            this.handler_locker.release();
          }
          this.$refs.confirm_dialog.close();
        })
        .catch((error) => {
          if (this.handler_locker.acquired) {
            this.handler_locker.release();
          }
          this.$refs.confirm_dialog.close();
        });
    },
    cancel_resign() {
      if (this.handler_locker.acquired) {
        this.handler_locker.release();
      }
    },
    end_game_popup(new_object) {
      this.$refs.board_play.hideLastMove();
      this.$refs.board_play.remove_anim_chess_mark();
      if (this.last_mark) {
        this.$refs.board_play.remove_chess_mark();
      }
      this.isFinished = true;
      this.board_click = false;
      if (
        (this.win_side === 3 &&
          this.lose_reason == "Draw" &&
          this.user_player) ||
        (this.win_side === 4 &&
          this.lose_reason == "Abstain" &&
          this.user_player)
      ) {
        this.$refs.peaceDialog.open();
      } else if (
        this.status.black_user_hash === this.user_hash &&
        this.win_side === "black"
      ) {
        this.$refs.gameSuccessDialog.open();
      } else if (
        this.status.white_user_hash === this.user_hash &&
        this.win_side === "white"
      ) {
        this.$refs.gameSuccessDialog.open();
      } else if (
        this.status.black_user_hash !== this.user_hash &&
        this.win_side === "black" &&
        this.user_player
      ) {
        this.$refs.gameFailDialog.open();
      } else if (
        this.status.white_user_hash !== this.user_hash &&
        this.win_side === "white" &&
        this.user_player
      ) {
        this.$refs.gameFailDialog.open();
      }
      this.$refs.is_rule_show.close();
      this.is_rule_show = false;
    },
    check_area_score() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }
      if (this.game_end == false) {
        if (this.areaScoreing == false) {
          this.areaScoreing = true;
          if (
            this.setup >= this.apply_score_number &&
            this.board_click === true
          ) {
            this.board_click = false;
            this.handler_locker.tryAcquire();
            gameApi
              .ApplyAreaScore(this.send_data)
              .then((res) => {
                if (this.last_mark) {
                  this.$refs.board_play.remove_chess_mark();
                }
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
              })
              .catch((err) => {
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
                this.board_click = true;
                this.areaScoreing = false;
                Toast(err.err);
              });
          } else if (
            this.setup < this.apply_score_number &&
            this.board_click === true
          ) {
            Toast(`请在${this.apply_score_number}手后申请数子`);
          }
          this.areaScoreing = false;
        }
      }
    },
    // 检测仲裁
    watchArbitrationStatus() {
      this.toolsGroup.isFinished = this.isFinished;
      gameApi
        .GetArbitrationStatus({ game_id: parseInt(this.game_id) })
        .then((res) => {
          this.toolsGroup.arbitrationStatus = res.data ?? {};
          this.toolsGroup.hasArbitration = res.data["can_apply"];
          if (
            this.toolsGroup.arbitrationStatus["arbitration_status"] ==
            "not_applied"
          ) {
            this.toolsGroup.waitArbitration = false;
          } else if (
            this.toolsGroup.arbitrationStatus["arbitration_status"] == "waiting"
          ) {
            this.toolsGroup.waitArbitration = true;
          } else if (
            this.toolsGroup.arbitrationStatus["arbitration_status"] ==
            "finished"
          ) {
            this.toolsGroup.waitArbitration = false;
            this.toolsGroup.finishedArbitration = true;
            this.toolsGroup.changeResultArbitration =
              this.toolsGroup.arbitrationStatus["result_changed"];
          }
        });
    },
    //仲裁弹窗
    arbitration(val) {
      this.changeApplyArbitrationDialogShow = val;
    },
    //申请仲裁
    arbitrationChange(val) {
      if (val) {
        gameApi
          .ArbitrationCreate({
            game_id: parseInt(this.game_id),
            lose_reason: this.status.lose_reason,
            reason: val
          })
          .then((res) => {
            this.watchArbitrationStatus();
            this.changeApplyArbitrationDialogShow = false;
          });
      } else {
        this.changeApplyArbitrationDialogShow = false;
      }
    },
    applyAreaPopup(val) {
      if (val.status == 1) {
        this.areaScoreing = true;
        this.area_score_time_left = val.left_time;
        if (this.timer) {
          clearInterval(this.timer);
        }
        if (this.timer1) {
          clearInterval(this.timer1);
        }
        if (val.apply_user_hash == this.user_hash) {
          this.showApplyAreaScoreTotast = "show";
        }
        if (this.user_player && val.apply_user_hash != this.user_hash) {
          this.showAgreeAreaScoreDialog = true;
        }
      } else if (val.status == 2 || val.status == 4) {
        this.showApplyAreaScoreTotast = null;
        this.showAgreeAreaScoreDialog = false;
        if (this.user_player && val.apply_user_hash == this.user_hash) {
          Toast("对手拒绝数目");
          this.can_play();
        }
        this.areaScoreing = false;
      } else if (val.status == 3) {
        this.showApplyAreaScoreTotast = null;
        this.showAgreeAreaScoreDialog = false;
      }
    },
    check_apply_draw() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }
      if (!this.board_click) {
        return;
      }

      console.log("check_apply_draw");
      if (this.game_end === false) {
        if (this.applyPeaceing === false) {
          this.applyPeaceing = true;
          if (this.board_click) {
            this.handler_locker.tryAcquire();
            gameApi
              .ApplyDraw(this.send_data)
              .then((res) => {
                if (this.last_mark) {
                  this.$refs.board_play.remove_chess_mark();
                }
                this.board_click = false;
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
              })
              .catch((err) => {
                this.board_click = true;
                this.applyPeaceing = false;
                Toast(err.err);
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
              });
          } else {
            this.applyPeaceing = false;
          }
        }
      }
    },
    applyDraw(val) {
      if (val.status == 1) {
        this.applyPeaceing = true;
        this.apply_draw_time_left = val.left_time;
        if (this.timer) {
          clearInterval(this.timer);
        }
        if (this.timer1) {
          clearInterval(this.timer1);
        }
        if (val.apply_user_hash == this.user_hash) {
          this.showApplyAreaScoreTotast = "show";
        }
        if (this.user_player && val.apply_user_hash != this.user_hash) {
          this.showAgreeAreaScoreDialog = true;
        }
      } else if (val.status == 2 || val.status == 4) {
        this.showApplyAreaScoreTotast = null;
        this.showAgreeAreaScoreDialog = false;
        if (this.user_player && val.apply_user_hash == this.user_hash) {
          Toast("对手拒绝和棋");
          this.can_play();
        }
        this.applyPeaceing = false;
      } else if (val.status == 3) {
        this.showApplyAreaScoreTotast = null;
        this.showAgreeAreaScoreDialog = false;
      }
    },
    changeStepStyle(num) {
      this.remove_owner_ship();
      if (num == 0) {
        if (this.setup > 0) {
          this.$refs.board_play.move_to_first();
        }
      } else if (num == -5) {
        if (this.setup >= 5) {
          this.$refs.board_play.goTo(-5);
        }
      } else if (num == -1) {
        if (this.setup >= 1) {
          this.$refs.board_play.move_to_previous();
        }
      } else if (num == 1) {
        if (this.end_step >= this.setup + 1) {
          this.$refs.board_play.move_to_next();
        }
      } else if (num == 5) {
        if (this.end_step >= this.setup + 5) {
          this.$refs.board_play.goTo(5);
        }
      } else if (num == 361) {
        if (this.end_step != this.setup) {
          this.$refs.board_play.move_to_last();
        }
      }
    },
    goToStep(e) {
      this.$refs.board_play.goToStep(e);
    },
    show_step() {
      if (!this.showMoveNumber) {
        this.$refs.board_play.show_move_number();
      } else {
        this.$refs.board_play.close_move_number();
      }
      this.showMoveNumber = !this.showMoveNumber;
    },
    cancel() {
      this.$socket.send({
        message_type: "unbind_group",
        data: { group_id: "game:" + this.dis_game_id }
      });
    }
  },
  mounted() {
    this.handler_locker.tryAcquire();
    this.handler_locker.release();
    this.dis_game_id = parseInt(this.game_id);
    this.user_id = storage.$getStroage("userId");
    this.user_hash = `${this.user_id}:1`;
    this.send_data = {
      game_id: parseInt(this.game_id),
      user_hash: this.user_hash,
      user_id: this.user_id
    };
    this.move_audio = document.getElementById("move-audio");
    this.getStatus();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.timer1) {
      clearInterval(this.timer1);
    }
    this.cancel();
  }
};
</script>

<style scoped lang="less">
.gameWrap {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  vertical-align: middle;
  .wifi {
    position: absolute;
    right: 16px;
    top: 40px;
    display: flex;
    z-index: 10;
    flex-direction: row-reverse;
  }

  .content {
    // width: 2016px;
    // height: 1344px;
    padding-top: 40px;
    display: flex;
    justify-content: center;
    margin: 0 auto;
    .left {
      width: 1000px;
      height: 1000px;
      background: #f7a448;
      border: 16px solid #fee194;
      box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.1);
      border-radius: 40px;
      position: relative;
      box-sizing: border-box;
    }

    .right {
      width: 488px;
      height: 1000px;
      margin-left: 16px;
      background: rgba(255, 255, 255, 0.5);
      border: 2px solid rgba(255, 255, 255, 0.5);
      box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.1);
      border-radius: 30px;
      box-sizing: border-box;

      .game_type_wrap {
        width: 488px;
        height: 104px;
        padding: 16px 20px 16px 16px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        background-image: linear-gradient(180deg, #ffffff 6%, #c6ecff 87%);
        box-shadow: 0 4px 15px 0 #5bb9e7, inset 0 -3px6px 0 #e1f1fc;
        border-radius: 30px 30px 0 0;
        position: relative;
        align-items: center;

        .game_index {
          width: 144px;
          height: 72px;
          line-height: 72px;
          text-align: center;
          background: rgba(198, 236, 255, 0.5);
          border: 1.49px solid rgba(108, 206, 255, 0.3);
          box-shadow: inset 0 0 9px 0 #6cceff;
          border-radius: 37.2px;
          font-size: 26.79px;
          color: #344770;
        }

        .fuc_wrap {
          // width: 304px;
          // height: 92px;
          display: flex;

          .back {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
            margin-left: 12px;
          }

          .reload {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
          }

          .rule {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
            margin-left: 12px;
          }
        }
      }

      .drop_countdown {
        width: calc(100% - 32px);
        height: 64px;
        line-height: 64px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 32px;
        font-size: 32px;
        color: #ffffff;
        text-align: center;

        margin: 16px auto 0;
      }
    }
  }
}

.footer-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  > div {
    width: 312px;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    font-weight: 500;
  }

  .footer-confirm-button {
    border: 6px solid #00bdff;
    color: #00bdff;
  }

  .footer-cancel-button {
    background: #08ccfd;
    color: #fff;
  }
}

.footer_text {
  color: #959393;
  margin-top: 20px;
  font-size: 30px;
}

.toast_loading {
  font-size: 30px;
  height: 90px;
  border-radius: 28px;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  text-align: center;
  line-height: 90px;
  color: #ffffff;
  padding: 0 25px;
}

.arbitration_container {
  width: 100%;

  .arbitration_btn {
    width: 100%;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    font-weight: 500;
    background: #00bdff;
    margin-bottom: 30px;
    color: #ffffff;
    cursor: pointer;
  }

  .arbitration_close {
    width: 100%;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    font-weight: 500;
    background: #edebeb;
    color: #bbbbbb;
    cursor: pointer;
  }
}
</style>
