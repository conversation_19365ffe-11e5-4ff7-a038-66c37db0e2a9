<template>
  <div class="report" :style="{'background-image':`url(${require('@/assets/evaluation/比赛大厅背景.png')})`}">
    <img class="back" @click="back" src="@/assets/index/back.png"/>
    <div class="content" :style="{'background-image':`url(${require('@/assets/evaluation/比赛报告背景.png')})`}">
      <div class="pic" :style="{'background-image': matchStatus.match_status == 'is_pass' ? `url(${require('@/assets/course/pad通过.png')})` : `url(${require('@/assets/course/pad未通过.png')})`}"
      >
        <span class="jcyt600">{{username}}的比赛结果</span>
      </div>
      <img class="left-balloon" v-if="matchStatus.match_status == 'is_pass'" src="@/assets/course/气球左.png"/>
      <img class="right-balloon" v-if="matchStatus.match_status == 'is_pass'" src="@/assets/course/气球右.png"/>
      <div class="content-second">
        <div class="result jcyt600">{{matchInfo.level_name}}</div>
        <div class="reason jcyt500">{{matchStatus.match_status == 'is_pass' ? matchInfo.pass_text : "很遗憾你未通过本次比赛，继续加油哦～"}}</div>
        <img class="result-img" :src="matchStatus.match_status == 'is_pass' ? require('@/assets/evaluation/pass.png') : require('@/assets/evaluation/not_pass.png')"/>
      </div>
      <div class="content-third">
        <div class="title jcyt600">答题详情</div>
        <div class="describle flex-row content-center">
          <div class="flex-row items-center">
            <div class="circle right"></div>
            <span class="jcyt500">正确</span>
          </div>
          <div class="flex-row items-center desc-margin">
            <div class="circle wrong"></div>
            <span class="jcyt500">错误</span>
          </div>
        </div>
        <div class="question flex-row flex-wrap align-baseline">
          <div class="question-index jcyt500" v-for="(item,index) in matchStatus.question_history" :key="index" :class="matchStatus.question_history[index].status == 'is_right' ? 'right' : 'wrong'" @click="goQuestion(index)">{{index + 1}}</div>
        </div>
        <div class="tips flex-row items-center content-center">
          <img class="circle" src="@/assets/course/笑脸.png"/>
          <span class="jcyt500">点击编号可查看题目详情</span>
        </div>
      </div>
      <div class="content-third content-forth">
        <div class="title">对弈详情</div>
        <div class="describle flex-row content-center">
          <div class="flex-row items-center">
            <div class="circle right"></div>
            <span>胜利</span>
          </div>
          <div class="flex-row items-center desc-margin">
            <div class="circle wrong"></div>
            <span>失败</span>
          </div>
        </div>
        <div class="question flex-row flex-wrap align-baseline">
          <div class="question-index right" 
          v-show="item.status != 'not_start'"
          v-for="(item,index) in matchStatus.game_history" :class="matchStatus.game_history[index].status == 'is_win' ? 'right' : 'wrong'" :key="index" @click="goGame(index)">{{index + 1}}</div>
        </div>
        <div class="tips flex-row items-center content-center">
          <img class="circle" src="@/assets/course/笑脸.png"/>
          <span>点击编号可查看对弈详情</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import evaluationApi from "@/api/evaluationMatch";
export default {
  data(){
    return {
      username: "",
      matchID: "",
      matchStatus: {},
      matchInfo: {}
    }
  },
  mounted(){
    this.username = this.$storage.$getStroage("username");
    this.matchID = this.$route.query.matchID;
    this.getStatus();
    this.getInfo();
  },
  methods: {
    back(){
      this.$router.push({path: "/evaluationIndex"})
    },
    async getStatus(){
      let res = await evaluationApi.evaluationMatchStatusApi({'match_id': this.matchID});
      this.matchStatus = res.data;
      console.log( this.matchStatus )
    },
    async getInfo(){
      let res = await evaluationApi.evaluationMatchInfoApi({'match_id': this.matchID});
      this.matchInfo = res.data;
    },
    goQuestion(index){
      this.$router.push({path: "/evaluationQuestionPlay", query: {
        "match_id": this.matchID,
        "question_index": (index + 1).toString(),
        "from": "evaluationReport"
      }})
    },
    goGame(index){
      this.$router.push({path: "/evaluationGame", query: {
        "matchId": this.matchID,
        "from": 'evaluationReport',
        "gameIndex": (index + 1).toString()
      }})
    }
  }
}
</script>
<style lang="less" scoped>
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}

.content-between {
  justify-content: space-between;
}

.content-around {
  justify-content: space-around;
}
.content-center {
  justify-content: center;
}
.align-baseline {
  align-content: baseline;
}
.report {
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  overflow-y: scroll;
  padding-top: 392px;
  box-sizing: border-box;
  &::-webkit-scrollbar {
    width: 0px;
  }
}
.back {
  width: 120px;
  height: 120px;
  position: fixed;
  top: 24px;
  left: 40px;
  cursor: pointer;
  z-index: 30;
}
.content {
  width: 1725px;
  height: 2565px;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  .left-balloon{
    position: absolute;
    left: 0;
    top: -146px;
    width: 362px;
    height: 302px;
  }
  .right-balloon {
    position: absolute;
    right: 0;
    top: -146px;
    width: 362px;
    height: 302px;
  }
  .pic {
    width: 1088px;
    height: 544px;
    position: absolute;
    left: 300px;
    top: -384px;
    text-align: center;
    line-height: 865px;
    background-repeat: no-repeat;
    background-size: cover;
    span{
      font-size: 56px;
      color: #fff;
    }
  }
  .content-second {
    width: 1608px;
    margin: 0 auto;
    padding-top: 214px;
    box-sizing: border-box;
  }
  .result {
    font-size: 128px;
    color: #FF4E4E;
    text-align: center;
    line-height: 136px;
  }
  .reason {
    margin-top: 25px;
    font-size: 32px;
    color: #FF6461;
    line-height: 40px;
    text-align: center;
  }
  .result-img {
    width: 250px;
    height: 250px;
    position: absolute;
    top: 188px;
    right: 196px;
  }
  .content-third{
    width: 1480px;
    height: 928px;
    margin: 302px auto 0;
    background: #FFF8E5;
    border-radius: 70px;
    padding: 136px 0 80px 0;
    position: relative;
    box-sizing: border-box;
    .title {
      background: #FFBA19;
      border-radius: 52px;
      width: 416px;
      height: 104px;
      position: absolute;
      top:  -52px;
      left: 525px;
      color: #fff;
      font-size: 48px;
      text-align: center;
      line-height: 104px;
    }
    .describle {
      width: inherit;
      .circle {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 16px;
      }
      span {
        font-size: 32px;
        color: #333;
        line-height: 32px;
      }
    }
    .desc-margin {
      margin-left: 96px;
    }
    .right {
      background: #37D27C;
    }
    .wrong {
      background: #FF6461;
    }
    .question {
      height: 488px;
      overflow-y: scroll;
      padding: 80px 92px 0 92px;
      &::-webkit-scrollbar {
        width: 0px;
      }
      .question-index {
        margin-right: 48px;
        margin-top: 64px;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        text-align: center;
        line-height: 120px;
        color: #fff;
        cursor: pointer;
        font-size: 64px;
        &:nth-child(8n) {
          margin-right: 0;
        }
        &:nth-child(1),&:nth-child(2),&:nth-child(3),&:nth-child(4),
        &:nth-child(5),&:nth-child(6),&:nth-child(7),&:nth-child(8) {
          margin-top: 0;
        }
      }
    }
    .tips {
      margin-top: 80px;
      span {
        color: #C87E18;
        font-size: 32px;
        line-height: 32px;
      }
      .circle {
        height: 32px;
        width: 32px;
        margin-right: 16px;
      }
    }
  }
  .content-forth {
    margin-top: 184px;
    height: 608px;
    .question {
      height: 200px;
    }
    .tips {
      margin-top: 40px;
    }
  }
}

</style>