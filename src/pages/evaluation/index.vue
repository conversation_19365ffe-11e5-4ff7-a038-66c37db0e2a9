<template>
  <div
    class="bg"
    :style="{
      'background-image': `url(${require('@/assets/evaluation/比赛大厅背景.png')})`
    }"
  >
    <div class="title-box">
      <img class="back" @click="back" src="@/assets/index/back.png" />
      <span class="title jcyt600">比赛大厅</span>
    </div>
    <div class="content">
      <div class="no-Message-container" v-if="list.length == 0">
        <img class="no-Message" src="@/assets/nothing/暂无课程.png" />
        <p class="no-message-content jcyt500">暂无内容</p>
      </div>
      <div class="list-wrap flex-row flex-wrap align-baseline">
        <div
          class="list-item flex-row items-center"
          v-for="(item, index) in list"
          :key="index"
          @click="goNext(item)"
        >
          <img :src="item.head_pic" />
          <div class="list-item-content flex-column content-between">
            <span class="list-item-title jcyt600">{{ item.name }}</span>
            <span class="list-item-value jcyt500"
              >开始时间：{{ getTime(item.event_start_time) }}</span
            >
            <span class="list-item-value jcyt500"
              >结束时间：{{ getTime(item.event_end_time) }}</span
            >
            <span
              class="list-item-value jcyt500"
              :class="
                item.status == 'is_pass'
                  ? 'is_pass'
                  : item.status == 'not_pass'
                  ? 'not_pass'
                  : 'not'
              "
              >{{
                `${chooseIndex == 1 ? "考试状态：" : "比赛状态："}${
                  item.status == "not_start"
                    ? "未开始"
                    : item.status == "is_start"
                    ? "已开始"
                    : item.status == "is_pass"
                    ? "已通过"
                    : "未通过"
                }`
              }}</span
            >
          </div>
        </div>
      </div>
    </div>
    <div class="page_box">
      <div style="display: flex">
        <div
          class="page_btn margin_2 jcyt600"
          :style="{ opacity: page == 1 ? '0.5' : '1' }"
          @click="switchPaging('1')"
        >
          首页
        </div>
        <div
          class="page_btn jcyt600"
          :style="{ opacity: page == 1 ? '0.5' : '1' }"
          @click="switchPaging('2')"
        >
          上一页
        </div>
      </div>
      <div class="page_text jcyt600">共{{ total == 0 ? 0 : page }}/{{ total }}页</div>
      <div style="display: flex">
        <div
          class="page_btn margin_2 jcyt600"
          :style="{ opacity: page >= total ? '0.5' : '1' }"
          @click="switchPaging('3')"
        >
          下一页
        </div>
        <div
          class="page_btn jcyt600"
          :style="{ opacity: page >= total ? '0.5' : '1' }"
          @click="switchPaging('4')"
        >
          尾页
        </div>
      </div>
    </div>
    <div class="modal" v-if="continueFlag"></div>
    <div class="continue-dialog" v-if="continueFlag">
      <span class="dialog-title">你有未完成的比赛,是否继续？</span>
      <div class="btn-wrap flex-row content-center">
        <div class="btn btn-1" @click="goGame">继续</div>
        <div class="btn btn-2" @click="back">返回</div>
      </div>
    </div>
  </div>
</template>
<script>
import evaluationApi from "@/api/evaluationMatch";
import timeFormat from "@/public/timeFormat";
import { number } from "echarts";
export default {
  data() {
    return {
      list: [],
      page: 1,
      total: 0,
      chooseIndex: 2,
      count: 0,
      limit: 4,
      isLoading: false,
      continueFlag: false,
      questionStatus: {},
      matchID: ""
    };
  },
  mounted() {
    this.page =
      Number(this.$storage.$getStroage("evaluation_index")) == 0
        ? 1
        : Number(this.$storage.$getStroage("evaluation_index"));
    this.getList();
    this.checkHasNotEndMatch();
  },
  methods: {
    back() {
      this.$storage.$setStroage("evaluation_index", 1);
      this.$router.push("/");
    },
    switchPaging(type) {
      if (!this.isLoading) {
        switch (type) {
          case "1":
            this.page = 1;
            break;
          case "2":
            if (this.page == 1) return;
            this.page = this.page - 1;
            break;
          case "3":
            if (this.page >= this.total) return;
            this.page = this.page + 1;
            break;
          case "4":
            if (this.page >= this.total) return;
            this.page = this.total;
            break;
        }
        this.$storage.$setStroage("evaluation_index", this.page);
        this.getList();
      }
    },
    getTime(time) {
      return timeFormat.GetCustTime(time, "MM月DD日 HH:mm");
    },
    goNext(item) {
      console.log(item);
      switch (item.status) {
        case "not_start":
          this.$router.push({
            path: "/evaluationStart",
            query: {
              matchID: item.id,
              type: this.chooseIndex == 1 ? "score" : "match"
            }
          });
          break;
        case "is_start":
          break;
        case "is_pass":
          this.$router.push({
            path: "/evaluationReport",
            query: {
              matchID: item.id
            }
          });
          break;
        case "not_pass":
          this.$router.push({
            path: "/evaluationReport",
            query: {
              matchID: item.id
            }
          });
          break;
      }
    },
    goGame() {
      console.log(this.questionStatus)
      if (
        (this.questionStatus["game_status"] == "is_start" ||
          this.questionStatus["game_status"] == "not_start") &&
        this.questionStatus["current_game_index"] != 0
      ) {
        this.$router.push({
          path: "/evaluationGame",
          query: {
            matchId: this.matchID,
            gameIndex: this.questionStatus["current_game_index"]
          }
        });
      } else if (
        this.questionStatus["question_status"] == "is_start" &&
        this.questionStatus["game_status"] == "not_start"
      ) {
        this.$router.push({
          path: "/evaluationQuestionPlay",
          query: {
            match_id: this.matchID,
            question_index: this.questionStatus["current_question_index"],
            from: "evaluationIndex"
          }
        });
      }
    },
    async getList() {
      this.isLoading = true;
      this.$store.commit("setApiLoading", true);
      let res = await evaluationApi.evaluationMatchListApi({
        page: this.page,
        limit: this.limit,
        event_type: this.chooseIndex == 1 ? "score" : "match"
      });
      this.list = res.data.results ?? [];
      this.count = res.data.count ?? 0;
      this.total = Math.ceil(this.count / this.limit);
      this.$store.commit("setApiLoading", false);
      this.isLoading = false;
    },
    async checkHasNotEndMatch() {
      let res = await evaluationApi.evaluationMatchNotEndApi();
      if (res.data["has_not_end_match"]) {
        this.matchID = res.data["id"];
        let res1 = await evaluationApi.gameStatus({ match_id: res.data["id"] });
        this.questionStatus = res1.data;
        this.continueFlag = true;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}

.content-between {
  justify-content: space-between;
}

.content-around {
  justify-content: space-around;
}
.content-center {
  justify-content: center;
}
.align-baseline {
  align-content: baseline;
}
.bg {
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.title-box {
  position: relative;
  text-align: center;
  padding-top: 56px;
}
.back {
  width: 120px;
  height: 120px;
  position: absolute;
  top: 24px;
  left: 40px;
  cursor: pointer;
}
.title {
  font-size: 56px;
  color: #fff;
}
.no-Message-container {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.no-Message {
  width: auto;
  height: 442px;
  margin-top: 60px;
  margin-bottom: 31px;
  margin-left: 70px;
}
.no-message-content {
  font-size: 36px;
  color: #3A3B3B;
}
.content {
  margin: 48px 80px 0;
}
.list-wrap {
  height: 100%;
  width: 100%;
}
.list-item {
  width: 864px;
  height: 304px;
  border-radius: 50px;
  box-shadow: 0 6px 20px 0 rgba(0, 85, 136, 0.3), #9ADDFF 0 -5px 8px 0;
  background: #fff;
  margin-right: 32px;
  margin-top: 32px;
  &:nth-child(1),
  &:nth-child(2) {
    margin-top: 0;
  }
  &:nth-child(2n) {
    margin-right: 0;
  }
  img {
    width: 224px;
    height: 224px;
    margin: 0 40px;
  }
  .list-item-title {
    font-size: 48px;
    color: #333;
    display: inline-block;
    width: 520px;
    line-height: 56px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .list-item-value {
    font-size: 32px;
    color: #999;
    line-height: 40px;
  }
  .is_pass {
    color: #00c26e;
  }
  .not_pass {
    color: #ff6461;
  }
  .not {
    color: #999;
  }
}
.list-item-content {
  height: 224px;
}

.page_box {
  width: 1760px;
  height: 136px;
  background: rgba(39, 151, 147, 0.25);
  border-radius: 68px;
  position: absolute;
  bottom: 64px;
  left: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-sizing: border-box;
  .page_btn {
    width: 224px;
    height: 88px;
    background-image: linear-gradient(180deg, #fcfdfe 6%, #eef8fc 94%);
    box-shadow: 0 4px 4px 0 rgba(0, 56, 79, 0.1), inset 0 8px 8px 0 #ffffff,
      inset 0 -8px 8px 0 #bbe5f4;
    border-radius: 40px;
    font-size: 36px;
    color: #16c3ff;
    letter-spacing: 0;
    text-align: center;
    line-height: 88px;
    cursor: pointer;
  }
  .margin_2 {
    margin-right: 40px;
  }
  .page_text {
    font-size: 36px;
    color: #fff;
    letter-spacing: 0;
    text-align: center;
    line-height: 44px;
  }
}
.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}
.continue-dialog {
  width: 600px;
  height: 330px;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  border-radius: 55px;
  background: #fff;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 10;
  text-align: center;
  .dialog-title {
    display: inline-block;
    padding-top: 30px;
    font-size: 34px;
    font-weight: 500;
    color: #333;
  }
  .btn-wrap {
    margin-top: 60px;
    .btn {
      width: 200px;
      height: 88px;
      font-size: 34px;
      font-weight: 600;
      border-radius: 44px;
      line-height: 88px;
    }
    .btn-1 {
      color: #fff;
      background: #31bfff;
    }
    .btn-2 {
      color: #fff;
      background: #c2ccdb;
      margin-left: 20px;
    }
  }
}
</style>
