<template>
  <div class="start" :style="{'background-image': `url(${require('@/assets/evaluation/比赛大厅背景.png')})`}">
    <img class="back" @click="back" src="@/assets/index/back.png"/>
    <div class="content">
      <div class="content-box">
        <img class="right-img" src="@/assets/evaluation/滑板小汪.png"/>
        <div class="title jcyt600" :style="{'background-image': `url(${require('@/assets/evaluation/title_bg.png')})`}">{{matchInfo.name}}</div>
        <div class="list">
          <div class="list-item flex-row items-center">
            <div class="circle"></div>
            <div class="flex-row items-center">
              <span class="list-item-label jcyt500">学生姓名：</span>
              <span class="list-item-value jcyt400" :class="{ 'special': type == 'score' }">{{username}}</span>
            </div>
          </div>
          <div class="list-item flex-row items-center">
            <div class="circle"></div>
            <div class="flex-row items-center">
              <span class="list-item-label jcyt500">{{type == 'score'? '考试等级：' : "比赛等级："}}</span>
              <span class="list-item-value jcyt400" :class="{ 'special': type == 'score' }">{{matchInfo.level_name}}</span>
            </div>
          </div>
          <div class="list-item flex-row items-center">
            <div class="circle"></div>
            <div class="flex-row items-center">
              <span class="list-item-label jcyt500">{{type == 'score'? '考试时间：' : "比赛时间："}}</span>
              <span class="list-item-value jcyt400" :class="{ 'special': type == 'score' }" v-html="matchInfo.time_config"></span>
            </div>
          </div>
          <div class="list-item flex-row items-center">
            <div class="circle"></div>
            <div class="flex-row items-center">
              <span class="list-item-label jcyt500">{{type == 'score'? '考试内容：' : "比赛内容："}}</span>
              <span class="list-item-value jcyt400" :class="{ 'special': type == 'score' }" v-html="matchInfo.content_config"></span>
            </div>
          </div>
          <div class="list-item-second">
            <div class="list-item-second-label jcyt600">{{type == 'score'? '考试规则' : "比赛规则"}}</div>
            <div v-html="matchInfo.math_rule_text" class="list-item-second-value jcyt400" :class="{ 'special': type == 'score' }"></div>
          </div>
          <div class="list-item-second">
            <div class="list-item-second-label jcyt600">注意事项</div>
            <div v-html="matchInfo.rule_text" class="list-item-second-value jcyt400" :class="{ 'special': type == 'score' }"></div>
          </div>
        </div>
        <div class="btn jcyt600" :style="{'background-image': matchInfo.can_start_match ? `url(${require('@/assets/evaluation/评测开始按钮.png')})` : `url(${require('@/assets/course/已结束.png')})`}" @click="goNext">{{ matchInfo.match_status == 'not_start' && matchInfo.can_start_match ? 
          type == 'score' ? '开始考试' : '开始比赛' :
          matchInfo.can_start_match == false ? matchInfo.cant_start_match_reason : '开始考试'}}</div>
      </div>
    </div>
  </div>
</template>
<script>
import evaluationApi from "@/api/evaluationMatch";
export default {
  data(){
    return {
      type: '',
      username: "",
      matchInfo: {},
      matchID: ""
    }
  },
  mounted(){
    this.username = this.$storage.$getStroage("username");
    this.matchID = this.$route.query.matchID;
    this.type = this.$route.query.type;
    this.getInfo();
  },
  methods: {
    back(){
      this.$router.push("/evaluationIndex");
    },
    async getInfo(){
      let res = await evaluationApi.evaluationMatchInfoApi({"match_id": this.matchID});
      this.matchInfo = res.data;
    },
    async goNext(){
      if (this.matchInfo.can_start_match) {
          let res = await evaluationApi.evaluationMatchInfoApi({"match_id": this.matchID});
          this.matchInfo = res.data;
          if (this.matchInfo.can_start_match) {
            this.$router.push({path: "/evaluationQuestionPlay", query: {
              "match_id": this.matchID,
              "question_index": '1',
              'event_type': this.type,
              "from": "/evaluationIndex"
            }})
          }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}

.content-between {
  justify-content: space-between;
}

.content-around {
  justify-content: space-around;
}
.content-center {
  justify-content: center;
}
.align-baseline {
  align-content: baseline;
}
.start {
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
  padding-top: 148px;
  box-sizing: border-box;
}
.back {
  width: 120px;
  height: 120px;
  position: absolute;
  top: 24px;
  left: 40px;
  cursor: pointer;
  z-index: 30;
}
.content {
  width: 1728px;
  height: 840px;
  background: linear-gradient(to bottom, #FCD24C,#F6BF26, #F9C93D);
  box-shadow: rgba(39, 151, 147, .55) 0 10px 20px 0, #FFEBAF 0 10px 20px 0 inset, #E39D1E 0 -10px 20px 0 inset;
  margin: 0 auto;
  position: relative;
  box-sizing: border-box;
  padding: 80px 40px 40px 40px;
  border-radius: 100px;
  .content-box {
    background: #FFFCF9;
    width: 100%;
    height: 100%;
    border-radius: 80px;
  }
  .list {
    height: 456px;
    padding: 56px 105px 0 105px;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 0px;
    }
  }
  .title {
    background-size: 100% 100%;
    font-size: 56px;
    color:#fff;
    min-width: 728px;
    height: 168px;
    margin: 0 auto;
    text-align: center;
    line-height: 130px;
    position: absolute;
    top: -100px;
    left: 50%;
    transform: translateX(-50%);
    padding: 0 140px;
    box-sizing: border-box;
    text-shadow: 0 -2px 6px #ff3900;
  }
  .right-img {
    position: absolute;
    right: -55px;
    bottom: -25px;
    height: 416px;
    width: 392px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .list-item {
    margin-top: 15px;
    &:nth-child(1) {
      margin-top: 0;
    }
    .circle {
      width: 32px;
      height: 32px;
      // border: 2px solid #FCC939;
      background: #FFDFA4;
      border-radius: 50%;
      margin-right: 16px;
    }
    .list-item-label {
      font-size: 40px;
      color: #333;
      line-height: 48px;
    }
    .list-item-value {
      font-size: 40px;
      color: #A16007;
      line-height: 48px;
      margin-left: 24px;
    }
  }
  .special {
    color: #A16007!important;
  }
  .list-item-second {
    .list-item-second-label {
      font-size: 36px;
      color: #fff;
      background: #FF7748;
      border-radius: 40px;
      margin-top: 56px;
      height: 80px;
      width: 208px;
      text-align: center;
      line-height: 80px;
    }
    .list-item-second-value {
      font-size: 36px;
      margin-top: 32px;
      line-height: 48px;
      color: #A16007;
      box-sizing: border-box;
      word-break: break-all;
      position: relative;
      z-index: 20;
    }
  }
  .btn {
    width: 667px;
    height: 131px;
    line-height: 120px;
    margin: 48px auto 0;
    // padding-bottom: 10px;
    box-sizing: border-box;
    text-align: center;
    color: #fff;
    font-size: 40px;
    cursor: pointer;
    background-size: cover;
    background-repeat: no-repeat;
  }
}
</style>