<template>
  <div class="timeoutDialog" v-if="flag">
    <div
      class="dialog_content"
      :style="{
          'background-image': `url(${require('@/assets/unitTest/no_time.png')})`,
          'background-size': '100% 100%'
        }"
    >
      <div class="btn_row">
        <div class="back jcyt500" @click="back">返回</div>
        <div class="goGame jcyt500" @click="goGame">进入对弈</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: { matchId: {} },
  data() {
    return {
      flag: false
    };
  },
  methods: {
    open() {
      this.flag = true;
    },
    close() {
      this.flag = false;
    },
    back() {
      this.flag = false;
      this.$router.push({
        path: "/",
      });
    },
    goGame() {
      this.flag = false;
      this.$router.push({
        path: "/evaluationGame",
        query: {
          matchId: this.matchId,
          gameIndex: 1,
          from: this.$route.query.from,
        }
      });
    }
  }
};
</script>

<style lang="less">
.timeoutDialog {
  width: 100vw;
  height: 100vh;
  position: fixed;
  background: rgba(0, 0, 0, 0.7);
  z-index: 311;
  display: flex;
  justify-content: center;
  align-items: center;
  .dialog_content {
    width: 880px;
    height: 900px;
    .btn_row {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 680px;
      .back {
        width: 300px;
        height: 100px;
        border-radius: 50px;
        background: #f3f3f3;
        color: #8c8c8c;
        text-align: center;
        line-height: 100px;
        margin-right: 20px;
        font-size: 40px;
      }
      .goGame {
        width: 300px;
        height: 100px;
        border-radius: 50px;
        background: #149eee;
        color: #ffffff;
        text-align: center;
        line-height: 100px;
        margin-right: 20px;
        font-size: 40px;
      }
    }
  }
}
</style>