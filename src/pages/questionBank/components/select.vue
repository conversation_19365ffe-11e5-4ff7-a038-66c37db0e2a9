<template>
  <div class="select_box" >
    <div class="select" @click="()=>{option_type=!option_type}">
      <div class="select-left">
        <div class="select_icon"></div>
      </div>
      <span class="jcyt600 hint">{{ hint }}</span>
    </div>
    <div class="option_box" v-if="option_type">
      <div
        class="option"
        v-for="(item,index) in list"
        :key="index"
        @click="optionChange(item)"
        :class="[option_active==index?'option_active':'' , option_active==index?'jcyt600':'jcyt500']"
      >{{item.name}}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    hint: {
      type: String,
      default: "切换分类"
    },
    list: {
      default: []
    },
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      option_type: false,
      option_active: 0
    };
  },
  watch: {
    list(new_arr) {
    },
    active(new_num){
        this.option_active = new_num;
    }
  },
  methods: {
    optionChange(val) {
      this.option_type = false;
      // this.hint = val.name;
      this.$emit("optionChange", val);
    }
  }
};
</script>

<style lang="less">
.select_box {
  .select {
    height: 80px;
    background-image: linear-gradient(to bottom, #FFFFFF, #DAF4FF);
    // background: #ffffff;
    border-radius: 44px;
    box-shadow: #8BB6E1 0 -3px 3px 0 inset, 0 4px 8px 0 rgba(0, 33, 135, .5);
    box-sizing: border-box;
    text-align: center;
    font-size: 36px;
    cursor: pointer;
    display: flex;
    .hint {
      margin-left: 22px;
      line-height: 80px;
      color: #294584;
      padding-right: 30px;
      width: 144px;
      text-align: center;
    }
    .select_icon {
      width: 49px;
      height: 52px;
      background: url("../../../assets/questionBank/Fill.png") no-repeat;
      background-size: 100% 100%;
      margin-top: 18px;
      margin-left: 28px;
    }
    .select-left {
      width: 84px;
      height: 80px;
      border-radius: 44px 0 0 44px;
      background: #3168E7;
      position: relative;
      box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      &::after {
        content:"";
        width: 0; height: 0;
        border-color: transparent #3168E7; /*上下颜色 左右颜色*/
        border-width: 0 0 80px 20px;
        border-style: solid;
        position: absolute;
        top: 0;
        left: 84px;
        box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      }
    }
  }
  .option_box {
    min-width: 320px;
    padding: 32px 40px;
    background: #ffffff;
    box-shadow: rgba(18, 103, 180, .5) 0 6px 29px 0, rgba(154, 221, 255, .75) 0 -5px 8px 0 inset;
    border-radius: 40px;
    margin-top: 16px;
    position: absolute;
    top: 88px;
    right: 0;
    z-index: 19;
    box-sizing: border-box;
    overflow: hidden;
    .option {
      text-align: center;
      height: 80px;
      line-height: 80px;
      font-size: 32px;
      cursor: pointer;
      color: #666666;
    }
    .option_active {
      background: rgba(0, 150, 255, 0.1);
      border-radius: 41.9px;
      color: #0096FF;
      letter-spacing: 0;
      text-align: center;
    }
    .option:hover {
      background: rgba(0, 150, 255, 0.1);
      border-radius: 41.9px;
      color: #0096FF;
      letter-spacing: 0;
      text-align: center;
    }
  }
}
</style>