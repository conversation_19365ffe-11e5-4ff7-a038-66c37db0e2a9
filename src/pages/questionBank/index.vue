<template>
  <div
    class="quesitionBank"
    :style="{
          'background-image': `url(${require('@/assets/questionBank/题库背景.png')})`,
          'background-size': '100% 100%'
        }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
          'background-image': `url(${require('@/assets/index/back.png')})`,
          'background-size': '100% 100%'
        }"
    ></div>
    <div
      class="my_icon"
      @click="goCollect"
      :style="{
          'background-image': `url(${require('@/assets/questionBank/我的收藏.png')})`,
          'background-size': '100% 100%'
        }"
    ></div>
    <div class="content">
      <ul class="menu">
        <li :class="{'menu-item-ac': top_category_id == item.id}" class="menu-item jcyt600" v-for="(item,index) in question_list" @click="classifyHandle(item)">{{item.name}}</li>
      </ul>
      <div class="classify-blue">
        <img src="@/assets/questionBank/title.png" class="title" />
        <div class="classify-yellow-behind"></div>
        <div class="classify-yellow">
          <div class="dashed-boder">
            <div class="classify-white flex-row items-center content-center">
              <div class="item-box" v-if="classify_info.data">
                <div class="item flex-row items-center content-center" v-for="(item,index) in classify_info.data" :key="index" @click="goList(item)"> <!---->
                  <div class="item-black flex-column items-center content-center">
                    <span class="jcyt600 name">{{item.name}}</span>
                    <span class="jcyt500 num">（{{item.total_question_numb}}）</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <img src="@/assets/questionBank/stone.png" class="stone" />
      </div>
    </div>
    <!-- <div class="classify" id="nav">
      <div>
        <div
          class="door_one"
          :style="{
          'background-image': `url(${require('@/assets/questionBank/指示牌.png')})`,
          'background-size': '100% 100%'
        }"
        ></div>
        <div
          class="stone_1"
          :style="{
          'background-image': `url(${require('@/assets/questionBank/石头1.png')})`,
          'background-size': '100% 100%'
        }"
        ></div>
      </div>
      <div v-for="(item,index) in question_list" :key="index" @click="classifyHandle(item)" class="list_box">
        <div
          class="door_three"
          v-if="index%2!==0"
          :style="{
          'background-image': item.category_numb>0 ? `url(${require('@/assets/questionBank/门.png')})` : `url(${require('@/assets/questionBank/门锁1.png')})`,
          'background-size': '100% 100%'
        }"
        >
          <span>{{item.name}}</span>
        </div>
        <div
          class="door_two"
          v-else
          :style="{
          'background-image': item.category_numb>0 ? `url(${require('@/assets/questionBank/门1.png')})` : `url(${require('@/assets/questionBank/门锁.png')})`,
          'background-size': '100% 100%'
        }"
        >
          <span>{{item.name}}</span>
        </div>
      </div>
      <div class="dialog_box" v-if="classify_type">
        <div
          class="dialog_content"
          :style="{
          'background-image': `url(${require('@/assets/questionBank/题库弹窗背景.png')})`,
          'background-size': '100% 100%'
        }"
        >
          <div
            class="dialog_title"
            :style="{
          'background-image': `url(${require('@/assets/questionBank/dialog_title.png')})`,
          'background-size': '100% 100%'
        }"
          >{{ classify_info.name }}</div>
          <div class="classify_box">
            <div
              class="question_name"
              v-for="(item,index) in classify_info.data"
              :key="index"
              @click="goList(item)"
              :style="{
          'background-image': `url(${require('@/assets/questionBank/子题库背景.png')})`,
          'background-size': '100% 100%'
        }"
            >
              <div class="name">{{item.name}}</div>
              <div class="num">({{ item.total_question_numb }})</div>
            </div>
          </div>
        </div>
        <div
          class="dialog_close"
          @click="dialogClose"
          :style="{
          'background-image': `url(${require('@/assets/questionBank/关闭按钮.png')})`,
          'background-size': '100% 100%'
        }"
        ></div>
      </div>
    </div> -->
  </div>
</template>

<script>
import questionApi from "../../api/questionBank";
export default {
  name: "quesitionBank",
  data() {
    return {
      question_list: [],
      // classify_type: false,
      classify_info: {},
      top_category_id: "",
    };
  },
  mounted() {
    this.$store.commit("setApiLoading", true);
    questionApi.GetTopCategoryList().then(res => {
      this.question_list = res.data;
      this.classifyHandle(this.question_list[0]);
      this.$store.commit("setApiLoading", false);
    });
    // this.scrollInit();
  },
  methods: {
    // scrollInit(){
    //    // 获取要绑定事件的元素
    //    const nav = document.getElementById("nav")
    //   var flag; // 鼠标按下
    //   var downX; // 鼠标点击的x下标
    //   var scrollLeft; // 当前元素滚动条的偏移量
    //   nav.addEventListener("mousedown", function (event) {
    //   flag = true;
    //   downX = event.clientX; // 获取到点击的x下标
    //   scrollLeft = this.scrollLeft; // 获取当前元素滚动条的偏移量
    //   });
    //   nav.addEventListener("mousemove", function (event) {
    //   if (flag) { // 判断是否是鼠标按下滚动元素区域
    //   var moveX = event.clientX; // 获取移动的x轴
    //   var scrollX = moveX - downX; // 当前移动的x轴下标减去刚点击下去的x轴下标得到鼠标滑动距离
    //   this.scrollLeft = scrollLeft - scrollX // 鼠标按下的滚动条偏移量减去当前鼠标的滑动距离
    //   }
    //   });
    //   // 鼠标抬起停止拖动
    //   nav.addEventListener("mouseup", function () {
    //   flag = false;
    //   });
    //   // 鼠标离开元素停止拖动
    //   nav.addEventListener("mouseleave", function (event) {
    //   flag = false;
    //   });
    // },
    classifyHandle(item, index) {
      __bl.sum(item.name + "题库");
      // this.classify_type = true;
      this.top_category_id = item.id;
      questionApi
        .GetCategoryList({
          source: "library",
          knowledge_point_top_category_id: item.id
        })
        .then(res => {
          this.classify_info = res.data;
        });
    },
    goList(val) {
      this.$router.push({
        path: "/questionList",
        query: {
          id: val.id,
          source: "library",
          top_category_id: this.top_category_id
        }
      });
    },
    goCollect() {
      this.$router.push({ path: "/myCollect" });
    },
    // dialogClose() {
    //   this.classify_type = false;
    // },
    goBack() {
      this.$store.commit("setImitateRoute", "studyRoom");
      this.$router.push({
        path: "/"
      });
    }
  }
};
</script>

<style lang="less">
.quesitionBank {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    cursor: pointer;
    z-index: 30;
  }
  .my_icon {
    width: 128px;
    height: 128px;
    position: absolute;
    top: 24px;
    right: 40px;
    cursor: pointer;
    z-index: 30;
  }
  .content {
    padding-top: 120px;
    // margin-left: 336px;
    box-sizing: border-box;
    position: relative;
  }
  .menu {
    position: absolute;
    left: 144px;
    top: 168px;
    width: 224px;
    height: 75.55vh;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    .menu-item {
      width: 162px;
      height: 8.14vh;
      border-radius: 30.99px 0 0 30.99px;
      box-shadow: #648FFC 0 3px 4px 0 inset, #2854F8 0 -8px 6px 0 inset;
      background: #487BFB;
      color: #2153D0;
      font-size: 38px;
      padding-left: 40px;
      box-sizing: border-box;
      line-height: 88px;
    }
    .menu-item + .menu-item {
      margin-top: 18px;
    }
    .menu-item-ac {
      width: 194px;
      box-shadow: #FFF5CD 0 3px 4px 0 inset, #F6B93D 0 -8px 6px 0 inset;
      background: #FBDA5B;
      color: #BA6419;
    }
  }
  .stone {
    position: absolute;
    width: 190.68px;
    height: 99.72px;
    right: 59.89px;
    bottom: 0;
    z-index: 10;
  }
  .classify-blue {
    width: 1424px;
    height: 85vh;
    border-radius: 64px;
    background: #7EADFF;
    box-shadow: rgba(0, 0, 0, 0.35) 0 11px 15px 0, #D7E6FF 0 5px 8px 0 inset, #82A6FB 0 -4px 8px 0 inset, #4F83FF 0 -15px 9px 0 inset;
    position: relative;
    margin-left: 328px;
  }
  .title {
    width: 267px;
    height: 121px;
    position: absolute;
    right: 59px;
    top: -17px;
    z-index: 20;
  }
  .classify-yellow {
    width: 1363.18px;
    height: 78.88vh;
    border-radius: 50px;
    background: #FFF0D2;
    box-shadow: rgba(6, 31, 70, .4) 0 4px 15px 4px;
    position: absolute;
    padding: 19px 19.99px;
    box-sizing: border-box;
    left: 20.6px;
    top: 26.34px;
    z-index: 4;
  }
  .classify-yellow-behind {
    width: 1363.18px;
    height: 78.88vh;
    border-radius: 50px;
    background: #FFF0D2;
    box-shadow: rgba(6, 31, 70, .4) 0 4px 15px 4px;
    position: absolute;
    left: 20.6px;
    top: 26.34px;
    z-index: 3;
    transform: rotate(-2deg);
  }
  .dashed-boder {
    width: 100%;
    height: 100%;
    border-radius: 36.8px;
    border: 3.87px dashed #FBD7A3;
    padding: 19.66px 18.25px 18.34px 17.73px;
    box-sizing: border-box;
  }
  .classify-white {
    background: #FFFDFA;
    border: 3.87px solid transparent;
    width: 100%;
    border-radius: 30px;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    background-clip: padding-box;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      z-index: -1;
      margin: -3.87px;
      border-radius: inherit; /*important*/
      background: linear-gradient(to bottom, #FFFFFF, rgba(255,255,255,0));
    }
  }
  .item-box {
    width: 1144px;
    height: 63.7vh;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .item {
      background-image: linear-gradient(to bottom, #F5D480, #F3B128);
      width: 328px;
      height: 19.25vh;
      border-radius: 24px;
      margin-right: 80px;
      margin-top: 32px;
    }
    div.item:nth-child(1), div.item:nth-child(2),div.item:nth-child(3) {
      margin-top: 0;
    }
    div.item:nth-child(3n) {
      margin-right: 0;
    }
    .item-black {
      background: #695D6F;
      border: 3.27px solid #F3B431;
      border-radius: 20px;
      width: 288px;
      height: 168px;
      .name {
        color: #FFCB4E;
        font-size: 36px;
        line-height: 46px;
      }
      .num {
        color: rgba(255, 255, 255, 0.5);
        font-size: 26px;
        line-height: 32px;
        margin-top: 16px;
      }
    }
  }

  // .classify::-webkit-scrollbar{
  //   display: none;
  // }
  // .classify {
  //   display: flex;
  //   padding: 510px 50px 0 127px;
  //   overflow-x: auto;
  //   // width: 180%;
  //   height: 800px;
  //   .list_box{
  //     height: 410px;
  //   }
  //   .door_one {
  //     width: 314px;
  //     height: 318px;
  //   }
  //   .stone_1 {
  //     width: 88px;
  //     height: 48px;
  //     margin: 50px 0 0 216px;
  //   }
  //   .door_two {
  //     width: 494px;
  //     height: 302px;
  //     margin: 256px 0 0 6px;
  //     text-align: center;
  //     font-size: 56px;
  //     color: #ff7800;
  //     font-weight: 600;
  //     cursor: pointer;
  //   }
  //   .door_three {
  //     width: 314px;
  //     height: 400px;
  //     margin: 16px 0 0 10px;
  //     text-align: center;
  //     font-size: 56px;
  //     color: #ff7800;
  //     font-weight: 600;
  //     cursor: pointer;
  //   }
  // }
  .dialog_box {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.6);

    .dialog_content {
      width: 1296px;
      height: 872px;
      margin: auto;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      padding: 151px 88px;
      box-sizing: border-box;
      .dialog_title {
        width: 508px;
        height: 144px;
        position: absolute;
        top: -57px;
        left: 394px;
        text-align: center;
        font-size: 56px;
        color: #ffffff;
        font-weight: 600;
        padding-top: 15px;
        box-sizing: border-box;
      }
      .classify_box {
        height: 560px;
        overflow-y: auto;
      }
      .classify_box::-webkit-scrollbar {
        display: none;
      }
      .question_name {
        width: 352px;
        height: 264px;
        float: left;
        margin: 0 32px 32px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .name {
          font-size: 44px;
          color: #ffcb4e;
          margin-bottom: 16px;
        }
        .num {
          font-size: 32px;
          color: #ffffff;
        }
      }
      .question_name:nth-child(3n) {
        margin-right: 0;
      }
    }
    .dialog_close {
      width: 96px;
      height: 96px;
      background: url("../../assets/questionBank/关闭按钮.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 85%;
      top: 27%;
      transform: translate(-60%, -50%);
      cursor: pointer;
    }
  }
}
</style>