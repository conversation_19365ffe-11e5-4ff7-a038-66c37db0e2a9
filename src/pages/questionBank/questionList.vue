<template>
  <div
    class="questionList"
    :style="{
          'background-image': `url(${require('@/assets/questionBank/题库背景.png')})`,
          'background-size': '100% 100%'
        }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="top_row flex-row content-center">
      <div class="title jcyt600">{{questionObj.name}}</div>
    </div>
    <selectView
      class="q-select"
      :list="top_category_list"
      @optionChange="optionChange"
      :active="option_index"
    ></selectView>
    <div class="list_box">
      <div class="list_ul">
        <div
          class="list_li"
          v-for="(item,index) in questionObj.data"
          :key="index"
          @click="goPlay(item,index)"
        >
          <div class="list_img">
            <img :src="item.img" />
            <div class="min_icon" v-if="item.status != 'not_answer'">
              <img src="../../assets/questionBank/is-right.png" v-if="item.status == 'is_right'" />
              <img src="../../assets/questionBank/is-wrong.png" v-else />
            </div>
          </div>

          <div class="list_num jcyt500">Q{{item.id}}</div>
        </div>
      </div>
      <div class="page_box">
        <div style="display:flex;">
          <div
            class="page_btn margin_2 jcyt600"
            :style="{'opacity':page==1?'0.5':'1'}"
            @click="switchPaging('1')"
          >首页</div>
          <div
            class="page_btn jcyt600"
            :style="{'opacity':page==1?'0.5':'1'}"
            @click="switchPaging('2')"
          >上一页</div>
        </div>
        <div class="page_text jcyt600">共{{page}}/{{total}}页</div>
        <div style="display:flex;">
          <div
            class="page_btn margin_2 jcyt600"
            :style="{'opacity':page==total?'0.5':'1'}"
            @click="switchPaging('3')"
          >下一页</div>
          <div
            class="page_btn jcyt600"
            :style="{'opacity':page==total?'0.5':'1'}"
            @click="switchPaging('4')"
          >尾页</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import selectView from "./components/select.vue";
import questionBankApi from "@/api/questionBank";
export default {
  data() {
    return {
      questionObj: [],
      top_category_list: [],
      classify_id: "",
      source: "",
      limit: 12,
      page: 1,
      total: 0,
      option_index: 0
    };
  },
  components: { selectView },
  mounted() {
    this.classify_id = this.$route.query.id;
    this.source = this.$route.query.source;
    if (this.$route.query.page) {
      this.page = this.$route.query.page;
    }
    this.initList();
    this.initTopCategory();
  },
  methods: {
    optionChange(val) {
      this.classify_id = val.id;
      this.page = 1;
      this.initList();
    },
    initList() {
      this.$store.commit("setApiLoading", true);
      questionBankApi
        .GetQuestionList({
          knowledge_point_category_id: this.classify_id,
          source: this.source,
          limit: this.limit,
          page: this.page
        })
        .then(res => {
          this.questionObj = res.data;
          this.total = Math.ceil(this.questionObj.count / this.limit);
          setTimeout(() => {
            this.top_category_list.forEach((t, index) => {
              if (t.id == this.questionObj.id) {
                this.option_index = index;
              }
            });
          }, 500);
          this.$store.commit("setApiLoading", false);
        });
    },
    initTopCategory() {
      questionBankApi
        .GetCategoryList({
          source: this.source,
          knowledge_point_top_category_id: this.$route.query.top_category_id
        })
        .then(res => {
          this.top_category_list = res.data.data;
        });
    },
    switchPaging(val) {
      if (val == 1) {
        this.page = 1;
      } else if (val == 2) {
        if (this.page > 1) {
          this.page--;
        }
      } else if (val == 3) {
        if (this.page < this.total) {
          this.page++;
        }
      } else if (val == 4) {
        this.page = this.total;
      }
      this.initList();
    },
    goPlay(val, i) {
      let obj = {
        questionIndex: (this.page - 1) * this.limit + i + 1,
        matchId: val.id,
        lesson_id: this.$route.query.id,
        source: this.$route.query.source
      };
      this.$router.push({ path: "/questionPlay", query: obj });
    },
    goBack() {
      if (this.$route.query.index) {
        this.$router.replace({
          path: "/myCollect?index=" + this.$route.query.index
        });
      } else {
        this.$router.push("/questionBank");
      }
    }
  }
};
</script>

<style lang="less">
.questionList {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    cursor: pointer;
    z-index: 30;
  }
  .title {
    width: 483px;
    height: 88px;
    background-image: linear-gradient(to right, #1092FF, #147DFF);
    line-height: 88px;
    color: #fff;
    font-size: 56px;
    text-align: center;
    border-radius: 44px;
  }
  .top_row {
    width: 100%;
    height: 88px;
    padding-top: 48px;
  }
  .q-select {
    position: absolute;
    right: 48px;
    top: 48px;
    z-index: 10;
    width: 280px;
    border-radius: 44px;
  }
  // .top_row {
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  //   width: 100vw;
  //   margin-top: 50px;
  //   .back {
  //     width: 128px;
  //     height: 128px;
  //     margin: 0 0 0 50px;
  //   }
  //   .title {
  //     font-size: 56px;
  //     color: #ffffff;
  //     letter-spacing: 0;
  //     text-align: center;
  //     line-height: 64px;
  //   }
  //   .margin_1 {
  //     margin: 0 50px 0 0;
  //   }
  // }
  .list_box {
    width: 1760px;
    height: 880px;
    background: #7EADFF;
    box-shadow: 0 8px 11px 0 rgba(0, 0, 0, 0.35), inset 0 4px 6px 0 #BEF6FF,
      inset 0 -3px 6px 0 #82A6FB, inset 0 -11px 7px 0 #4F83FF;
    border-radius: 50px;
    margin: 24px auto;
    position: relative;
    overflow: hidden;
    .list_ul {
      width: 1696px;
      height: 676px;
      // margin: 56px auto;
      background: #FFF6EE;
      // border: 5.63px solid transparent;
      border-radius: 40px;
      box-sizing: border-box;
      margin: 32px 32px 0 32px;
      padding: 26.37px 22.37px 22.37px 22.37px;
      box-shadow: 0 3px 7px 0 #5588FE, 0 -4px 4px 0 #FFE0A5 inset;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        z-index: -1;
        margin: -5.63px;
        border-radius: inherit; /*important*/
        background: linear-gradient(to bottom, rgba(255, 255, 255, .5), rgba(255,255,255,0));
      }
      .list_li {
        width: 240px;
        float: left;
        margin-right: 40px;
        margin-bottom: 24px;
        cursor: pointer;
        .list_img {
          width: 240px;
          height: 240px;
          background: #f7a448;
          border-radius: 22.11px;
          overflow: hidden;
          position: relative;
          padding: 10px;
          box-sizing: border-box;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .list_num {
          font-size: 32px;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          margin-top: 16px;
        }
        .min_icon {
          width: 48px;
          height: 48px;
          border-radius: 24px;
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
      .list_li:nth-child(6n) {
        margin: 0;
      }
    }
    .page_box {
      width: 1760px;
      height: 88px;
      position: absolute;
      bottom: 48px;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32px;
      box-sizing: border-box;
      .page_btn {
        width: 240px;
        height: 88px;
        background-image: linear-gradient(180deg, #fffcf2 6%, #fff5e7 94%);
        box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1), inset 0 7px 4px 0 #ffffff,
          inset 0 -7px 7px 0 #ffe8c8;
        border-radius: 40px;
        font-size: 36px;
        color: #b65600;
        letter-spacing: 0;
        text-align: center;
        line-height: 88px;
        cursor: pointer;
      }
      .margin_2 {
        margin-right: 32px;
      }
      .page_text {
        font-size: 36px;
        color: #FFFFFF;
        line-height: 44px;
        letter-spacing: 0;
        text-align: center;
      }
    }
  }
}
</style>