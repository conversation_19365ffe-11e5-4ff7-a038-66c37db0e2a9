<template>
  <div class="myCollect" :style="{
          'background-image': `url(${require('@/assets/questionBank/题库背景.png')})`,
          'background-size': '100% 100%'
        }">
    <div class="back" @click="goBack" :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"></div>
    <div class="content">
      <ul class="menu">
        <li :class="{'menu-item-ac': category_id == item.id}" class="menu-item jcyt600" v-for="(item,index) in question_list" @click="swichNav(item, index)">{{item.name}}</li>
      </ul>
      <div class="classify-blue">
        <img src="@/assets/questionBank/collect_title.png" class="title" />
        <div class="classify-yellow-behind"></div>
        <div class="classify-yellow">
          <div class="dashed-boder">
            <div class="classify-white flex-row items-center content-center">
              <div class="item-box">
                <div class="item flex-row items-center content-center" v-for="(item,index) in category_List" :key="index" @click="goList(item)"> <!---->
                  <div class="item-black flex-column items-center content-center">
                    <span class="jcyt600 name">{{item.name}}</span>
                    <span class="jcyt500 num">（{{item.favorite_question_numb}}）</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <img src="@/assets/questionBank/stone.png" class="stone" />
      </div>
    </div>
    <!-- <div class="list_box">
      <div class="nav_ul">
        <div
          class="nav_li"
          v-for="(item,index) in question_list"
          :key="index"
          :class="active==index?'nav_active':''"
          @click="swichNav(item,index)"
        >{{item.name}}</div>
      </div>
      <div class="list_content">
        <div class="classify_box">
          <div
            class="question_name"
            v-for="(item,index) in category_List"
            :key="index"
            @click="goList(item)"
            :style="{
          'background-image': `url(${require('@/assets/questionBank/子题库背景.png')})`,
          'background-size': '100% 100%'
        }"
          >
            <div class="name">{{item.name}}</div>
            <div class="num">({{item.favorite_question_numb}})</div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import questionApi from "../../api/questionBank";
export default {
  data() {
    return {
      question_list: [],
      active: 0,
      category_List: [],
      category_id: 0
    };
  },
  mounted() {
    if(this.$route.query.index){
      this.active = this.$route.query.index
    }
    questionApi.GetTopCategoryList().then(res => {
      this.question_list = res.data;
      this.category_id = this.question_list[0].id;
      // var obj = { name: "全部", id: 0 };
      // this.question_list.unshift(obj);
    });
    this.initCollectList();
  },
  methods: {
    initCollectList() {
      this.$store.commit("setApiLoading", true);
      questionApi
        .GetCategoryList({
          source: "favorite",
          knowledge_point_top_category_id: this.category_id
        })
        .then(res => {
          this.category_List = res.data.data;
          this.$store.commit("setApiLoading", false);
        });
    },
    swichNav(item, index) {
      this.active = index;
      this.category_id = item.id;
      this.initCollectList();
    },
    goList(val) {
      this.$router.push({
        path: "/questionList",
        query: {
          id: val.id,
          source: "favorite",
          top_category_id: "this.category_id",
          index:this.active
        }
      });
    },
    goBack() {
      // if(this.$route.query.from_url == '' || typeof this.$route.query.from_url == "undefined"){
      //   this.$router.replace({path:"/questionBank"});
      // }else {
      //   this.$router.push({path: this.$route.query.from_url});
      // }
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="less">
.myCollect {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    cursor: pointer;
    z-index: 30;
  }
  .content {
    padding-top: 120px;
    // margin-left: 336px;
    box-sizing: border-box;
    position: relative;
  }
  .menu {
    position: absolute;
    left: 144px;
    top: 168px;
    width: 224px;
    height: 75.55vh;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    .menu-item {
      width: 162px;
      height: 8.14vh;
      border-radius: 30.99px 0 0 30.99px;
      box-shadow: #FFF5CD 0 3px 4px 0 inset, #F6B93D 0 -8px 6px 0 inset;
      background: #FFD654;
      color: #BA6419;
      font-size: 38px;
      padding-left: 40px;
      box-sizing: border-box;
      line-height: 8.14vh;
    }
    .menu-item + .menu-item {
      margin-top: 18px;
    }
    .menu-item-ac {
      width: 194px;
      box-shadow: #FFD7CD 0 3px 4px 0 inset, #F6493D 0 -8px 6px 0 inset;
      background: #FB755B;
      color: #BA1919;
    }
  }
  .stone {
    position: absolute;
    width: 190.68px;
    height: 99.72px;
    right: 59.89px;
    bottom: 0;
    z-index: 10;
  }
  .classify-blue {
    width: 1424px;
    height: 85vh;
    border-radius: 64px;
    background: #FFD648;
    box-shadow: rgba(13, 123, 28, 0.35) 0 11px 15px 0, #FFF9D7 0 5px 8px 0 inset, #FBB982 0 -4px 8px 0 inset, #FF7C4F 0 -15px 9px 0 inset;
    position: relative;
    margin-left: 328px;
  }
  .title {
    width: 267px;
    height: 121px;
    position: absolute;
    right: 59px;
    top: -17px;
    z-index: 20;
  }
  .classify-yellow {
    width: 1363.18px;
    height: 78.88vh;
    border-radius: 50px;
    background: #FFF0D2;
    box-shadow: rgba(255, 124, 23, .4) 0 4px 15px 4px;
    position: absolute;
    padding: 19px 19.99px;
    box-sizing: border-box;
    left: 20.6px;
    top: 26.34px;
    z-index: 4;
  }
  .classify-yellow-behind {
    width: 1363.18px;
    height: 78.88vh;
    border-radius: 50px;
    background: #FFF0D2;
    box-shadow: rgba(255, 124, 23, .4) 0 4px 15px 4px;
    position: absolute;
    left: 20.6px;
    top: 26.34px;
    z-index: 3;
    transform: rotate(-2deg);
  }
  .dashed-boder {
    width: 100%;
    height: 100%;
    border-radius: 36.8px;
    border: 3.87px dashed #FBD7A3;
    padding: 19.66px 18.25px 18.34px 17.73px;
    box-sizing: border-box;
  }
  .classify-white {
    background: #FFFDFA;
    border: 3.87px solid transparent;
    width: 100%;
    border-radius: 30px;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    background-clip: padding-box;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      z-index: -1;
      margin: -3.87px;
      border-radius: inherit; /*important*/
      background: linear-gradient(to bottom, #FFFFFF, rgba(255,255,255,0));
    }
  }
  .item-box {
    width: 1144px;
    height: 63.7vh;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    .item {
      background-image: linear-gradient(to bottom, #F5D480, #F3B128);
      width: 328px;
      height: 19.25vh;
      border-radius: 24px;
      margin-right: 80px;
      margin-top: 32px;
    }
    div.item:nth-child(1), div.item:nth-child(2),div.item:nth-child(3) {
      margin-top: 0;
    }
    div.item:nth-child(3n) {
      margin-right: 0;
    }
    .item-black {
      background: #695D6F;
      border: 3.27px solid #F3B431;
      border-radius: 20px;
      width: 288px;
      height: 168px;
      .name {
        color: #FFCB4E;
        font-size: 36px;
        line-height: 46px;
      }
      .num {
        color: rgba(255, 255, 255, 0.5);
        font-size: 26px;
        line-height: 32px;
        margin-top: 16px;
      }
    }
  }
  // .top_row {
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  //   width: 100vw;
  //   margin-top: 50px;
    
  //   .title {
  //     font-family: PingFangSC-Medium;
  //     font-size: 56px;
  //     color: #ffffff;
  //     letter-spacing: 0;
  //     text-align: center;
  //     line-height: 64px;
  //   }
  //   .margin_1 {
  //     margin: 0 50px 0 0;
  //   }
  // }
  // .list_box {
  //   display: flex;
  //   align-items: center;
  //   margin-top: 32px;
  //   .nav_ul {
  //     margin: 0 40px 0 80px;
  //     .nav_li {
  //       width: 232px;
  //       height: 96px;
  //       background-image: linear-gradient(180deg, #fffcf2 6%, #fff5e7 94%);
  //       box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.1), inset 0 8px 6px 0 #ffffff,
  //         inset 0 -8px 8px 0 #ffe8c8;
  //       border-radius: 48px;
  //       font-size: 40px;
  //       color: #a93b00;
  //       letter-spacing: 0;
  //       text-align: center;
  //       margin-bottom: 24px;
  //       font-size: 40px;
  //       color: #a93b00;
  //       letter-spacing: 0;
  //       text-align: center;
  //       line-height: 96px;
  //       cursor: pointer;
  //     }
  //     .nav_active {
  //       background-image: linear-gradient(180deg, #fed018 6%, #fdb100 94%);
  //       box-shadow: 0 4px 8px 0 rgba(0, 56, 79, 0.1), inset 0 8px 8px 0 #ffe570,
  //         inset 0 -8px 8px 0 #ff9b08;
  //       border-radius: 48px;
  //       color: #ffffff;
  //       cursor: pointer;
  //     }
  //   }
  //   .list_content {
  //     width: 1616px;
  //     height: 1216px;
  //     background-image: linear-gradient(180deg, #fffcf2 6%, #fff5e7 94%);
  //     box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.1), inset 0 8px 6px 0 #ffffff,
  //       inset 0 -8px 8px 0 #ffe8c8;
  //     border-radius: 50px;
  //     padding: 56px;
  //     box-sizing: border-box;
  //     .classify_box {
  //       height: 560px;
  //       overflow-y: auto;
  //     }
  //     .classify_box::-webkit-scrollbar {
  //       display: none;
  //     }
  //     .question_name {
  //       width: 352px;
  //       height: 264px;
  //       float: left;
  //       margin: 0 32px 32px 0;
  //       display: flex;
  //       flex-direction: column;
  //       align-items: center;
  //       justify-content: center;
  //       cursor: pointer;
  //       .name {
  //         font-size: 44px;
  //         color: #ffcb4e;
  //         margin-bottom: 16px;
  //       }
  //       .num {
  //         font-size: 32px;
  //         color: #ffffff;
  //       }
  //     }
  //     .question_name:nth-child(4n) {
  //       margin-right: 0;
  //     }
  //   }
  // }
}
</style>