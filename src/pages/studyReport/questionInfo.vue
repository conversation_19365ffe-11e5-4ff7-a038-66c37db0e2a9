<template>
  <div class="">
    <div class="question_wrap">
      <p class="title jcyt600">{{ date_type == "week" ? "本周" : "本月" }}答题总览</p>
      <div class="progress_wrap">
        <div class="left_progress">
          <div class="this_week_progress">
            <div
              class="this_week jcyt400"
              :style="{
                width:
                  getLength(
                    studyReportDetailInfo['answer_data'][
                      'answer_count_this_time'
                    ],
                    studyReportDetailInfo['answer_data'][
                      'answer_count_last_time'
                    ]
                  ) + '%'
              }"
            ></div>

            <p class="question_num jcyt400">
              {{
                studyReportDetailInfo["answer_data"]["answer_count_this_time"]
              }}题/{{ date_type == "week" ? "本周" : "本月" }}
            </p>
          </div>
          <div class="last_week_progress">
            <div
              class="last_week"
              :style="{
                width:
                  getLength(
                    studyReportDetailInfo['answer_data'][
                      'answer_count_last_time'
                    ],
                    studyReportDetailInfo['answer_data'][
                      'answer_count_this_time'
                    ]
                  ) + '%'
              }"
            ></div>

            <p class="question_num jcyt400">
              {{
                studyReportDetailInfo["answer_data"]["answer_count_last_time"]
              }}题/{{ date_type == "week" ? "上周" : "上月" }}
            </p>
          </div>
        </div>
        <div class="left_progress right_progress">
          <div class="this_week_progress">
            <div
              class="this_week jcyt400"
              :style="{
                width:
                  getLength(
                    studyReportDetailInfo['answer_data'][
                      'answer_count_this_time'
                    ] == 0
                      ? 0
                      : studyReportDetailInfo['answer_data'][
                          'answer_rate_this_time'
                        ] /
                          studyReportDetailInfo['answer_data'][
                            'answer_count_this_time'
                          ],
                    studyReportDetailInfo['answer_data'][
                      'answer_count_last_time'
                    ] == 0
                      ? 0
                      : studyReportDetailInfo['answer_data'][
                          'answer_rate_last_time'
                        ] /
                          studyReportDetailInfo['answer_data'][
                            'answer_count_last_time'
                          ]
                  ) + '%'
              }"
            ></div>

            <p class="question_num jcyt400">
              {{
                Math.round(
                  studyReportDetailInfo["answer_data"][
                    "answer_rate_this_time"
                  ] != 0 &&
                    studyReportDetailInfo["answer_data"][
                      "answer_count_this_time"
                    ] != 0
                    ? (studyReportDetailInfo["answer_data"][
                        "answer_rate_this_time"
                      ] *
                        100) /
                        studyReportDetailInfo["answer_data"][
                          "answer_count_this_time"
                        ]
                    : 0
                )
              }}%/{{ date_type == "week" ? "本周" : "本月" }}
            </p>
          </div>
          <div class="last_week_progress">
            <div
              class="last_week jcyt400"
              :style="{
                width:
                  getLength(
                    studyReportDetailInfo['answer_data'][
                      'answer_count_last_time'
                    ] == 0
                      ? 0
                      : studyReportDetailInfo['answer_data'][
                          'answer_rate_last_time'
                        ] /
                          studyReportDetailInfo['answer_data'][
                            'answer_count_last_time'
                          ],
                    studyReportDetailInfo['answer_data'][
                      'answer_count_this_time'
                    ] == 0
                      ? 0
                      : studyReportDetailInfo['answer_data'][
                          'answer_rate_this_time'
                        ] /
                          studyReportDetailInfo['answer_data'][
                            'answer_count_this_time'
                          ]
                  ) + '%'
              }"
            ></div>

            <p class="question_num jcyt400">
              {{
                Math.round(
                  studyReportDetailInfo["answer_data"][
                    "answer_rate_last_time"
                  ] != 0 &&
                    studyReportDetailInfo["answer_data"][
                      "answer_count_last_time"
                    ] != 0
                    ? (studyReportDetailInfo["answer_data"][
                        "answer_rate_last_time"
                      ] *
                        100) /
                        studyReportDetailInfo["answer_data"][
                          "answer_count_last_time"
                        ]
                    : 0
                )
              }}%/{{ date_type == "week" ? "上周" : "上月" }}
            </p>
          </div>
        </div>
      </div>
      <div class="question_info_wrap">
        <div
          v-for="(item, index) in studyReportDetailInfo['answer_data'][
            'AnswerLabel'
          ]"
          :key="index"
          class="question_info_item"
          :style="{
            'background-image': `url(${require('@/assets/studyReportDetail/答题总览item背景.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <p class="name jcyt400">{{ item["LabelName"] }}</p>
          <div class="question_info_content">
            <p class="tex jcyt400">
              总答题
              <span class="jcyt500">{{
                item["TotalCount"] != 0 ? item["TotalCount"] : "0"
              }}</span>
            </p>
            <p class="tex jcyt400">
              正确率
              <span class="jcyt500"
                >{{
                  Math.round(
                    item["TotalCount"] != 0 && item["RightCount"] * 100 != 0
                      ? (item["RightCount"] * 100) / item["TotalCount"]
                      : "0"
                  )
                }}%</span
              >
            </p>
          </div>
        </div>
      </div>
      <p class="h1 jcyt400">
        {{ date_type == "week" ? "本周" : "本月" }}答题练习超过全国
        <span>
          {{
            (
              studyReportDetailInfo["answer_data"]["AnswerRank"][
                "percent_all"
              ] * 100
            ).toFixed(1)
          }}%</span
        >
        的小棋手
      </p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    date_type: {},
    studyReportDetailInfo: {}
  },

  methods: {
    //计算进度条长度
    getLength(x, y) {
      if (x > y) {
        return (460 / 642) * 100;
      } else {
        if (x == 0 || y == 0) {
          return 0;
        } else {
          return (x / y) * (460 / 642) * 100;
        }
      }
    }
  }
};
</script>

<style lang="less">
.question_wrap {
  width: 1608px;
  height: 1496px;
  margin: -75px auto 0;
  overflow: hidden;
  p.title {
    font-size: 56px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 4px 8px rgba(235, 108, 42, 0.75);
  }
  .progress_wrap {
    width: 1368px;
    height: 108px;
    margin: 260px auto 0;
    display: flex;
    justify-content: space-between;
    .left_progress {
      width: 642px;
      height: 108px;

      .this_week_progress {
        width: 642px;
        height: 48px;
        display: flex;
        justify-content: left;
        align-items: center;

        .this_week {
          height: 48px;
          background: #ffb117;
          border-radius: 24px;
        }

        .question_num {
          float: left;
          font-size: 32px;
          color: #666666;
          margin-left: 12px;
        }
      }
      .last_week_progress {
        width: 642px;
        height: 48px;
        margin-top: 12px;
        display: flex;
        justify-content: left;
        align-items: center;

        .last_week {
          height: 48px;
          background: rgba(255, 177, 23, 0.5);
          border-radius: 24px;
          font-size: 32px;
          color: #666666;
        }

        .question_num {
          font-size: 32px;
          color: #666666;
          margin-left: 12px;
        }
      }
    }
    .right_progress {
      .this_week {
        background: #2ea7ff !important;
      }
      .last_week {
        background: rgba(46, 167, 255, 0.5) !important;
      }
    }
  }
}
.question_info_wrap {
  width: 1368px;
  height: 536px;
  margin: 50px auto 0;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
  flex-wrap: wrap;
  .question_info_item {
    width: 655px;
    height: 240px;
    padding: 40px;
    box-sizing: border-box;
    display: flex;
    justify-content: left;
    .name {
      width: 170px;
      height: 180px;
      font-size: 48px;
      color: #a45c34;
    }
    .question_info_content {
      margin-left: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .tex {
        font-size: 36px;
        color: #666666;

        span {
          color: #ff7766;
        }
      }
      .tex:nth-child(1) {
        margin-bottom: 10px;
      }
    }
  }
}
.h1 {
  font-size: 36px;
  color: #8d8e93;
  letter-spacing: 0.6px;
  text-align: center;
  width: 1368px;
  height: 160px;
  line-height: 160px;
  background: #f7f5f2;
  border-radius: 30px;
  text-align: center;
  margin: 75px auto 0;
  span {
    color: #ff7766;
  }
}
</style>
