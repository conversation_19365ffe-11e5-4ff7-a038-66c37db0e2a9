<template>
  <div class="studyReportDetail">
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/studyReportDetail/new/back.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div
      v-if="apiDone"
      class="report_box"
      :style="{
        'background-image': `url(${require('@/assets/studyReportDetail/学习报告背景.png')})`,
        'background-size': '100% 100%',
      }"
    >
      <div class="user_info">
        <div class="info">
          <div class="head">
            <img :src="studyReportDetailInfo['user_info']['avatar']" alt="" />
          </div>
          <div class="name_wrap">
            <p class="name jcyt600">{{ studyReportDetailInfo["user_info"]["name"] }}</p>
            <div class="level jcyt500">
              等级：{{
                studyReportDetailInfo["user_info"]["student_profile"][
                  "nwp_level_name"
                ]
              }}
            </div>
          </div>
        </div>
        <div class="date jcyt600">{{ studyReportDetailInfo["report_name"] }}</div>
      </div>
      <div class="study_time">
        <p class="title jcyt600">{{ date_type == "week" ? "本周" : "本月" }}学习时长</p>
        <div class="study_time_wrap">
          <div class="pie_chart">
            <charts
              :video_time="
                studyReportDetailInfo['video_cost_data']['video_cost_this_time']
              "
              :game_time="
                studyReportDetailInfo['game_data']['game_cost_this_time']
              "
              :date_type="date_type"
            ></charts>
          </div>
          <div class="study_time_p">
            <div class="game_time study_time_item">
              <div class="icon">
                <img src="@/assets/studyReportDetail/对局时长icon.png" alt="" />
                <p class="title_time jcyt500">对局时长</p>
              </div>

              <p class="seconds jcyt500">
                <span class="time">{{
                  Math.round(
                    studyReportDetailInfo["game_data"]["game_cost_this_time"] /
                      60
                  )
                }}</span
                >分钟
              </p>
            </div>
            <div class="video_time study_time_item">
              <div class="icon">
                <img src="@/assets/studyReportDetail/视屏学习icon.png" alt="" />
                <p class="title_time jcyt500">视频学习</p>
              </div>

              <p class="seconds jcyt500">
                <span class="time">{{
                  Math.round(
                    studyReportDetailInfo["video_cost_data"][
                      "video_cost_this_time"
                    ] / 60
                  )
                }}</span
                >分钟
              </p>
            </div>
          </div>
        </div>
      </div>

      <questionInfo
        :studyReportDetailInfo="studyReportDetailInfo"
        :date_type="date_type"
      ></questionInfo>
      <gameInfo
        :studyReportDetailInfo="studyReportDetailInfo"
        :date_type="date_type"
        :newList="newList"
      ></gameInfo>
    </div>
  </div>
</template>

<script>
import studyReportApi from "@/api/studyReport";
import mineApi from "@/api/mine";
import questionInfo from "./questionInfo";
import gameInfo from "./gameInfo";
import charts from "./charts";

export default {
  data() {
    return {
      studyReportDetailInfo: {},
      regTime: "",
      apiDone: false,
      newList: [],
    };
  },
  watch: {},
  components: {
    questionInfo,
    gameInfo,
    charts,
  },
  computed: {
    date_type() {
      return this.$route.query.date_type;
    },
    id() {
      return this.$route.query.id;
    },
  },
  mounted() {
    studyReportApi
      .StudyReportListApi({
        page: 1,
        limit: 10,
        date_type: this.date_type,
      })
      .then((res) => {
        // console.log(res.data);
      });
    studyReportApi.GetStudyReportDetailApi({ id: this.id }).then((res) => {
      this.studyReportDetailInfo = res.data ?? {};
      mineApi.GetInfo().then((res) => {
        this.regTime = (res.data ?? {})["reg_time"] ?? 0;
        this.apiDone = true;
      });
      for (
        var i = 0;
        i < this.studyReportDetailInfo["game_data"]["GameLabel"].length;
        i++
      ) {
        if (
          !(
            this.studyReportDetailInfo["game_data"]["GameLabel"][i][
              "label_name"
            ] == "课后对弈" ||
            this.studyReportDetailInfo["game_data"]["GameLabel"][i][
              "label_name"
            ] == "定级赛" ||
            (this.studyReportDetailInfo["game_data"]["GameLabel"][i][
              "label_name"
            ] == "智能棋盘" &&
              this.studyReportDetailInfo["game_data"]["GameLabel"][i][
                "black_win"
              ] +
                this.studyReportDetailInfo["game_data"]["GameLabel"][i][
                  "white_win"
                ] +
                this.studyReportDetailInfo["game_data"]["GameLabel"][i][
                  "black_lose"
                ] +
                this.studyReportDetailInfo["game_data"]["GameLabel"][i][
                  "white_lose"
                ] ==
                0)
          )
        ) {
          this.newList.push(
            this.studyReportDetailInfo["game_data"]["GameLabel"][i]
          );
        }
      }
    });
  },
  methods: {
    goBack() {
      this.$router.push({
        path: "/studyList",
        query: {
          type: this.date_type,
        },
      });
    },
    //计算进度条长度
    getLength1(x, y) {
      if (x > y) {
        return (1131 / 1368) * 100;
      } else {
        if (x == 0 || y == 0) {
          return 0;
        } else {
          return (x / y) * (1131 / 1368) * 100;
        }
      }
    },
    getLength(x, y) {
      if (x > y) {
        return (460 / 642) * 100;
      } else {
        if (x == 0 || y == 0) {
          return 0;
        } else {
          return (x / y) * (460 / 642) * 100;
        }
      }
    },
    drawCharts() {
      this.drawPieChart();
    },
    drawPieChart() {
      this.chartPie = echarts.init(document.getElementById("chartPie"));
      this.chartPie.setOption({
        title: {
          text: "Pie Chart",
          subtext: "纯属虚构",
          x: "center",
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          left: "left",
          data: ["直接访问", "邮件营销", "联盟广告", "视频广告", "搜索引擎"],
        },
        series: [
          {
            name: "访问来源",
            type: "pie",
            radius: "55%",
            center: ["50%", "60%"],
            data: [
              { value: 335, name: "直接访问" },
              { value: 310, name: "邮件营销" },
              { value: 234, name: "联盟广告" },
              { value: 135, name: "视频广告" },
              { value: 1548, name: "搜索引擎" },
            ],
            itemStyle: {
              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      });
    },
  },
};
</script>

<style lang="less">
.studyReportDetail {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow-y: auto;
  .back {
    width: 128px;
    height: 128px;
    position: absolute;
    top: 80px;
    left: 50px;
    cursor: pointer;
    z-index: 1;
  }
  .report_box {
    width: 100vw;
    height: 5050px;
    position: relative;
    overflow: hidden;
    .user_info {
      width: 1608px;
      height: 352px;
      margin: 240px auto 0;
      padding: 0 128px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .info {
        display: flex;
        justify-content: left;
        .head {
          width: 240px;
          height: 240px;
          background: #ffffff;
          box-shadow: 0 12px 20px 0 rgba(199, 103, 0, 0.1);
          border-radius: 50%;
          img {
            width: 220px;
            height: 220px;
            margin: 10px;
            border-radius: 50%;
          }
        }
        .name_wrap {
          margin-left: 40px;
          .name {
            font-size: 56px;
            color: #333333;
            margin-top: 40px;
            margin-bottom: 17px;
          }
          .level {
            width: 220px;
            height: 64px;
            background: #ffefc6;
            border-radius: 32px;
            font-size: 32px;
            color: #ff8a3e;
            text-align: center;
            line-height: 68px;
          }
        }
      }
      .date {
        padding: 0 40px;
        height: 120px;
        line-height: 120px;
        background-image: linear-gradient(180deg, #ffe9a6 0%, #ffce5b 100%);
        box-shadow: 0 4px 8px 0 rgba(255, 135, 27, 0.16),
          inset 0 -6px 12px 0 #ffb157;
        border-radius: 60px;
        font-size: 40px;
        color: #e26b00;
      }
    }
    .study_time {
      width: 1608px;
      height: 968px;
      margin: 30px auto 0;
      overflow: hidden;
      p.title {
        font-size: 56px;
        color: #ffffff;
        text-align: center;
        text-shadow: 0 4px 8px rgba(235, 108, 42, 0.75);
      }
      .study_time_wrap {
        width: 1448px;
        height: 630px;
        margin: 89px auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .pie_chart {
          width: 630px;
          height: 630px;
          //   background-color: pink;
        }
        .study_time_p {
          width: 750px;
          height: 612px;
          display: flex;
          flex-direction: column;
          // justify-content: space-around;

          .study_time_item {
            width: 720px;
            height: 188px;
            background: rgba(255, 243, 208, 0.5);
            border-radius: 40px;
            // display: flex;
            // flex-direction: row;
            // justify-content:left;
            // align-items: center;
            .icon {
              img {
                width: 120px;
                height: 120px;
                float: left;
                margin-top: 34px;
                margin-left: 56px;
              }
              .title_time {
                line-height: 188px;
                font-size: 36px;
                color: #333333;
                float: left;
                margin-left: 32px;
              }
            }

            .seconds {
              font-size: 36px;
              color: #333333;
              line-height: 188px;
              float: right;
              margin-right: 56px;
              .time {
                font-size: 72px;
                color: #ff8233;
                line-height: 188px;
              }
            }
          }
          .game_time {
            margin-top: 60px;
          }
          .video_time {
            margin-top: 40px;
            background: rgba(226, 248, 255, 0.5);
            .seconds {
              .time {
                font-size: 72px;
                color: #308aff;
              }
            }
          }
        }
      }
    }
  }
}
</style>
