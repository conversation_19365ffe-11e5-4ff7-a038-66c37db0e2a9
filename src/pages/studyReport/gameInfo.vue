<template>
  <div class="game_wrap">
    <p class="title jcyt600">{{ date_type == "week" ? "本周" : "本月" }}对局总览</p>
    <div class="progress_wrap">
      <div class="left_progress">
        <div class="this_week_progress">
          <div class="max_long">
            <div
              class="this_week jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_count_this_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_count_this_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'black_game_count_this_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_count_this_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_count_this_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "black_game_count_this_time"
                ] == 0
                  ? ""
                  : studyReportDetailInfo["game_data"][
                      "black_game_count_this_time"
                    ]
              }}
            </div>
            <div
              class="this_week this_week_right jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_count_this_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_count_this_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'white_game_count_this_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_count_this_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_count_this_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "white_game_count_this_time"
                ] == 0
                  ? ""
                  : studyReportDetailInfo["game_data"][
                      "white_game_count_this_time"
                    ]
              }}
            </div>
          </div>

          <p class="question_num jcyt400">
            {{
              studyReportDetailInfo["game_data"]["black_game_count_this_time"] +
              studyReportDetailInfo["game_data"]["white_game_count_this_time"]
            }}局/{{ date_type == "week" ? "本周" : "本月" }}
          </p>
        </div>
        <div class="last_week_progress">
          <div class="max_long">
            <div
              class="this_week jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_count_last_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_count_last_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'black_game_count_last_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_count_last_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_count_last_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "black_game_count_last_time"
                ] == 0
                  ? ""
                  : studyReportDetailInfo["game_data"][
                      "black_game_count_last_time"
                    ]
              }}
            </div>
            <div
              class="this_week this_week_right jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_count_last_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_count_last_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'white_game_count_last_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_count_last_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_count_last_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "white_game_count_last_time"
                ] == 0
                  ? ""
                  : studyReportDetailInfo["game_data"][
                      "white_game_count_last_time"
                    ]
              }}
            </div>
          </div>
          <p class="question_num jcyt400">
            {{
              studyReportDetailInfo["game_data"]["black_game_count_last_time"] +
              studyReportDetailInfo["game_data"]["white_game_count_last_time"]
            }}局/{{ date_type == "week" ? "上周" : "上月" }}
          </p>
        </div>
      </div>
      <div class="left_progress">
        <div class="this_week_progress">
          <div class="max_long">
            <div
              class="this_week jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_rate_this_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_rate_this_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'black_game_rate_this_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_rate_this_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_rate_this_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "black_game_rate_this_time"
                ] != 0 &&
                studyReportDetailInfo["game_data"][
                  "black_game_rate_this_time"
                ] +
                  studyReportDetailInfo["game_data"][
                    "white_game_rate_this_time"
                  ] !=
                  0
                  ? Math.ceil(
                      (studyReportDetailInfo["game_data"][
                        "black_game_rate_this_time"
                      ] *
                        100) /
                        (studyReportDetailInfo["game_data"][
                          "black_game_rate_this_time"
                        ] +
                          studyReportDetailInfo["game_data"][
                            "white_game_rate_this_time"
                          ])
                    ) + "%"
                  : ""
              }}
            </div>
            <div
              class="this_week this_week_right jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_rate_this_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_rate_this_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'white_game_rate_this_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_rate_this_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_rate_this_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "white_game_rate_this_time"
                ] != 0 &&
                studyReportDetailInfo["game_data"][
                  "black_game_rate_this_time"
                ] +
                  studyReportDetailInfo["game_data"][
                    "white_game_rate_this_time"
                  ] !=
                  0
                  ? Math.floor(
                      (studyReportDetailInfo["game_data"][
                        "white_game_rate_this_time"
                      ] *
                        100) /
                        (studyReportDetailInfo["game_data"][
                          "black_game_rate_this_time"
                        ] +
                          studyReportDetailInfo["game_data"][
                            "white_game_rate_this_time"
                          ])
                    ) + "%"
                  : ""
              }}
            </div>
          </div>

          <p class="question_num jcyt400">
            {{
              studyReportDetailInfo["game_data"]["black_game_rate_this_time"] +
              studyReportDetailInfo["game_data"]["white_game_rate_this_time"]
            }}局/{{ date_type == "week" ? "本周" : "本月" }}
          </p>
        </div>
        <div class="last_week_progress">
          <div class="max_long">
            <div
              class="this_week jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_rate_last_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_rate_last_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'black_game_rate_last_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_rate_last_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_rate_last_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "black_game_rate_this_time"
                ] != 0 &&
                studyReportDetailInfo["game_data"][
                  "black_game_rate_this_time"
                ] +
                  studyReportDetailInfo["game_data"][
                    "white_game_rate_this_time"
                  ] !=
                  0
                  ? Math.ceil(
                      (studyReportDetailInfo["game_data"][
                        "black_game_rate_this_time"
                      ] *
                        100) /
                        (studyReportDetailInfo["game_data"][
                          "black_game_rate_this_time"
                        ] +
                          studyReportDetailInfo["game_data"][
                            "white_game_rate_this_time"
                          ])
                    ) + "%"
                  : ""
              }}
            </div>
            <div
              class="this_week this_week_right jcyt400"
              :style="{
                width:
                  (studyReportDetailInfo['game_data'][
                    'black_game_rate_last_time'
                  ] +
                    studyReportDetailInfo['game_data'][
                      'white_game_rate_last_time'
                    ] !=
                  0
                    ? (studyReportDetailInfo['game_data'][
                        'white_game_rate_last_time'
                      ] /
                        (studyReportDetailInfo['game_data'][
                          'black_game_rate_last_time'
                        ] +
                          studyReportDetailInfo['game_data'][
                            'white_game_rate_last_time'
                          ])) *
                      100
                    : 0) + '%'
              }"
            >
              {{
                studyReportDetailInfo["game_data"][
                  "white_game_rate_this_time"
                ] != 0 &&
                studyReportDetailInfo["game_data"][
                  "black_game_rate_this_time"
                ] +
                  studyReportDetailInfo["game_data"][
                    "white_game_rate_this_time"
                  ] !=
                  0
                  ? Math.floor(
                      (studyReportDetailInfo["game_data"][
                        "white_game_rate_this_time"
                      ] *
                        100) /
                        (studyReportDetailInfo["game_data"][
                          "black_game_rate_this_time"
                        ] +
                          studyReportDetailInfo["game_data"][
                            "white_game_rate_this_time"
                          ])
                    ) + "%"
                  : ""
              }}
            </div>
          </div>
          <p class="question_num jcyt400">
            {{
              studyReportDetailInfo["game_data"]["black_game_rate_last_time"] +
              studyReportDetailInfo["game_data"]["white_game_rate_last_time"]
            }}局/{{ date_type == "week" ? "上周" : "上月" }}
          </p>
        </div>
      </div>
    </div>

    <div class="mean_time">
      <div class="left_progress right_progress">
        <div class="this_week_progress">
          <div
            class="this_week jcyt400"
            :style="{
              width:
                getLength1(
                  studyReportDetailInfo['game_data']['game_cost_this_time'] /
                    (studyReportDetailInfo['game_data'][
                      'black_game_count_this_time'
                    ] +
                      studyReportDetailInfo['game_data'][
                        'white_game_count_this_time'
                      ]) ==
                    0
                    ? 1
                    : studyReportDetailInfo['game_data'][
                        'black_game_count_this_time'
                      ] +
                        studyReportDetailInfo['game_data'][
                          'white_game_count_this_time'
                        ],
                  studyReportDetailInfo['game_data']['game_cost_last_time'] /
                    (studyReportDetailInfo['game_data'][
                      'black_game_count_last_time'
                    ] +
                      studyReportDetailInfo['game_data'][
                        'white_game_count_last_time'
                      ]) ==
                    0
                    ? 1
                    : studyReportDetailInfo['game_data'][
                        'black_game_count_last_time'
                      ] +
                        studyReportDetailInfo['game_data'][
                          'white_game_count_last_time'
                        ]
                ) + '%'
            }"
          ></div>

          <p class="question_num jcyt400">
            {{
              studyReportDetailInfo["game_data"]["game_cost_this_time"] != 0 &&
              studyReportDetailInfo["game_data"]["black_game_count_this_time"] +
                studyReportDetailInfo["game_data"][
                  "white_game_count_this_time"
                ] !=
                0
                ? (
                    studyReportDetailInfo["game_data"]["game_cost_this_time"] /
                    60 /
                    (studyReportDetailInfo["game_data"][
                      "black_game_count_this_time"
                    ] +
                      studyReportDetailInfo["game_data"][
                        "white_game_count_this_time"
                      ])
                  ).toFixed(1)
                : 0
            }}分钟/{{ date_type == "week" ? "本周" : "本月" }}
          </p>
        </div>
        <div class="last_week_progress">
          <div
            class="last_week jcyt400"
            :style="{
              width:
                getLength1(
                  studyReportDetailInfo['game_data']['game_cost_last_time'] /
                    (studyReportDetailInfo['game_data'][
                      'black_game_count_last_time'
                    ] +
                      studyReportDetailInfo['game_data'][
                        'white_game_count_last_time'
                      ]) ==
                    0
                    ? 1
                    : studyReportDetailInfo['game_data'][
                        'black_game_count_last_time'
                      ] +
                        studyReportDetailInfo['game_data'][
                          'white_game_count_last_time'
                        ],
                  studyReportDetailInfo['game_data']['game_cost_this_time'] /
                    (studyReportDetailInfo['game_data'][
                      'black_game_count_this_time'
                    ] +
                      studyReportDetailInfo['game_data'][
                        'white_game_count_this_time'
                      ]) ==
                    0
                    ? 1
                    : studyReportDetailInfo['game_data'][
                        'black_game_count_this_time'
                      ] +
                        studyReportDetailInfo['game_data'][
                          'white_game_count_this_time'
                        ]
                ) + '%'
            }"
          ></div>

          <p class="question_num jcyt400">
            {{
              studyReportDetailInfo["game_data"]["game_cost_last_time"] != 0 &&
              studyReportDetailInfo["game_data"]["black_game_count_last_time"] +
                studyReportDetailInfo["game_data"][
                  "white_game_count_last_time"
                ] !=
                0
                ? (
                    studyReportDetailInfo["game_data"]["game_cost_last_time"] /
                    60 /
                    (studyReportDetailInfo["game_data"][
                      "black_game_count_last_time"
                    ] +
                      studyReportDetailInfo["game_data"][
                        "white_game_count_last_time"
                      ])
                  ).toFixed(1)
                : 0
            }}分钟/{{ date_type == "week" ? "上周" : "上月" }}
          </p>
        </div>
      </div>
    </div>
    <div class="question_info_wrap1">
      <div
        v-for="(item, index) in newList"
        :key="index"
        class="question_info_item"
        :style="{
          'background-image': `url(${require('@/assets/studyReportDetail/对局总览item背景.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <p class="name jcyt400">{{ item["label_name"] }}</p>
        <div class="question_info_content">
          <p class="tex jcyt400">
            总对局
            <span class="jcyt500">{{
              item["black_win"] +
              item["white_win"] +
              item["black_lose"] +
              item["white_lose"]
            }}</span>
          </p>
          <p class="tex jcyt400">
            获胜
            <span class="jcyt500">{{ item["black_win"] + item["white_win"] }}%</span>
          </p>
        </div>
      </div>
    </div>
    <p class="h2 jcyt400">
      {{ date_type == "week" ? "本周" : "本月" }}对弈练习超过全国
      <span>
        {{
          (
            studyReportDetailInfo["game_data"]["GameRank"]["percent_all"] * 100
          ).toFixed(1)
        }}%</span
      >
      的小棋手
    </p>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    date_type: {},
    studyReportDetailInfo: {},
    newList: {}
  },
  methods: {
    //计算进度条长度
    getLength1(x, y) {
      if (x > y) {
        return (1131 / 1368) * 100;
      } else {
        if (x == 0 || y == 0) {
          return 0;
        } else {
          return (x / y) * (1131 / 1368) * 100;
        }
      }
    }
  }
};
</script>

<style lang="less">
.game_wrap {
  width: 1608px;
  height: 2088px;
  margin: -150px auto 0;
  overflow: hidden;
  p.title {
    font-size: 56px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 4px 8px rgba(235, 108, 42, 0.75);
    margin-top: 5px;
  }
  .progress_wrap {
    width: 1368px;
    height: 108px;
    margin: 280px auto 0;
    display: flex;
    justify-content: space-between;
    .left_progress {
      width: 642px;
      height: 108px;

      .this_week_progress {
        width: 642px;
        height: 48px;
        display: flex;
        justify-content: left;
        align-items: center;
        .max_long {
          width: 460px;
          background-image: linear-gradient(180deg, #ededed 0%, #e0e0e0 100%);
          box-shadow: inset 0 -2px 3px 0 #d5d5d5, inset 0 2px 3px 0 #ffffff;
          border-radius: 24px;
          display: flex;

          .this_week {
            height: 48px;
            line-height: 48px;
            background-image: linear-gradient(180deg, #515151 0%, #4d4d4d 100%);
            box-shadow: inset 0 -2px 3px 0 #353535, inset 0 2px 4px 0 #727272;
            border-radius: 24px;

            font-size: 28px;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
          }
          .this_week_right {
            line-height: 48px;
            background: transparent;
            box-shadow: none;
            color: #4e4e4e;
          }
        }

        .question_num {
          float: left;
          font-size: 32px;
          color: #666666;
          margin-left: 12px;
        }
      }
      .last_week_progress {
        width: 642px;
        height: 48px;
        margin-top: 12px;
        display: flex;
        justify-content: left;
        align-items: center;
        .max_long {
          width: 460px;

          background-image: linear-gradient(180deg, #ededed 0%, #e0e0e0 100%);
          box-shadow: inset 0 -2px 3px 0 #d5d5d5, inset 0 2px 3px 0 #ffffff;
          border-radius: 24px;
          display: flex;
          .this_week {
            height: 48px;
            line-height: 48px;
            background-image: linear-gradient(180deg, #515151 0%, #4d4d4d 100%);
            box-shadow: inset 0 -2px 3px 0 #353535, inset 0 2px 4px 0 #727272;
            border-radius: 24px;

            font-size: 28px;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
          }
          .this_week_right {
            background: transparent;
            box-shadow: none;
            color: #4e4e4e;
            line-height: 48px;
          }
        }

        .question_num {
          font-size: 32px;
          color: #666666;
          margin-left: 12px;
        }
      }
    }
    .right_progress {
      .this_week {
        background: #2ea7ff !important;
      }
      .last_week {
        background: rgba(46, 167, 255, 0.5) !important;
      }
    }
  }
  .mean_time {
    width: 1368px;
    height: 108px;
    margin: 258px auto 0;
    .left_progress {
      width: 1368px;
      height: 108px;
      .this_week_progress {
        width: 1368px;
        height: 48px;
        display: flex;
        justify-content: left;
        align-items: center;
        .this_week {
          height: 48px;
          line-height: 48px;
          border-radius: 24px;
          font-size: 28px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          background: #5dd04c;
          border-radius: 24px;
        }
        .question_num {
          float: left;
          font-size: 32px;
          color: #666666;
          margin-left: 12px;
        }
      }
      .last_week_progress {
        width: 1368px;
        height: 48px;
        margin-top: 12px;
        display: flex;
        justify-content: left;
        align-items: center;
        .last_week {
          height: 48px;
          line-height: 48px;
          font-size: 28px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;

          background: rgba(93, 208, 76, 0.5);
          border-radius: 24px;
        }
        .this_week_right {
          background: transparent;
          box-shadow: none;
          color: #4e4e4e;
          line-height: 48px;
        }

        .question_num {
          font-size: 32px;
          color: #666666;
          margin-left: 12px;
        }
      }
    }
  }
}
.question_info_wrap1 {
  width: 1368px;
  margin: 100px auto 0;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
  flex-wrap: wrap;
  .question_info_item {
    width: 655px;
    height: 240px;
    padding: 40px;
    box-sizing: border-box;
    display: flex;
    justify-content: left;
    margin-bottom: 56px;
    .name {
      width: 170px;
      height: 180px;
      font-size: 48px;
      color: #2072A6;
    }
    .question_info_content {
      margin-left: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .tex {
        font-size: 36px;
        color: #666666;

        span {
          color: #ff7766;
        }
      }
      .tex:nth-child(1) {
        margin-bottom: 10px;
      }
    }
  }
}
.h2 {
  width: 1368px;
  height: 160px;
  line-height: 160px;
  background: #f7f5f2;
  border-radius: 30px;
  text-align: center;
  font-size: 36px;
  color: #8d8e93;
  letter-spacing: 0.6px;
  text-align: center;
  margin: 35px auto 149px;
  span {
    color: #ff7766;
  }
}
</style>
