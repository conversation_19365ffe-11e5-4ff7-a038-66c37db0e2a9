<template>
  <div class="pei_wrap">
    <div id="main1" ref="imageDom"></div>
    <div class="time">
      <p class="time_tex jcyt600">{{ Math.round((video_time + game_time) / 60) }}</p>
      <p class="seconds jcyt600">总时长(分钟)</p>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
require("echarts/lib/component/tooltip");
require("echarts/lib/component/legend");
require("echarts/lib/chart/pie");
import html2canvas from "html2canvas";
export default {
  data() {
    return {
      loadImgDone: true,
      imgUrl: "",
      myChart: null
    };
  },
  props: {
    video_time: {},
    game_time: {},
    date_type: {}
  },
  created() {},
  mounted() {
    this.initData();
  },
  methods: {
    //初始化数据
    initData() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById("main1"));
      // 绘制图表
      this.myChart.setOption({
        tooltip: {
          trigger: "item"
        },
        legend: {
          top: "5%",
          left: "center"
        },
        color: ["#FFB117", "#5DD04C"],
        series: [
          {
            name: "",
            type: "pie",
            radius: ["68%", "88%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2
            },
            labelLine: {
              show: false
            },
            data: [
              { value: Math.round(this.game_time / 60) },
              { value: Math.round(this.video_time / 60) }
            ]
          }
        ]
      });
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
    }
  }
};
</script>
<style lang="less" scoped>
.pei_wrap {
  width: 540px;
  height: 540px;
  margin: 0 70px;
  border-radius: 50%;
  position: relative;
  #main1 {
    width: 540px;
    height: 540px;
    float: left;
    border-radius: 50%;
    margin-top: 12px;
  }
  .time {
    position: absolute;
    width: 286px;
    height: 200px;
    left: 50%;
    top: 50%;
    margin-left: -143px;
    margin-top: -80px;
    .time_tex {
      font-size: 100px;
      color: #4e4e4e;
      text-align: center;
    }
    .seconds {
      font-size: 36px;
      color: #999999;
      text-align: center;
      margin-top: 22px;
    }
  }
}
</style>
