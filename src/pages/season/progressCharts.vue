<template>
  <div class="pei_wrap">
    <div id="main1" ref="imageDom"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
require("echarts/lib/component/tooltip");
require("echarts/lib/component/legend");
require("echarts/lib/chart/pie");
import html2canvas from "html2canvas";
export default {
  data() {
    return {
      myChart: null
    };
  },
  props: {
    percent: {},
    high_protect_point: {}
  },
  created() {},
  mounted() {
    this.initData();
  },
  methods: {
    //初始化数据
    initData() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = echarts.init(document.getElementById("main1"));
      // 绘制图表
      this.myChart.setOption({
        // tooltip: {
        //   trigger: "item"
        // },
        legend: {
          top: "5%",
          left: "center"
        },
        color: ["#EAC16D", "transparent"],
        series: [
          {
            name: "",
            type: "pie",
            radius: ["90%", "100%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "transparent",
              borderWidth: 0
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.percent },
              {
                value:
                  this.high_protect_point - this.percent == 0
                    ? 1
                    : this.high_protect_point - this.percent
              }
            ],
            cursor: "default"
          }
        ]
      });
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
    },
    clickGeneratePicture() {
      //生成图片
      html2canvas(this.$refs.imageDom).then((canvas) => {
        // 转成图片，生成图片地址
        this.imgUrl = canvas.toDataURL("image/png"); //可将 canvas 转为 base64 格式
      });
    }
  }
};
</script>
<style lang="less" scoped>
.pei_wrap {
  width: 128px;
  height: 128px;

  border-radius: 50%;
  // position: relative;
  position: absolute;
  left: -7px;
  top: -6.5px;

  #main1 {
    width: 128px;
    height: 128px;
    float: left;
    border-radius: 50%;
    // margin-top: 10px;
  }
}
</style>
