<template>
  <div class="seasonUserInfo">
    <div class="left">
      <div
        class="back"
        @click="goBack()"
        :style="{
          'background-image': `url(${require('@/assets/season/back_arrow.png')})`,
          'background-size': '100% 100%'
        }"
        :class="type == 'capture' ? 'bigBottom' : ''"
      ></div>
      <div
        class="info"
        :style="{
          'background-image':
            type == 'capture'
              ? `url(${require('@/assets/season/index/个人信息_吃子.png')})`
              : `url(${require('@/assets/season/index/个人信息_排位.png')})`,
          'background-size': '100% 100%',
          height: type == 'capture' ? '22.9vw' : '29.16vw'
        }"
      >
        <div class="head">
          <img :src="seasonInfo['avatar']" alt="" />
        </div>
        <p class="name jcyt600">{{ seasonInfo["student_name"] ?? "" }}</p>
        <div
          class="record"
          :style="{ 'padding-top': type == 'capture' ? '2vw' : '1.7vw' }"
        >
          <div class="record_item">
            <p class="record_item_title jcyt500">总战绩</p>
            <div class="record_item_content">
              <p class="record_item_content_t jcyt500">
                {{ seasonInfo["win_count"] ?? "" }}
                <span>胜</span>
              </p>
              <p class="record_item_content_t jcyt500">
                {{ seasonInfo["lose_count"] ?? "" }}
                <span>负</span>
              </p>
            </div>
          </div>
          <div class="record_item">
            <p class="record_item_title jcyt500">上周战绩</p>
            <div class="record_item_content">
              <p class="record_item_content_t jcyt500">
                {{ weekRecordInfo["win_count"] ?? "" }}
                <span>胜</span>
              </p>
              <p class="record_item_content_t jcyt500">
                {{ weekRecordInfo["lose_count"] ?? "" }}
                <span>负</span>
              </p>
            </div>
          </div>
          <div class="record_item">
            <p class="record_item_title jcyt500">最高段位</p>
            <div class="record_item_content">
              <p class="level jcyt500">
                {{ seasonInfo["highest_level"] ?? "" }}
              </p>
            </div>
          </div>
        </div>
        <!-- 段位保护 -->
        <div class="segment_protection">
          <div class="circle">
            <progressCharts
              :high_protect_point="seasonInfo['high_protect_point']"
              :percent="
                seasonInfo['protect_point'] == null ||
                seasonInfo['protect_point'] >
                  seasonInfo['high_protect_point'] ||
                seasonInfo['high_protect_point'] == 0
                  ? 0
                  : seasonInfo['protect_point'] /
                    seasonInfo['high_protect_point']
              "
            ></progressCharts>
            <div class="circle_content">
              <p class="circle_title jcyt500">积分</p>
              <p class="circle_num jcyt500">{{ seasonInfo["protect_point"] }}</p>
            </div>
          </div>
          <div class="segment_content">
            <div
              class="img"
              :style="{
                'background-image': `url(${require('@/assets/season/index/段位保护.png')})`,
                'background-size': '100% 100%'
              }"
            ></div>
            <p class="word jcyt400">
              {{ seasonInfo["high_protect_point"] }}分=1次段位保护
            </p>
          </div>
        </div>
      </div>
      <!-- 赛季任务 -->
      <div
        class="season_task"
        :style="{
          'background-image': `url(${require('@/assets/season/index/赛季任务.png')})`,
          'background-size': '100% 100%'
        }"
      >
        <div class="task_item">
          <div class="task_left">
            <div class="task_left_top">
              <div class="task_left_top_wrap">
                <span class="white jcyt500">对局数</span>
                <span class="yellow jcyt500">{{
                  seasonInfo["game_count"] != null && seasonInfoList.length > 0
                    ? seasonInfo["game_count"] >
                      seasonInfoList[listIndex]["reward_game_count"]
                      ? seasonInfoList[listIndex][
                          "reward_game_count"
                        ].toString()
                      : seasonInfo["game_count"].toString()
                    : ""
                }}</span>
                <span class="grey jcyt500"
                  >/{{
                    seasonInfoList.length > 0
                      ? seasonInfoList[listIndex]["reward_game_count"]
                      : ""
                  }}</span
                >
              </div>
              <div
                class="status jcyt500"
                :style="{
                  color:
                    seasonInfo['game_count'] >=
                      seasonInfoList[listIndex]['reward_game_count'] &&
                    avatarRankList[0]['state'] == false
                      ? '#FCCA02'
                      : '#C9CCDE'
                }"
              >
                {{
                  avatarRankList[0]["state"] == false
                    ? seasonInfo["game_count"] >=
                      seasonInfoList[listIndex]["reward_game_count"]
                      ? "可领取"
                      : "未完成"
                    : "已领取"
                }}
              </div>
            </div>
            <div class="task_left_bottom">
              <div
                class="progress jcyt500"
                :style="{
                  width:
                    (((seasonInfo['game_count'] <
                    seasonInfoList[listIndex]['reward_game_count']
                      ? seasonInfo['game_count'] /
                        seasonInfoList[listIndex]['reward_game_count']
                      : 1) *
                      272) /
                      280) *
                      100 +
                    '%'
                }"
              ></div>
            </div>
          </div>
          <div
            class="task_right"
            :style="{
              'background-image': `url(${require('@/assets/season/头像背景.png')})`,
              'background-size': '100% 100%'
            }"
            :class="
              avatarRankList[0]['state']
                ? 'no-shadow'
                : seasonInfoList[listIndex]['reward_game_count'] <=
                  seasonInfo['game_count']
                ? ''
                : 'isGray'
            "
          >
            <img
              :src="avatarRankList[0]['url']"
              alt=""
              class="task_img"
              @click="$emit('getAvatar', 0)"
            />
          </div>
        </div>
        <div class="task_item">
          <div class="task_left">
            <div class="task_left_top">
              <div class="task_left_top_wrap">
                <span class="white jcyt500">胜场数</span>
                <span class="yellow">{{
                  seasonInfoApiDone == true && seasonInfoListApiDone == true
                    ? seasonInfo["win_count"] >
                      seasonInfoList[listIndex]["reward_win_count"]
                      ? seasonInfoList[listIndex]["reward_win_count"].toString()
                      : seasonInfo["win_count"].toString()
                    : ""
                }}</span>
                <span class="grey jcyt500"
                  >/{{
                    seasonInfoList.length > 0
                      ? seasonInfoList[listIndex]["reward_win_count"].toString()
                      : ""
                  }}</span
                >
              </div>
              <div
                class="status jcyt500"
                :style="{
                  color:
                    seasonInfo['win_count'] >=
                      seasonInfoList[listIndex]['reward_win_count'] &&
                    avatarRankList[1]['state'] == false
                      ? '#FCCA02'
                      : '#C9CCDE'
                }"
              >
                {{
                  avatarRankList[1]["state"] == false
                    ? seasonInfo["win_count"] >=
                      seasonInfoList[listIndex]["reward_win_count"]
                      ? "可领取"
                      : "未完成"
                    : "已领取"
                }}
              </div>
            </div>
            <div class="task_left_bottom">
              <div
                class="progress jcyt500"
                :style="{
                  width:
                    (((seasonInfo['win_count'] <
                    seasonInfoList[listIndex]['reward_win_count']
                      ? seasonInfo['win_count'] /
                        seasonInfoList[listIndex]['reward_win_count']
                      : 1) *
                      272) /
                      280) *
                      100 +
                    '%'
                }"
              ></div>
            </div>
          </div>
          <div
            class="task_right"
            :style="{
              'background-image': `url(${require('@/assets/season/头像背景.png')})`,
              'background-size': '100% 100%'
            }"
            :class="
              avatarRankList[1]['state']
                ? 'no-shadow'
                : seasonInfoList[listIndex]['reward_win_count'] <=
                  seasonInfo['win_count']
                ? ''
                : 'isGray'
            "
          >
            <img
              :src="avatarRankList[1]['url']"
              alt=""
              @click="$emit('getAvatar', 1)"
              class="task_img"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
import seasonApi from "@/api/season";
import progressCharts from "./progressCharts";
export default {
  name: "seasonUserInfo",
  data() {
    return {};
  },
  props: {
    type: {
      type: String,
      default: "capture"
    },
    seasonInfo: {},
    weekRecordInfo: {},
    weekRecord: {},
    seasonInfoList: {},
    listIndex: {},
    avatarRankList: {},
    seasonInfoApiDone: {},
    seasonInfoListApiDone: {},
    avatarRankListApiDone: {}
  },
  components: {
    progressCharts
  },

  methods: {
    goBack() {
      this.$router.push({
        path: "/",
        query: {
          defaultIndex: 2
        }
      });
    }
  },
  mounted() {}
};
</script>

<style scoped lang="less">
.seasonIndex {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-around;

    .left {
      width: 520px;
      height: 100%;
      .back {
        width: 120px;
        height: 120px;
        margin: 40px 0 0px 56px;
        cursor: pointer;
      }
      .bigBottom {
        margin-bottom: 75px;
      }
      .info {
        width: 368px;
        height: 560px;
        margin-left: 152px;
        overflow: hidden;
        box-shadow: rgba(0, 0, 0, 0.2) 0 3px 24px 0;
        margin-top: -59px;
        .head {
          width: 136px;
          height: 136px;
          border-radius: 50%;
          background-image: linear-gradient(180deg, #ecca82 0%, #d18b3f 100%);
          box-shadow: 0 3px 17px 0 rgba(0, 0, 0, 0.2);
          margin: 16px auto 0;

          img {
            width: 122.4px;
            height: 122.4px;
            margin: 6.8px;
            border-radius: 50%;
          }
        }
        .name {
          font-size: 40px;
          color: #ffffff;
          text-align: center;
          margin-top: 15px;
          line-height: 48px;
        }
        .record {
          padding-left: 32px;
          padding-right: 32px;
          padding-bottom: 15px;
          .record_item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            .record_item_title {
              font-size: 28px;
              color: #c9ccde;
              line-height: 40px;
            }
            .record_item_content {
              display: flex;
              .record_item_content_t {
                font-size: 28px;
                color: #fcca02;
                text-align: right;
                margin-left: 20px;
                line-height: 40px;
                min-width: 50px;
                span {
                  color: #ffffff;
                }
              }
              .level {
                font-size: 28px;
                color: #ffffff;
                text-align: right;
              }
            }
          }
        }
        .segment_protection {
          display: flex;
          justify-content: space-between;
          padding: 5px 44px 0 44px;
          .circle {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.2);
            border: 6px solid rgba(255, 255, 255, 0.2);
            box-sizing: border-box;
            position: relative;
            .circle_content {
              width: 72px;
              height: 72px;
              position: absolute;
              // background-color: pink;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              box-sizing: border-box;
              margin-top: 4px;
              .circle_title {
                font-size: 18px;
                color: rgba(255, 255, 255, 0.5);
                text-align: center;
              }
              .circle_num {
                font-size: 36px;
                color: #eac16d;
                text-align: center;
                line-height: 36px;
                margin-top: 4px;
              }
            }
          }
          .segment_content {
            width: 180px;
            margin-left: 24px;
            .img {
              width: 143px;
              height: 30px;
              margin-top: 18px;
            }
            .word {
              font-size: 18px;
              color: rgba(255, 255, 255, 0.5);
              margin-top: 5px;
              line-height: 24px;
            }
          }
        }
      }
      .season_task {
        width: 368px;
        height: 312px;
        margin-top: 16px;
        overflow: hidden;
        margin-left: 152px;
        .task_item {
          width: 312px;
          height: 86px;

          margin-left: 28px;
          display: flex;
          justify-content: space-between;

          .task_left {
            width: 224px;
            .task_left_top {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .task_left_top_wrap {
                .white {
                  font-size: 24px;
                  color: #ffffff;
                  margin-right: 6px;
                  line-height: 32px;
                }
                .yellow {
                  font-size: 24px;
                  color: #fcca02;
                  line-height: 32px;
                }
                .grey {
                  font-size: 24px;
                  color: #c9ccde;
                  line-height: 32px;
                }
              }
              .status {
                font-size: 16px;
                color: #c9ccde;
                line-height: 20px;
                text-align: right;
              }
            }
            .task_left_bottom {
              width: 224px;
              height: 20px;
              background: rgba(0, 0, 0, 0.2);
              border: 1.66px solid rgba(255, 255, 255, 0.13);
              border-radius: 10px;
              margin-top: 8px;

              .progress {
                width: 224px;
                height: 13.33px;
                background-image: linear-gradient(
                  180deg,
                  #fcdf67 0%,
                  #ffa73a 100%
                );
                box-shadow: inset 0 -1px 0 0 #d15a03, inset 0 1px 0 0 #ffff86,
                  inset 0 0 3px 0 #ffff82;
                border-radius: 11.62px;
                margin-top: 4.46px;
                margin-left: 4px;
              }
            }
          }
          .task_right {
            width: 74.5px;
            height: 74.5px;
            margin-top: 6px;
            box-shadow: 0 0 6px 0 #ffdc00;
            border-radius: 50%;
            .task_img {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              margin: 7.25px;
            }
          }
          .no-shadow {
            box-shadow: none;
          }
          .isGray {
            margin-top: 10px;
            /*grayscale(val):val值越大灰度就越深*/
            -webkit-filter: grayscale(100%);
            filter: grayscale(100%);
            //filter: gray;
            border-radius: 50%;

            box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.1);
          }
        }
        .task_item:nth-child(1) {
          margin-top: 94px;
        }
        .task_item:nth-child(2) {
          margin-top: 22px;
        }
      }
    }
    .centerWrap {
      width: 894px;
      height: 100%;
      background-color: green;
    }
    .right {
      width: 500px;
      height: 100%;
      background-color: orange;
    }
  }
}
</style>
