<template>
  <div class="seasonInfo">
    <div class="centerWrap">
      <div class="centerContentWrap">
        <div
          class="flag"
          :style="{
            'background-image': `url(${require('@/assets/season/index/旗帜.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <star
            v-if="seasonInfoApiDone == true && seasonInfoListApiDone == true"
            :finish_num="seasonInfo['inner_star'] ?? 0"
            :total_num="seasonInfo['star_range'] ?? 0"
          ></star>
        </div>
        <div
          class="changeFlag"
          :style="{
            'background-image':
              type == 'capture'
                ? `url(${require('@/assets/season/index/吃子模式.png')})`
                : `url(${require('@/assets/season/index/围地模式.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <div class="capture" @click="$emit('changeType', 'capture')"></div>
          <div
            class="season_level"
            :style="{
              'background-image':
                seasonInfoList.length == 0
                  ? ``
                  : `url(${require(`@/assets/season/index/S5.png`)})`,
              'background-size': '100% 100%'
            }"
          ></div>
          <div
            class="territory"
            @click="$emit('changeType', 'territory')"
          ></div>
        </div>
        <div
          class="icon"
          :style="{
            'background-image': `url(${seasonInfo['rank_star_icon']})`,
            'background-size': '100% 100%'
          }"
        ></div>
        <div
          class="level_flag jcyt600"
          :style="{
            'background-image': `url(${require('@/assets/season/index/等级标识.png')})`,
            'background-size': '100% 100%'
          }"
          :class="
            seasonInfo['current_level'] == '巅峰选手' ? 'current_level' : ''
          "
        >
          {{ seasonInfo["current_level"] ?? "" }}
        </div>
        <div class="current_level_name jcyt600">
          {{ seasonInfo["current_level_name"] ?? "" }}
        </div>
        <div
          @click="goMatch"
          class="button"
          v-if="seasonInfoApiDone == true && seasonInfoListApiDone == true"
          :style="{
            'background-image':
              seasonInfoList[listIndex]['status'] == 'close' || enabled == false
                ? seasonInfo['is_init_level'] == true && type == 'territory'
                  ? `url(${require('@/assets/season/index/开始定级_灰.png')})`
                  : `url(${require('@/assets/season/index/开始匹配_灰.png')})`
                : seasonInfo['is_init_level'] == true && type == 'territory'
                ? `url(${require('@/assets/season/index/开始定级.png')})`
                : `url(${require('@/assets/season/index/开始匹配.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
import seasonApi from "@/api/season";
import star from "./star";
export default {
  name: "seasonInfo",
  data() {
    return {};
  },
  props: {
    type: {
      type: String,
      default: "capture"
    },
    seasonInfo: {},
    seasonInfoList: {},
    listIndex: {},
    enabled: {},
    seasonInfoApiDone: {},
    seasonInfoListApiDone: {}
  },
  components: { star },

  methods: {
    goMatch() {
      let m = this.type == "capture" ? "吃子" : "围地";
      let o =
        this.seasonInfo["is_init_level"] == true && this.type == "territory"
          ? "定级"
          : "匹配";
      __bl.sum(`${m}${o}`);
      this.$emit("goMatchLoading");
    }
  },
  mounted() {}
};
</script>

<style scoped lang="less">
.content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .centerWrap {
    width: 894px;
    height: 100%;

    .centerContentWrap {
      width: 894px;
      height: 1367px;
      position: relative;
      .flag {
        width: 504px;
        height: 720px;
        margin: 184px auto 0;
        overflow: hidden;
        position: relative;
      }
      .changeFlag {
        position: absolute;
        top: -100px;
        left: 125px;
        width: 640px;
        height: 144px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .capture {
          width: 440px;
          height: 180px;
          cursor: pointer;
        }
        .season_level {
          width: 71px;
          height: 29px;
        }
        .territory {
          width: 440px;
          height: 180px;
          cursor: pointer;
        }
      }
      .icon {
        width: 464px;
        height: 464px;
        position: absolute;
        left: 50%;
        margin-left: -232px;
        top: 70px;
      }
      .level_flag {
        position: absolute;

        width: 232px;
        height: 80px;
        left: 330px;
        top: 390px;
        line-height: 76px;
        font-size: 48px;
        color: #fff7d9;
        letter-spacing: -2px;
        text-align: center;
      }
      .current_level {
        font-size: 42px;
      }
      .current_level_name {
        position: absolute;
        left: 277px;
        top: 487px;
        width: 330px;
        height: 48px;
        font-size: 40px;
        color: #ffffff;
        text-align: center;
        line-height: 48px;
      }
      .button {
        position: absolute;
        left: 225px;
        // bottom: 0;
        width: 440px;
        height: 110px;
        margin-bottom: 30px;
        cursor: pointer;
      }
    }
  }
  .right {
    width: 500px;
    height: 100%;
    background-color: orange;
  }
}
</style>
