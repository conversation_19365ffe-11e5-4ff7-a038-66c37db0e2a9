<template>
  <!-- v-if="
      seasonInfoApiDone == true &&
      seasonInfoListApiDone == true &&
      avatarRankListApiDone == true
    " -->
  <div class="right">
    <div
      @click="goRule"
      class="rule"
      :style="{
        'background-image': `url(${require('@/assets/season/index/赛事规则.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="right_wrap">
      <div
        class="top_img"
        :style="{
          'background-image': `url(${require('@/assets/season/index/每周新星.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
      <div class="content_week">
        <div class="week_title_wrap">
          <div
            @click="$emit('changeIndex', index)"
            class="title_wrap jcyt500"
            v-for="(item, index) in areaList"
            :key="index"
            :class="areaList.length > 1 ? '' : 'long_title_wrap'"
            :style="{
              'background-image':
                chooseIndex == index
                  ? 'linear-gradient(180deg, #ECCA82 0%, #D18B3F 100%)'
                  : null,
              'box-shadow': chooseIndex == index
                  ? 'rgba(0,0,0,.2) 0 0 4px 0'
                  : null
            }"
          >
            {{ item }}
          </div>
        </div>
        <div class="scroll_week">
          <div
            class="item_week"
            v-for="(item, index) in weekRecord"
            :key="index"
          >
            <div
              class="gold_icon jcyt600"
              :style="{
                'background-image':
                  index + 1 == 1
                    ? `url(${require('@/assets/season/index/first.png')})`
                    : index + 1 == 2
                    ? `url(${require('@/assets/season/index/second.png')})`
                    : index + 1 == 3
                    ? `url(${require('@/assets/season/index/third.png')})`
                    : null,
                'background-size': '100% 100%'
              }"
            >
              {{ index + 1 > 3 ? index + 1 : "" }}
            </div>
            <div
              class="head_img"
              :style="{
                'background-image': `url(${item['avatar']})`,
                'background-size': '100% 100%'
              }"
            ></div>
            <div class="week_name_wrap">
              <p class="name jcyt500">{{ item["student_name"] ?? "" }}</p>
              <div class="win_wrap">
                <span class="win_word jcyt400">胜{{ item["win_count"] }}场</span>
                <div class="up_star_wrap">
                  <img src="@/assets/season/index/上升.png" alt="" class="up" />
                  <img
                    src="@/assets/season/index/亮星.png"
                    alt=""
                    class="star"
                  />
                  <span class="up_star jcyt400">{{ "X" + item["up_star"] }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        @click="goRankingList"
        class="more"
        :style="{
          'background-image': `url(${require('@/assets/season/查看更多排名.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
    </div>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
import seasonApi from "@/api/season";
import Progress from "easy-circular-progress";
import left from "./seasonUserInfo.vue";
import seasonInfoContents from "./seasonInfo.vue";
import gameApi from "@/api/game";

export default {
  name: "weekInfo",
  data() {
    return {};
  },
  props: {
    chooseIndex: { type: Number, default: 0 },
    areaList: {},
    weekRecord: {},
    seasonInfoApiDone: {},
    seasonInfoListApiDone: {},
    avatarRankListApiDone: {},
    type: {},
    seasonId: ""
  },
  methods: {
    goRule() {
      __bl.sum("排位赛赛事规则");
      this.$router.push({
        path: "/seasonRule",
        query: {
          type: this.type
        }
      });
    },
    goRankingList() {
      __bl.sum("排位赛排行榜");
      this.$router.push({
        path: "/RankingList",
        query: {
          seasonId: this.seasonId,
          type: this.type
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
.right {
  width: 520px;
  height: 100%;
  margin-top: 40px;
  .rule {
    height: 120px;
    width: 128px;
    margin-left: 320px;
    margin-bottom: 80px;
    cursor: pointer;
  }
  .right_wrap {
    // height: 688px;
    margin-right: 136px;
    width: 384px;
    border-radius: 32px;
    background: #2a377c;
    box-shadow: 0 3px 24px 0 rgba(0, 0, 0, 0.2);
    .top_img {
      height: 64px;
      width: 100%;
      box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
    }
    .content_week {
      width: 100%;
      padding: 4px 4px 0 4px;
      box-sizing: border-box;
      .week_title_wrap {
        display: flex;
        justify-content: space-between;
        height: 64px;
        width: 376px;
        background: rgba(255, 255, 255, 0.16);
        border-radius: 12px;
        align-items: center;
        padding: 4px;
        margin-top: 5px;
        box-sizing: border-box;
        .title_wrap {
          width: 120px;
          height: 56px;
          border-radius: 10px;
          font-size: 28px;
          color: #ffffff;
          text-align: center;
          line-height: 56px;
          cursor: pointer;
        }
        .long_title_wrap {
          width: 100%;
          height: 56px;
        }
      }
      .scroll_week::-webkit-scrollbar {
        display: none;
      }
      .scroll_week {
        height: 463px;
        overflow-y: scroll;
        padding-top: 8px;
        box-sizing: border-box;
        .item_week {
          height: 104px;
          width: 100%;
          margin-bottom: 4px;
          background: rgba(255, 255, 255, 0.16);
          border-radius: 12px;
          display: flex;
          align-items: center;
          .gold_icon {
            height: 48px;
            width: 48px;
            font-size: 26px;
            color: #ffffff;
            line-height: 48px;
            text-align: center;
            margin-left: 10px;
          }
          .head_img {
            height: 72px;
            width: 72px;
            margin-left: 8px;
            margin-right: 8px;
          }
          .week_name_wrap {
            .name {
              font-size: 26px;
              color: #ffde7f;
              line-height: 40px;
            }
            .win_wrap {
              height: 40px;
              width: 226px;
              padding-left: 16px;
              padding-right: 14px;
              background: rgba(0, 0, 0, 0.2);
              border-radius: 10px;
              font-size: 22px;
              color: #ffffff;
              display: flex;
              justify-content: space-between;
              align-items: center;
              box-sizing: border-box;
              .win_word {
                font-size: 22px;
                color: #ffffff;

                line-height: 40px;
              }
              .up_star_wrap {
                display: flex;
                align-items: center;
                .up {
                  width: 8.6px;
                  height: 12.91px;
                }
                .star {
                  height: 24px;
                  width: 24px;
                  margin: 0px 4px 0 8.4px;
                }
              }
              .up_star {
                height: 40px;
                line-height: 40px;
              }
            }
          }
        }
      }
    }
    .more {
      height: 80px;
      width: 384px;
      box-shadow: 0 -1px 4px 0 rgba(0, 0, 0, 0.2);
      // margin-top: 8px;
      border-bottom-left-radius: 32px;
      border-bottom-right-radius: 32px;
      cursor: pointer;
    }
  }
}
</style>
