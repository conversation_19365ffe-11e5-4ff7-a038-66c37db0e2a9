<template>
  <div
    class="seasonIndex"
    :style="{
      'background-image': `url(${require('@/assets/season/排位赛背景.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="content">
      <left
        v-if="
          seasonInfoApiDone == true &&
          seasonInfoListApiDone == true &&
          avatarRankListApiDone == true
        "
        :type="type"
        :seasonInfo="seasonInfo"
        :weekRecordInfo="weekRecordInfo"
        :weekRecord="weekRecord"
        :seasonInfoList="seasonInfoList"
        :listIndex="listIndex"
        :avatarRankList="avatarRankList"
        :seasonInfoApiDone="seasonInfoApiDone"
        :seasonInfoListApiDone="seasonInfoListApiDone"
        :avatarRankListApiDone="avatarRankListApiDone"
        @getAvatar="getAvatar"
      ></left>

      <seasonInfoContents
        :type="type"
        :seasonInfo="seasonInfo"
        :seasonInfoList="seasonInfoList"
        :listIndex="listIndex"
        :enabled="enabled"
        :seasonInfoApiDone="seasonInfoApiDone"
        :seasonInfoListApiDone="seasonInfoListApiDone"
        @changeType="changeType"
        @goMatchLoading="goMatchLoading"
      ></seasonInfoContents>
      <weekInfo
        :areaList="areaList"
        :chooseIndex="chooseIndex"
        :weekRecord="weekRecord"
        :seasonInfoApiDone="seasonInfoApiDone"
        :seasonInfoListApiDone="seasonInfoListApiDone"
        :avatarRankListApiDone="avatarRankListApiDone"
        :type="type"
        :seasonId="seasonId"
        @changeIndex="changeIndex"
        v-if="
          seasonInfoApiDone == true &&
          seasonInfoListApiDone == true &&
          avatarRankListApiDone == true
        "
      ></weekInfo>
    </div>
    <tips
      v-if="hasNotEndGame"
      :isOpen="hasNotEndGame"
      msg="还有未结束的对局"
      cancelBtn="返回"
      reallyBtn="继续对局"
      @cancel="goBack"
      @really="goGame"
    >
    </tips>
    <!-- <mt-dialog
      :visible.sync="hasNotEndGame"
      :showClose="false"
      title="还有未结束的对局"
      :isCustomPadding="true"
      :customStyleWidth="customWidth"
    >
      <template #footer>
        <div class="footer-container">
          <div class="footer-confirm-button" @click="goBack">返回</div>
          <div class="footer-cancel-button" @click="goGame">继续对局</div>
        </div>
      </template>
    </mt-dialog> -->
    <checkLevel
      ref="checklevel"
      @changeLevelStatus="changeLevelStatus"
    ></checkLevel>
    <portraitDialog
      ref="portraitDialog"
      :avatarUrl="avatarUrl"
    ></portraitDialog>
  </div>
</template>

<script>
import { Toast } from "mint-ui";
import seasonApi from "@/api/season";
import Progress from "easy-circular-progress";
import left from "./seasonUserInfo.vue";
import seasonInfoContents from "./seasonInfo.vue";
import weekInfo from "./weekInfo.vue";
import gameApi from "@/api/game";
import dialog from "../mine/dialog/publicDialog.vue";
import checkLevel from "./checkLevel.vue";
import portraitDialog from "./portraitDialog.vue";
import tips from "@/components/tips/tips";

export default {
  name: "seasonIndex",
  data() {
    return {
      customWidth: 840,
      type: "capture",
      chooseIndex: 0,
      areaList: [],
      showCheckLevel: false,
      showPortrait: false,
      seasonInfo: {},
      weekRecordInfo: {},
      weekRecord: [],
      seasonInfoList: [],
      seasonId: "",
      listIndex: "",
      checkNotEndGameStatus: {},
      hasNotEndGame: false,
      avatarRankList: [],
      receiveAvatarIndex: "",
      seasonInfoApiDone: false,
      seasonInfoListApiDone: false,
      avatarRankListApiDone: false,
      apiDone: false,
      enabled: true,
      avatarUrl: "",
    };
  },
  components: {
    left,
    seasonInfoContents,
    weekInfo,
    "mt-dialog": dialog,
    checkLevel,
    portraitDialog,
    tips
  },

  methods: {
    goGame() {
      this.hasNotEndGame = false;
      this.$router.push({
        path: "/season/game",
        query: {
          game_id: this.checkNotEndGameStatus["game_id"],
          from: "qualifying",
        },
      });
    },
    goBack() {
      this.$router.push({
        path: "/",
        query: {
          defaultIndex: 2,
        },
      });
    },
    goMatchLoading() {
      if (this.enabled == false) {
        Toast("对弈服务已关闭");
      } else {
        console.log("去loading");
        this.$router.push({
          path: "/matching?type=" + this.type,
        });
        // Routes.navigateTo(
        //     context, Routes.matchLoding,
        //     params: {"type": type});
      }
    },
    changeIndex(e) {
      this.chooseIndex = e;
      if (e == 0) {
        seasonApi
          .WeekRecordTotalApi({
            type: this.type,
            season_id: this.seasonId.toString(),
            page: 1,
          })
          .then((res) => {
            this.weekRecord = res.data["results"] ?? [];
          });
      } else if (e == 2) {
        seasonApi
          .WeekRecordClassApi({
            type: this.type,
            season_id: this.seasonId.toString(),
            page: 1,
          })
          .then((res) => {
            this.weekRecord = res.data["results"] ?? [];
          });
      } else if (e == 1) {
        seasonApi
          .WeekRecordBranchApi({
            type: this.type,
            season_id: this.seasonId.toString(),
            page: 1,
          })
          .then((res) => {
            this.weekRecord = res.data["results"] ?? [];
          });
      }
    },
    getAvatar(e) {
      let canReceive =
        e == 1
          ? this.seasonInfoList[this.listIndex]["reward_win_count"] <=
            this.seasonInfo["win_count"]
          : this.seasonInfoList[this.listIndex]["reward_game_count"] <=
            this.seasonInfo["game_count"];
      if (canReceive && this.avatarRankList[e]["state"] == false) {
        this.receiveAvatarIndex = e;
        this.avatarUrl = this.avatarRankList[this.receiveAvatarIndex]["url"];
        seasonApi
          .AvatarAddApi({
            avatar_id: this.avatarRankList[this.receiveAvatarIndex]["id"],
          })
          .then((res) => {
            this.$refs.portraitDialog.open();
          });
      }
      if (this.avatarRankList[e]["state"]) {
        Toast("奖励已领取");
      }
    },

    closeLevelDialog() {
      this.$refs.checklevel.open();
      this.changeType("capture");
    },
    changeSatus() {
      this.seasonInfoApiDone = false;
      this.seasonInfoListApiDone = false;
      this.avatarRankListApiDone = false;
    },
    changeType(type) {
      this.chooseIndex = 0;
      this.seasonInfo = {};
      this.avatarRankList = [];
      this.seasonInfoList = [];
      this.type = type;
      this.$storage.$setStroage("season_type", type);
      this.changeSatus();
      // 用户信息
      seasonApi.SeasonInfoApi(type).then((res) => {
        this.seasonInfo = res.data ?? {};
        this.seasonInfo["verify_status"] == "is_confirm"
          ? (this.areaList = ["总榜", "校区榜", "班级榜"])
          : (this.areaList = ["总榜"]);
        if (
          this.seasonInfo["verify_status"] != "is_confirm" &&
          this.seasonInfo["level_id"] <= 0 &&
          this.type == "territory"
        ) {
          this.$refs.checklevel.open();
        }
        this.seasonInfoApiDone = true;
      });

      seasonApi.SeasonInfoListApi().then((res) => {
        this.seasonInfoList = res.data ?? [];
        this.seasonInfoListApiDone = true;
        for (var i = 0; i < this.seasonInfoList.length; i++) {
          if (this.type == this.seasonInfoList[i]["type"]) {
            this.listIndex = i;
            this.seasonId = this.seasonInfoList[i]["id"];
            //  周信息
            seasonApi
              .WeekRecordInfoApi({
                type: this.type,
                season_id: this.seasonId.toString(),
              })
              .then((res) => {
                this.weekRecordInfo = res.data ?? {};
              });
            // 榜单信息
            seasonApi
              .WeekRecordTotalApi({
                type: this.type,
                season_id: this.seasonId.toString(),
                page: 1,
              })
              .then((res) => {
                this.weekRecord = res.data["results"] ?? [];
              });
          }
        }
      });

      seasonApi.AvatarRankListApi().then((res) => {
        this.avatarRankList = res.data ?? [];
        this.avatarRankListApiDone = true;
      });

      seasonApi.CheckNotEndApi(type).then((res) => {
        this.checkNotEndGameStatus = res.data ?? {};
        if (this.checkNotEndGameStatus["has_not_end"] == true) {
          this.hasNotEndGame = true;
        }
      });
    },
    checkGameOpen() {
      gameApi.CheckGameOpenApi().then((res) => {
        this.enabled = res.data["enabled"] ?? true;
      });
    },
    changeLevelStatus() {
      this.$refs.checklevel.close();
      this.changeType("territory");
    },
  },
  mounted() {
    seasonApi.SeasonOpenApi("capture").then((res) => {
      seasonApi.SeasonOpenApi("territory").then((res) => {
        this.changeType(
          this.$storage.$getStroage("season_type") == undefined ||
            this.$storage.$getStroage("season_type") == null ||
            this.$storage.$getStroage("season_type") == ""
            ? "capture"
            : this.$storage.$getStroage("season_type")
        );
      });
    });
    this.checkGameOpen();
  },
};
</script>

<style scoped lang="less">
.seasonIndex {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-around;
    // align-items: center;
  }
  .footer-container {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
    .footer-confirm-button {
      height: 112px;
      width: 312px;
      background: #ffffff;
      border: 4px solid #00bdff;
      border-radius: 60px;
      font-size: 40px;
      color: #00bdff;
      text-align: center;
      line-height: 112px;
      cursor: pointer;
    }
    .footer-cancel-button {
      height: 112px;
      width: 312px;
      background: #08ccfd;
      border-radius: 60px;
      font-size: 40px;
      color: #ffffff;
      text-align: center;
      line-height: 112px;
      margin-left: 32px;
      cursor: pointer;
    }
  }
}
</style>
