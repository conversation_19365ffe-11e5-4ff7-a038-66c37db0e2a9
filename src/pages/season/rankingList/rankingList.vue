<template>
  <div class="ranking-list" :style="{'background-image': `url(${require('@/assets/season/排位赛背景.png')})`}">
    <div class="top flex-row justify-between items-end">
      <img src="@/assets/season/back_arrow.png" class="back" @click="back"/>
      <ul class="title-ul">
        <div class="line" :class="currentIndex === 0 ? 'first' : 'second'"></div>
        <li v-for="(item,index) in tabs" :key="index" @click="changeTab(index)">
          <span class="jcyt500" :class="{'active': currentIndex === index}">{{item}}</span>
        </li>
      </ul>
      <span class="tips jcyt400">{{currentIndex === 0 ? '每周日24点更新排名' : ''}}</span>
    </div>
    <div class="times" v-if="currentIndex === 0">
      <span class="jcyt400">{{`${lastMondayTime}-${lastSundayTime}`}}</span>
    </div>
    <ul class="left-tabs flex-column">
      <li v-for="(item,index) in leftTabs" :key="index" class="flex-row justify-center items-center" :class="{'active': leftCurrentIndex == index}" @click="changeLeftTab(index)">{{item}}</li>
    </ul>
    <div class="ranking-table">
      <div class="ranking-th flex-row items-center">
        <div class="ranking-th-1 th-title jcyt500">排名</div>
        <div class="ranking-th-2 th-title jcyt500">姓名</div>
        <div class="ranking-th-3 th-title jcyt500">等级</div>
        <div class="ranking-th-4 th-title jcyt500">{{ currentIndex == 0 ? '涨星数' : '等级称号' }}</div>
      </div>
      <div class="ranking-tbody">
        <div class="ranking-tbody-bg" ref="tbody">
          <div class="ranking-td flex-row items-center" v-for="(item,index) in rankingList" :key="index">
            <div class="ranking-td-1">
              <span class="th-title jcyt500" v-if="item.rank == 0">未上榜</span>
              <img :src="item.rank == 1 ? require('@/assets/season/ranking_list_1.png') : item.rank == 2 ? require('@/assets/season/ranking_list_2.png') : require('@/assets/season/ranking_list_3.png')" class="reward" v-else-if="item.rank < 4"/>
              <span class="th-title jcyt500" v-else>{{item.rank}}</span>
            </div>
            <div class="ranking-td-2 flex-row items-center">
              <img :src="(item.avatar == null || item.avatar == '') ? item.sex == 2 ? require('@/assets/season/avatar_girls.png') : require('@/assets/season/avatar_boys.png') : item.avatar" class="avatar"/>
              <span class="name jcyt500">{{item.student_name}}</span>
            </div>
            <div class="ranking-td-3 flex-row items-center">
              <span class="level-name jcyt500">{{item.current_level}}</span>
              <div class="flex-row items-center ">
                <img src="@/assets/season/star.png" class="star"/>
                <span class="star-num jcyt400">x{{item.inner_star != null ? item.inner_star : 0}}</span>
              </div>
            </div>
            <div class="ranking-td-4 jcyt500" :class="item.up_star > 0 ? 'up' : 'down'" v-if="currentIndex == 0">{{item.up_star == null? '' : item.up_star > 0 ? `+${item.up_star}` : item.up_star}}</div>
            <div class="ranking-td-4 jcyt500" v-else>{{item.current_level_name}}</div>

          </div>
        </div>
      </div>
      <div class="ranking-tfoot flex-row items-center">
        <div class="ranking-td-1 ranking-tfoot-1">
            <span class="th-title jcyt500" v-if="ranking == 0">未上榜</span>
            <img :src="ranking == 1 ? require('@/assets/season/ranking_list_1.png') : ranking == 2 ? require('@/assets/season/ranking_list_2.png') : require('@/assets/season/ranking_list_3.png')" class="reward" v-else-if="ranking < 4"/>
            <span class="th-title jcyt500" v-else>{{ranking}}</span>
        </div>
        <div class="ranking-td-2 flex-row items-center">
          <img :src="(rankingInfo.avatar == null || rankingInfo.avatar == '') ? rankingInfo.sex == 2 ? require('@/assets/season/avatar_girls.png') : require('@/assets/season/avatar_boys.png') : rankingInfo.avatar" class="avatar"/>
          <span class="name jcyt500">{{rankingInfo.student_name}}</span>
        </div>
        <div class="ranking-td-3 flex-row items-center">
          <span class="level-name jcyt500">{{rankingInfo.current_level}}</span>
          <div class="flex-row items-center ">
            <img src="@/assets/season/star.png" class="star"/>
            <span class="star-num jcyt400">x{{rankingInfo.inner_star != null ? rankingInfo.inner_star : 0}}</span>
          </div>
        </div>
        <div class="ranking-td-4 jcyt500" :class="rankingInfo.up_star > 0 ? 'up' : 'down'" v-if="currentIndex == 0">{{rankingInfo.up_star == null? '' : rankingInfo.up_star > 0 ? `+${rankingInfo.up_star}` : rankingInfo.up_star}}</div>
        <div class="ranking-td-4 jcyt500" v-else>{{rankingInfo.current_level_name}}</div>
      </div>
    </div>

  </div>
</template>
<script>
import timeFormat from "@/public/timeFormat";
import rankingListApi from "@/api/rankingList";
import weekRecordApi from "@/api/weekRecord";
import seasonMatchApi from "@/api/seasonMatch";

export default {
  data(){
    return {
      currentIndex: 0,
      tabs: ["每周新星","排行榜"],
      leftTabs: ["总榜","校区榜","班级榜"],
      leftCurrentIndex: 0,
      lastMondayTime: "",
      lastSundayTime: "",
      page: 1,
      rankingList: [],
      count: 0,
      rankingInfo: {},
      ranking: 0,
      loading: false,
      type: "capture",
      rankingType: "every",
      seasonId: ""
    }
  },
  mounted(){
    if(this.$route.query.type) this.type = this.$route.query.type;
    if(this.$route.query.rankingType) this.rankingType = this.$route.query.rankingType;
    if(this.$route.query.seasonId) this.seasonId = this.$route.query.seasonId;
    this.setWeek();
    this.currentIndex = this.rankingType === 'every' ? 0 : 1;
    this.getSeasonInfo();
    this.getRankingList(this.page);
  },
  methods: {
    back(){
      this.$router.go(-1);
    },
    getMonthDay(time){
      return timeFormat.GetCustTime( time ,"M月D日");
    },
    changeTab(index) {
      if(!this.loading){
        this.currentIndex = index;
        this.leftCurrentIndex = 0;
        this.getRankingList(this.page);
        let r = this.$refs["tbody"];
        r.scrollTop = 0;
      }
    },
    changeLeftTab(index){
      if(!this.loading){
        this.leftCurrentIndex = index;
        this.getRankingList(this.page);
        let r = this.$refs["tbody"];
        r.scrollTop = 0;
      }
    },
    setWeek(){
      const {lastMondayTime,lastSundayTime } = this.getWeek();
      this.lastMondayTime = this.getMonthDay(lastMondayTime);
      this.lastSundayTime = this.getMonthDay(lastSundayTime);
    },
    getWeek(){
      var today = new Date();
      var year = today.getFullYear(); //本年
      var month = today.getMonth() + 1; //本月
      var day = today.getDate(); //本日
      var newDate = new Date(year+"-"+month+"-"+day+" 00:00:00");
      var weekDay = newDate.getDay();
      var oneDayTime = 24*60*60*1000;
      var nowTime  = newDate.getTime();
      var lastMondayTime = nowTime - (weekDay+6)*oneDayTime;
      var lastSundayTime = nowTime - (weekDay+0)*oneDayTime;
      return {lastMondayTime,lastSundayTime};
    },
    loadBottom(){
      this.page = this.page + 1;
      this.getRankingList(this.page);
    },
    async getRankingList(page){
      if(!this.loading) {
        this.loading = true;
        var data = this.currentIndex == 0
          ? {'page': page, "season_id": this.seasonId}
          : {'page': page};
        var newApi;
        if (this.currentIndex == 0) {
          if (this.type == "capture") {
            newApi = this.leftCurrentIndex == 0
                ? weekRecordApi.weekCaptureListApi
                : this.leftCurrentIndex == 1
                    ? weekRecordApi.weekCaptureBranchListApi
                    : weekRecordApi.weekCaptureClassListApi;
          } else {
            newApi = this.leftCurrentIndex == 0
                ? weekRecordApi.weekTerritoryListApi
                : this.leftCurrentIndex == 1
                    ? weekRecordApi.weekTerritoryBranchListApi
                    : weekRecordApi.weekTerritoryClassListApi;
          }
        } else {
          if (this.type == "capture") {
            newApi = this.leftCurrentIndex == 0
                ? rankingListApi.rankingCaptureListApi
                : this.leftCurrentIndex == 1
                    ? rankingListApi.rankingCaptureBranchListApi
                    : rankingListApi.rankingCaptureClassListApi;
          } else {
            newApi = this.leftCurrentIndex == 0
                ? rankingListApi.rankingTerritoryListApi
                : this.leftCurrentIndex == 1
                    ? rankingListApi.rankingTerritoryBranchListApi
                    : rankingListApi.rankingTerritoryClassListApi;
          }
        }
        var res = await newApi(data);
        const {count, results, rank_data, rank} = res.data;
        // var showCount;
        // if (this.leftCurrentIndex == 0) {
        //   showCount = count < 100 ? count : 100;
        // } else if (this.leftCurrentIndex == 1) {
        //   showCount = count < 20 ? count : 20;
        // } else {
        //   showCount = count;
        // }
        // this.count = showCount;
        this.rankingList = results;
        this.rankingInfo = rank_data;
        this.ranking = rank;
        setTimeout(() => {
          this.loading = false;
        }, 800);
      }
    },
    async getSeasonInfo(){
      let res = await seasonMatchApi.SeasonInfoApi(this.type);
      this.leftTabs = res.data['verify_status'] == 'is_confirm' ? ["总榜","校区榜","班级榜"] : ["总榜"];
    }
  }
}
</script>
<style lang="less" scoped>
.top {
  padding-top: 40px;
}
.tips {
  width: 259px;
  color: rgba(255, 255, 255, .5);
  font-size: 28px;
  margin-right: 116px;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.justify-between {
  justify-content: space-between;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.items-end {
  align-items: flex-end;
}
.back {
  width: 120px;
  height: 120px;
  margin-left: 56px;
  cursor: pointer;
}
.ranking-list {
  width: 100vw;
  height: 100vh;
  background-size: cover;
}
.title-ul {
  border-radius: 56px;
  width: 624px;
  height: 112px;
  position: relative;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  margin-left: 228px;
  li {
    width: 308px;
    text-align: center;
    height: 112px;
    line-height: 104px;
    font-size: 40px;
    padding: 4px;
    font-weight: bold;
    display: inline-block;
    background: rgba(255, 255, 255, .16);
    cursor: pointer;
    span {
      position: relative;
      z-index: 12;
      color: #fff;
    }
  }
}
.line {
  position: absolute;
  top: 4px;
  width: 308px;
  height: 104px;
  margin-left: 0;
  transition: all 0.3s;
  background: linear-gradient(to bottom, #ECCA82, #CA751D);
  z-index: 12;
  border-radius: 56px;
}
.first {
  left: 0px;
}
.second {
  left: 310px;
}
.times {
  background: rgba(255, 255, 255, .16);
  width: 256px;
  height: 102px;
  // padding-left: 50px;
  text-align: center;
  box-sizing: border-box;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
  // text-align: center;
  position: absolute;
  top: 116px;
  left: 204.5px;
  span {
    font-size: 28px;
    color: rgba(255, 255, 255, .75);
    margin-top: 15px;
    display: inline-block;
  }
}
.left-tabs {
  position: absolute;
  left: 116px;
  top: 288px;
  z-index: 11;
  align-items: flex-end;
  li.active {
    width: 88px;
    height: 176px;
    background: linear-gradient(to bottom, #ECCA82, #CA751D);
    font-size: 36px;
  }
  li {
    width: 72px;
    height: 152px;
    margin-bottom: 16px;
    border-top-left-radius: 24px;
    border-bottom-left-radius: 24px;
    background: linear-gradient(to bottom, #88A2C8, #6A82AF);
    box-shadow: 0 3px 12px 0 rgba(0, 0, 0, .2);
    color: #fff;
    font-weight: bold;
    font-size: 30px;
    writing-mode: vertical-rl;
    cursor: pointer;
  }
}
.ranking-table {
  width: calc(100vw - 320px);
  height: calc(100vh - 216px);
  background: #2A377C;
  border-radius: 40px;
  box-shadow: 0 0 30px 0 rgba(38, 49, 112, .65);
  position: absolute;
  left: 204px;
  top: 176px;
  z-index: 10;
  .ranking-th {
    height: 96px;
    width: calc(100% - 6px);
    background: linear-gradient(to bottom, #9E5F50, #803C40);
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    margin-left: 3px;
    &::before {
      content: '';
      position: absolute;
      top: -3px;
      right: 0;
      left: 0;
      bottom: 0;
      z-index: -1;
      height: 98.25px;
      border-radius: inherit; /*important*/
      background: linear-gradient(to bottom, #D28D41, #803C40);
    }
  }
  .ranking-tbody {
    width: calc(100vw - 384px);
    height: calc(100vh - 492px);
    background: rgba(0, 0, 0, .1);
    margin: 0 32px;
    position: relative;
    box-sizing: border-box;
    .ranking-tbody-bg {
      overflow-y: scroll;
      margin: 0 16px;
      padding-top: 16px;
      height: calc(100vh - 492px);
      background: rgba(255, 255, 255, .1);
      box-sizing: border-box;
      -ms-overflow-style: none;;
      &::-webkit-scrollbar { width: 0 !important }
    }

    .ranking-td {
      height: 126px;
      margin: 0 16px 12px 16px;
      border-radius: 16px;
      background: rgba(255, 255, 255, .16);
    }
  }
  .ranking-tfoot {
    height: 150px;
    margin: 0 32px;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
    padding-left: 32px;
    padding-right: 32px;
    background: linear-gradient(to bottom, #779CD7, #3F6ABA);
  }
}
.th-title {
  color: #fff;
  font-size: 30px;
}
.ranking-th-1 {
  margin-left: 64px;
  width: 156px;
  text-align: center;
}
.ranking-th-2 {
  width: 411px;
  margin-left: 197px;
}
.ranking-th-3 {
  width: 240px;
  text-align: center;
  box-sizing: border-box;
}
.ranking-th-4 {
  width: 240px;
  margin-left: 146px;
  text-align: right;
}
.ranking-td-1 {
  width: 148px;
  text-align: center;
  .reward {
    width: 84px;
    height: 84px;
    border-radius: 50%;
  }
}
.ranking-td-2 {
  width: 580px;
  .avatar {
    width: 84px;
    height: 84px;
    border-radius: 50%;
    margin-left: 44px;
  }
  .name {
    margin-left: 62px;
    font-size: 30px;
    color: #FFDE7F;
  }
}
.ranking-td-3 {
  height: 56px;
  margin: 0 40px;
  // box-sizing: border-box;
  background: rgba(0, 0, 0, .2);
  border-radius: 28px;
  width: 310px;
  .level-name {
    font-size: 30px;
    font-weight: bold;
    color: #fff;
    width: 180px;
    text-align: center;
  }
  .star {
    width: 40px;
    height: 40px;
    margin-top: 7px;
  }
  .star-num {
    font-size: 30px;
    color: #A9CBF4;
  }
}
.ranking-td-4 {
  font-size: 30px;
  text-align: right;
  // width: 314px;
  flex:1;
  padding-right: 91px;
  box-sizing: border-box;
  color: #D8DBEC;
}
.ranking-td-4.up {
  color: #FCCA02;
}
.ranking-td-4.down {
  color: #4AD947;
}

</style>