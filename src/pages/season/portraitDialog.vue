<template>
  <transition name="van-fade">
    <div class="portraitDialog" v-if="show">
      <div
        class="portrait-dialog"
        :style="{
          'background-image': `url(${require('@/assets/season/index/领取头像弹窗.png')})`,
          'background-size': '100% 100%',
        }"
      >
        <p class="title jcyt600">精品头像领取成功！</p>
        <p class="contents jcyt500">请前往头像库中查看使用</p>
        <img :src="avatarUrl" alt="" class="head" />

        <div class="button jcyt600" @click="goMine">去使用</div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: "portraitDialog",
  data() {
    return {
      show: false,
    };
  },
  props: {
    avatarUrl: {},
  },
  components: {},

  watch: {},
  mounted() {},
  methods: {
    open() {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    goMine() {
      this.$router.push({
        path: "/information",
        query: {
          fromUrl: "portrait",
        },
      });
    },
  },
};
</script>
<style scoped lang="less">
.portraitDialog {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  z-index: 201;
  left: 0;
  top: 0;
}
.portrait-dialog {
  width: 968px;
  height: 976px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -484px;
  margin-top: -488px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 10px 20px 0;
  .title {
    font-size: 40px;
    color: #333333;
    line-height: 48px;
    text-align: center;
    margin-top: 368px;
  }
  .contents {
    font-size: 32px;
    color: #999999;
    text-align: center;
    margin-top: 16px;
    line-height: 40px;
  }
  .head {
    width: 192px;
    height: 192px;
    margin: 64px auto;
    display: block;
  }
  .button {
    width: 360px;
    height: 96px;
    line-height: 96px;
    background-image: linear-gradient(180deg, #fe8a3a 0%, #fd5b25 100%);
    border: 8px solid #ffffff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #f14b0e;
    border-radius: 50px;
    font-size: 40px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    text-shadow: 0 4px 4px #f14b0e;
    margin: 0 auto;
    cursor: pointer;
  }
}
</style>
