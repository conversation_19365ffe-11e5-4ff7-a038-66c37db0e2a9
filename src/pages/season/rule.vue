<template>
  <div
    class="ruleWrap"
    :style="{
      'background-image': `url(${require('@/assets/season/排位赛背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="top">
      <div
        @click="goBack"
        class="back"
        :style="{
          'background-image': `url(${require('@/assets/season/back_arrow.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
      <div class="title_wrap">
        <div
          class="item jcyt500"
          v-for="(item, index) in list"
          :key="index"
          :class="active == index ? 'active' : ''"
          @click="active = index"
        >
          {{ item }}
        </div>
      </div>
      <div class="back"></div>
    </div>
    <div class="content jcyt500">
      <div v-if="seasonInfoList.length > 0">
        <div v-if="active == 2">
          <img
            :src="img"
            alt=""
            v-for="(img, index) in seasonInfoList[listIndex]['describe']?.split(
              ','
            )"
            :key="index"
          />
        </div>
        <div v-else-if="active == 1">
          <img
            :src="img"
            alt=""
            v-for="(img, index) in seasonInfoList[listIndex][
              'game_describe'
            ]?.split(',')"
            :key="index"
          />
        </div>
        <div v-else>
          <img
            :src="img"
            alt=""
            v-for="(img, index) in seasonInfoList[listIndex][
              'rank_describe'
            ]?.split(',')"
            :key="index"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import seasonApi from "@/api/season";
export default {
  name: "ruleWrap",
  data() {
    return {
      active: 0,
      list: ["晋级规则", "对局规则", "赛季说明"],
      seasonInfoList: [],
      listIndex: ""
    };
  },
  computed: {
    type() {
      return this.$route.query.type;
    }
  },
  components: {},

  methods: {
    goBack() {
      console.log(3232);
      this.$router.push({
        path: "/seasonIndex"
      });
    }
  },
  mounted() {
    seasonApi.SeasonInfoListApi().then((res) => {
      this.seasonInfoList = res.data ?? [];
      for (var i = 0; i < this.seasonInfoList.length; i++) {
        if (this.type == this.seasonInfoList[i]["type"]) {
          this.listIndex = i;
        }
      }
    });
  }
};
</script>

<style scoped lang="less">
.ruleWrap {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .top {
    width: 100vw;
    height: 112px;
    box-sizing: border-box;
    margin: 40px auto 37px;
    display: flex;
    justify-content: space-between;
    .back {
      width: 120px;
      height: 120px;
      cursor: pointer;
      margin-left: 56px;
    }
    .title_wrap {
      margin-top: 16px;
      width: 932px;
      height: 112px;
      background: rgba(255, 255, 255, 0.16);
      border-radius: 56px;
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);
      display: flex;
      justify-content: space-between;
      .item {
        width: 308px;
        height: 104px;
        line-height: 104px;
        margin: 4px;
        border-radius: 56px;
        font-size: 40px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }
      .active {
        background-image: linear-gradient(180deg, #ecca82 0%, #d18b3f 100%);
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.2);
      }
    }
  }
  .content {
    width: 1808px;
    height: 816px;
    background: #fff1d4;
    box-shadow: 0 0 30px 0 rgba(77, 4, 9, 0.5), inset 0 -10px 20px 0 #ffdf9d,
      inset 0 10px 20px 0 rgba(255, 253, 243, 0.5);
    border-radius: 40px;
    margin: 0 auto;
    padding: 42px;
    box-sizing: border-box;
    overflow-y: scroll;
    img {
      width: 1728px;
      height: auto;
    }
  }
  .content::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}
</style>
