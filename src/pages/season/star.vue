<template>
  <div
    class="star_wrap"
    :style="{ width: 14 + (demo_width / 518) * 100 + '%' }"
  >
    <div class="star_item_wrap" :class="{'star-item-top': total_num > 10}" v-if="total_num > 10">
      <img src="@/assets/season/index/亮星.png" alt="" />
      <span class="star_word jcyt600">X {{ total_num }}</span>
    </div>
    <div
     v-else
      class="star_item"
      v-for="(star, index) in star_arr"
      :key="index"
      :class="total_num == 5 ? 'short_star_item' : 'long_star_item'"
    >
      <img
        class="star_item_img"
        :src="
          star == true
            ? require('@/assets/season/index/亮星.png')
            : require('@/assets/season/index/灰星.png')
        "
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "courseWrap",
  props: {
    total_num: {},
    finish_num: {}
  },
  data() {
    return {
      star_arr: []
    };
  },
  computed: {
    demo_width() {
      return (this.star_arr.length - 1) * 36;
    }
  },

  mounted() {
    if (this.finish_num > 0) {
      for (var i = 0; i < this.finish_num; i++) {
        this.star_arr.push(true);
      }
    }
    if (this.total_num - this.finish_num > 0) {
      for (var j = 0; j < this.total_num - this.finish_num; j++) {
        this.star_arr.push(false);
      }
    }
  }
};
</script>

<style lang="less">
.star_wrap {
  height: 107.95px;
  width: 355.83px;
  position: relative;
  margin: 170px auto 0;
  left: 50%;
  margin-left: -177.915px;
  .star_item_wrap {
    width: 155px;
    height: 48px;
    position: absolute;
    left: 50%;
    margin-left: -77.5px;
    top: -110px;
    display: flex;
    justify-content:center;
    align-items: center;
    img {
      width: 40px;
      height: 40px;
      margin-right:12px ;
    }
    .star_word {
      font-size: 40px;
      color: #fff9ee;
      line-height: 48px;
    }
  }
  .star_item {
    width: 30px;
    height: 30px;
    position: absolute;
    .star_item_img {
      width: 30px;
      height: 30px;
      position: absolute;
    }
  }
  .long_star_item {
    width: 30px;
    height: 30px;
    position: absolute;
    .star_item_img {
      width: 30px;
      height: 30px;
      position: absolute;
    }
  }
  .long_star_item:nth-child(1) {
    top: -20px;
    left: 10px;
    transform: rotate(-42deg);
  }
  .long_star_item:nth-child(2) {
    top: -49px;
    left: 38px;
    transform: rotate(-40deg);
  }
  .long_star_item:nth-child(3) {
    top: -72px;
    left: 70px;
    transform: rotate(-30deg);
  }
  .long_star_item:nth-child(4) {
    top: -88px;
    left: 105px;
    transform: rotate(-20deg);
  }
  .long_star_item:nth-child(5) {
    top: -95px;
    left: 142px;
    transform: rotate(-6deg);
  }
  .long_star_item:nth-child(6) {
    top: -95px;
    left: 180px;
    transform: rotate(6deg);
  }
  .long_star_item:nth-child(7) {
    top: -88px;
    left: 217px;
    transform: rotate(20deg);
  }
  .long_star_item:nth-child(8) {
    top: -72px;
    left: 252px;
    transform: rotate(30deg);
  }
  .long_star_item:nth-child(9) {
    top: -49px;
    left: 284px;
    transform: rotate(40deg);
  }
  .long_star_item:nth-child(10) {
    top: -22px;
    left: 312px;
    transform: rotate(42deg);
  }
  .short_star_item {
    width: 30px;
    height: 30px;
    position: absolute;
    .star_item_img {
      width: 30px;
      height: 30px;
      position: absolute;
    }
  }
  .short_star_item:nth-child(1) {
    top: -62px;
    left: 60px;
    transform: rotate(-34deg);
  }
  .short_star_item:nth-child(2) {
    top: -88px;
    left: 107px;
    transform: rotate(-25deg);
  }
  .short_star_item:nth-child(3) {
    top: -98px;
    left: 162px;
    transform: rotate(0deg);
  }
  .short_star_item:nth-child(4) {
    top: -88px;
    left: 217px;
    transform: rotate(25deg);
  }
  .short_star_item:nth-child(5) {
    top: -62px;
    left: 264px;
    transform: rotate(34deg);
  }
}
.star-item-top {
  margin-top: 10px;
}
</style>
