<template>
  <transition name="van-fade">
    <div>
      <div class="check-level-dialog_wrap" v-if="show">
        <div class="check-level-resign-dialog">
          <p class="title jcyt600">选择等级</p>
          <p class="content jcyt500">
            注：围地从14K开始，请选择初始等级，若所选等级与您的实力不符，将给您带来不好的对局体验哦！
          </p>
          <div class="field-container" @click="levelVisible = true">
            <input placeholder="请选择等级" readonly v-model="level" class="jcyt500"/>
            <div
              class="pull-down"
              :style="{
                'background-image': `url(${require('@/assets/season/level箭头.png')})`,
                'background-size': 'contain'
              }"
            ></div>
          </div>
          <div class="buttons jcyt500" @click="goGrading">开始定级</div>
        </div>
      </div>
      <mt-popup class="mint-popup" v-model="levelVisible" position="bottom">
        <p class="addChooseButton">
          <span class="cancelBut jcyt500" @click="cancelp(1)">取消</span>
          <span class="confirmBut jcyt500" @click="cancelp(2)">确认</span>
        </p>
        <mt-picker
          :slots="slots"
          @change="onValuesChange"
          :visible-item-count="3"
          :show-toolbar="false"
          ref="picker"
          v-model="levelPicker"
          value-key="name"
          :itemHeight="80 / 1920 * w"
        ></mt-picker>
      </mt-popup>
    </div>
  </transition>
</template>
<script>
import { Picker, Popup } from "mint-ui";
import seasonApi from "@/api/season";
import { Toast } from "mint-ui";

export default {
  name: "checkLevelDialog",
  data() {
    return {
      slots: [
        {
          flex: 1,
          values: ["男", "女"],
          className: "slot1",
          textAlign: "center"
        }
      ],
      show: false,
      levelPicker: "",
      levelVisible: false, //选择器的显示与影藏
      level: "",
      levelId: "",
      w: 1
    };
  },
  components: {
    "mt-picker": Picker,
    "mt-popup": Popup
  },
  props: {},
  watch: {},
  mounted() {
    seasonApi.TerritoryLevelListApi().then((res) => {
      this.slots[0]["values"] = res.data ?? [];
    });
    this.w = document.body.clientWidth;
  },
  methods: {
    open() {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    onValuesChange(picker, values) {
      this.levelPicker = values[0]["name"];
      this.levelId = values[0]["id"];
    },
    cancelp(index) {
      if (index === 2) {
        this.level = this.levelPicker;
        this.levelVisible = false;
      } else {
        this.levelPicker = this.level;
        this.levelVisible = false;
      }
    },
    goGrading() {
      if (this.levelId != "") {
        seasonApi.LevelUpdateApi({ level_id: this.levelId }).then((res) => {
          this.$emit("changeLevelStatus");
        });
      } else {
        Toast("请选择等级");
      }
    }
  }
};
</script>
<style scoped lang="less">
.check-level-dialog_wrap {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 201;
  left: 0;
  top: 0;
}
.check-level-resign-dialog {
  width: 1152px;
  height: 744px;
  background: #ffffff;
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 30px 0 #ccfaff;
  border-radius: 50px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -576px;
  margin-top: -372px;
  padding: 63px 80px 88px 80px;
  box-sizing: border-box;
  .title {
    font-size: 56px;
    color: #333333;
    text-align: center;
    line-height: 64px;
    margin: 0 auto;
  }
  .content {
    font-size: 32px;
    color: #666666;
    text-align: left;
    margin-top: 57px;
    line-height: 50px;
  }
  .buttons {
    width: 800px;
    height: 120px;
    line-height: 120px;
    background-image: linear-gradient(180deg, #2dd5ff 6%, #00ccff 94%);
    box-shadow: 0 4px 8px 0 rgba(0, 56, 79, 0.1), inset 0 8px 8px 0 #96e6ff,
      inset 0 -8px 8px 0 #00bdff;
    border-radius: 60px;
    font-size: 48px;
    color: #ffffff;
    text-align: center;
    margin: 76px auto;
    cursor: pointer;
  }
  .field-container {
    box-sizing: border-box;
    width: 800px;
    height: 120px;
    border-radius: 60px;
    background-color: #f6f8fb;
    line-height: 120px;
    padding: 0 48px;
    display: flex;
    align-items: center;
    margin: 56px auto 0;
    input {
      border: none;
      background-color: #f6f8fb;
      font-size: 40px;
      line-height: 48px;
      outline: none;
      width: 720px;
    }
    input::-webkit-input-placeholder {
      /* WebKit browsers 适配谷歌 */
      color: #bfc1c5;
      font-family: jcyt500w;
    }
    input::placeholder {
      color: #bfc1c5;
      font-family: jcyt500w;
    }
    .pull-down {
      width: 48px;
      height: 48px;
    }
  }
  .bottom-field-container {
    width: 100%;
  }
}
.addChooseButton {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 32px;
  padding: 45px 56px 43px;
  cursor: pointer;
  z-index: 1;
  position: relative;
  background-color: #fff;
  .cancelBut {
    color: #666666;
  }
  .confirmBut {
    color: #00baff;
  }
}
.mint-popup {
  width: 100%;
  height: 416px;
  z-index: 10;
  background: #fff;
  border-radius: 32px 32px 0 0;
  ::v-deep .picker-center-highlight {
    background-color: #EEEEF0;
    border-radius: 14.04px;
    margin: 0 calc((100vw - 640px) / 2);
    width: 640px;
    border: none;
    z-index: -1;
  }
  ::v-deep .picker-item {
    font-size: 38px;
    font-family: jcyt500w;
  }
  ::v-deep .picker-center-highlight:before,
  ::v-deep .picker-center-highlight:after {
    height: 0px;
  }
}
</style>
