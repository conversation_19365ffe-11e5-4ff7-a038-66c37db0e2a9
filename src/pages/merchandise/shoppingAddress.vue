<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt500">修改收货地址</div>
      <div class="center-box">
        <div class="input-container">
          <div class="label jcyt500">收货人</div>
          <input placeholder="请输入收货人姓名" v-model="info.actualName" class="jcyt400"/>
        </div>
        <div class="input-container">
          <div class="label jcyt500">联系电话</div>
          <input placeholder="请输入收货人联系电话" v-model="info.phone" class="jcyt400"/>
        </div>
        <div
          class="input-container address-container"
          @click="openAddressPicker()"
        >
          <div class="label jcyt500">所在地区</div>
          <input placeholder="请选择" v-model="region" readonly class="jcyt400"/>
          <div
            class="pull-down"
            :style="{
              'background-image': `url(${require('@/assets/mine/下拉.png')})`,
              'background-size': '100% 100%',
            }"
          ></div>
        </div>
        <div class="input-container">
          <div class="label jcyt500">详细地址</div>
          <input
            placeholder="详细地址：如街道、门牌"
            v-model="info.detailedAddress"
            class="jcyt400"
          />
        </div>
      </div>
      <div class="submit jcyt500" @click="sumbitInformation()">保存</div>
    </div>
    <mt-popup class="mint-popup" v-model="addressVisible" position="bottom">
      <p class="addChooseButton">
        <span class="cancelBut jcyt500" @click="cancel()">取消</span>
        <span class="confirmBut jcyt500" @click="save()">确认</span>
      </p>
      <mt-picker
        ref="addressPicker"
        :slots="addressSlots"
        value-key="label"
        @change="changeCity"
        :visible-item-count="3"
        :itemHeight="80 / 1920 * w"
      ></mt-picker>
    </mt-popup>
  </div>
  <!-- </div> -->
</template>

<script>
import orderApi from "@/api/order";
import { Picker, Popup, DatetimePicker } from "mint-ui";
import moment from "moment";
import { Toast } from "mint-ui";
import area from "@/plugins/area";
export default {
  name: "mineIndex",
  components: {
    "mt-picker": Picker,
    "mt-popup": Popup,
    "mt-datetime-picker": DatetimePicker,
  },
  data() {
    return {
      info: {},
      count: 0,
      sendVal: false,
      sexVisible: false, //选择器的显示与影藏
      region: "",
      sex: "",
      sexPicker: "",
      pickerDate: "",
      slots: [
        {
          flex: 1,
          values: ["男", "女"],
          className: "slot1",
          textAlign: "center",
        },
      ],
      birthday: "", //出生日期
      startDate: new Date("1968-01-01"),
      addressVisible: false,
      addressStr: "",
      regionList: [],
      mrpId: "", //省id
      mrcid: "", //市id
      mrrid: "", //区id
      addressSlots: [
        {
          flex: 1,
          defaultIndex: 1,
          values: area, //省份数组
          className: "slot1",
          textAlign: "center",
        },
        {
          flex: 1,
          values: [],
          className: "slot2",
          textAlign: "center",
        },
        {
          flex: 1,
          values: [],
          className: "slot3",
          textAlign: "center",
        },
      ],
      w: 1
    };
  },
  created() {
    this.initAddress("110000", "110100", "110101");
    orderApi.GetPersonalInformation().then((res) => {
      this.info = res.data || {};
      this.$storage.$setStroage("user_token", res.data.token);

      this.info.actualName = res.data.consignee || "";
      this.info.phone = res.data.consignee_mobile || "";

      this.info.detailedAddress = res.data.address || "";

      if (
        res.data.address_region != null &&
        res.data.address_region.length != 0
      ) {
        this.regionList = res.data.address_region;
        this.initAddress(res.data.address_region);
      } else {
        this.regionList = ["", "", ""];
      }
    });
    this.w = document.body.clientWidth;
  },
  computed: {
    order_id() {
      return this.$route.query.order_id;
    },
  },
  methods: {
    initAddress(regionArr) {
      let province, city, regions;
      area.map((item, index) => {
        if (item.value === regionArr[0] + "") {
          this.mrpId = item.value;
          province = item.label;
          this.addressSlots[0].defaultIndex = index;
          this.addressSlots[1].values = item.children;
          //   this.addressSlots[1].defaultIndex = item.children.findIndex(
          //     (item) => {
          //       item.value === region[1]+'';
          //     }
          //   );
          item.children.map((item, index) => {
            if (item.value === regionArr[1] + "") {
              this.mrcid = item.value;
              city = item.label;
              this.addressSlots[1].defaultIndex = index;
              this.addressSlots[2].values = item.children;
              this.addressSlots[2].defaultIndex = item.children.findIndex(
                (item3) => {
                  if (item3.value === regionArr[2] + "") {
                    regions = item3.label;
                    this.mrrid = item3.value;
                    return true;
                  }
                }
              );
            }
          });
        }
      });
      if (regions) {
        this.region = province + city + regions;
      } else {
        this.region = province + city;
      }
    },
    openMask() {
      this.sendVal = true;
    },
    clickCancel() {
      console.log("点击了取消");
    },
    clickConfirm(val) {
      this.info.avatar = val;
      console.log(val, "avatar");
    },
    goBack() {
      this.$router.push({
        path: "/orderDetail",
        query: {
          order_id: this.order_id,
        },
      });
    },
    cancelp(index) {
      // const _this = this;
      if (index === 2) {
        this.sex = this.sexPicker;
        this.sexVisible = false;
      } else {
        this.sexVisible = false;
        this.sexPicker = this.sex;
        // _this.sex = "";
      }
    },
    save() {
      this.region = this.addressStr;
      this.regionList = [
        parseInt(this.mrpId),
        parseInt(this.mrcid),
        parseInt(this.mrrid),
      ];
      this.addressVisible = false;
    },
    openAddressPicker() {
      this.addressVisible = true;
      this.initAddress(this.regionList);
    },
    cancel() {
      this.addressStr = "";
      this.mrpId = this.regionList[0];
      this.mrcid = this.regionList[1];
      this.mrrid = this.regionList[2];
      this.addressVisible = false;
    },
    onValuesChange(picker, values) {
      //console.log(values)
      this.sexPicker = values[0];
      //   this.sexVisible = false;
    },
    openPicker() {
      this.pickerDate = new Date(this.birthday);
      this.$refs.picker.open();
    },
    sumbitInformation() {
      //   if (
      //     this.info.name == "" ||
      //     this.sex == "" ||
      //     this.sex == null ||
      //     this.birthday == null ||
      //     this.birthday == "" ||
      //     this.regionList[0] == 0 ||
      //     this.regionList[0] == "" ||
      //     this.regionList[0] == null ||
      //     this.regionList.isEmpty
      //   ) {
      //     Toast("请完善信息");
      //   }
      //   mineApi
      //     .UpdateInformation({
      //       name: this.info.name, //姓名
      //       birthday: this.birthday, //生日 yyyy-mm-dd
      //       region: this.regionList, //区域
      //       gender: this.sex == "女" ? "female" : "male", //性别
      //       school: 1,
      //       branch: 1,
      //     })
      //     .then((res) => {
      //       console.log(res, "update");
      //       if (res.status == 200) {
      //         //   if (globalVar.isRedirect) {
      //         //     globalVar.isRedirect = false;
      //         //     Routes.navigateTo(context, globalVar.redirectPath,
      //         //         params: globalVar.redirectParam, clearStack: true);
      //         //   }

      //         //   if (widget.type == 'change') {
      //         //     Routes.goBack(context, MinePadView());
      //         //   } else {
      //         this.goBack();
      //         //   }
      //       }
      //     });
      if (this.info.actualName == null || this.info.actualName == "") {
        Toast("请填写收货人");
      } else if (
        !this.isPhoneExp(this.info.phone) ||
        this.info.phone == null ||
        this.info.phone == ""
      ) {
        Toast("请填写正确的手机号");

        // ignore: unrelated_type_equality_checks
      } else if (
        this.regionList[0] == 0 ||
        this.regionList[0] == "" ||
        this.regionList[0] == null ||
        this.regionList.isEmpty
      ) {
        Toast("请选择所在地区");
      } else if (
        this.info.detailedAddress == "" ||
        this.info.detailedAddress == null
      ) {
        Toast("请填写详细地址");
      } else {
        var params = {
          order_id: this.order_id,
          consignee: this.info.actualName,
          consignee_mobile: this.info.phone,
          address_region: this.regionList,
          address: this.info.detailedAddress,
        };

        orderApi.UpdateAddress(params).then((res) => {
          if (res.status == 200) {
            this.$router.push({
              path: "/orderDetail",
              query: {
                order_id: this.order_id,
              },
            });
          }
        });
      }
    },
    isPhoneExp(str) {
      let pattern = /^1([0-9]{10})$/;
      return pattern.test(str);
    },
    changeCity(picker, val) {
      console.log(val);
      if (this.mrpId !== val[0].value) {
        picker.setSlotValues(1, val[0].children); //只要有一级数据就绑二级数据
        this.mrpId = val[0].value;
        // this.selectAddress = values;
      }
      if (val[1] && this.mrcid !== val[1].value) {
        picker.setSlotValues(2, val[1].children);
        this.mrcid = val[1].value; //只要有一级数据就绑二级数据
      }
      this.mrrid = val[2] && val[2].value;
      if (val[0] && val[1] && val[2]) {
        this.addressStr = val[0]["label"] + val[1]["label"] + val[2]["label"];
      }
    },
  },
  onBackPress() {
    // uni.reLaunch({
    //   url: "/",
    // });
    return true;
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  // height: 100%;
  overflow-y: scroll;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .title {
    font-size: 52px;
    line-height: 64px;
    color: #333333;
    padding-top: 103px;
    padding-bottom: 41px;
  }
  .center-box {
    width: 1824px;
    height: 800px;
    border-radius: 40px;
    background: #ffffff;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 10px;
    box-sizing: border-box;
    .input-container {
      width: 1668px;
      height: 142px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      border-bottom: 1px solid #dddee4;
      .label {
        font-size: 36px;
        color: #333;
        width: 240px;
        text-align: left;
      }
      input {
        flex: 1;
        outline: none;
        border: none;
        height: 56px;
        font-size: 36px;
      }
      &:last-child {
        border-bottom: none;
      }
    }
    .address-container {
      .pull-down {
        width: 48px;
        height: 48px;
      }
    }
    .field-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      height: 112px;
    }
  }
  .submit {
    width: 800px;
    height: 120px;
    border-radius: 60px;
    background: linear-gradient(
      to bottom,
      rgba(45, 213, 255),
      rgba(0, 204, 255)
    );
    box-shadow: 0px 8px 8px 0px #96e6ff inset, 0px -8px 8px 0px #00bdff inset;
    text-align: center;
    line-height: 120px;
    font-size: 48px;
    color: #ffffff;
    margin-bottom: 32px;
  }
  .field-container {
    box-sizing: border-box;
    width: 720px;
    height: 112px;
    border-radius: 60px;
    background-color: #f6f8fb;
    line-height: 48px;
    padding: 11px 40px 11px 48px;
    display: flex;
    align-items: center;
    .field-label {
      font-size: 40px;
      line-height: 90px;
      color: #333333;
    }
    .divider {
      display: inline-block;
      height: 48px;
      width: 2px;
      background-color: #bfc1c5;
      margin: 0 30px;
    }
    input {
      border: none;
      background-color: #f6f8fb;
      font-size: 40px;
      line-height: 48px;
      outline: none;
      width: 440px;
    }
    .pull-down {
      width: 48px;
      height: 48px;
    }
  }
  .bottom-field-container {
    width: 100%;
  }
}
.addChooseButton {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 32px;
  padding: 45px 56px 43px;
  cursor: pointer;
  z-index: 1;
  position: relative;
  background-color: #fff;
  .cancelBut {
    color: #666666;
  }
  .confirmBut {
    color: #00baff;
  }
}
.mint-popup {
  width: 100%;
  height: 416px;
  z-index: 10;
  background: #fff;
  border-radius: 32px 32px 0 0;
  ::v-deep .picker-center-highlight {
    background-color: #EEEEF0;
    border-radius: 14.04px;
    margin: 0 calc((100vw - 640px) / 2);
    width: 640px;
    border: none;
    z-index: -1;
  }
  ::v-deep .picker-item {
    font-size: 38px;
    font-family: jcyt500w;
  }
  ::v-deep .picker-center-highlight:before,
  ::v-deep .picker-center-highlight:after {
    height: 0px;
  }
}
</style>
