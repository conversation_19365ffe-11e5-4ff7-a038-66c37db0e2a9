<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt500">订单详情</div>
      <div class="center-box">
        <div class="center-container">
          <div class="order-title-container">
            <div class="merchandise-image">
              <img :src="orderDetail?.merchandise?.pad_header_image_url" />
            </div>
            <div class="order-title">
              <div class="merchandise-name jcyt500">
                {{ orderDetail?.merchandise?.title }}
              </div>
              <div class="order-number jcyt400">
                订单号：<span>{{ orderDetail?.id }}</span>
              </div>
              <div
                v-if="
                  orderDetail?.teacher_id !== 0 &&
                  orderDetail?.teacher_time_id !== 0
                "
              >
                <div class="course-detail jcyt400">
                  {{
                    orderDetail?.merchandise?.merchandise_type == "course"
                      ? "授课老师："
                      : "赛事名称："
                  }}<span>{{ orderDetail?.teacher_name }}</span>
                </div>
                <div class="course-date jcyt400">
                  {{
                    orderDetail?.merchandise?.merchandise_type == "course"
                      ? "上课时间："
                      : "比赛日期："
                  }}
                  <div
                    class="date-list jcyt400"
                    v-if="
                      orderDetail?.merchandise?.merchandise_type == 'course'
                    "
                  >
                    <span
                      v-for="(
                        item, index
                      ) in orderDetail?.teacher_time_date_conf || []"
                      :key="index"
                      >{{ item }}</span
                    >
                  </div>
                  <span v-else class="jcyt400">{{
                    orderDetail?.merchandise?.match_starting_date -
                    orderDetail?.merchandise?.match_end_date
                  }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="order-detail-container">
            <div class="order-price-container jcyt500">订单金额</div>
            <div class="order-detail-list">
              <div class="jcyt400">
                订单时间：<span>{{
                  orderDetail?.merchandise?.created_at.substring(0, 10) +
                  " " +
                  orderDetail?.merchandise?.created_at.substring(11, 16)
                }}</span>
              </div>
              <div class="jcyt400">
                支付方式：<span>{{
                  orderDetail?.order_type == "wechat"
                    ? "微信支付"
                    : orderDetail?.order_type == "cdkey"
                    ? "兑换码兑换"
                    : orderDetail?.order_type == "appointment"
                    ? "预约"
                    : orderDetail?.order_type == "iap"
                    ? "IOS内购"
                    : "免费"
                }}</span>
              </div>
              <div class="jcyt400">
                交易状态：<span>{{
                  orderDetail?.order_status == "is_pay"
                    ? "已支付"
                    : orderDetail?.order_status == "is_close"
                    ? "已关闭"
                    : orderDetail?.order_status == "is_free"
                    ? "已领取"
                    : orderDetail?.order_status == "is_refund"
                    ? "已退款"
                    : orderDetail?.order_status == "is_appointment"
                    ? "已预约"
                    : orderDetail?.order_status == "close_appointment"
                    ? "已取消"
                    : orderDetail?.order_status == "confirm_appointment"
                    ? "已确认"
                    : ""
                }}</span>
              </div>
            </div>
          </div>
          <div
            class="button-container"
            v-if="
              orderDetail?.order_status == 'is_pay' ||
              orderDetail?.order_status == 'is_appointment' ||
              orderDetail?.order_status == 'confirm_appointment'
            "
          >
            <div
              class="study-button jcyt500"
              v-if="
                orderDetail.merchandise.merchandise_type == 'course' &&
                !(
                  orderDetail.order_type == 'appointment' &&
                  orderDetail.order_status != 'confirm_appointment'
                )
              "
              @click="goStudy"
            >
              开始学习计划
            </div>
            <div
              v-if="
                orderDetail.merchandise.merchandise_type == 'course' &&
                orderDetail.merchandise.need_address == true
              "
              class="jcyt500"
              @click="goChangeAddress"
            >
              更改地址
            </div>
            <!-- <div @click="goChangeAddress">更改地址</div> -->
            <div
              class="jcyt500"
              v-if="
                orderDetail.merchandise.merchandise_type == 'course' &&
                orderDetail.order_type == 'appointment' &&
                orderDetail.order_status == 'is_appointment'
              "
              @click="cancelOrderVisible = true"
            >
              取消预约
            </div>
            <!-- <div @click="cancelOrderVisible = true">取消预约</div> -->
            <div
              v-if="orderDetail.merchandise.merchandise_type !== 'course'"
              @click="goIndex"
              class="jcyt500"
            >
              回到首页
            </div>
          </div>
        </div>
      </div>
    </div>
    <mt-dialog
      :visible.sync="cancelOrderVisible"
      :showClose="false"
      :isCustomPadding="true"
    >
      <template>
        <div class="canel-order-container">
          <p class="cancel-order-title jcyt600">确认取消预约吗</p>
          <p class="cancel-order-content jcyt500">取消预约后可重新报名哦</p>
        </div>
      </template>
      <template #footer>
        <div class="footer-container">
          <div class="footer-confirm-button jcyt500" @click="confirmCancel">确认</div>
          <div class="footer-cancel-button jcyt500" @click="cancelOrderVisible = false">
            取消
          </div>
        </div>
      </template>
    </mt-dialog>
  </div>
  <!-- </div> -->
</template>

<script>
import orderApi from "@/api/order";
// import informApi from "@/api/inform";
import { Indicator } from "mint-ui";
import dialog from "../mine/dialog/publicDialog";
export default {
  name: "mineIndex",
  components: { "mt-dialog": dialog },
  data() {
    return {
      orderDetail: {},
      apiDone: false,
      totalCount: 0,
      cancelOrderVisible: false,
    };
  },
  computed: {
    order_id() {
      return this.$route.query.order_id;
    },
  },
  created() {
    this.initInfo();
    Indicator.open({
      text: "加载中...",
      //文字
      spinnerType: "fading-circle",
      //样式
    });
  },
  destroyed() {
    Indicator.close();
  },
  methods: {
    initInfo() {
      orderApi
        .GetOrderDetail({
          order_id: this.order_id,
        })
        .then((res) => {
          this.apiDone = true;
          Indicator.close();
          this.orderDetail = res.data;
          // this.totalCount = res.data.total_number;
          //   if (this.totalCount % 10 == 0) {
          //     this.totalPage = this.totalCount ~/ 10;
          //   } else {
          //     this.totalPage = this.totalCount ~/ 10 + 1;
          //   }
          // this.totalPage = Math.ceil(this.totalCount / 10);
        });
    },
    goDetail(id) {
      this.$router.push(`/informDetail?order_id=${id}`);
    },
    goBack() {
      this.$router.push(`/mineOrder`);
    },
    goStudy() {
      this.$store.commit("setImitateRoute", "studyRoom");
      this.$router.push({
        path: "/",
      });
    },
    goIndex() {
      this.$router.push({
        path: "/",
        query: {
          defaultIndex: 0,
        },
      });
    },
    goChangeAddress() {
      this.$router.push({
        path: "/shoppingAddress",
        query: {
          order_id: this.order_id,
        },
      });
    },
    confirmCancel() {
      orderApi
        .GetCancelOrder({
          order_id: this.order_id,
        })
        .then((res) => {
          if (res.statusCode == 200) {
            // this.$router.push({
            //   path: "merchandiseDetail",
            //   query: {
            //     merchandiseDetail_id: this.orderDetail.merchandise_id,
            //   },
            // });
          }
        });
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    z-index: 9;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    margin-top: 103px;
    margin-bottom: 41px;
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }

  .center-box {
    width: 100%;
    flex: 1;
    overflow: auto;

    .center-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .order-title-container {
        width: 1824px;
        height: 368px;
        background-color: #ffffff;
        border-radius: 40px;
        box-shadow: 0 0px 50px 0px rgba(124, 143, 166, 0.1);
        margin-bottom: 40px;
        padding: 40px 48px;
        box-sizing: border-box;
        display: flex;
        align-items: flex-start;
        .merchandise-image {
          width: 424px;
          height: 288px;
          overflow: hidden;
          border-radius: 32px;
          margin-right: 48px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 100%;
            max-width: 100%;
            height: auto;
            max-height: 100%;
          }
        }
        .order-title {
          flex: 1;
          .merchandise-name {
            font-size: 48px;
            line-height: 56px;
            color: #333;
          }
          .order-number {
            font-size: 36px;
            line-height: 48px;
            color: #999;
            margin: 24px 0;
          }
        }
        .course-detail {
          font-size: 36px;
          line-height: 56px;
          color: #333;
          span {
            color: #00bdff;
          }
        }
        .course-date {
          font-size: 36px;
          line-height: 56px;
          color: #333;
          display: flex;
          align-items: flex-start;
          span {
            font-size: 24px;
            color: #00bdff;
            font-family: "PingFang SC";
            font-weight: 500;
          }
          .date-list {
            display: flex;
            align-items: flex-start;
            flex-direction: column;
          }
        }
      }
      .order-detail-container {
        width: 1824px;
        height: 404px;
        background-color: #ffffff;
        border-radius: 40px;
        box-shadow: 0 0px 50px 0px rgba(124, 143, 166, 0.1);
        padding: 55px 60px 49px 48px;
        box-sizing: border-box;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        // justify-content: space-between;
        .order-price-container {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 48px;
          line-height: 56px;
          color: #333;
        }
        .order-detail-list {
          font-size: 36px;
          line-height: 56px;
          color: #999;
          margin-top: 20px;
        }
      }
      .button-container {
        width: 100vw;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 88px;
        font-size: 38px;
        color: #fff;
        text-align: center;
        line-height: 88px;
        margin-top: 32px;
        margin-bottom: 32px;
        div {
          margin: 0 10px;
          background-image: linear-gradient(180deg, #2dd5ff 0%, #00ccff 100%);
          box-shadow: 0px 6px 20px 0px rgba(45, 191, 255, 0.25);
          border-radius: 60px;
          width: 300px;
        }
        .study-button {
          width: 600px;
        }
      }
    }
    .no-Message-container {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
    }
    .no-Message {
      width: auto;
      //   background: url("@/assets/nothing/暂无课程.png") center no-repeat;
      //   background-size: cover;
      height: 500px;
      margin-top: 30px;
    }
    .no-message-content {
      font-size: 34px;
      color: #b2b2b2;
    }
  }
  .center-box::-webkit-scrollbar {
    width: 0px;
  }
}
.fading-circle {
  color: rgba(100, 49, 191, 255);
}
.canel-order-container {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  font-size: 40px;
  color: #333;
  margin-bottom: 25px;
  .cancel-order-title {
    font-size: 40px;
    color: #333;
  }
  .cancel-order-content {
    font-size: 32px;
    color: #b2b2b2;
  }
}
.footer-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    width: 312px;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    box-sizing: border-box;
  }
  .footer-confirm-button {
    border: 6px solid #00bdff;
    color: #00bdff;
    line-height: 102px;
    margin-right: 32px;
  }
  .footer-cancel-button {
    background: #08ccfd;
    color: #fff;
  }
}
::v-deep .dialog .dialog-container {
  width: 784px!important;
  height: 416px;
  border-radius: 50px;
  padding: 56px 64px;
  box-shadow: #CCFAFF 0 -8px 30px 0 inset, rgba(0, 0, 0, 0.1) 0 12px 40px 0;
  .cancel-order-title {
    margin-bottom: 32px;
  }
  .content {
    overflow: unset;
    margin-top: -30px;
    margin-bottom: 20px;
  }
}
</style>
