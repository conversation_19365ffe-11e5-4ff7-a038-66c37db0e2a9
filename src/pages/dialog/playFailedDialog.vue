<template>
  <div class="dialog"  v-if="black_status?.student_id > 0 || white_status?.student_id > 0">
    <div
      class="dialog-container"
     
    >
      <div class="to-settlement-container" v-if="!apiDone">
        <div
          class="to-settlement-content"
          :style="{
            'background-image': `url(${require('@/assets/season/正在结算背景.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <div class="to-settlement">
            <img
              :src="require('@/assets/season/正在结算字体.png')"
              class="typeface-container"
            />
            <img :src="require('@/assets/season/椭圆形.png')" />
            <img :src="require('@/assets/season/椭圆形.png')" />
            <img :src="require('@/assets/season/椭圆形.png')" />
          </div>
        </div>
      </div>
      <div class="settlement-container" v-if="apiDone">
        <div
          class="settlement-content"
          :style="{
            'background-image': `url(${require('@/assets/season/failBg.png')})`,
            'background-size': '100% 100%'
          }"
        >
          <div
            class="win-container"
            :style="{
              'background-image': `url(${require('@/assets/season/失败.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
          <div class="user-container">
            <div
              class="user-box top-user-box"
            >
              <div class="user-box-left">
                <div class="user-avactar-container">
                  <img
                    :style="{
                      'border-color': `${'#FFDE6F'}`,
                      'background-color': `${'#FFDE6F'}`
                    }"
                    :src="
                      black_status?.student_avatar ||
                      require('@/assets/season/默认头像.png')
                    "
                    alt=""
                  />
                  <div
                    class="chess-container"
                    :style="{
                      'background-image': `url(${require('@/assets/mine/black.png')})`,
                      'background-size': '100% 100%'
                    }"
                  ></div>
                </div>
                <div class="user-info-container">
                  <div class="user-name jcyt600">{{ black_status?.student_name }}</div>
                  <div class="user-chess jcyt500">
                    <span v-if="lose_reason == 'captured'"
                      >提 {{ black_captured }}子</span
                    >
                    <span v-if="lose_reason == 'resign'">
                      {{ win_side == "black" ? "中盘胜" : "中盘负" }}
                    </span>
                    <span v-if="lose_reason == 'time_out'">
                      {{ win_side == "black" ? "胜利" : "超时负" }}
                    </span>
                    <span v-if="lose_reason == 'area_score'">
                      {{ black_score }}子
                    </span>
                    <span v-if="lose_reason == 'withdraw'">
                      {{ win_side == "black" ? "胜利" : "弃权负" }}
                    </span>
                  </div>
                </div>
              </div>

              <div
                class="user-box-right"
                v-if="
                  user_id == black_status?.student_id &&
                  (end_status?.win_captured > 0 ||
                    seasonInfo?.protect_point_range == 0)
                "
              >
                <div
                  class="star-container"
                  :style="{
                    'background-image': `url(${require('@/assets/season/index/亮星.png')})`,
                    'background-size': '100% 100%'
                  }"
                ></div>
                <div class="star-num jcyt600">
                  {{ seasonInfo?.add_star.toString() }}
                </div>
              </div>
              <template class="user-box-right" v-else>
                <div
                  class="user-box-right user-box-protect"
                  v-if="user_id == black_status?.student_id"
                >
                  <div class="progress-container">
                    <progressCharts
                      :high_protect_point="seasonInfo['protect_point_range']"
                      :percent="
                        seasonInfo?.protect_point == null
                          ? 0
                          : seasonInfo?.protect_point
                      "
                    ></progressCharts>
                    <div class="content">
                      <div class="progress-title jcyt500">积分</div>
                      <div class="progress-content jcyt600">
                        {{ seasonInfo?.protect_point }}
                      </div>
                    </div>
                  </div>
                  <div class="progress-right-container">
                    <div class="start-num-container">
                      <div
                        class="star-container"
                        :style="{
                          'background-image': `url(${require('@/assets/season/index/亮星.png')})`,
                          'background-size': '100% 100%'
                        }"
                      ></div>
                      <div class="start-num jcyt600">
                        {{ seasonInfo?.add_star?.toString() }}
                      </div>
                    </div>
                    <div class="protect-used-container">
                      <div class="jcyt400">
                        {{
                          seasonInfo?.protect_used
                            ? "段位保护卡生效"
                            : "段位积分"
                        }}
                      </div>
                      <div class="jcyt400">{{ seasonInfo?.add_protect_point }}</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
            <div
              class="user-box"
              :style="{
                'background-image': `url(${require('@/assets/season/蒙版蓝.png')})`,
                'background-size': '100% 100%'
              }"
            >
              <div class="user-box-left">
                <div class="user-avactar-container">
                  <img
                    :style="{
                      'border-color': `${'#ffffff'}`,
                      'background-color': `${'#ffffff'}`
                    }"
                    :src="
                      white_status?.student_avatar ||
                      require('@/assets/season/默认头像.png')
                    "
                    alt=""
                  />
                  <div
                    class="chess-container"
                    :style="{
                      'background-image': `url(${require('@/assets/mine/white.png')})`,
                      'background-size': '100% 100%'
                    }"
                  ></div>
                </div>
                <div class="user-info-container">
                  <div class="user-name jcyt600">{{ white_status?.student_name }}</div>
                  <div class="user-chess jcyt500">
                    <span v-if="lose_reason == 'captured'"
                      >提 {{ white_captured }}子</span
                    >
                    <span v-if="lose_reason == 'resign'">
                      {{ win_side == "white" ? "中盘胜" : "中盘负" }}
                    </span>
                    <span v-if="lose_reason == 'time_out'">
                      {{ win_side == "white" ? "胜利" : "超时负" }}
                    </span>
                    <span v-if="lose_reason == 'area_score'">
                      {{ white_score }}子
                    </span>
                    <span v-if="lose_reason == 'withdraw'">
                      {{ win_side == "white" ? "胜利" : "弃权负" }}
                    </span>
                  </div>
                </div>
              </div>

              <div
                class="user-box-right"
                v-if="
                  user_id == white_status?.student_id &&
                  (end_status?.win_captured > 0 ||
                    seasonInfo?.protect_point_range == 0)
                "
              >
                <div
                  class="star-container"
                  :style="{
                    'background-image': `url(${require('@/assets/season/index/亮星.png')})`,
                    'background-size': '100% 100%'
                  }"
                ></div>
                <div class="star-num jcyt600">
                  {{ seasonInfo?.add_star.toString() }}
                </div>
              </div>
              <template v-else>
                <div
                  class="user-box-right user-box-protect"
                  v-if="user_id == white_status?.student_id"
                >
                  <div class="progress-container">
                    <progressCharts
                      :high_protect_point="seasonInfo['protect_point_range']"
                      :percent="
                        seasonInfo?.protect_point == null
                          ? 0
                          : seasonInfo?.protect_point
                      "
                    ></progressCharts>
                    <div class="content">
                      <div class="progress-title jcyt500">积分</div>
                      <div class="progress-content jcyt600">
                        {{ seasonInfo?.protect_point }}
                      </div>
                    </div>
                  </div>
                  <div class="progress-right-container">
                    <div class="start-num-container">
                      <div
                        class="star-container"
                        :style="{
                          'background-image': `url(${require('@/assets/season/index/亮星.png')})`,
                          'background-size': '100% 100%'
                        }"
                      ></div>
                      <div class="start-num jcyt600">
                        {{ seasonInfo?.add_star?.toString() }}
                      </div>
                    </div>
                    <div class="protect-used-container">
                      <div class="jcyt400">
                        {{
                          seasonInfo?.protect_used
                            ? "段位保护卡生效"
                            : "段位积分"
                        }}
                      </div>
                      <div class="jcyt400">{{ seasonInfo?.add_protect_point }}</div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div
            class="flag-container"
            :style="{
              'background-image': `url(${require('@/assets/season/旗帜3.png')})`,
              'background-size': '100% 100%'
            }"
          >
            <div class="star-rank-container">
              <star
                :finish_num="seasonInfo?.inner_star || 0"
                :total_num="seasonInfo?.star_range"
              ></star>
              <!-- <star :finish_num="4" :total_num="5"></star> -->
            </div>
            <div
              class="badge-container"
              :style="{
                'background-image': `url(${seasonInfo?.rank_star_icon})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="rank-container jcyt500"
              :text="seasonInfo?.level"
              :style="{
                'background-image': `url(${require('@/assets/season/失败等级标识.png')})`,
                'background-size': '100% 100%'
              }"
            >
              {{ seasonInfo?.level }}
            </div>
            <div
              class="level-name jcyt600"
              :style="{
                'background-image': `url(${require('@/assets/season/称号背景.png')})`,
                'background-size': '100% 100%'
              }"
            >
              {{ seasonInfo?.current_level_name }}
            </div>
          </div>
        </div>
        <div class="button-container">
          <div
            class="back jcyt600"
            :style="{
              'background-image': `url(${require('@/assets/season/返回大厅.png')})`,
              'background-size': '100% 100%'
            }"
            @click="close"
          >
            返回棋盘
          </div>
          <div
            class="go-on jcyt600"
            :style="{
              'background-image': `url(${require('@/assets/season/继续匹配.png')})`,
              'background-size': '100% 100%'
            }"
            @click="goGame"
          >
            继续匹配
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import star from "../season/star";
import Progress from "easy-circular-progress";
import storage from "@/public/storage.js";
import progressCharts from "../season/progressCharts";

export default {
  props: {
    black_status: {
      type: Object
    },
    white_status: {
      type: Object
    },
    end_status: {
      type: Object
    }
  },
  data() {
    return {
      seasonInfo: {},
      apiDone: false,
      user_id: 0,
      black_captured: 0,
      white_captured: 0,
      lose_reason: "",
      win_side: "",
      white_score: 0,
      black_score: 0
    };
  },
  components: { star, Progress, progressCharts },
  watch: {
    end_status: {
      handler: function (data) {
        this.user_id == this.black_status?.student_id
          ? (this.seasonInfo = this.black_status)
          : (this.seasonInfo = this.white_status);
        this.check_result(this.end_status);
      }
    },
    deep: true
  },
  methods: {
    close() {
      this.$emit("closePlayDialog");
    },
    goGame() {
      this.$router.push({
        path: "/matching",
        query: {
          type: this.end_status.win_captured > 0 ? "capture" : "territory"
        }
      });
    },
    check_result(data) {
      console.log("check_result" + data);
      this.white_score = data.w_score;
      this.black_score = data.b_score;
      this.black_captured = data.b_captured;
      this.white_captured = data.w_captured;
      this.win_side =
        data.win === 1 ? "black" : data.win === 2 ? "white" : data.win;
      if (this.win_side === 3 || this.win_side === 4) {
        this.lose_reason = data.win_result;
      } else {
        if (data.win_result === "Abstain") {
          this.lose_reason = "Abstain";
          return;
        }
        var str = data.win_result.substring(
          data.win_result.indexOf("+") + 1,
          data.win_result.length
        );
        if (str.indexOf("C")) {
          var num = str.substring(1, str.length);
          // data.win === 1
          //   ? (this.black_captured = num)
          //   : (this.white_captured = num);
        }
        this.lose_reason =
          str.indexOf("C") > -1 || str == "O"
            ? "captured"
            : str === "R"
            ? "resign"
            : str === "T"
            ? "time_out"
            : parseInt(str) > 0
            ? "area_score"
            : str === "L"
            ? "withdraw"
            : "Draw";
      }
      this.apiDone = true;
    }
  },
  mounted() {
    this.user_id = storage.$getStroage("userId");
  }
};
</script>
<style lang="less" scoped>
.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  .dialog-container {
    width: 100vw;
    height: 100vh;
    // background: #ffffff;
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 110px;
    display: flex;
    align-items: center;
    flex-direction: column;
    // padding: 64px 176px;
    .star-num {
      margin-left: 16px;
    }
    .to-settlement-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .to-settlement-content {
        width: 100%;
        height: 28.125vw;
        display: flex;
        align-items: center;
        justify-content: center;
        .to-settlement {
          width: 700px;
          height: 160px;
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          img {
            width: 30px;
            height: 30px;
            margin-left: 30px;
            margin-bottom: 30px;
            animation: float1 1s ease-in infinite;

            &:nth-child(2) {
              animation-delay: 0.25s;
            }
            &:nth-child(3) {
              animation-delay: 0.5s;
            }
            &:nth-child(4) {
              animation-delay: 0.75s;
            }
          }
          @keyframes float1 {
            0% {
            }
            50% {
              transform: translateY(-50px);
            }
            100% {
            }
          }
          .typeface-container {
            width: 450px;
            height: 160px;
            margin-left: 0;
            margin-bottom: 0;
            animation: none;
          }
        }
      }
    }
    .settlement-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      .settlement-content {
        width: 100%;
        height: 28.125vw;
        position: relative;
        margin-top: 216px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .win-container {
          width: 298px;
          height: 167px;
          position: absolute;
          top: -85px;
          left: calc((100vw / 2) + 250px);
          z-index: 10;
        }
        .user-container {
          width: 1136px;
          height: 432px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .user-box {
            width: 100%;
            height: 160px;
            border-radius: 80px 0 0 80px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            .user-is-define {
              position: absolute;
              top: -32px;
              right: 0;
              width: 320px;
              height: 64px;
            }
            .user-box-left {
              display: flex;
              align-items: center;
              height: 100%;

              .user-avactar-container {
                border-radius: 50%;
                position: relative;
                height: 100%;
                img {
                  width: 160px;
                  height: 160px;
                  border-radius: 50%;
                  border: 5px solid;
                  box-sizing: border-box;
                }
                .chess-container {
                  width: 48px;
                  height: 48px;
                  border-radius: 50%;
                  position: absolute;
                  top: 0;
                  right: 0;
                }
              }
              .user-info-container {
                height: 100%;
                display: flex;
                flex-direction: column;
                align-self: flex-start;
                justify-content: center;
                margin-left: 32px;
                color: #fff;
                .user-name {
                  font-size: 40px;
                  margin-bottom: 8px;
                }
                .user-chess {
                  font-size: 32px;
                }
              }
            }
            .user-box-right {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: auto;
              height: 56px;
              margin-right: 106px;
              font-size: 48px;
              line-height: 56px;
              color: rgba(255, 222, 111, 1);
              .progress-container {
                margin-top: 10px;
                margin-right: 32px;
                width: 128px;
                position: relative;
                text-align: center;
                .content {
                  margin-top: 5px;
                  margin-right: 10px;
                }
              }
              .star-container {
                width: 56px;
                height: 56px;
              }
              .progress-right-container {
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                flex-direction: column;
                margin-left: 24px;
                .start-num-container {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  // width: 121px;
                  .start-num {
                    font-size: 48px;
                    color: rgba(187, 217, 255, 1);
                    margin-left: 24px;
                  }
                  .star-container {
                    width: 48px;
                    height: 48px;
                  }
                }
                .protect-used-container {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 226px;
                  font-size: 32px;
                  margin-top: 18px;
                  color: rgba(255, 255, 255, 0.5);
                }
              }
            }
            .user-box-protect {
              margin-right: 40px;
            }
          }
          .other-user-box {
            width: 100%;
            height: 180px;
          }
        }
        .flag-container {
          position: absolute;
          width: 544px;
          height: 751.78px;
          top: -57.4px;
          left: 172px;
          display: flex;
          align-items: center;
          flex-direction: column;
          .star-rank-container {
            width: 264px;
            height: 69px;
            margin-top: 5px;
          }
          .badge-container {
            width: 544px;
            height: 544px;
            position: relative;
            top: -21.6px;
          }
          .rank-container {
            width: 240px;
            height: 92px;
            font-size: 48px;
            text-align: center;
            line-height: 80px;
            // color: #ffeac0;
            position: relative;
            top: -163.2px;
            color: #EDF3FF;
            &::before {
              content: attr(text);
              position: absolute;
              z-index: 10;
              color: #B6C2DE;
              -webkit-mask: linear-gradient(to bottom, transparent, #B6C2DE);
            }

          }
          .level-name {
            width: 436px;
            height: 64px;
            font-size: 36px;
            text-align: center;
            line-height: 64px;
            color: #fff;
            position: relative;
            top: -144px;
          }
        }
      }
      .button-container {
        width: 704px;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 100px;
        > div {
          width: 312px;
          height: 124px;
          line-height: 115px;
          text-align: center;
          color: #fff;
          font-size: 40px;
        }
      }
    }
  }
  .progress-title {
    font-size: 24px;
    color: rgba(255, 255, 255, 0.5);
    // margin-top: -25px;
  }
  .progress-content {
    font-size: 48px;
    color: #eac16d;
    margin-bottom: 20px;
  }
}
.top-user-box {
  background-image: linear-gradient(to right, rgba(0,0,0,.3), rgba(0,0,0,0));
  margin-bottom: 32px;
}
</style>
