<template>
  <div class="top_wrap">
    <div class="left_feature">
      <div class="course_status">
        <span class="dian" :style="{
            backgroundColor:
            doneLiveStage !== 'not_started' && doneLiveStage !== 'broadcast-starts'
                ? 'red'
                : ''
          }"></span>
        <span
          >{{
            doneLiveStage == "not_started"
              ? "将要上课"
              : doneLiveStage == "broadcast-starts"
              ? "直播中"
              : "超时"
          }}
          <span
            class="countdown"
            v-if="doneLiveStage === 'not_started' || doneLiveStage === 'yeondo'"
          >
            {{ s_to_hs(diffTime) }}
          </span>
        </span>
        <!-- <span class="flex" v-else><span>直播中</span></span> -->
      </div>
      <div class="live_title">
        <comHidtext>{{ config.roomName }}</comHidtext>
      </div>
      <div class="flex">
        <div class="course_time">课程时长：{{ liveData.course_len }}</div>
        <div class="live_time" v-if="doneLiveStage === 'broadcast-starts'">
          {{ s_to_hs(diffTime) }}
        </div>
      </div>
      <!-- <div class="rewards_num">获得奖励数量：1</div> -->
    </div>
    <div class="right_feature">
      <div class="flex" v-if="liveType">
        <!-- <el-popover
          placement="bottom"
          trigger="hover"
          popover-class="mi_popover"
        >
        <div class="pro">
          <div class="title">
          <span>选择本地麦克风</span>
          <div class="mic" v-for="(item,index) in config.microphoneList" :key="index" @click="getActive(item.deviceId)">
          <p :class="item.deviceId == micI? 'active flex' : 'flex'" >
            <span style="width:7px;height:7px;margin-right:2px">
              <img src="@/assets/living/small.png" alt="" v-if="item.deviceId == micI" style="width:7px;height:7px;">  
            </span>
              {{ item.label }}
          </p>
          </div>
          <div class="mic" @click="getActive()">
            <p :class="!micI?'active flex':'flex'"> 
            <span style="width:7px;height:7px;margin-right:2px">
              <img src="@/assets/living/small.png" alt="" v-if="!micI" style="width:7px;height:7px;">  
            </span>
            禁用
          </p>
          </div>
          <div class="config" @click="getConfig">
            <p>设置</p>
          </div>
        </div>
        </div>
         
        </el-popover>
        <el-popover
          placement="bottom"
          trigger="hover"
            popover-class="mi_popover"
        >
        <div class="pro">
        <div class="title">
            <span>选择本地摄像头</span>
            <div class="mic" v-for="(item,index) in config.cameraList" :key="index" @click="getCom(item.deviceId)">
            <p :class="item.deviceId == comI? 'active flex' : 'flex'" >
              <span style="width:7px;height:7px;margin-right:2px">
                <img src="@/assets/living/small.png" alt="" v-if="item.deviceId == comI" style="width:7px;height:7px;">
              </span>
              {{ item.label }}
            </p>
            </div>
            <div class="mic" @click="getCom()">
              <p :class="!comI?'active flex':'flex'"> 
                <span style="width:7px;height:7px;margin-right:2px">
                  <img src="@/assets/living/small.png" alt="" v-if="!comI" style="width:7px;height:7px;">  
                </span>
                禁用
              </p>
            </div>
          </div>
          <div class="config" @click="getConfig">
            <p>设置</p>
            
          </div>
        </div>
          
        </el-popover> -->
        <div
          class="icon-button flex"
          slot="reference"
          style="margin-right: 3px"
        >
          <div
            class="voice_mic"
            :style="
              !isAudioMuted
                ? [
                    {
                      background: `url(${require('@/assets/living/she.png')}) no-repeat`,
                    },
                    { 'background-size': '100% 100%' },
                  ]
                : ''
            "
            @click="toggleMuteAudio"
          ></div>
        </div>
        <div
          class="icon-button flex"
          slot="reference"
          style="margin-right: 3px"
        >
          <div
            class="voice_com"
            slot="reference"
            :style="
              !isVideoMuted
                ? [
                    {
                      background: `url(${require('@/assets/living/micnow.png')}) no-repeat`,
                    },
                    { 'background-size': '100% 100%' },
                  ]
                : ''
            "
            @click="toggleMuteVideo"
          ></div>
        </div>
        <div
          class="icon-button flex"
          slot="reference"
          style="margin-right: 3px"
        >
          <div
            class="voice_com"
            slot="reference"
            :style="[
              {
                background: `url(${require('@/assets/living/全部_all-application 1.png')}) no-repeat`,
              },
              { 'background-size': '100% 100%' },
            ]"
            @click="getConfig"
          ></div>
        </div>
        <conFig ref="conFig"></conFig>
      </div>
      <div class="back" @click="back"></div>
    </div>
  </div>
</template>
<script>
// 头部配置
import conFig from "../../diag/config.vue";
import liveApi from "@/api/living.js";
import comHidtext from "@/component/com-hidtext.vue";
export default {
  data() {
    return {
      timer: "",
      diffTime: "",
    };
  },
  props: {
    //直播间信息
    liveData: {
      type: Object,
      default: () => {},
    },
    liveType: {
      type: Boolean,
      default: false,
    },
    config: {
      type: Object,
      default: () => {},
    },
    interval_end: {},
    interval_start: {},
    interval_starting: {},
  },
  components: {
    conFig,
    comHidtext,
  },
  computed: {
    isAudioMuted() {
      return this.$store.getters.doneAudioState;
    },
    isVideoMuted() {
      return this.$store.getters.doneVideoState;
    },
    doneLiveStage() {
      return this.$store.getters.doneLiveStage;
    },
    doneOnStage() {
      return this.$store.getters.doneOnStage;
    },
  },
  mounted() {},
  watch: {
    doneLiveStage: {
      handler: function (val) {
        console.log(val);
        if (this.doneLiveStage === "not_started") {
          this.diffTime = this.interval_start;
          this.reGetCountdown();
        } else if (this.doneLiveStage === "broadcast-starts") {
          this.diffTime = this.interval_starting;
          this.reGetCountUp();
        } else {
          this.diffTime = this.interval_end;
          this.reGetCountUp();
        }
      },
      immediate: true,
      deep: true,
    },
    diffTime: {
      handler: function (val) {
        const total =
          this.liveData.course_len.substring(
            0,
            this.liveData.course_len.lastIndexOf("时")
          ) *
            3600 +
          this.liveData.course_len.substring(
            this.liveData.course_len.lastIndexOf("时") + 1,
            this.liveData.course_len.lastIndexOf("分")
          ) *
            60;
        if (val === 0 && this.doneLiveStage === "not_started") {
          clearInterval(this.timer);
          this.timer = null;
          this.$store.dispatch("UPDATE_LIVE_STAGE", "broadcast-starts");
        } else if (
          this.doneLiveStage === "broadcast-starts" &&
          this.interval_end === 0 &&
          val === total
        ) {
          clearInterval(this.timer);
          this.timer = null;
          this.$store.dispatch("UPDATE_LIVE_STAGE", "yeondo");
        }
      },
      deep: true,
    },
  },
  beforeCreate() {},
  methods: {
    async back() {
      // await liveApi.StudentOuter({room_id: this.config.roomId, student_id: this.config.userId});
      this.$emit("leave");
      // this.$router.push({
      //   path: "/",
      //   query: {
      //     defaultIndex: 1
      //   }
      // });
      // this.$storage.$removeStroage("studentId");
    },
    getActive(e) {
      this.$emit("switchDevice", "audio", e);
    },
    getCom(e) {
      this.$emit("switchDevice", "video", e);
    },
    getConfig() {
      this.$refs.conFig.onShow();
    },
    reGetCountdown() {
      if (this.timer) {
        clearInterval(this.timer);
      } else {
        this.timer = setInterval(() => {
          if (this.diffTime > 0) {
            this.diffTime--;
          } else {
            clearInterval(this.timer);
          }
        }, 1000);
      }
    },
    reGetCountUp() {
      if (this.timer) {
        clearInterval(this.timer);
      } else {
        this.timer = setInterval(() => {
          this.diffTime++;
        }, 1000);
      }
    },
    s_to_hs(value){
      let secondTime = parseInt(value); // 秒
      let minuteTime = 0; // 分
      let hourTime = 0; // 时
      if (secondTime > 60) {
        // 如果秒数大于60，将秒数转换成整数
        // 获取分钟，除以60取整，得到整数分钟
        minuteTime = parseInt(secondTime / 60);
        // 获取秒数，秒数取余，得到整数秒数
        secondTime = parseInt(secondTime % 60);
        // 如果分钟大于60，将分钟转换成小时
        if (minuteTime > 60) {
          // 获取小时，获取分钟除以60，得到整数小时
          hourTime = parseInt(minuteTime / 60);
          // 获取小时后取余的分，获取分钟除以60取余的分
          minuteTime = parseInt(minuteTime % 60);
        }
      }
      // 若秒数是个位数，前面用0补齐
      secondTime = secondTime < 10 ? "0" + secondTime : secondTime;
      let result = "" + secondTime + "";
      if (minuteTime > 0) {
        // 若分钟数是个位数，前面用0补齐
        minuteTime = minuteTime < 10 ? "0" + minuteTime : minuteTime;
        result = "" + minuteTime + ":" + result;
      } else {
        // 若分钟数为0，用"00"表示
        result = "" + "00" + ":" + result;
      }

      if (hourTime > 0) {
        // 若小时数是个位数，前面用0补齐
        hourTime = hourTime < 10 ? "0" + hourTime : hourTime;
        result = "" + hourTime + ":" + result;
      } else {
        // 若小时数为0，用"00"表示
        result = "" + "00" + ":" + result;
      }
      // console.log("result", result);
      return result;
    },
    toggleMuteAudio() {
      this.$store.dispatch("UPDATE_AUDIO_STATE", !this.isAudioMuted);
      this.setStudentControl({
        microphone_status: this.isAudioMuted,
        operate_type: 1,
      });
    },
    toggleMuteVideo() {
      this.$store.dispatch("UPDATE_VIDEO_STATE", !this.isVideoMuted);
      this.setStudentControl({
        camera_status: this.isVideoMuted,
        operate_type: 2,
      });
    },
    async setStudentControl(type) {
      let res = await liveApi.StudentControl({
        room_id: this.config.roomId,
        student_id: this.config.userId,
        ...type,
      });
      // console.log(res);
    },
    formatDuration(val) {
      // return val;
      // let left_hour = Math.floor(val / 3600);
      // let left_minute = Math.floor((val % 3600) / 60);
      // let left_second = val % 60;
      // return `${left_hour}:${left_minute}:${left_second}`;
      if (val < 60) {
        // 不满一分钟展示秒数
        return val;
      } else {
        let $moment = this.moment.duration(val, "second");
        let hours = $moment.hours();
        let minutes = $moment.minutes();
        let seconds = $moment.seconds();
        // 补零操作
        if (hours < 10) {
          hours = "0" + hours;
        } else if (minutes < 10) {
          minutes = "0" + minutes;
        } else if (seconds < 10) {
          seconds = "0" + seconds;
        }
        return `${hours}:${minutes}:${seconds}`;
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
};
</script>
<style lang="scss" scoped>
.top_wrap {
  width: 1920px;
  height: 90px;
  background-color: #438eff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index:20;
  .left_feature {
    width: 1500px;
    // height: 128px;
    display: flex;
    align-items: center;
    font-size: 25px;
    .course_status {
      // width: 208px;
      height: 56px;
      text-align: center;
      border-radius: 40px;
      background: rgba(140, 186, 255, 0.5);
      color: #fff;
      font-size: 25px;
      font-style: normal;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 79px;
      padding: 0px 20px 0 20px;
      .dian {
        display: inline-block;
        width: 20px;
        height: 20px;
        background-color: #34ff41;
        border-radius: 50%;
        margin-right: 10px;
      }
    }

    .live_title {
      width: 200px;
      color: #fff;
      font-size: 25px;
      font-style: normal;
      font-weight: 600;
      margin: 0 30px 0 30px;
      // vertical-align: top;
      overflow: hidden;
      white-space: nowrap;
      // text-overflow: ellipsis;
    }
    .course_time {
      color: #fff;
      // font-size: 40px;
      font-style: normal;
      font-weight: 600;
      // margin-left: 64px;
    }
    .live_time {
      color: #34ff41;
      // font-size: 40px;
      font-style: normal;
      font-weight: 600;
      margin-left: 60px;
    }
    .rewards_num {
      color: #fff;
      // font-size: 40px;
      font-style: normal;
      font-weight: 600;
      margin-left: 51px;
    }
    .felx {
      display: flex;
    }
  }
  .right_feature {
    // width: 193px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 31px;
    .voice_mic {
      width: 50px;
      height: 50px;
      background: url("@/assets/living/huanow.png") no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }
    .voice_com {
      width: 50px;
      height: 50px;
      background: url("@/assets/living/hua.png") no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }
    .back {
      width: 54px;
      height: 54px;
      background-color: pink;
      background: url("@/assets/living/close.png") no-repeat;
      background-size: contain;
      margin-right: 20px;
      cursor: pointer;
    }
  }
}

.pro {
  width: 100%;
  font-size: 28px;
  font-weight: 400;
  .title {
    span {
      display: block;
      height: 58px;
      padding: 10px 20px 0px 20px;
      line-height: 33px;
    }
    p {
      height: 58px;
      padding: 10px 20px 10px 20px;
      // height: 40px;
    }
    .active {
      background: rgba(140, 186, 255, 0.2);
      color: #438eff;
    }
  }
  .config {
    border-top: 2px solid #438eff;
    display: block;
    height: 58px;
    padding: 10px 20px 0px 20px;
    line-height: 33px;
    margin-left: 20px;
    margin-top: 20px;
  }
}
.flex {
  display: flex;
  align-items: center;
  cursor: pointer;
}
</style>
