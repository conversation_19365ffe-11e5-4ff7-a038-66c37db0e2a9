<template>
  <div
    :class="type ? 'containers' : 'content_false'"
    :style="doneStandard === 2 ? 'height:90%' : ''"
    ref="containers"
  >
    <VueDragResize
      v-if="doneStandard === 2 && type"
      :h="height"
      :w="width"
      :isActive="true"
      :isDraggable="isDraggable"
      :isResizable="false"
      :y="top"
      :x="left"
      :minw="70"
      :minh="41"
      :z="99999999"
      v-on:resizing="resize"
      v-on:dragging="resize"
      :parentLimitation="false"
      ref="dragPlus"
    >
      <div
        :id="doneTeacherId"
        :class="
          type
            ? doneUpdateRollCall || doneStandard === 2
              ? 'item'
              : 'item items'
            : 'loca_item'
        "
        v-if="remoteList.find((item) => item.userId == doneTeacherId)"
        :key="doneTeacherId"
      >
        <liveConfig
          v-if="doneTeacherId"
          :item="remoteList.find((item) => item.userId == doneTeacherId)"
          type="farend"
          :liveType="true"
          :key="doneTeacherId"
        />
      </div>
    </VueDragResize>
    <template v-else>
      <template v-for="item in remoteList">
        <div
          :id="item.userId"
          :class="
            type ? (doneUpdateRollCall ? 'item' : 'item items') : 'loca_item'
          "
          :key="item.userId"
          :style="
            !type
              ? ''
              : remoteList.length >= 5
              ? `width:calc(352px/${remoteList.length});height:calc(202px / ${remoteList.length})`
              : 'min-width: 19.140625vw;min-height: 11.328125vw;'
          "
        >
          <liveConfig
            v-if="item.userId"
            :item="item"
            :type="item.type"
            :liveType="type"
            :key="item.userId"
          />
          <div class="down" v-if="type && stageti && item.type == 'loca'">
            {{
              doneOnStage
                ? "你上台了，积极的和大家互动吧！"
                : "你已下台，暂时无法与大家互动"
            }}
          </div>
        </div>
      </template>
    </template>
  </div>
</template>
<script>
// 视频
import liveConfig from "../live-config.vue";
import liveApi from "@/api/living.js";
import config from "@/config.js";
import VueDragResize from "vue-drag-resize";
export default {
  data() {
    return {
      stageti: true,
      screenHeight: 0,
      screenWidth: 0,
      left: 0,
      top: 0,
      isDraggable: true,
      width: 70,
      height: 45,
      // remoteList: [],
      operate_type: 1,
      widthS: 80,
      heightS: 40,
    };
  },
  components: {
    liveConfig,
    VueDragResize,
  },
  props: {
    remoteStreamList: {
      type: Array,
      default: () => [],
    },
    type: Boolean,
    remoteList: {
      type: Array,
      default: () => [ {
          userId: "localStream",
          // streamType,
          receiveAudio: true, // 麦克风
          receiveVideo: true, // 摄像头
          hasLimit: false,
          onStage: true, // 上台状态
          student_microphone_status: true, // 老师介入的权限
          camera_status: true, // 摄像头权限
          raiseHand: false, // 举手
          student_role_status: false, //白板权限
          userName: "",
          volume: 0,
          type: "loca",
          cupNumber: 0,
          isShow: true,
          reasureCount: false,
        },],
    },
    doneStandard: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    doneOnStage() {
      return this.$store.getters.doneOnStage;
    },
    remoteArray() {
      return this.remoteStreamList.map((item) => item.userId);
    },
    doneMicrophone() {
      return this.$store.getters.doneMicrophone;
    },
    doneCamera() {
      return this.$store.getters.doneCamera;
    },
    doneTeacherId() {
      return this.$store.getters.doneTeacherId;
    },
    doneUpdateRollCall() {
      return this.$store.getters.doneUpdateRollCall;
    },
    // doneTeacherInfo() {
    //   return this.remoteList.find((item) => item.userId == this.doneTeacherId);
    // },
  },
  watch: {
    doneOnStage: {
      handler(newVal, oldVal) {
        console.log(newVal);
        // if(newVal && this.type){
        this.stageti = true;
        this.closeStagetiOut();
        // }
      },
      deep: true,
    },
    remoteArray: {
      handler(newVal, oldVal) {
        if (oldVal.length != newVal.length) {
          console.log(newVal, oldVal, "wwwww");
        }
      },
      deep: true,
      // immediate: true,
    },
    type: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.$emit("adjustStage"); 
          this.$nextTick(() => {
            this.$emit("sendTotal", {
              type: "student_coordinate",
            });
          });
        }
      },
      deep: true,
    },
    remoteList: {
      handler(newVal, oldVal) {
        this.doneTeacherInfo = this.remoteList.find(
          (item) => item.userId == this.doneTeacherId
        );
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.screenHeight = this.$refs.containers.clientHeight;
    this.screenWidth = this.$refs.containers.clientWidth;
    // this.$emit("adjustStage");
    this.$bus.on("receive_teacher_coordinate_move", this.setVideoPosition);
    this.$nextTick(()=>{
      console.log(document.getElementById("localStream"),'w---ww')
      // this.$emit("startPublish");
    })
  },
  beforeCreate() {
    this.$nextTick(() => {});
  },
  methods: {
    closeStagetiOut() {
      setTimeout(() => {
        this.stageti = false;
      }, 3000);
      // 关闭
      clearTimeout(this.closeStagetiOut);
    },
    resize(newRect) {
      console.log(newRect.left, newRect.top, newRect.width, newRect.height);
      this.top = newRect.top;
      this.left = newRect.left;
      // this.width = newRect.width;
      // this.height = newRect.height;
      // 禁止超出body
      if (this.top <= 0) {
        this.$refs.dragPlus.top = 0;
      }
      if (this.left <= 0) {
        this.$refs.dragPlus.left = 0;
      }
      if (
        this.top >=
        document.getElementById("content_wrap").clientHeight - this.heightS
      ) {
        this.$refs.dragPlus.top =
          document.getElementById("content_wrap").clientHeight - this.heightS;
      }
      if (
        this.left >=
        document.getElementById("content_wrap").clientWidth - this.widthS
      ) {
        this.$refs.dragPlus.left =
          document.getElementById("content_wrap").clientWidth - this.widthS;
      }
      // this.screenHeight = newRect.height;
      // this.screenWidth = newRect.width;
    },
    convertPosition(xPos, xHeight, xWidth, xHeightTotal, yWidth, yHeightTotal) {
      // 计算x屏幕和y屏幕在宽度和高度上的缩放比例
      let widthScale = yWidth / xWidth;
      let heightScale = yHeightTotal / xHeightTotal;

      // 使用缩放比例来计算y屏幕上的新位置
      let yPos = Math.round(xPos * widthScale);
      let yHeight = Math.round(xHeight * heightScale);

      // 确保新位置在y屏幕的边界内
      yPos = yPos;
      yHeight = yHeight;

      // 返回y屏幕上的新位置
      return {
        yPos: ((yPos / 2048) * 100).toFixed(2),
        yHeight: ((yHeight / 1538) * 100).toFixed(2),
      };
    },
    // 设置视频框的位置
    setVideoPosition(item) {
      this.teacher_camera_move = item.teacher_camera_move;
      if (item.teacher_camera_move === 1) {
        let videoTeacher = document.getElementById(this.doneTeacherId);
        // top:${newPosition.yPos}vw;left:${newPosition.yHeight}vw;
        this.$refs.dragPlus.top = item.teacher_camera_move !== 2 ? 0 : "";
        this.$refs.dragPlus.left = item.teacher_camera_move !== 2 ? 0 : "";
        videoTeacher.style = `width:42vw;height:32vw`;
        this.widthS = 180;
        this.heightS = 130;
      } else {
        let videoTeacher = document.getElementById(this.doneTeacherId);
        // top:${newPosition.yPos}vw;left:${newPosition.yHeight}vw;
        // this.$refs.dragPlus.top = item.teacher_camera_move !== 2 ? 0:'';
        // this.$refs.dragPlus.left = item.teacher_camera_move !== 2 ? 0:'';
        videoTeacher.style = `    width: 19.140625vw;
    height: 11.328125vw;`;
        this.widthS = 90;
        this.heightS = 55;
      }
      return;
      // this.isDraggable = item.teacher_camera_move === 2 ? true :false;
      // 以下是位置的计算过程 但是遇到分辨率不同 暂时关闭
      let yWidth = document.body.clientHeight;
      let yHeightTotal = document.body.clientWidth;
      // 假设老师的宽高
      let xHeightTotal = item.window_height;
      let xWidth = item.window_width;
      // 老师给到的位置
      let xPos = item.top;
      let xHeight = item.left;
      // 计算学生端的相对位置
      // 设置视频框的位置
      let newPosition = this.convertPosition(
        xPos,
        xHeight,
        xWidth,
        xHeightTotal,
        yWidth,
        yHeightTotal
      );
      // 算出item.with 在 item.window_width的占比
      // 算出item.height 在 item.window_height的占比
      // let wid = ((((((item.with / item.window_width) * 100) / 100)*2048) / 2048)* 100).toFixed(2)
      // let height = ((((((item.height / item.window_height) * 100)/100)*1538) / 2048) * 100).toFixed(2)
      let wid = ((item.with / 2048) * 100).toFixed(2);
      let height = ((item.height / 1538) * 100).toFixed(2);
      console.log(newPosition.yPos);
      console.log(newPosition.yHeight);
      let videoTeacher = document.getElementById(this.doneTeacherId);
      // top:${newPosition.yPos}vw;left:${newPosition.yHeight}vw;
      this.$refs.dragPlus.top = item.teacher_camera_move !== 2 ? 0 : "";
      this.$refs.dragPlus.left = item.teacher_camera_move !== 2 ? 0 : "";
      videoTeacher.style = `width:${wid}vw;height:${height}vw`;
      // 设置视频框的位置
    },
  },
  beforeDestroy() {
    this.$bus.off("receive_teacher_coordinate_move");
  },
};
</script>
<style lang="less" scoped>
// .containers_width{
//   width:calc(100% - 422px);
// }
.containers {
  // width: calc(100% - 422px);
  width: 100%;
  display: flex;
  height: 240px;
  margin-top: 20px;
  margin-left: 32px;
  justify-content: center;
  // max-height: 200px;
  position: relative;
  .swiper-container {
    width: 100%;
  }
  .swiper-slide {
    width: 406px !important;
    // margin-left: 20px;
  }
  .down {
    width: 392px;
    margin: 12px auto;
    text-align: center;
    padding: 30px;
    background: rgba(53, 104, 101, 0.6);
    border-radius: 18px;
    color: #fff;
    font-size: 20px;
    font-weight: 400;
    position: fixed;
    z-index: 9999;
    top: 354px;
  }
  .items {
    animation: spread 1s 1 alternate;
  }
  .item {
    width: 392px;
    height: 232px;
    // min-width: 392px;
    // min-height: 232px;
    margin-right: 3%;
    background-color: #3c3c3c;
    border-radius: 18px;
    overflow: hidden;
    position: relative;
    ::v-deep video {
      border-radius: 0.878906vw;
    }
    .post {
      border-radius: 0.878906vw;
    }
    .stage {
      width: 28px;
      height: 28px;
      position: absolute;
      top: 2px;
      right: 3px;
      z-index: 1;
    }
    .item_bottom {
      width: 100%;
      height: 48px;
      position: absolute;
      bottom: 9px;
      left: 0;
      padding: 0 20px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      z-index: 1;
      .info_button {
        display: flex;
        align-items: center;
        img {
          width: 28px;
          height: 28px;
        }
        .sutdent_name {
          color: #fff;
          font-size: 20px;
          font-style: normal;
          font-weight: 600;
          line-height: 32px;
        }
      }
      .trophy {
        font-size: 12px;
        color: #fff740;
        img {
          width: 28px;
          height: 28px;
        }
      }
    }
  }
}
.content_false {
  width: 100%;
  height: calc(100% - 6.25vw);
  .loca_item {
    width: 2048px;
  }
}
.loca_item {
  position: absolute;
  width: 100%;
  height: calc(100vh - 92px);
}
// ::v-deep .vdr{
//   width:100%;
// }
@keyframes spread {
  0% {
    transform: scale(0); /* 开始时缩小到原来的一半 */
  }
  100% {
    transform: scale(1); /* 结束时放大到原来的大小 */
  }
}
::v-deep .vdr-stick {
  width: 20px !important;
  height: 20px !important;
}
</style>
