<template>
  <div :class="type ? 'content':'content_false'">
    <div id="localStream" :class="type ? 'item':'loca_item'">
      <liveConfig :item="config" :type="'loca'" :liveType="type" v-if="type" :key="type"/>
    </div>
    <div class="down" v-if="type && stageti"> 
      {{ doneOnStage ? '你上台了，积极的和大家互动吧！' : '你以下台，暂时无法与大家互动' }}
    </div>
  </div>
</template>
<script>
// 废弃 展示本地视频
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
import liveConfig from "../live-config.vue"
import config from "@/config";
export default {
  data() {
    return {
      stageti:true,
      config
    };
  },
  props: {
    // remoteStreamList: {
    //   type: Array
    // },
    localStream : {},
    type:{
      type:Boolean,
      default:false
    }
  },
  computed: {
    doneOnStage() {
      return this.$store.getters.doneOnStage;
    }
  },
  watch: {
    doneOnStage:{
      handler(newVal,oldVal){
        console.log(newVal)
        // if(newVal && this.type){
          this.stageti = true;
          this.closeStagetiOut();
        // }
      },
      deep:true
    }
  },
  methods: {
    // 切换摄像头
    changeCamera() {
      this.$store.commit("changeCamera");
    },
    closeStagetiOut() {
      setTimeout(() => {
        this.stageti = false;
      },3000)
      // 关闭
      clearTimeout(this.closeStagetiOut);
    }
  },
  components: { swiper, swiperSlide, liveConfig },
  mounted() {
      console.log(document.getElementById("localStream"))
      this.$emit("startLocalVideo", document.getElementById("localStream"));
      this.$emit("startLocalAudio", document.getElementById("localStream"));  
      this.closeStagetiOut();
  },
  beforeCreate() {},
};
</script>
<style lang="less" scoped>
.content_false{
  width: 100%;
  height: calc(100% - 6.25vw);
}
.down{
  width: 392px;
  margin: 12px auto;
  text-align: center;
  padding: 30px;
  background: rgba(53,104,101,0.6);
  border-radius: 18px;
  color: #fff;
  font-size: 20px;
  font-weight: 400;
  position: absolute;
  z-index: 9999;
}
.content {
  // width: 100%;
  height: 240px;
  margin-top: 24px;
  margin-left:20px;
    .item {
      width: 392px;
      height: 194px;
      margin-right: 3%;
      // background-color: pink;
      border-radius: 18px;
      position: relative;
      overflow: hidden;
      .stage{
        width: 28px;
        height: 28px;
        position: absolute;
        top: 2px;
        right: 3px;
        z-index: 1;
      }
      .item_bottom {
        width: 100%;
        height: 48px;
        position: absolute;
        bottom: 9px;
        left: 0;
        padding: 0 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 1;
        .trophy{
          font-size: 12px;
          color: #FFF740;
          img{
            width: 28px;
            height: 28px;
          }
        }
        .sutdent_name {
          color: #fff;
          font-size: 20px;
          font-style: normal;
          font-weight: 600;
          line-height: 32px;
        }
        .img_button {
          display: flex;
          justify-items: center;
          margin-right: 10px;
          img {
            width: 35px;
            height: 35px;
          }
        }
        
      // }
    }
    .item:nth-child(3n) {
      margin-right: 0;
    }
  }
}
#localStream{
  video{
    object-fit: contain;
  }
}
.loca_item{
  position: absolute;
  width:100%;
  height: calc(100% - 128px);
}
</style>
