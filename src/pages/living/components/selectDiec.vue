<template>
  <select id="dropdown" v-model="activeDeviceId" @change="handleChange(activeDeviceId)">
    <option :value="item.deviceId" v-for="(item,index) in deviceList" :key="index">{{ item.label }}</option>
  </select>
</template>

<script>
// 切换设备
import TRTC from "trtc-sdk-v5";
export default {
  name: "selectDiec",
  props: {
    deviceType: String,
    onChange: Function,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      deviceList: [],
      activeDevice: {},
      activeDeviceId: ""
    };
  },
  computed: {
    activeMicrophoneId() {
      return this.$store.getters.activeMicrophoneId;
    },
    activeSpeakerId() {
      return this.$store.getters.activeSpeakerId;
    },
    activeCameraId() {
      return this.$store.getters.activeCameraId;
    },
  },
  watch: {
    
  },
  methods: {
    async getDeviceList() {
      switch (this.deviceType) {
        case "camera":
          this.deviceList = await TRTC.getCameraList();
          this.activeDeviceId =
            this.activeCameraId ?? this.deviceList[0].deviceId;
          break;
        case "microphone":
          this.deviceList = await TRTC.getMicrophoneList();
          this.activeDeviceId =
            this.activeMicrophoneId ?? this.deviceList[0].deviceId;
          break;
        case "speaker":
          this.deviceList = await TRTC.getSpeakerList();
          this.activeDeviceId =
            this.activeSpeakerId ?? this.deviceList[0].deviceId;
          break;
        default:
          break;
      }
    },
    handleChange(deviceId) {
      const device = this.deviceList.find(
        (device) => device.deviceId === deviceId
      );
      switch (this.deviceType) {
        case "camera":
          this.$store.dispatch("UPDATE_ACTIVE_CAMERA", device);
          break;
        case "microphone":
          this.$store.dispatch("UPDATE_ACTIVE_MICROPHONE", device);
          break;
        case "speaker":
          this.$store.dispatch("UPDATE_ACTIVE_SPEAKER", device);
          break;
        default:
          break;
      }
    },
    async initDeviceList() {
      try{
        this.deviceList = await this.getDeviceList(this.deviceType);
          this.activeDeviceId = this.deviceList[0].activeDeviceId;
      }catch (error) {
        this.getDeviceList();
      }
    }
  },
  mounted() {
    navigator.mediaDevices.addEventListener(
      "devicechange",
      this.initDeviceList
    );
    this.getDeviceList();
  },
  beforeDestroy() {
    navigator.mediaDevices.removeEventListener(
      "devicechange",
      this.initDeviceList
    );
  }
}
</script> 

<style lang='scss' scoped>
select{
  border: 0.2px solid #438EFF;
  padding-left: 12px;
  border-radius: 4px;
  width: 100%;
  height: 48px;
  font-size: 25px;
  font-weight: 400;
  background: url('@/assets/living/Polygon 2.png');
  background-repeat: no-repeat;
  background-position: 500px center;
  background-size: 20px;
  appearance:none;
  -moz-appearance:none;
  -webkit-appearance:none;
  outline: none;
  line-height: 32px;
  color: #438EFF;
  cursor: pointer;
  &::-ms-expand{
    display: none;
  }
}
</style>
