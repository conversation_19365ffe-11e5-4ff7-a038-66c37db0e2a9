<template>
  <div class="forum_content">
    <div class="form_flex">
      <div class="form_title">
        <p>聊天</p>
      </div>
      <div class="form_cloe">
        <img src="@/assets/living/close_sm.png" alt="" @click="closeChat" />
      </div>
    </div>
    <div class="tim-container">
      <div class="content-top-chat" id="msg">
        <div class="out" v-if="messageList.length === 0">说句话吧～</div>

        <div class="single-message" v-else>
          <div
            v-for="(message, index) in messageList"
            class="message_wrap"
            :key="index"
          >
          <div
              v-if="message.type === 'tips'"
              class="text"
              :style="{
                color: message.content.includes('已关闭学生禁言') ? 'green' : ''
              }"
            >
              {{ message.content }}
            </div>
            <div v-else>
              <div class="message-info">{{ getUserNick(message) }}:</div>
              <div class="message-content">
                <div
                  v-for="(item, index) in message.renderContent"
                  :key="index"
                  style="display: inline-block"
                >
                  <span v-if="item.name === 'text'" :key="index">{{
                    item.content
                  }}</span>
                  <img
                    v-else-if="item.name === 'img'"
                    :src="item.src"
                    :key="index"
                    class="message-icon"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-bottom">
        <div class="word">
          <div class="content-bottom-feel">
            <el-popover
              placement="top"
              trigger="click"
              v-model="popoverVisible"
              popover-class="zy_popover"
            >
              <div class="emojis">
                <div
                  class="emoji"
                  v-for="emoji in emojiName"
                  :key="emoji"
                  @click="chooseEmoji(emoji)"
                >
                  <img :src="emojiUrl + emojiMap[emoji]" alt="" />
                </div>
              </div>
              <span class="icon-button" slot="reference">
                <img src="@/assets/living/表情.png" alt="" />
                <!-- <svg-icon class="emoji-icon" icon-name="emoji"></svg-icon> -->
              </span>
            </el-popover>
          </div>
          <textarea
            id="inputId"
            rows="4"
            cols="50"
            class="input"
            ref="inputRef"
            type="text"
            v-model="inputMsg"
            :placeholder="
              doneMute || doneIsAllMute
                ? '你已被禁言'
                : '请输入文字聊天(最多可输入20个字符)'
            "
            @keyup.enter="handleSendMsg"
            :disabled="doneMute || doneIsAllMute"
            required="required"
            style="resize: none"
            maxlength="20"
          ></textarea>
          <!-- <input
            class="input"
            ref="input"
            type="text"
            v-model="inputMsg"
            :placeholder="'请输入'"
            @keyup.enter="handleSendMsg"
            :disabled="isLiveEnded"
            required="required"
          /> -->
        </div>
        <div
          class="send-button"
          @click="handleSendMsg"
          :style="{
            background: doneMute || doneIsAllMute ||times ? '#979797' : '#438eff',
          }"
        >
          {{times ? times + 's后发送' :'发送'}}
          
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 聊天
import tim from "./mixins/tim.js";
import { emojiMap, emojiName, emojiUrl } from "@/utils/emojiMap";
import { LIVE_STAGE } from "./mixins/room.js";
import livePage from "@/api/living";
import { decodeText } from "@/utils/decodeText";
import { Toast } from "mint-ui";
export default {
  name: "compRoom",
  mixins: [tim],
  props: {
    group_id: String,
    doneChat:{
      type:Boolean,
      default:false}
  },
  computed: {
    doneMute() {
      return this.$store.getters.doneMute;
    },
    doneIsAllMute() {
      return this.$store.getters.doneIsAllMute;
    },
  },
  data() {
    return {
      inputMsg: "",
      popoverVisible: false,
      emojiMap,
      emojiName,
      emojiUrl,
      times:null,
      trotter:true
    };
  },
  watch: {
    messageList: {
      handler(val) {
        // 滚动到底部
        this.scrollToBottom();
      },
      deep: true,
    },
    doneIsAllMute: {
      handler(val) {
        this.changeStatus();
      },
      deep: true,
    },
    doneChat:{
      handler(val) {
       if(val){
        this.scrollToBottom();
       }
      },
      deep: true,
    }
  },
  async mounted() {
    this.initTim();
    // await this.getGroupHistoryMessageList();
    this.scrollToBottom();
  },
  methods: {
    // 获取用户昵称
    getUserNick({ nick, userID }) {
      return nick ? nick : userID;
    },
    getMessageTime({ time }) {
      let hour = new Date(time * 1000).getHours();
      let minute = new Date(time * 1000).getMinutes();
      hour = hour >= 10 ? hour.toString() : `0${hour}`;
      minute = minute >= 10 ? minute.toString() : `0${minute}`;
      return `${hour}:${minute}`;
    },
    // 发送消息
    handleSendMsg() {
      if (this.doneMute || this.doneIsAllMute) {
        Toast("你已被禁言")
        return;
      }else if(this.times || !this.trotter){
        Toast("请勿连续发送，请耐心等待");
        return;
      }else if(this.inputMsg.trim() === ""){
        Toast("请填写需要发送的内容");
        return;
      };
      this.trotter = false;
      livePage.StudentSensitive({ content: this.inputMsg.trim() }).then((res) => {
        if (res.data.code === 0) {
          this.sendMessage(res.data.data.content);
          this.inputMsg = "";
          this.popoverVisible = false;
          // 倒计时5秒
          this.countDown(5);
        } else {
          Toast(res.data.message);
        }
      }).catch((e)=>{
        this.countDown(5);
        Toast(`网络异常,返回出错${e}`);
      });
      setTimeout(()=>{
        this.trotter = true;
      },2000)
    },
    countDown(time){
      this.times = time;
      let timer = setInterval(() => {
        this.times--;
        if (this.times <= 0) {
          clearInterval(timer);
          this.times = null;
        }
      }, 1000);
      console.log(timer)
    },
    // 选择表情
    chooseEmoji(item) {
      if (this.doneMute || this.doneIsAllMute || (this.inputMsg.length + 4) >= 20) return;
      const fronText = this.inputMsg.substring(
        0,
        this.$refs.inputRef.selectionStart
      );
      const afterText = this.inputMsg.substring(
        this.$refs.inputRef.selectionEnd,
        this.inputMsg.length
      );
      let pos =  this.$refs.inputRef.selectionEnd + 4;
      this.inputMsg = fronText + item + afterText;

      this.$refs.inputRef.focus();
      setTimeout(()=>{
        this.$refs.inputRef.setSelectionRange(pos,pos)
      },200)
    },
    changeStatus() {
      this.messageList.push({
        type: "tips",
        content: !this.doneIsAllMute ? "已关闭学生禁言" : "已开启学生禁言",
        renderContent: decodeText(
          !this.doneIsAllMute ? "已关闭学生禁言" : "已开启学生禁言"
        ),
      });
    },
    // 销毁群聊
    destroyChat() {
      // this.dismissGroup(this.groupID);
      this.logout();
      // 清除对禁言用户ID的记录
      localStorage.removeItem(this.muteUserIdKey);
    },
    // 关闭聊天框
    closeChat() {
      this.$emit("closeChat");
    },
    // 滚动到最底部
    scrollToBottom() {
      try {
        this.$nextTick(() => {
          let msg = document.getElementById("msg");
          console.log(msg.scrollHeight, msg.clientHeight);
          msg.scrollTo(0, msg.scrollHeight - msg.clientHeight);
        });
      } catch (e) {
        console.log(e);
      }
    },
  },
  beforeDestroy() {
    // 销毁群聊
    this.destroyChat();
  },
};
</script>
<style lang="less" scoped>
.forum_content {
  width: 594px;
  height: 652px;
  border-radius: 24px;
  background: rgba(255, 255, 255);
  box-shadow: 0px 0px 44px 0px rgba(124, 143, 166, 0.1);
  // margin-top: 40px;
  .form_flex {
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 72px;
    line-height: 80px;
    color: #050505;
    font-size: 28px;
    font-style: normal;
    font-weight: 600;
    border-radius: 10px 10px 0 0;
    background: #FFFFFF;
    box-shadow: 0px 0px 44px 0px rgba(124, 143, 166, 0.1);
    .form_title {
      img {
        width: 36px;
        height: 22px;
      }
      p {
        margin-left: 32px;
      }
    }
    .form_cloe {
      width: 80px;
      img {
        width: 35px;
        height: 35px;
        vertical-align: middle;
        cursor: pointer;
      }
    }
  }

  .tim-container {
    width: 100%;
    height: 630px;
    display: flex;
    flex-direction: column;
    .content-top-chat::-webkit-scrollbar {
      display: none;
    }
    .content-top-chat {
      flex-grow: 1;
      // width: 480px;
      height: 725px;
      overflow: auto;
      padding: 14px 22px;

      font-size: 14px;
      box-sizing: border-box;
      .text {
        text-align: center;
        font-size: 1.2vw;
        color: red;
      }
      .out {
        color: rgba(151, 151, 151, 0.71);
        font-size: 26px;
        font-style: normal;
        font-weight: 500;
        line-height: 40px; /* 153.846% */
      }

      .single-message {
        width: 100%;
        text-align: left;
        .message_wrap {
          margin-top: 30px;
          margin-bottom: 4px;
        }
        .message-info {
          height: 30px;
          line-height: 30px;
          font-size: 26px;
          color: #050505;
          .user-name {
            padding-right: 12px;
          }
        }

        .message-content {
          font-size: 24px;
          font-weight: 400;
          word-break: break-all;
          background: #438eff;
          width: 100%;
          display: block;
          color: white;
          margin-top: 10px;
          border-radius: 10px;
          padding: 20px;
          letter-spacing: 2px;
          span {
            display: inline-block;
            vertical-align: center;
          }
          .message-icon {
            width: 30px;
            height: 30px;
            vertical-align: middle;
            margin-left: 3px;
          }
        }
      }
    }

    .content-bottom {
      width: 100%;
      height: 408px;
      background-color: #fff;
      border-radius: 0 0 10px 10px;
      // display: flex;
      // align-items: center;
      padding: 20px 26px;
      box-sizing: border-box;
      // box-shadow: 0px 0px 44px 0px rgba(124, 143, 166, 0.2);
      // border-radius: 0px 0px 21.12px 21.12px;

      div.content-bottom-feel {
        width: 40px;
        height: 40px;
        margin-bottom: 20px;
        .icon-button {
          cursor: pointer;
          img {
            width: 40px;
            height: 40px;
          }
          .emoji-icon {
            width: 40px;
            height: 40px;
          }
        }
        .smile-icon {
          display: inline-block;
          width: 40px;
          height: 40px;
        }
      }

      .word {
        // display: flex;
        // align-items: center;
        // width: 296px;
        // height: 64px;
        // border-radius: 16px;
        // opacity: 0.8;
        // background: rgba(233, 233, 233, 0.8);
        // margin-right: 20px;
      }

      .input {
        color: #110f0f;
        border-radius: 5px;
        font-size: 26px;
        top: 0;
        right: 0;
        width: 100%;
        height: 164px;
        // padding-left: 10px;
        // padding-right: 10px;
        padding: 10px;
        background: #e9e9e9;
        border-radius: 6px;
        opacity: 0.8;
        border: none;
        outline: none;
        box-sizing: border-box;
      }
      textarea::placeholder {
        color: #110f0f;
      }
      .send-button {
        // width: 98px;
        // height: 48px;
        // line-height: 48px;
        padding: 15px;
        cursor: pointer;
        color: #fff;
        font-size: 14px;
        border-radius: 6px;
        background: #438eff;
        text-align: center;
        font-size: 28.16px;
        font-style: normal;
        font-weight: 600;
        margin-top: 20px;
        display: inline-block;
        float: right;
      }
    }
  }
  .emojis {
    height: 40px;
    overflow: scroll;
    .emoji {
      height: 30px;
      width: 30px;
      float: left;
      box-sizing: border-box;
      img {
        width: 30px;
        height: 30px;
      }
    }
  }
}
</style>
<style>
::v-deep .el-popover .el-popper {
  width: 300px;
  height: 300px !important;
  padding: 5px;
  min-width: 400px;
}
</style>
