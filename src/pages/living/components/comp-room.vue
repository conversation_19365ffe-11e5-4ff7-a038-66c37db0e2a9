<template>
  <div class="rtc-container">
    <div class="rtc-control-container">
      <div
        class="button"
        type="primary"
        size="small"
        :disabled="isJoining || isJoined"
        @click="handleJoinRoom"
      >
        加入房间
      </div>
      <div class="button" type="primary" size="small" @click="handleLeave">
        离开房间
      </div>
    </div>

  </div>
</template>

<script>
import rtc from "./mixins/rtc.js";
import LibGenerateTestUserSig from "@/utils/lib-generate-test-usersig.min.js";
import config from "@/config";

export default {
  name: "compRoom",
  mixins: [rtc],
  data() {
    return {};
  },
  watch: {
    cameraId(val) {
      this.switchDevice("video", val);
    },
    microphoneId(val) {
      this.switchDevice("audio", val);
    }
  },
  methods: {
    // 点击【Join Room】按钮
    async handleJoinRoom() {
      await this.initClient();
      await this.join();
      await this.initLocalStream();
      await this.playLocalStream();
      await this.publish();
    },
    // 点击【Leave Room】按钮
    async handleLeave() {
      await this.leave();
    },
  }
};
</script>

<style lang="scss" scoped>
.rtc-container {
  .label {
    margin: 14px 0 6px;
    text-align: left;
    font-weight: bold;
  }

  .control-container {
    text-align: left;
    margin-bottom: 10px;
    div:not(:nth-last-child(1)) {
      margin-bottom: 10px;
    }
    .button:not(:first-child) {
      margin-left: 2px;
    }
  }

  .invite-link-container {
    width: 100%;
    color: #084298;
    background-color: #cfe2ff;
    position: relative;
    padding: 10px 16px;
    margin-bottom: 16px;
    border: 1px solid #b6d4fe;
    border-radius: 0.25rem;
    .invite-input {
      margin-top: 10px;
    }
    .invite-btn {
      display: flex;
      cursor: pointer;
    }
  }

  .info-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .log-container {
      flex-grow: 1;
      border: 1px solid #dddddd;
      height: 360px;
      padding: 10px;
      margin-right: 16px;
      overflow-y: scroll;
      .log-label {
        margin: 0 0 6px;
        font-weight: bold;
      }
      .log-state {
        display: inline-block;
        margin-right: 6px;
      }
      > div {
        font-size: 12px;
      }
    }
    .local-stream-container {
      width: 480px;
      height: 360px;
      position: relative;
      flex-shrink: 0;
      .local-stream-content {
        width: 100%;
        height: 100%;
      }
      .local-stream-control {
        width: 100%;
        height: 30px;
        position: absolute;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0 10px;
        .control {
          margin-left: 10px;
        }
        .icon-class {
          color: #fff;
          cursor: pointer;
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .info-container-mobile {
    display: block;
    .log-container {
      margin-right: 0;
    }
    .local-stream-container {
      width: 320px;
      height: 240px;
      margin-top: 10px;
    }
  }

  .remote-container {
    width: 100%;
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    .remote-stream-container {
      width: 320px;
      height: 240px;
      margin: 0 10px 10px 0;
    }
  }
}
</style>

<i18n>
  {
      "en": {
          "Operation": "Operation",
      "Join Room": "Join Room",
      "Publish": "Publish",
      "Unpublish": "Unpublish",
      "Leave Room": "Leave Room",
      "Start Screen Share": "Start Screen Share",
      "Stop Screen Share": "Stop Screen Share",
      "Please enter sdkAppId and secretKey": "Please enter sdkAppId and secretKey",
      "Please enter userId and roomId": "Please enter userId and roomId",
      "Please reacquire the invitation link": "Please reacquire the invitation link!"
      },
      "zh": {
          "Operation": "操作",
      "Join Room": "进入房间",
      "Publish": "发布流",
      "Unpublish": "取消发布流",
      "Leave Room": "离开房间",
      "Start Screen Share": "开始共享屏幕",
      "Stop Screen Share": "停止共享屏幕",
      "Please enter sdkAppId and secretKey": "请输入 sdkAppId 和 secretKey",
      "Please enter userId and roomId": "请输入 userId 和 roomId",
      "Please reacquire the invitation link": "请重新获取邀请链接！"
      }
  }
  </i18n>
