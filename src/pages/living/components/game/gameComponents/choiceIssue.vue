<template>
  <div class="choice-issue-list">
    <div
      v-for="(item, index) in option_list"
      :key="index"
      @click="check_answer(item)"
      :class="userAnswer == Object.keys(item)[0] ? 'choice-issue-option choice-issue-option-default choice-issue-option-true' : 'choice-issue-option choice-issue-option-default'"
    >
      <i class="option-icon"> </i>
      <div class="option-text">{{ Object.values(item)[0] }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "choice-issue",
  props: {
    issue_answer: {
      //题目答案
      type: String,
      default: "",
    },
    option_list: {
      default: "",
    },
    show_answer: {
      //是否展示答案
      type: Boolean,
    },
    is_redo: {
      type: Boolean,
    },
    user_answer: {
      default: "",
    },
    answer_result: {
      default: "",
    },
    answer_type:{
      type:Boolean,
    },
  },
  watch: {
    is_redo: {
      handler: function (data) {
        if (data === true) {
          this.$emit("reset");
        }
      },
      deep: true,
    },
    doneLastAnswer: {
      handler: function (data) {
        this.userAnswer = data;
      },
      deep: true,
    },
  },
  computed: {
    doneLastAnswer(){
      return this.$store.getters.doneLastAnswer
    }
  },
  data() {
    return {
      userAnswer:""
    };
  },
  mounted() {
    // console.log(this.option_list);
    this.userAnswer = this.doneLastAnswer;
  },
  methods: {
    check_answer: function (choice) {
      if(this.doneLastAnswer != ""){
        return;
      }
      if(!this.answer_type){
        this.userAnswer = Object.keys(choice)[0];
      }
      // if (this.answer_result === "not_answer") {
        if (choice) {
          if (Object.keys(choice)[0] === this.issue_answer) {
            this.$emit("change_answer_result", {
              user_answer: Object.keys(choice)[0],
              answer_result: "is_right",
            });
          } else {
            this.$emit("change_answer_result", {
              user_answer: Object.keys(choice)[0],
              answer_result: "is_wrong",
            });
          }
        }
      // }
    },
  },
};
</script>
<style lang="less" scoped>
.choice-issue-list {
  width: 100%;
//   height: 424px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-evenly;
  .choice-issue-option {
    line-height: 68px;
    width: 90%;
    height: 68px;
    border-radius: 44px;
    box-sizing: border-box;
    text-align: center;
    position: relative;
    margin-bottom: 10px;
    cursor: pointer;
    .option-icon {
      width: 72px;
      height: 72px;
      line-height: 72px;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      margin-top: -36px;
      left: 8px;
      font-style: normal;
      font-weight: 700;
      font-size: 20px;
      color: #3b3b3b;
      text-align: center;
    }
    .icon-default {
      background-color: #f1f1f1;
    }
    .icon-close {
      background: url("@/assets/exerciseDetail/差.png") no-repeat;
      background-size: contain;
      background-color: #ffffff;
    }
    .icon-check {
      background: url("@/assets/exerciseDetail/勾.png") no-repeat;
      background-size: contain;
      background-color: #54d773;
    }
    .option-text {
      font-size: 32px;
      color: #717883;
      text-align: center;
    }
  }
  .choice-issue-option-false {
    background-color: #ff6667;
    .option-text {
      color: #ffffff;
    }
    .option-icon {
      background: url("@/assets/exerciseDetail/差.png") no-repeat;
      background-size: contain;
      background-color: #ffffff;
    }
  }
  .choice-issue-option-true {
    background-color: #54d773 !important;
    .option-text {
      color: #ffffff !important;
    }
    .option-icon {
      // background: url("@/assets/exerciseDetail/勾.png") no-repeat;
      background-size: contain;
      background-color: #54d773;
    }
  }
  .choice-issue-option-default {
    background-color: #f7f8f8;
    .option-text {
      color: #717883;
    }
    .option-icon {
      background-color: transparent;
    }
  }
}
</style>
