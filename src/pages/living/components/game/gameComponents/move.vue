<template>
  <div class="move">
    <div id="board" class="board" @click="board_click && move_chess_pieces($event)"></div>
    <answer-right :enable_dialog="right_dialog" @close_dialog="right_dialog = false" />
    <answer-wrong :enable_dialog="wrong_dialog" @close_dialog="wrong_dialog = false" />
    <!-- <audio id="move-audio" type="audio/mp3">
      <source src="@/assets/audio/fall.mp3" />
    </audio> -->
  </div>
</template>

<script>
import tool from "../../../../../public/tool";
import answerRight from "./answerRight.vue";
import answerWrong from "./answerWrong.vue";

export default {
  name: "moveWrap",
  data() {
    return {
      player: "",
      tsumego: [],
      board_click: true,
      right_dialog: false,
      wrong_dialog: false,
      answer_sgf: "",
      prepare_mark: "",
      // move_audio: "",
    };
  },
  computed: {
    type() {
      return this.$route.query.type;
    },
  },
  components: { answerRight, answerWrong },
  props: {
    question: {
      type: Object,
    },
  },
  methods: {
    replay() {
      this.make_player();
      this.tsumego = [];
      this.board_click = true;
    },
    remove_branch_mark: function () {
      for (let index in this.mark_list) {
        this.player.board.removeObject({
          x: this.mark_list[index].x,
          y: this.mark_list[index].y,
          type: "LB",
        });
      }
    },
    add_branch_mark: function () {
      this.remove_branch_mark();
      this.mark_list = [];
      let move = {};
      let mark_numb = 0;
      if (this.tsumego.length !== 0) {
        for (let index in this.tsumego) {
          if ("B" in this.tsumego[index]) {
            move = tool.ConvertSgfToXY(this.tsumego[index].B);
            this.player.board.addObject({
              x: move.x,
              y: move.y,
              type: "LB",
              text: tool.GetAbc(mark_numb),
              c: tool.RandomColor(),
            });
            this.mark_list.push(move);
            mark_numb = mark_numb + 1;
          }
        }
      }
    },
    open_answer(new_str) {
      this.player.loadSgf(new_str, "last");
      let p = WGo.clone(this.player.kifuReader.path);
      p.m += 1000;
      this.player.goTo(p);
      this.board_click = true;
      this.tsumego = this.question.branch;
      this.add_branch_mark();
    },
    close_answer(new_str) {
      this.player.loadSgf(new_str, "last");
      let p = WGo.clone(this.player.kifuReader.path);
      p.m += 1000;
      this.player.goTo(p);
      this.board_click = false;
    },
    update_sgf(new_str) {
      this.player.loadSgf(new_str, "last");
      let p = WGo.clone(this.player.kifuReader.path);
      p.m += 1000;
      this.player.goTo(p);
      this.board_click = true;
    },
    make_player: function () {
      // 创建棋盘
      try {
        let elem = document.getElementById("board");
        this.player = new WGo.BasicPlayer(elem, {
          sgf: this.question.sgf,
          layout: {
            left: "",
            bottom: "",
          },
          board: {
            size: this.size,
            theme: {
              gridLinesColor: "#B75200",
              starColor: "#B75200",
            },
            section: {
              top: this.question.section["top"],
              right: this.question.section["right"],
              bottom: this.question.section["bottom"],
              left: this.question.section["left"],
            },
          },
          enableWheel: false,
        });
        this.player.board.removeEventListener("click", this.player.board._click);
        this.player.board.removeEventListener("click", this.player.board._ev_click);
        console.log(this.player);
        tool.MoveToLast(this.player);
        if (this.question.move_first === "white") {
          this.player.kifuReader.node.appendChild(
            new WGo.KNode({
              move: {
                pass: true,
                c: this.player.kifuReader.game.turn,
              },
            })
          );
          this.player.next(this.player.kifuReader.node.children.length - 1);
        }
      } catch (e) {
        // 出现错误进行重新绘制
        let elem = document.getElementById("board");
        this.player = new WGo.BasicPlayer(elem, {
          sgf: this.question.sgf,
          layout: {
            left: "",
            bottom: "",
          },
          board: {
            size: this.size,
            theme: {
              gridLinesColor: "#B75200",
              starColor: "#B75200",
            },
            section: {
              top: this.question.section["top"],
              right: this.question.section["right"],
              bottom: this.question.section["bottom"],
              left: this.question.section["left"],
            },
          },
          enableWheel: false,
        });
        this.player.board.removeEventListener("click", this.player.board._click);
        this.player.board.removeEventListener("click", this.player.board._ev_click);
        console.log(this.player);
        tool.MoveToLast(this.player);
        if (this.question.move_first === "white") {
          this.player.kifuReader.node.appendChild(
            new WGo.KNode({
              move: {
                pass: true,
                c: this.player.kifuReader.game.turn,
              },
            })
          );
          this.player.next(this.player.kifuReader.node.children.length - 1);
        }
      }
    },
    update_board: function (x, y) {
      this.player.kifuReader.node.appendChild(
        new WGo.KNode({
          move: {
            x: x,
            y: y,
            c: this.player.kifuReader.game.turn,
          },
        })
      );
      this.player.next(this.player.kifuReader.node.children.length - 1);
      this.answer_sgf = this.player.kifuReader.kifu.toSgf().replace(/[\r\n]/g, "");
    },
    change_answer_result(result) {
      this.$emit("change_answer_result", result);
    },
    move_chess_pieces: function (event) {
      if (this.prepare_mark != "") {
        this.player.board.removeObject(this.prepare_mark);
      }
      let coordinates = tool.ToObtainCoordinate(event, this.player);
      let x = coordinates.x;
      let y = coordinates.y;
      if (this.player.kifuReader.game.isValid(x, y)) {
        this.prepare_mark = {
          type: "outline",
          x: x,
          y: y,
          c: this.player.kifuReader.game.turn,
        };
        this.player.board.addObject(this.prepare_mark);
        this.$emit("show_confirm_button");
      } else {
        this.$emit("hide_confirm_button");
      }
    },
    confirm_move: function () {
      // this.move_audio.play();
      let move_sgf = tool.ConvertXYtoSgf(
        this.prepare_mark.x,
        this.prepare_mark.y,
        this.question.move_first === "black" ? 1 : -1
      );
      if (this.tsumego.length === 0) {
        this.tsumego = this.question.branch;
      }
      let has_move = false;
      this.update_board(this.prepare_mark.x, this.prepare_mark.y);
      for (let index in this.tsumego) {
        if (this.question.move_first === "black") {
          if (this.tsumego[index].B === move_sgf) {
            has_move = true;
            if ("W" in this.tsumego[index]) {
              if (this.tsumego[index].W != "") {
                let move_xy = tool.ConvertSgfToXY(this.tsumego[index].W);
                this.update_board(move_xy.x, move_xy.y);
              }
            }
          }
        } else if (this.question.move_first === "white") {
          if (this.tsumego[index].W === move_sgf) {
            has_move = true;
            if ("B" in this.tsumego[index]) {
              if (this.tsumego[index].B != "") {
                let move_xy = tool.ConvertSgfToXY(this.tsumego[index].B);
                this.update_board(move_xy.x, move_xy.y);
              }
            }
          }
        }
        if (has_move) {
          if ("status" in this.tsumego[index]) {
            if (this.tsumego[index].status !== "") {
              if (this.tsumego[index].status === "is_right") {
                this.$nextTick(() => {
                  this.change_answer_result({
                    answer_sgf: this.answer_sgf,
                    answer_result: "is_right",
                  });
                  this.board_click = false;
                  this.right_dialog = true;
                });
              } else if (this.tsumego[index].status === "is_wrong") {
                this.$nextTick(() => {
                  this.change_answer_result({
                    answer_sgf: this.answer_sgf,
                    answer_result: "is_wrong",
                  });
                  this.board_click = false;
                  this.wrong_dialog = true;
                });
              }
            }
          }
          if (this.tsumego[index].children && this.tsumego[index].children.length !== 0) {
            this.tsumego = this.tsumego[index].children;
          }
          return;
        }
      }
      if (has_move === false) {
        this.$nextTick(() => {
          this.board_click = false;
          this.wrong_dialog = true;
          this.change_answer_result({
            answer_sgf: this.answer_sgf,
            answer_result: "is_wrong",
          });
        });
      }
    },
  },
  mounted() {
    this.make_player();
    // this.move_audio = document.getElementById("move-audio");
    this.question.move_first = this.question.move_first || "black";
    console.log(this.question);
    // this.board_click =
    //   this.question["status"] == "not_answer" || this.type == "try_again" ? true : false;
  },
};
</script>

<style scoped lang="less">
.move {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>
