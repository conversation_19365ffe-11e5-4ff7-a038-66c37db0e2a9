<template>
  <transition name="van-fade">
    <div
      class="wrong-dialog"
      @click="$emit('close_dialog')"
      v-if="enable_dialog"
      :style="{
        background: `url(${require('@/assets/exerciseDetail/wrong.png')}) center no-repeat
      transparent`,
        'background-size': 'contain',
      }"
    ></div>
  </transition>
</template>
<script>
import tool from "../../../../../public/tool";
export default {
  name: "wrongDialog",
  props: {
    enable_dialog: {
      type: Boolean,
    },
  },
  watch: {
    enable_dialog: {
      handler: function (data) {
        if (data === true) {
          tool.Sleep(1000).then(() => {
            this.$emit("close_dialog");
          });
        }
      },
      deep: true,
    },
  },
};
</script>
<style scoped lang="less">
.wrong-dialog {
  position: absolute;
  width: 550px;
  height: 550px;
  top: 73%;
  left: 65%;
  margin-left: -375px;
  margin-top: -400px;

  z-index: 20001;
}
</style>
