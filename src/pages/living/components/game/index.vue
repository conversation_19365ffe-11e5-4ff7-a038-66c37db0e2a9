<template>
  <div class="exerciseDetail">
    <div class="content" v-if="reload">
      <div class="left">
        <move
          ref="move"
          :question="exercise_detail"
          @change_answer_result="changeAnswerResult"
          @show_confirm_button="showConfirmButton"
          v-if="exercise_detail.type === 'play_on_board'"
        ></move>
        <choice
          ref="choice"
          :question="exercise_detail"
          v-if="exercise_detail.type === 'multiple_choice'"
        ></choice>
        <culture
          ref="culture"
          :question="exercise_detail"
          v-if="exercise_detail.type === 'culture'"
        ></culture>
      </div>
      <div class="right">
        <div class="question_type_wrap">
          <div class="question_type">
            {{
              exercise_detail.type === "play_on_board"
                ? "落子题"
                : exercise_detail.type === "multiple_choice"
                ? "选择题"
                : "文化题"
            }}
          </div>
          <div class="timer">
            {{ diffTime }}
          </div>
        </div>
        <div class="question_content">
          <div class="issue-wrap">
            <div class="issue-title">{{ exercise_detail.name }}</div>
            <!-- 选择题 -->
            <choice-issue
              v-if="exercise_detail.type !== 'play_on_board'"
              :issue_answer="exercise_detail.answer"
              :option_list="exercise_detail.choices"
              :show_answer="show_answer"
              :user_answer="user_answer"
              :answer_result="answer_result"
              :answer_type="answer_type"
              :is_redo="is_redo"
              @change_answer_result="changeAnswerResult"
            ></choice-issue>
            <!-- 落子题 -->
            <div v-else>
              <p class="tips" v-if="answer_result == 'not_answer'">请在棋盘上落子</p>
              <!-- <p class="tips" v-if="answer_result === 'is_right'" style="color: #34cc67">
                答对了，你真棒！
              </p>
              <p class="tips" v-if="answer_result === 'is_wrong'" style="color: #ff5f69">
                答错了，继续加油！
              </p> -->
            </div>
          </div>
          <div class="confirm_wrap">
            <div
              class="confirm_move"
              v-if="exercise_detail.type != 'cultrue' && is_show_confirm_button"
              @click="confimMove"
              :style="{
                'background-image': `url(${require('@/assets/exerciseDetail/确认落子.png')})`,
                'background-size': '100% 100%',
              }"
            ></div>

            <!-- <div
              class="open_answer"
              @click="openAnswer()"
              v-if="
                show_answer &&
                exercise_detail.type == 'play_on_board' &&
                is_show_check_answer_button
              "
              :style="{
          'background-image': `url(${require('@/assets/exerciseDetail/查看答案.png')})`,
          'background-size': '100% 100%'
        }"
            ></div> -->
            <!-- <div
              class="close_answer"
              @click="closeAnswer"
              v-if="
                show_answer &&
                exercise_detail.type == 'play_on_board' &&
                is_show_check_answer_button == false &&
                is_show_confirm_button == false
              "
              :style="{
                'background-image': `url(${require('@/assets/exerciseDetail/关闭答案.png')})`,
                'background-size': '100% 100%'
              }"
            ></div> -->
          </div>
          <div class="line"></div>
          <div v-if="doneLastAnswer == ''">
            <div
              class="threeButton"
              v-if="exercise_detail.type === 'multiple_choice' && !answer_type"
            >
              <div
                class="redo"
                @click="handAnswer"
                :style="{
                  'background-image': `url(${require('@/assets/living/conmit.png')})`,
                  'background-size': '100% 100%',
                }"
              ></div>
            </div>
          </div>

          <!-- <div v-else class="button_wrap">
            <div
              class="next_wrap"
              :class="answer_result == 'not_answer' ? 'next_wrap_dis' : ''"
              v-if="question_id_list.length > question_index"
              @click="goNext"
            ></div>

            <div
              class="last_wrap"
              :class="answer_result == 'not_answer' ? 'last_wrap_dis' : ''"
              v-if="question_id_list.length == question_index"
            ></div>
          </div>-->
        </div>
      </div>
    </div>
    <speak ref="speak"></speak>
  </div>
</template>

<script>
// 题库
import choiceIssue from "./gameComponents/choiceIssue";
import move from "./gameComponents/move";
import choice from "./gameComponents/choice";
import culture from "@/components/question/culture";
import speak from "@/components/question/speak";
import liveApi from "@/api/living";
import { Toast } from "mint-ui";
import config from "@/config";
export default {
  data() {
    return {
      sgf: "",
      // exercise_list: {},
      question_id_list: [], //问题列表
      // question_index: 0, //第几题
      exercise_detail: {}, //当前问题详情
      answer_result: "not_answer", //答题结果
      show_answer: false, //整个答题结束 显示答案
      user_answer: "", //用户的答案
      is_redo: false, //是否重做
      // is_show_check_answer_button: true,
      is_show_confirm_button: false,
      board_click: false,
      reload: false,
      answer_sgf: "",
      status: "",
      answer_type: false,
      diffTime: "00:00:00",
      interval_unix:0
    };
  },
  components: { choiceIssue, move, choice, culture, speak },
  computed: {
    // course_id() {
    //   return this.$store.getters.doneChessBoardParam.course_id;
    // },
    lesson_id() {
      return this.$store.getters.doneChessBoardParam;
    },
    doneLastAnswer() {
      return this.$store.getters.doneLastAnswer;
    },
    doneAnswerTime() {
      return this.$store.getters.doneAnswerTime;
    },
    doneOnStage(){
      return this.$store.getters.doneOnStage;
    }
  },
  props: {},
  watch: {
    // question_index(new_str) {
    //   this.initQuestion(new_str);
    // }
    answer_result: {
      handler(newVal, oldVal) {
        if (newVal != "not_answer" && this.exercise_detail.type != "multiple_choice") {
          this.handAnswer();
        }
      },
    },
  },
  mounted() {
    // this.matchId = "15979";
    // await this.getExerciseInfo();
    this.initQuestion();
    this.startTimer();
  },
  destroyed() {
    clearInterval(this.timer);
  },
  methods: {
    initQuestion() {
      liveApi
        .QuestionBank({
          bank_type: 4,
          id: this.lesson_id,
          // question_type: this.source
        })
        .then((res) => {
          this.exercise_detail = res.data.data;
          this.sgf = this.exercise_detail["sgf"];
          this.type == "try_again" ? "not_answer" : this.exercise_detail["status"];
          this.reload = true;
        });
    },
    // getExerciseInfo() {
    //   positionApi
    //     .GetIdList({
    //       knowledge_point_category_id: this.lesson_id,
    //       source: this.source
    //     })
    //     .then(res => {
    //       this.question_id_list = res.data;
    //       // this.question_index = "27";
    //       this.question_index =this.$store.getters.doneChessBoardParam.question_index;
    //     });
    // },
    back() {
      // this.$router.push({
      //   path: "/lessonInfo",
      //   query: {
      //     lesson_id: this.lesson_id,
      //     course_id: this.course_id
      //   }
      // });
      this.$router.go(-1);
    },
    changeAnswerResult(e) {
      console.log(e, "ssss");
      if (this.exercise_detail["type"] == "play_on_board") {
        this.answer_result = e.answer_result;
        this.answer_sgf = e.answer_sgf;
        this.is_show_confirm_button = false;
        // if (this.is_show_check_answer_button == true) {
        //   this.sendAnswer("");
        // }
      } else {
        this.user_answer = e.user_answer;
        this.answer_result = e.answer_result;
        this.is_show_confirm_button = false;
        // this.exercise_detail["type"] == "culture"
        //   ? this.$refs.culture.changeResultDialog(e.answer_result)
        //   : this.$refs.choice.changeResultDialog(e.answer_result);
        // if (this.is_show_check_answer_button == true) {
        //   this.sendAnswer(e.user_answer);
        // }
      }
    },
    // sendAnswer(choice) {
    //   positionApi
    //     .PostQuestionAnswer({
    //       answer_sgf:
    //         this.exercise_detail["type"] == "play_on_board"
    //           ? this.answer_sgf
    //           : "",
    //       answer_choice: choice,
    //       status: this.answer_result,
    //       question_id: this.question_id_list[this.question_index - 1],
    //       source: this.source
    //     })
    //     .then(res => {
    //       this.exercise_detail["answer_sgf"] = this.answer_sgf;
    //       if (this.question_id_list.length == this.question_index) {
    //         // this.show_answer = true;
    //       }
    //     });
    // },
    // openAnswer() {
    //   this.exercise_detail["status"] = this.answer_result = "not_answer";
    //   this.$refs.move.open_answer(this.sgf);
    //   this.is_show_check_answer_button = false;
    // },
    // closeAnswer() {
    //   this.exercise_detail["status"] = this.answer_result = this.status;
    //   this.$refs.move.close_answer(this.exercise_detail["answer_sgf"]);
    //   this.is_show_check_answer_button = true;
    // },
    // goRedo() {
    //   this.$refs["move"]?.replay();
    //   this.exercise_detail["status"] = "not_answer";
    //   this.answer_result = "not_answer";
    //   this.user_answer = "";
    //   this.is_show_check_answer_button = null;
    //   if (this.exercise_detail["type"] != "culture") {
    //     this.$refs.move?.update_sgf(this.sgf);
    //   }
    // },
    showConfirmButton() {
      this.is_show_confirm_button = true;
    },
    confimMove() {
      this.$refs.move.confirm_move();
      this.is_show_confirm_button = false;
    },
    handAnswer() {
        clearInterval(this.timer);
      let correct = this.answer_result == "is_right" ? true : false;
      let question_type =
        this.exercise_detail.type === "play_on_board" ? "fruit" : "select";
      let answer = this.user_answer;
      let correct_answer = this.exercise_detail.answer;
      let option = this.exercise_detail.choices;
      try {
        option = option.map((item) => Object.keys(item)[0]);
      } catch (e) {
        option = [];
      }
      if (question_type === "fruit") {
        // if(answer){
        this.$emit("handAnswer", {
          id: this.lesson_id,
          correct,
          question_type,
          answer,
          correct_answer,
          option,
          interval_unix:this.interval_unix,
          student_name: config.tgStudentName,
          up_status: this.doneOnStage ? 1 : 2,
        });
        // }
      } else {
        if (answer) {
          this.answer_type = true;
          this.$emit("handAnswer", {
            id: this.lesson_id,
            correct,
            question_type,
            answer,
            correct_answer,
            option,
            interval_unix:this.interval_unix,
            student_name: config.tgStudentName,
          up_status: this.doneOnStage ? 1 : 2,
          });
          this.exercise_detail["type"] == "culture"
            ? this.$refs.culture.changeResultDialog(this.answer_result)
            : this.$refs.choice.changeResultDialog(this.answer_result);
        } else {
          Toast("请先选择答案！");
        }
      }
    },
    startTimer() {
      let remainingSeconds = this.doneAnswerTime % 3600;
      let seconds = this.doneAnswerTime != 0 ? remainingSeconds % 60 : 0;
      let minutes =
        this.doneAnswerTime != 0 ? Math.floor(remainingSeconds / 60) : 0;
      let hours =
        this.doneAnswerTime != 0
          ? Math.floor(this.doneAnswerTime / 3600)
          : 0;
      if(!this.doneLastAnswer){
        this.timer = setInterval(() => {
        seconds++;
        if (seconds === 60) {
          seconds = 0;
          minutes++;
        }
        if (minutes === 60) {
          minutes = 0;
          hours++;
        }
        // 格式化时间
        const formattedTime = `${hours
          .toString()
          .padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        this.interval_unix = this.convertTimeToSeconds(hours, minutes, seconds);
        this.diffTime = formattedTime;
      }, 1000);
      seconds++;
      if (seconds === 60) {
        seconds = 0;
        minutes++;
      }
      if (minutes === 60) {
        minutes = 0;
        hours++;
      }
      }

     
      // 格式化时间
      const formattedTime = `${hours
        .toString()
        .padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
      // 更新计时器显示
      this.diffTime = formattedTime;
    },
    convertTimeToSeconds(hours, minutes, seconds) {
      return hours * 3600 + minutes * 60 + seconds;
    },
  },
};
</script>
<style scoped lang="less">
.exerciseDetail {
  width: 80%;
  height: 650px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  .content {
    width: 65%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .left {
      width: 65%;
      height: 100%;
      background: #f7a448;
      border: 16px solid #fee194;
      box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.1);
      border-radius: 40px;
      position: relative;
      box-sizing: border-box;
    }
    .right {
      width: 35%;
      height: 100%;
      background-color: greenyellow;
      margin-left: 16px;
      border-radius: 40px;
      background-image: linear-gradient(180deg, #5296f7 0%, #2e79ff 10%, #2e79ff 100%);
      box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.1), inset 0 -16px 0 0 #0062e1;

      .question_type_wrap {
        width: 100%;
        height: 80px;
        margin: 20px 20px 0 20px;
        display: flex;
        // justify-content: space-between;
        align-items: center;
        .timer {
          margin-left: 17px;
          font-size: 30px;
          color: #ffffff;
        }
        .question_type {
          // width: 372px;
          height: 60px;
          line-height: 60px;
          background: rgba(49, 77, 189, 0.5);
          border: 2px solid rgba(49, 77, 189, 0.3);
          box-shadow: inset 0 0 12px 0 #314dbd;
          border-radius: 50px;
          font-size: 20px;
          color: #ffffff;
          text-align: center;
          padding: 0 25px;
          box-sizing: border-box;
        }
        .back {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          box-shadow: 0 4px 8px 0 rgba(0, 99, 255, 0.5);
          cursor: pointer;
        }
      }
      .question_content {
        width: 100%;
        height: calc(100% - 110px);
        background: #ffffff;
        box-shadow: inset 0 -16px 20px 0 #e1f1fc;
        border-radius: 40px;
        text-align: center;
        .voice {
          width: 88px;
          height: 88px;
          margin-top: 32px;
          cursor: pointer;
        }
        .issue-wrap {
          width: 100%;
          height: 50%;
          padding: 20px;
          // margin: 32px auto;
          .issue-title {
            padding-top: 20px;
            width: 90%;
            font-size: 30px;
            // height: 56px;
            color: #1f242e;
            text-align: justify;
            margin-bottom: 20px;
            overflow: hidden;
            text-overflow: ellipsis; /* 超出部分省略号 */
            word-break: break-all; /* break-all(允许在单词内换行。) */
            display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
            -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
            -webkit-line-clamp: 2;
          }
          .tips {
            font-size: 32px;
            color: #1eb4ff;
            text-align: left;
          }
        }
        .confirm_wrap {
          width: 100%;
          height: 120px;
          margin: 0 auto;
          .confirm_move {
            width: 80%;
            height: 102px;
            cursor: pointer;
            margin: 0 auto;
          }
          .open_answer {
            width: 100%;
            height: 132px;
            cursor: pointer;
          }

          .close_answer {
            width: 100%;
            height: 132px;
            cursor: pointer;
          }
        }

        .line {
          width: 100%;
          border-top: 1px dashed #c9c9c9;
          margin: 20px auto;
        }
        .threeButton {
          width: 90%;
          height: 60px;
          margin: 0 auto;
          display: flex;
          justify-content: space-between;
          .prev {
            width: 132px;
            height: 132px;
            background: url("@/assets/exerciseDetail/上一题小.png");
            background-size: cover;
            cursor: pointer;
          }
          .prev_dis {
            width: 132px;
            height: 132px;
            background: url("@/assets/exerciseDetail/上一题小灰.png");
            background-size: cover;
            pointer-events: none;
          }
          .redo {
            width: 100%;
            // height: 132px;
            // background: url("@/assets/exerciseDetail/重做中.png");
            background-size: 100% 100%;
            cursor: pointer;
          }
          .next {
            width: 132px;
            height: 132px;
            background: url("@/assets/exerciseDetail/下一题小.png");
            background-size: cover;
            cursor: pointer;
          }
        }
        // .button_wrap {
        //   width: 560px;
        //   height: 120px;
        //   margin: 0 auto;
        //   .next_wrap {
        //     width: 560px;
        //     height: 120px;
        //     background: url("@/assets/exerciseDetail/下一题大.png");
        //     background-size: cover;
        //     margin: 0 auto;
        //     cursor: pointer;
        //   }
        //   .next_wrap_dis {
        //     background: url("@/assets/exerciseDetail/下一题大灰.png");
        //     background-size: cover;

        //     pointer-events: none;
        //   }
        //   .last_wrap {
        //     width: 560px;
        //     height: 120px;
        //     background: url("@/assets/exerciseDetail/提交作业.png");
        //     background-size: cover;
        //     margin: 0 auto;
        //     cursor: pointer;
        //   }
        //   .last_wrap_dis {
        //     background: url("@/assets/exerciseDetail/提交作业灰.png");
        //     background-size: cover;
        //     pointer-events: none;
        //   }
        // }
      }
    }
  }
}
</style>
