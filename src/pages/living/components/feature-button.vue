<template>
  <div class="feature_content" v-preventReClick="getRaise">
    <img
      :src="
        require(doneRaiseHand
          ? '@/assets/living/jushou1.png'
          : '@/assets/living/jushou2.png')
      "
      alt=""
    />
  </div>
</template>
<script>
// 举手
export default {
  data() {
    return {};
  },
  watch:{
    doneRaiseHand: {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal);
        if (newVal) {
          // 三秒后关闭举手状态
          setTimeout(() => {
            this.$bus.emit("cancelRaiseHand");
            this.$store.dispatch("UPDATE_RAISE_HAND_STATUS", false);
          }, 3000);
        }
      },
      deep:true,
      immediate: true,
    },
  },
  computed: {
    doneRaiseHand() {
      return this.$store.getters.doneRaiseHand;
    },
  },
  mounted() {},
  beforeCreate() {},
  methods: {
    getRaise() {
      // 推送举手消息
      if (this.doneRaiseHand) {
        return;
      }
      this.$store.dispatch("UPDATE_RAISE_HAND_STATUS", true);
      this.$emit("raiseHand");
    },
  },
};
</script>
<style lang="less" scoped>
.feature_content {
  // display: flex;
  // justify-content: flex-end;
  /* align-items: center; */
  border-radius: 50%;
  cursor: pointer;
  // flex-direction: column;
  // height: 16.765625vw;
  position: absolute;
  bottom:100px;
  img {
    width: 120px;
    height: 120px;
  }
}
</style>
