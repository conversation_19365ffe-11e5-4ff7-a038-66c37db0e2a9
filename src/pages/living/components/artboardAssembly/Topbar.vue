<template>
  <div>
    <v-toolbar dense floating>
      <v-btn icon tile class="mr-2" style="width: 12px" @click="show = !show">
        <v-icon>
          {{ show ? "mdi-chevron-right" : "mdi-chevron-left" }}
        </v-icon>
      </v-btn>
      <!-- transition="slide-x-transition" -->
      <template v-if="show">
        <span class="mr-3">房间号: {{ $store.state.classInfo?.classId }}</span>
        <v-divider vertical inset></v-divider>
        <span class="mx-3"
          >IM群组ID: {{ $store.state.classInfo?.imGroupId }}</span
        >
        <v-divider vertical inset></v-divider>
        <span class="mx-3">用户ID: {{ $store.state.classInfo?.userId }}</span>
        <v-divider vertical inset></v-divider>
        <span class="mx-3">
          SDK版本号：{{ window.teduBoard && window.teduBoard.version }}
        </span>
        <v-divider vertical inset></v-divider>
        <span class="mx-3">
          当前文件：{{ $store.state.current?.fileInfo?.title }}
        </span>
      </template>
      <v-divider vertical inset></v-divider>
      <v-tooltip bottom>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            tile
            icon
            @click.stop="openInfoDialog()"
          >
            <v-icon dense color="deep-purple darken-1"
              >mdi-presentation-play</v-icon
            >
          </v-btn>
        </template>
        <span>功能演示</span>
      </v-tooltip>
      <v-divider vertical inset></v-divider>
      <v-tooltip bottom>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="quit()">
            <v-icon dense color="error">mdi-power</v-icon>
          </v-btn>
        </template>
        <span>退出</span>
      </v-tooltip>
    </v-toolbar>
    <v-dialog origin="right top" v-model="documentShow" max-width="720">
      <v-card>
        <v-card-title class="text-h5"> 功能演示 </v-card-title>

        <v-card-text>
          <Document />
        </v-card-text>

        <v-card-actions>
          <v-btn color="primary" @click="closeInfoDialog()" outlined block>
            我已知晓，不再显示（如需要可点击右上角<v-icon
              dense
              color="deep-purple darken-1"
              >mdi-presentation-play</v-icon
            >查看）
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import Document from "./Document.vue";
export default {
  name: "Topbar",
  components: {
    Document
  },
  props: {
    quitClassroom: Function
  },
  data() {
    return {
      window,
      show: true,
      documentShow: false
    };
  },
  mounted() {
    setTimeout(() => {
      // this.show = false;
      const hasShow = localStorage.getItem(`tiw_${TEduBoard.getVersion()}`);
      console.log("hasShow", hasShow);
      if (!hasShow) {
        this.documentShow = true;
      }
    }, 2500);
  },
  methods: {
    ...mapActions(["setSignalReady"]),
    async quit() {
      this.quitClassroom(() => {
        this.setSignalReady(false);
        this.$router.push("/login");
      });
    },

    async openInfoDialog() {
      this.documentShow = true;
    },

    async closeInfoDialog() {
      this.documentShow = false;
      localStorage.setItem(`tiw_${TEduBoard.getVersion()}`, 1);
    }
  }
};
</script>

<style scoped></style>
