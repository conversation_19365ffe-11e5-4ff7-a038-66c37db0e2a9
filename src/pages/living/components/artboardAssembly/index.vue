<template>
  <div class="drawing_wrap" data-app>
    <!-- v-if="liveStage !== 'not_started'" -->
    <sketch></sketch>
    <!-- <toolbar class="toolbar-style" v-if="doneDrawingAuthority"></toolbar> -->
    <!-- <topbar class="topbar-style" :quitClassroom="quitClassroom"></topbar> -->
    <!-- <bottombar class="bottombar-style" v-if="doneDrawingAuthority"></bottombar> -->
    <rightbar class="rightbar-style"  v-if="doneDrawingAuthority"></rightbar>
    <!-- <element-toolbar></element-toolbar> -->
  </div>
</template>
<script>
import Sketch from "./Sketch";
// import Toolbar from "./Toolbar";
// import Topbar from "./Topbar"
import bottombar from "./Bottombar";
import Rightbar from "./Rightbar";
// import ElementToolbar from "./ElementToolbar";
import Util from "./util/Util";
import Signal from "../mixins/Signal";
import { mapActions, mapState } from "vuex";
// import config from "@/config";

export default {
  data() {
    return {};
  },
  mixins: [Signal],
  components: {
    Sketch,
    // Toolbar,
    // Topbar,
    bottombar,
    Rightbar
    // ElementToolbar
  },

  computed: {
    ...mapState(["classInfo"]),
    liveStage() {
      return this.$store.getters.doneLiveStage;
    },
    doneDrawingAuthority() {
      return this.$store.getters.doneDrawingAuthority;
    }
  },
  mounted() {
    this.joinClassroom();
  },
  beforeCreate() {},
  methods: {
    ...mapActions(["setSignalReady"]),

    async joinClassroom() {
      this.setSignalReady(true);
      console.log("-------")
      // 1. 准备好信令通道
      let error;
      // 2. 登录
      [error] = await Util.awaitWrap(this.joinSignal());
      console.log(error,'===========error')
      if (error) {
        console.log(">>>>>>>>>>>11");
        // this.$toasted.error(error);
        console.error(
          error.code,
          error.message,
          "TIM的错误码文档: https://cloud.tencent.com/document/product/269/1671"
        );
        this.setSignalReady(false);
        // this.$router.push("/login");
      } else {
        // 3. 加入课堂
        [error] = await Util.awaitWrap(this.createAndJoinSignal());
        if (error) {
          console.log(">>>>>>>>>>221");
          // this.$toasted.error(error.message);
          console.error(
            error.code,
            error.message,
            "TIM的错误码文档: https://cloud.tencent.com/document/product/269/1671"
          );
          this.setSignalReady(false);
          //   this.$router.push("/login");
        } else {
          console.log(">>>>>>>>>>>>>>>22");
          this.setSignalReady(true);
        }
      }
    },

    async quitClassroom(cb = null) {
      // 1. 准备好信令通道
      const [error] = await Util.awaitWrap(this.quitSignal());
      if (error) {
        // this.$toasted.error(error.message);
        console.error(
          error.code,
          error.message,
          // "TIM的错误码文档: https://cloud.tencent.com/document/product/269/1671"
        );
      }
      cb && cb();
    }
  }
};
</script>
<style lang="less" scoped>
.drawing_wrap {
  width: 100%;
  height: 100%;
  // position: absolute;
  z-index: 999999;
}
.bottombar-style {
  transform-origin: left;
  transform: scale(0.8);
  position: fixed;
  left: 8px;
  bottom: 4px;
}

.toolbar-style {
  transform: translateY(-50%) scale(0.8);
  position: fixed;
  left: 0;
  top: 50%;
}

.topbar-style {
  transform-origin: right;
  transform: scale(0.8);
  position: fixed;
  right: 8px;
  top: 4px;
}
::v-deep .v-list{
  width: 100px !important;
  padding:20px 0 !important; 
}
::v-deep .v-size--default {
  height: 48px !important;
  width: 68px !important;
  // margin-right: 50px;
}
::v-deep .v-tooltip__content{
  font-size: 24px !important;
  line-height: 32px !important;
  // padding: 5px 16px !important;
}
::v-deep .bottombar-style{
  height: 80px !important;
  .v-toolbar__extension{
    padding: 4px 16px;
  }
  .v-toolbar__content{
    height: 80px !important;
    padding: 4px 16px !important;
    font-size: 30px;
    
  }
} 
::v-deep .v-icon{
  width: 48px !important;
  height: 48px !important;
  font-size: 50px !important;
}
::v-deep .v-list--nav {
    padding-left: 8px !important;
    padding-right: 8px !important;
}
::v-deep .v-list-item {
    padding: 0 8px !important;
}
::v-deep .v-list-item {
    min-height: 40px !important;
}
::v-deep .v-list-item--dense:not(:last-child):not(:only-child) {
    margin-bottom: 4px !important;
}
::v-deep .v-menu__content{
  // top: 859px !important;
  left: 100px !important;
  .v-list{
    font-size: 25px !important;
    width: 400px !important;
    line-height: 80px !important;
  }
}
::v-deep .v-slider__thumb{
  width: 48px !important;
  height: 48px !important;
}
::v-deep .v-slider__thumb:after{
  width: 42px !important;
  height: 42px !important;
}
::v-deep .tiw-sketch__canvas{
  width: 364px !important;
  height: 206px !important;
}
</style>
