<template>
  <div class="sketch__wrap">
    <div id="sketch"></div>
    <div class="sketch__load" v-if="loading">
        <img src="@/assets/living/bordLoad.png" alt="">
      </div>
    <div
      class="refe_wrap"
      @click="refesh"
      v-if="refeshStuts"
    >
      <img
        class="refe"
        src="@/assets/living/refs.png"
      />
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import config from "@/config";
import { Toast } from "mint-ui";
export default {
  name: "Sketch",
  data() {
    return {
      teduBoard: null,
      elementOperationAuthority: {},
      refeshStuts: false,
      loading:false
    };
  },
  computed: {
    isSignalReady() {
      return this.$store.getters.doneGetIsSignalReady;
    },
    doneDrawingAuthority() {
      return this.$store.getters.doneDrawingAuthority;
    },
    doneBoardSize() {
      return this.$store.getters.doneBoardSize
    },
    doneTeacherId(){
      return this.$store.getters.doneTeacherId
    }
  },
  watch: {
    doneDrawingAuthority: {
      handler: function (value) {
        this.frameUserSendPermit(value ? 1 : 0);
      },
      deep: true,
    },
    doneBoardSize: {
      handler: function (value) {
        if(value){
          this.teduBoard.setBoardRatio(value);
        }
      },
      deep: true,
    },
    // isSignalReady: {
    //   handler: function (value) {
    //     console.log(value,"asd====")
    //     if (value) {
    //       console.log("----------->>>>>>>>>", value);
    //       this.initBoard();
    //       this.initEvent();
    //     }
    //   },
    //   deep: true
    // }
      // "$store.state.isSignalReady"(value) {
      //   if (value) {
      //     // 信令通道准备好了后，则开始初始化白板
      //     console.log(
      //       "value1------------------1111-------------------1111------------"
      //     );
      //     this.initBoard();
      //     this.initEvent();
      //   }
      // }
  },

  mounted() {
    this.frameUserHandler()
    console.log("----------->>>>>>>>>");
    setTimeout(() => {
      this.initBoard();
      this.initEvent();
    }, 500);

    this.$bus.on("tiw-recv-sync-data", this.addSyncData);
    this.elementOperationAuthority = JSON.parse(
      localStorage.getItem("elementOperationAuthority")
    );
  },

  methods: {
    ...mapActions(["setCurrentFile", "updateBoardSetting"]),
    frameUserHandler() {
      window.addEventListener("message", (e) => {
        console.log(e.data);
        if (typeof e.data === "string" && e.data === "ready") {
          const doms = document.getElementsByClassName("tiw-board-iframe-el");
          const send = doms[doms.length - 1].contentWindow;
          console.log("board logger:", "permit", this.doneDrawingAuthority);
          send.postMessage(
            JSON.stringify({
              from: "higo_living",
              init: true,
              role: "user_" + config.userName,
              permit: this.doneDrawingAuthority ? 1 : 0,
              liveToken: this.$storage.$getStroage("liveToken") || "",
              baseUrl: config.BaseLiveUrl,
              roomId: Number(config.roomId),
              horizontal: false,
            }),
            "*"
          );
        }
        if (typeof e.data === "string" && e.data === "load") {
          this.loading = false;
        }
      });
    },
    frameUserSendPermit(permit) {
      const doms = document.getElementsByClassName("tiw-board-iframe-el");
      const send = doms[doms.length - 1].contentWindow;
      send.postMessage(
        JSON.stringify({ from: "higo_living", permit: permit }),
        "*"
      );
    },
    initBoard() {
      const sketchDom = document.getElementById("sketch");
      console.log(sketchDom.offsetWidth + ":" + sketchDom.offsetHeight , this.doneBoardSize)
      this.destroyBoard();
      // const { classInfo } = this.$store.state;
      const initParams = {
        id: "sketch",
        sdkAppId: config.sdkAppId,
        userId: config.userId,
        userSig: config.userSig,
        classId: config.roomId,
        toolType: 0,
        config: {
          ratio:this.doneBoardSize ??  sketchDom.offsetWidth + ":" + sketchDom.offsetHeight,
          h5PPTLoadTimeout:1,
          h5PPTResourceTimeout:1,
          // h5PPTDownGradeTimeoutTimes: classInfo.dowmGradeTimes
        },

        userConfig: {
          nickname: config.userName,
        },

        styleConfig: {
          brushThin: 50,
          selectBoxColor: "#888",
          selectAnchorColor: "#888",
          scrollbarThumbColor: sessionStorage.getItem(
            "boardScrollbarThumbColor"
          ),
          scrollbarTrackColor: sessionStorage.getItem(
            "boardScrollbarTrackColor"
          ),
          globalBackgroundColor: "rgba(00, 00, 00, 0)",
        },

        authConfig: {
          mathGraphEnable: true,
          formulaEnable: true,
          elementOperationAuthority: this.elementOperationAuthority,
          dataSyncEnable: true,
          isAutoHideRemoteCursor: true,
          showCursorOnTouch: true,
        },
      };
      console.log("=== Board init parameters:", initParams);
      this.teduBoard = new TEduBoard(initParams);
      this.loading = true;
      this.teduBoard.addBackupDomain("https://tg-live-cdn.estar-go.com","https://tg-live-cdn2.estar-go.com");
      // this.teduBoard.showVideoControl(false);
      // 定义橡皮擦可擦除类型
      this.teduBoard.setEraseLayerType(
       [0,1,2]
      );
      this.teduBoard.setMouseToolBehavior({
        turnPage: {
          whiteBoard: false, // 普通白板点击可翻页
          h5PPT: false, // 动态ppt转码的文件点击可翻页
          imgPPT: false, // 静态ppt转码文件点击不可翻页
          imgFile: false, // 图片文件（addImagesFile接口添加的文件）点击不可翻页
          audiovisual: false, // 音视频播放控制,默认为false（1、h5PPT：true-->audiovisual:true；2、h5PPT: false && audiovisual：true--> audiovisual：true; 3、h5PPT: false && audiovisual：false--> audiovisual：false; ）
        }
      })
      // 禁用学生视频权限
      this.teduBoard.enablePermissionChecker(['File::Update::Video'], ['operator/']);
      // this.teduBoard.enablePermissionChecker(['File::Update::Video'], ['operator/']);
      // this.teduBoard.setBackgroundColor("rgba(00, 00, 00, 0)");
      // 设置橡皮擦自定义图标
      this.teduBoard.setCursorIcon(
        TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ERASER,
        {
          cursor: "url",
          url: "http://test-1259648581.file.myqcloud.com/image/eraser_32.svg",
          offsetX: 16,
          offsetY: 16,
        }
      );

      // 设置画笔自定义图标
      this.teduBoard.setCursorIcon(
        TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN,
        {
          cursor: "url",
          url: "https://demo.qcloudtiw.com/web/latest/lead-pencil.svg",
          offsetX: 2,
          offsetY: 10,
        }
      );
      // 设置激光笔自定义图标
      this.teduBoard.setCursorIcon(
        TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LASER,
        {
          cursor: "url",
          url: require("@/assets/living/wecom-temp-805-18a1982bc4360137c1f36d5b2e79d379.png"),
          offsetX: 0,
          offsetY: 0,
        }
      );
      window.teduBoard = this.teduBoard;
    },

    initEvent() {
      // 监听错误事件
      this.teduBoard.on(
        TEduBoard.EVENT.TEB_ERROR,
        (errorCode, errorMessage) => {
          console.log(
            "======================:  ",
            "TEB_ERROR",
            " errorCode:",
            errorCode,
            " errorMessage:",
            errorMessage
          );
          let message = "";
          switch (errorCode) {
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_INIT:
              message = "初始化失败，请重试";
              break;
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_AUTH:
              message = "服务鉴权失败，请先购买服务";
              break;
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_LOAD:
              message = "加载失败，请重试";
              break;
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_HISTORYDATA:
              message = "同步历史数据失败，请重试";
              break;
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_RUNTIME:
              message = "网络错误error（1）";
              break;
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_ERROR_AUTH_TIMEOUT:
              message = "服务鉴权超时，请重试";
              break;
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_MAX_BOARD_LIMITED:
              message = "单课堂内白板页数已经到达上限";
              break;
            case TEduBoard.TEduBoardErrorCode.TEDU_BOARD_SIGNATURE_EXPIRED:
              message =
                "userSig过期了，请重新生成新的userSig，再重新初始化白板";
              break;
          }
          console.log(message);
          this.$toasted.error(message, {
            duration: 5000,
          });
          this.$store.commit("  ", false);
          this.loading = false;
          Toast({message:"网络异常,正在重新加载（数据加载出现问题）",duration: 3000});
          this.teduBoard.refesh();
          // this.$router.push("/login");
        }
      );

      // 监听警告事件
      this.teduBoard.on(
        TEduBoard.EVENT.TEB_WARNING,
        (warnCode, warnMessage) => {
          console.warn(
            "======================:  ",
            "TEB_WARNING",
            " warnCode:",
            warnCode,
            " warnMessage:",
            warnMessage
          );

          let message = "";
          switch (warnCode) {
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_SYNC_DATA_PARSE_FAILED:
              message = "实时数据格式错误，请检查白板信令是否有进行二次包装";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_H5PPT_ALREADY_EXISTS:
              message = "重复添加文件";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WANNING_ILLEGAL_OPERATION:
              message = "非法操作，请在历史数据完成回调后再调用sdk相关接口";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_H5FILE_ALREADY_EXISTS:
              message = "重复添加文件";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_VIDEO_ALREADY_EXISTS:
              message = "重复添加文件";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_IMAGESFILE_ALREADY_EXISTS:
              message = "重复添加文件";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_GRAFFITI_LOST:
              message = "涂鸦丢失";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_CUSTOM_GRAPH_URL_NON_EXISTS:
              message = "自定义图形url为空";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_IMAGESFILE_TOO_LARGE:
              message = "图片组超大";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_IMAGE_COURSEWARE_ALREADY_EXISTS:
              message = "重复添加文件";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_IMAGE_MEDIA_BITRATE_TOO_LARGE:
              message =
                "多媒体资源码率大于2048kb/s，网络不好情况下容易造成卡顿，建议对视频码率进行压缩";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_IMAGE_WATERMARK_ALREADY_EXISTS:
              message = "已经存在图片水印，不能重复添加";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_FORMULA_LIB_NOT_LOADED:
              message = "数学公式库没有重新加载";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_ILLEGAL_FORMULA_EXPRESSION:
              message = "非法的数学公式";
              break;
            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_TEXT_WATERMARK_ALREADY_EXISTS:
              message = "已经存在文本水印，不能重复添加";
              break;

            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_EXPORTIMPORT_FILTERRULE_ILLEGAL:
              message = "已经存在文本水印，不能重复添加";
              break;

            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_ELEMENTTYPE_NOT_EXISTS:
              message = "元素类型不存在";
              break;

            case TEduBoard.TEduBoardWarningCode
              .TEDU_BOARD_WARNING_ELEMENTID_NOT_EXISTS:
              message = "元素ID不存在";
              break;
          }
          if(warnCode !=1) this.showErrorTip(`网络波动较大，请更换网络环境后重新进入直播间(${warnCode})`);
          console.warn(message);
        }
      );
        this.teduBoard.on(TEduBoard.EVENT.TEB_BOARD_PERMISSION_CHANGED, (permissions, filters) => {
          console.log(
            "======================:  TEB_BOARD_PERMISSION_CHANGED",
            " permissions:",
            permissions,
            " filters:",
            filters
          );
});

      this.teduBoard.on(TEduBoard.EVENT.TEB_INIT, () => {
        // 设置背景色为透明
        this.teduBoard.setBackgroundColor("rgba(00, 00, 00, 0)");
      });

      // 白板历史数据同步完成回调
      this.teduBoard.on(TEduBoard.EVENT.TEB_HISTROYDATA_SYNCCOMPLETED, () => {
        console.log(
          "======================:  ",
          "TEB_HISTROYDATA_SYNCCOMPLETED"
        );
        this.setCurrentFile(
          this.teduBoard.getFileInfo(this.teduBoard.getCurrentFile())
        );
        // 设置开启笔锋
        this.teduBoard.setHandwritingEnable(true);
        // 设置开启点击擦除
        this.teduBoard.setPiecewiseErasureEnable(true);

        // 白板已经ready了
        this.$store.commit("setTiwReady", true);
        this.loading = false;
      });

      this.teduBoard.on(
        TEduBoard.EVENT.TEB_H5PPT_DOWN_GRADE,
        (fid, slideIndex) => {
          console.log(
            "======================:  TEB_H5PPT_DOWN_GRADE",
            " fid:",
            fid,
            " slideIndex:",
            slideIndex
          );
          this.teduBoard.setDownGradeEnable(fid, slideIndex, true);
        }
      );

      this.teduBoard.on(TEduBoard.EVENT.TEB_SYNCDATA, (data) => {
          this.$bus.emit("tiw-send-sync-data", data);
      });

      // 切换文件回调
      this.teduBoard.on(TEduBoard.EVENT.TEB_SWITCHFILE, (fid) => {
        this.setCurrentFile(this.teduBoard.getFileInfo(fid));
        this.loading = false;
      });

      // 跳转白板页回调
      this.teduBoard.on(TEduBoard.EVENT.TEB_GOTOBOARD, (boardId, fid) => {
        this.setCurrentFile(this.teduBoard.getFileInfo(fid));
        this.loading = false;
      });
      this.teduBoard.on(TEduBoard.EVENT.TEB_DELETEBOARD,(fid)=>{
        this.loading = false;
      })
      // 缩放白板页回调
      this.teduBoard.on(
        TEduBoard.EVENT.TEB_ZOOM_DRAG_STATUS,
        ({ boardId, scale }) => {
          console.log({ boardId, scale });
          this.setCurrentFile(this.teduBoard.getFileInfo());
        }
      );

      // 监听视频播放状态
      this.teduBoard.on(TEduBoard.EVENT.TEB_VIDEO_STATUS_CHANGED, (data) => {
        if (
          data.status ===
          TEduBoard.TEduBoardVideoStatus.TEDU_BOARD_VIDEO_STATUS_ERROR
        ) {
          this.showErrorTip("视频播放/加载失败");
          // 刷新白板
          this.teduBoard.refresh();
          console.error("视频播放/加载失败");
        }
        this.loading = false;
      });
      // 刷新白板回调
      this.teduBoard.on(TEduBoard.EVENT.TEB_REFRESH, () => {
        this.loading = true;
        this.teduBoard.syncAndReload();
      })
      // 转码进度
      this.teduBoard.on(TEduBoard.EVENT.TEB_TRANSCODEPROGRESS, (res) => {
        console.log("转码进度", res);
        if (res.code) {
          this.showErrorTip(`转码失败code:${res.code} message:${res.message}`);
        } else {
          const { status } = res;
          if (
            status ===
            TEduBoard.TEduBoardTranscodeFileStatus
              .TEDU_BOARD_TRANSCODEFILE_STATUS_ERROR
          ) {
            this.showErrorTip("转码失败");
          } else if (
            status ===
            TEduBoard.TEduBoardTranscodeFileStatus
              .TEDU_BOARD_TRANSCODEFILE_STATUS_UPLOADING
          ) {
            this.showTip(`上传中，当前进度:${parseInt(res.progress)}%`);
          } else if (
            status ===
            TEduBoard.TEduBoardTranscodeFileStatus
              .TEDU_BOARD_TRANSCODEFILE_STATUS_CREATED
          ) {
            this.showTip("创建转码任务");
          } else if (
            status ===
            TEduBoard.TEduBoardTranscodeFileStatus
              .TEDU_BOARD_TRANSCODEFILE_STATUS_QUEUED
          ) {
            this.showTip("正在排队等待转码");
          } else if (
            status ===
            TEduBoard.TEduBoardTranscodeFileStatus
              .TEDU_BOARD_TRANSCODEFILE_STATUS_PROCESSING
          ) {
            this.showTip(`转码中，当前进度:${res.progress}%`);
          } else if (
            status ===
            TEduBoard.TEduBoardTranscodeFileStatus
              .TEDU_BOARD_TRANSCODEFILE_STATUS_FINISHED
          ) {
            this.showTip("转码完成");
            const config = {
              url: res.resultUrl,
              title: res.title,
              pages: res.pages,
              resolution: res.resolution,
            };
            this.teduBoard.addTranscodeFile(config);
          }
        }
      });

      // 操作权限
      this.teduBoard.on(TEduBoard.EVENT.TEB_BOARD_PERMISSION_DENIED, () => {
        this.showErrorTip("无操作权限");
      });

      // 调用importInLocalMode接口导入数据完成后的回调
      this.teduBoard.on(
        TEduBoard.EVENT.TEB_BOARD_IMPORTINLOCALMODE_COMPLETED,
        (code) => {
          if (code === 0) {
            this.showTip("导入数据成功");
          } else {
            this.showErrorTip("导入数据失败");
          }
        }
      );

      // 监听截图事件，image为截图内容的base64数据
      this.teduBoard.on(TEduBoard.EVENT.TEB_SNAPSHOT, ({ image }) => {
        const downloadEl = document.createElement("a");
        const event = new MouseEvent("click");
        downloadEl.download = Date.now() + ".png";
        downloadEl.href = image;
        downloadEl.dispatchEvent(event);
      });
      // 白板缩放回调事件
      this.teduBoard.on(TEduBoard.EVENT.TEB_BOARD_SCALE_CHANGE, (data) => {
        console.log(
          "======================:  TEB_BOARD_SCALE_CHANGE",
          " boardId:",
          data.boardId,
          " scale:",
          data.scale,
          "xOffset:",
          data.xOffset,
          "yOffset:",
          data.yOffset
        );
      });
      // 绘制动作切换事件
      this.teduBoard.on(TEduBoard.EVENT.TEB_DRAW_STATUS_CHANGED, (code) => {
        console.log("======================: TEB_DRAW_STATUS_CHANGED", code);
      });
      // 添加课件事件
      this.teduBoard.on(TEduBoard.EVENT.TEB_ADDTRANSCODEFILE, (fid) => {
        console.log(
          "======================: TEB_ADDTRANSCODEFILE回调, 文件id:",
          fid
        );
      });
      // PPT状态变更事件
      this.teduBoard.on(
        TEduBoard.EVENT.TEB_H5PPT_STATUS_CHANGED,
        (statusCode, data) => {
          const { fid, message } = data;
          const statusMap = new Map();
          statusMap.set(1, "ppt加载中");
          statusMap.set(2, "ppt加载完成");
          statusMap.set(3, "ppt取消加载");
          statusMap.set(4, "ppt加载超时");
          statusMap.set(5, "ppt资源加载失败");
          statusMap.set(6, "ppt内部运行错误");
          statusMap.set(7, "调用addTranscodeFile接口添加文件的时候超时");
          statusMap.set(8, "ppt内部的图片资源加载异常");
          console.log(
            "======================: TEB_H5PPT_STATUS_CHANGED回调, PPT状态码",
            statusCode,
            statusMap.get(statusCode),
            `回调信息 文件id:${fid} 描述信息:${message}`
          );
          this.loading = false;
          if (!document.getElementById("sketch").getElementsByClassName("tic_iframe_H5")[0].src || [4,5,6,7,8,9].includes(statusCode)) {
            this.refeshStuts = true;
            Toast({message:"网络波动较大，为了不影响您的观看体验，建议切换网络",duration: 3000});
            // window.teduBoard.refresh();
          }
          if(statusCode === 2){
            this.$bus.emit("tiw-send-sync-data", data);
          }
          // if(statusCode > 3){
          //   this.teduBoard.refresh();
          // }
          this.teduBoard.enablePermissionChecker(['Board::Switch::Step', 'Board::Switch::Page'], ['operator/']);
        }
      );

      this.teduBoard.on(TEduBoard.EVENT.TIW_RESET_DATA, () => {
        console.log("========= TIW_RESET_DATA", "重置白板");
      });

      this.teduBoard.on(TEduBoard.EVENT.TEB_TEXT_ELEMENT_WARNING, () => {
        console.log("========= TEB_TEXT_ELEMENT_WARNING", "文本高度达到上限");
      });

      this.teduBoard.on(
        TEduBoard.EVENT.TEB_H5PPT_MEDIA_STATUS_CHANGED,
        (fileId, mediaId, status, currentTime) => {
          console.log(
            "========= TEB_H5PPT_MEDIA_STATUS_CHANGED",
            "视频媒体状态变化",
            "媒体ID:",
            mediaId,
            "播放状态:",
            status === 1 ? "播放" : "暂停",
            "当前播放进度（秒）:",
            currentTime
          );
        }
      );
    },

    destroyBoard() {
      if (this.teduBoard) {
        // 如果白板存在，则先销毁掉，避免页面多个白板对象
        this.teduBoard.destroy();
        this.teduBoard = null;
        this.$store.commit("setTiwReady", false);
        this.loading = false;
      }
    },

    addSyncData(realtimeData) {
      if (this.teduBoard) {
        this.teduBoard.addSyncData(realtimeData);
      }
    },

    showTip(tip) {
      Toast(tip);
    },

    showErrorTip(tip) {
      Toast(tip);
    },

    onUpdateSetting(settingItems) {
      this.settingItems = settingItems;
    },
    refesh() {
      window.teduBoard.refresh();
      this.refeshStuts = false;
    }
  },
  beforeDestroy() {
    this.destroyBoard();
  },
};
</script>

<style lang="less" scoped>
.sketch__wrap {
  width: 100%;
  height: 100%;
  .refe_wrap {
    width: 80px;
    height: 80px;
    position: absolute;
    background-color: aliceblue;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
    /* width: 50px; */
    /* height: 50px; */
    cursor: pointer;
    border-radius: 100%;
    text-align: center;
    padding-top: 19px;
    .refe {
      width: 40px;
      height: 40px;
    }
  }
}

#sketch {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  // background: #fff;
}
</style>
