<template>
  <v-toolbar dense floating>
    <template v-if="show">
      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="undo()">
            <v-icon dense>mdi-undo-variant</v-icon>
          </v-btn>
        </template>
        <span>撤销</span>
      </v-tooltip>
      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="redo()">
            <v-icon dense>mdi-redo-variant</v-icon>
          </v-btn>
        </template>
        <span>重做</span>
      </v-tooltip>

      <!-- <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="confirmDownGrade()">
            <v-icon dense>mdi-text-box-plus-outline</v-icon>
          </v-btn>
        </template>
        <span>降级渲染</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="cancelDownGrade()">
            <v-icon dense>mdi-text-box-plus-outline</v-icon>
          </v-btn>
        </template>
        <span>取消降级</span>
      </v-tooltip> -->

      <v-divider inset vertical></v-divider>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="setBoardScale(100)">
            <v-icon dense>mdi-target</v-icon>
          </v-btn>
        </template>
        <span>重置比例</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            tile
            icon
            :disabled="boardScale <= 100"
            @click="setBoardScale(boardScale - 10)"
          >
            <v-icon dense>mdi-minus</v-icon>
          </v-btn>
        </template>
        <span>缩小</span>
      </v-tooltip>

      <span>{{ boardScale }}%</span>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            tile
            icon
            @click="setBoardScale(boardScale + 10)"
          >
            <v-icon dense>mdi-plus</v-icon>
          </v-btn>
        </template>
        <span>放大</span>
      </v-tooltip>

      <v-divider inset vertical></v-divider>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="prevPage()">
            <v-icon dense>mdi-page-first</v-icon>
          </v-btn>
        </template>
        <span>上一页</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="prevStep()">
            <v-icon dense>mdi-chevron-left</v-icon>
          </v-btn>
        </template>
        <span>上一步(PPT)</span>
      </v-tooltip>

      <span>
        {{ currentFile.currentPageIndex + 1 }} / {{ currentFile.pageCount }}
      </span>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="nextStep()">
            <v-icon dense>mdi-chevron-right</v-icon>
          </v-btn>
        </template>
        <span>下一步(PPT)</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="nextPage()">
            <v-icon dense>mdi-page-last</v-icon>
          </v-btn>
        </template>
        <span>下一页</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="addBoard()">
            <v-icon dense>mdi-text-box-plus-outline</v-icon>
          </v-btn>
        </template>
        <span>添加白板</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            tile
            icon
            @click="toggleAddBoardDialog(true)"
          >
            <v-icon dense>mdi-text-box-plus-outline</v-icon>
          </v-btn>
        </template>
        <span>指定页码添加白板</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="deleteBoard()">
            <v-icon dense>mdi-text-box-minus-outline</v-icon>
          </v-btn>
        </template>
        <span>删除白板</span>
      </v-tooltip>

      <v-tooltip top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" tile icon @click="openRightBar()">
            <v-icon dense>mdi-file-multiple-outline</v-icon>
          </v-btn>
        </template>
        <span>文件列表</span>
      </v-tooltip>
    </template>
    <v-btn
      icon
      tile
      :style="{ width: `${show ? '16px' : '36px'}` }"
      @click="show = !show"
    >
      <v-icon>
        {{ show ? "mdi-chevron-left" : "mdi-chevron-right" }}
      </v-icon>
    </v-btn>
    <v-dialog
      v-if="showAddBoardDialog"
      v-model="showAddBoardDialog"
      max-width="300"
    >
      <v-card>
        <v-card-title> 插入新的白板 </v-card-title>
        <v-card-text>
          <v-text-field
            v-model="addBoardIndex"
            label="指定白板页码"
            prefix="第"
            suffix="页"
            :style="{ width: '100px' }"
          >
          </v-text-field>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="addBoardWithSequence()"
              >确认添加</v-btn
            >
          </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-toolbar>
</template>

<script>
import { mapActions } from "vuex";
export default {
  name: "Bottombar",
  data() {
    return {
      boardScale: 100,
      window,
      currentFile: {
        currentPageIndex: 0,
        pageCount: 0
      },
      show: true,
      showAddBoardDialog: false,
      addBoardIndex: 1
    };
  },
  watch: {
    "$store.state.current.fileInfo"(value) {
      this.currentFile = value;
      this.boardScale = window.teduBoard.getBoardScale();
    }
  },
  methods: {
    ...mapActions(["setRightBarShow"]),
    setBoardScale(boardScale) {
      this.boardScale = boardScale;
      window.teduBoard.setBoardScale(this.boardScale);
      this.boardScale = window.teduBoard.getBoardScale();
    },
    undo() {
      window.teduBoard.undo();
    },
    redo() {
      window.teduBoard.redo();
    },
    confirmDownGrade() {
      const currentFileId = window.teduBoard.getCurrentFile();
      const fileInfo = window.teduBoard.getFileInfo(currentFileId);
      window.teduBoard.setDownGradeEnable(
        fileInfo.fid,
        fileInfo.currentPageIndex,
        true
      );
    },
    cancelDownGrade() {
      const currentFileId = window.teduBoard.getCurrentFile();
      const fileInfo = window.teduBoard.getFileInfo(currentFileId);
      window.teduBoard.setDownGradeEnable(
        fileInfo.fid,
        fileInfo.currentPageIndex,
        false
      );
    },
    prevPage() {
      window.teduBoard.prevBoard();
    },
    nextPage() {
      window.teduBoard.nextBoard();
    },
    prevStep() {
      window.teduBoard.prevStep();
    },
    nextStep() {
      window.teduBoard.nextStep();
    },
    openRightBar() {
      this.setRightBarShow(true);
    },
    addBoard() {
      window.teduBoard.addBoard();
    },
    deleteBoard() {
      window.teduBoard.deleteBoard();
    },
    addBoardWithSequence() {
      console.log("===== addBoardIndex", this.addBoardIndex);
      window.teduBoard.addBoard({ index: Number(this.addBoardIndex) - 1 });
      this.toggleAddBoardDialog(false);
    },
    toggleAddBoardDialog(visible = true) {
      this.showAddBoardDialog = visible;
    }
  }
};
</script>

<style scoped>

</style>
