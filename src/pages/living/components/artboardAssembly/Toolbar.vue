<template>
  <v-list
    dense
    nav
    elevation="4"
    width="50"
    class="pa-0 pt-2 pb-2 toolbar-container"
    :class="toolbarClassName"
  >
    <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE
              )
            "
          >
            <v-icon>mdi-cursor-default-outline</v-icon>
          </v-btn>
        </template>
        <span>鼠标</span>
      </v-tooltip>
    </v-list-item>
    <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT_SELECT
              )
            "
          >
            <v-icon>mdi-selection-drag</v-icon>
          </v-btn>
        </template>
        <span>选框</span>
      </v-tooltip>
    </v-list-item>
    <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="
              setPenToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN
              )
            "
          >
            <v-icon>mdi-draw</v-icon>
          </v-btn>
        </template>
        <span>画笔</span>
      </v-tooltip>
    </v-list-item>
    <v-list-item style="position: relative">
      <v-menu transition="slide-x-transition" right absolute :position-x="50">
        <template v-slot:activator="{ on: menuOn, attrs: menuAttrs }">
          <v-tooltip right>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-bind="{ ...menuAttrs, ...attrs }"
                v-on="{ ...menuOn, ...on }"
                icon
                tile
                @click="
                  setToolType(
                    TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ERASER
                  )
                "
              >
                <v-icon>mdi-eraser</v-icon>
              </v-btn>
            </template>
            <span>橡皮擦</span>
          </v-tooltip>
        </template>
        <v-card>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                icon
                tile
                v-bind="attrs"
                v-on="on"
                @click="setPiecewiseErasureEnable(false)"
              >
                <v-icon>mdi-eraser</v-icon>
              </v-btn>
            </template>
            <span>整段擦除</span>
          </v-tooltip>

          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                icon
                tile
                v-bind="attrs"
                v-on="on"
                @click="setPiecewiseErasureEnable(true)"
              >
                <v-icon>mdi-eraser-variant</v-icon>
              </v-btn>
            </template>
            <span>分段擦除</span>
          </v-tooltip>
        </v-card>
      </v-menu>
    </v-list-item>
    <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="
              setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_TEXT)
            "
          >
            <v-icon>mdi-format-text</v-icon>
          </v-btn>
        </template>
        <span>文本</span>
      </v-tooltip>
    </v-list-item>

    <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LASER
              )
            "
          >
            <v-icon>mdi-flare</v-icon>
          </v-btn>
        </template>
        <span>激光笔</span>
      </v-tooltip>
    </v-list-item>

    <!-- <v-list-item>
      <v-menu
        transition="slide-x-transition"
        right
        absolute
        :position-x="50"
        :close-on-content-click="false"
      >
        <template v-slot:activator="{ on: menuOn, attrs: menuAttrs }">
          <v-tooltip right>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                icon
                tile
                v-bind="{ ...menuAttrs, ...attrs }"
                v-on="{ ...menuOn, ...on }"
              >
                <v-icon>mdi-palette</v-icon>
              </v-btn>
            </template>
            <span>工具样式</span>
          </v-tooltip>
        </template>
        <v-card>
          <v-card-text>
            <div>
              <span>画笔粗细</span>
              <v-slider
                v-model="thin"
                dense
                hide-details
                :thumb-size="24"
                :label="String(thin)"
                step="10"
                max="500"
                min="10"
                @change="setBrushThin"
              ></v-slider>
            </div>
            <div>
              <span>橡皮擦大小（像素）</span>
              <v-slider
                v-model="eraserSize"
                dense
                hide-details
                :thumb-size="24"
                :label="String(eraserSize)"
                step="1"
                max="100"
                min="16"
                @change="setEraserSize"
              ></v-slider>
            </div>
            <div>
              <span>文本大小</span>
              <v-slider
                v-model="fontSize"
                dense
                hide-details
                :thumb-size="24"
                :label="String(fontSize)"
                step="10"
                max="500"
                min="100"
                @change="setTextSize"
              ></v-slider>
            </div>
            <div>
              <span>行高设置</span>
              <v-select
                :items="lineHeightItems"
                label="行高设置"
                dense
                hide-details
                @change="handleLineHeightChange"
                single-line
                v-model="lineHeight"
              ></v-select>
            </div>
            <span>画笔颜色</span>
            <v-color-picker
              width="300"
              canvas-height="100"
              flat
              hide-sliders
              hide-inputs
              class="ma-2"
              show-swatches
              :swatches="[
                ['#FF0000'],
                ['#FFFF00'],
                ['#00FF00'],
                ['#00FFFF'],
                ['#0000FF']
              ]"
              @update:color="setBrushColor"
            ></v-color-picker>
            <span>文本工具颜色</span>
            <v-color-picker
              width="300"
              canvas-height="100"
              flat
              hide-sliders
              hide-inputs
              class="ma-2"
              show-swatches
              :swatches="[
                ['#FF0000'],
                ['#FFFF00'],
                ['#00FF00'],
                ['#00FFFF'],
                ['#0000FF']
              ]"
              @update:color="setTextColor"
            ></v-color-picker>
            <span>荧光笔颜色</span>
            <v-color-picker
              value="#ddfd8e80"
              width="300"
              hide-canvas
              hide-inputs
              flat
              @update:color="setHighlighterColor"
            ></v-color-picker>
            <span>背景颜色</span>
            <v-color-picker
              value="#FF0000FF"
              width="300"
              hide-canvas
              hide-inputs
              flat
              @update:color="setBackgroundColor"
            ></v-color-picker>
          </v-card-text>
        </v-card>
      </v-menu>
    </v-list-item> -->
    <!-- <v-list-item>
      <v-menu transition="slide-x-transition" right absolute :position-x="50">
        <template v-slot:activator="{ on: menuOn, attrs: menuAttrs }">
          <v-tooltip right>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-on="{ ...menuOn, ...on }"
                v-bind="{ ...menuAttrs, ...attrs }"
                icon
                tile
              >
                <v-icon>mdi-broom</v-icon>
              </v-btn>
            </template>
            <span>一键清空</span>
          </v-tooltip>
        </template>
        <v-list>
          <v-list-item @click="clearBackground">清空背景图/h5</v-list-item>
          <v-list-item @click="cleanAll()">清空白板</v-list-item>
          <v-list-item @click="cleanAll(true)">清空白板+背景</v-list-item>
        </v-list>
      </v-menu>
    </v-list-item> -->
    <!-- <v-list-item>
      <v-menu transition="slide-x-transition" right absolute :position-x="50">
        <template v-slot:activator="{ on: menuOn, attrs: menuAttrs }">
          <v-tooltip right>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-on="{ ...menuOn, ...on }"
                v-bind="{ ...menuAttrs, ...attrs }"
                icon
                tile
              >
                <v-icon>mdi-gift</v-icon>
              </v-btn>
            </template>
            <span>资源</span>
          </v-tooltip>
        </template>
        <v-list>
          <v-list-item
            v-for="type in Object.keys(uploadTypeList)"
            :key="type"
            @click="showCoursewareAndElementDialog(type)"
            >{{ uploadTypeList[type].title }}</v-list-item
          >
        </v-list>
      </v-menu>
    </v-list-item> -->

    <v-list-item>
      <v-menu transition="slide-x-transition" right absolute :position-x="50">
        <template v-slot:activator="{ on: menuOn, attrs: menuAttrs }">
          <v-tooltip right>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-on="{ ...menuOn, ...on }"
                v-bind="{ ...menuAttrs, ...attrs }"
                icon
                tile
              >
                <v-icon>mdi-camera</v-icon>
              </v-btn>
            </template>
            <span>截图</span>
          </v-tooltip>
        </template>
        <v-list>
          <v-list-item @click="onSnapshot({ full: false })"
            >所见即所得模式</v-list-item
          >
          <v-list-item @click="onSnapshot({ full: true })"
            >全尺寸模式(请放大白板后体验)</v-list-item
          >
        </v-list>
      </v-menu>
    </v-list-item>

    <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="showSettingPanelDialog()"
          >
            <v-icon>mdi-cog-outline</v-icon>
          </v-btn>
        </template>
        <span>其他设置</span>
      </v-tooltip>
    </v-list-item>
<!-- 
    <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="changeAllPPTMediaPlaybackStatus('video', 1)"
          >
            <v-icon>mdi-play-box</v-icon>
          </v-btn>
        </template>
        <span>播放所有课件视频</span>
      </v-tooltip>
    </v-list-item> -->
    <!-- <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="changeAllPPTMediaPlaybackStatus('video', 2)"
          >
            <v-icon>mdi-pause-box</v-icon>
          </v-btn>
        </template>
        <span>暂停所有课件视频</span>
      </v-tooltip>
    </v-list-item> -->
    <!-- <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="changeAllPPTMediaPlaybackStatus('audio', 1)"
          >
            <v-icon>mdi-play</v-icon>
          </v-btn>
        </template>
        <span>播放所有课件音频</span>
      </v-tooltip>
    </v-list-item> -->
    <!-- <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            v-on="on"
            v-bind="attrs"
            icon
            tile
            @click="changeAllPPTMediaPlaybackStatus('audio', 2)"
          >
            <v-icon>mdi-pause</v-icon>
          </v-btn>
        </template>
        <span>暂停所有课件音频</span>
      </v-tooltip>
    </v-list-item> -->
    <!-- <v-list-item>
      <v-tooltip right>
        <template v-slot:activator="{ on, attrs }">
          <v-btn v-on="on" v-bind="attrs" icon tile @click="chat()">
            <v-icon>mdi-weichat</v-icon>
          </v-btn>
        </template>
        <span>微信</span>
      </v-tooltip>
    </v-list-item> -->
    <courseware-dialog ref="coursewareDialog"></courseware-dialog>
    <element-dialog ref="elementDialogRef"></element-dialog>
    <setting-dialog
      ref="settingDialogRef"
      @updateSetting="onUpdateSetting"
    ></setting-dialog>
    <custom-graph-dialog
      ref="customGraphDialogRef"
      @updateCustomGraph="onUpdateCustomGraph"
    ></custom-graph-dialog>
    <media-dialog ref="mediaDialogRef"></media-dialog>
    <background-dialog ref="backgroundDialogRef"></background-dialog>
    <watermark-dialog ref="watermarkDialogRef"></watermark-dialog>
  </v-list>
</template>

<script>
import ElementDialog from "./ToolbarDialog/ElementDialog.vue";
import SettingDialog from "./ToolbarDialog/SettingDialog.vue";
import CustomGraphDialog from "./ToolbarDialog/CustomGraphDialog.vue";
import CoursewareDialog from "./ToolbarDialog/CoursewareDialog.vue";
import MediaDialog from "./ToolbarDialog/MediaDialog.vue";
import BackgroundDialog from "./ToolbarDialog/BackgroundDialog.vue";
import WatermarkDialog from "./ToolbarDialog/WatermarkDialog.vue";

export default {
  name: "Toolbar",
  components: {
    BackgroundDialog,
    MediaDialog,
    WatermarkDialog,
    ElementDialog,
    SettingDialog,
    CustomGraphDialog,
    CoursewareDialog
  },
  data() {
    return {
      currentToolType: TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN,
      toolbarClassName: "",
      dialog: false,
      thin: 50,
      eraserSize: 16,
      fontSize: 320,
      lineHeight: 1,
      // 只有该值才不会触发初次打开回调
      lineHeightItems: [
        // 1、1.15、1.3、1.5、2、3
        {
          text: "1",
          value: 1
        },
        {
          text: "1.15",
          value: 1.15
        },
        {
          text: "1.3",
          value: 1.3
        },
        {
          text: "1.5",
          value: 1.5
        },
        {
          text: "2",
          value: 2
        },
        {
          text: "3",
          value: 3
        }
      ],
      uploadTypeList: {
        courseware: {
          title: "课件资源"
        },
        media: {
          title: "多媒体资源"
        },
        background: {
          title: "背景资源"
        },
        element: {
          title: "白板元素"
        },
        watermark: {
          title: "水印元素"
        }
      },

      customGraphImages: [
        "https://test-1259648581.file.myqcloud.com/%E4%B8%89%E8%A7%92%E5%BD%A2.svg",
        "https://test-1259648581.file.myqcloud.com/%E6%8A%9B%E7%89%A9%E7%BA%BF_parabolic9.svg",
        "https://test-1259648581.file.myqcloud.com/%E8%8F%B1%E5%BD%A2.svg",
        "https://test-1259648581.file.myqcloud.com/%E6%B1%BD%E8%BD%A6.svg",
        "https://test-1259648581.file.myqcloud.com/%E7%83%A7%E6%9D%AF.svg",
        "https://test-1259648581.file.myqcloud.com/%E7%A3%81%E9%93%81.svg"
      ],
      colorTask: null // 设置背景颜色的任务
    };
  },

  watch: {
    "$store.state.isTiwReady"(value) {
      if (value) {
        const teduBoard = window.teduBoard;
        // 监听元素位置更新事件
        teduBoard.on(
          TEduBoard.EVENT.TEB_BOARD_ELEMENT_POSITION_CHANGE,
          (data, status) => {
            if (
              status ===
              TEduBoard.TEduBoardPositionChangeStatus
                .TEDU_BOARD_POSITION_CHANGE_START
            ) {
              // 开始变化
              this.toolbarClassName = "disabled-event";
            } else if (
              status ===
              TEduBoard.TEduBoardPositionChangeStatus
                .TEDU_BOARD_POSITION_CHANGE_END
            ) {
              // 结束变化
              this.toolbarClassName = "";
            }
          }
        );
        // 监听新增元素
        teduBoard.on(
          TEduBoard.EVENT.TEB_ADDELEMENT,
          ({ id, type, userData }) => {
            // (id, type, userData, title) => {
            console.log("新增元素", id, type, userData);
            if (
              this.settingItems[0].graphicAutoSelect &&
              type ===
                TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_GEOMETRY
            ) {
              window.teduBoard.autoSelectedElement(id);
            }
          }
        );
      }
    }
  },

  methods: {
    chat() {
      this.$eventBus.$emit("chat", { showCompChat: true });
    },

    showTip(tip) {
      this.$toasted.show(tip);
    },

    showErrorTip(tip) {
      this.$toasted.error(tip);
    },

    formatColor(color) {
      const [r, g, b, a] = color.match(/[\d.]+/).map((i) => +i);
      return { r, g, b, a };
    },

    setToolType(
      toolType,
      isSolidLine = true,
      isFill = false,
      startArrowType = 1,
      endArrowType = 1
    ) {
      console.log(
        toolType,
        "setToolType:",
        isSolidLine,
        isFill,
        startArrowType,
        endArrowType
      );
      this.currentToolType = toolType;
      console.log(window.window);
      window.window.teduBoard.setToolType(toolType);
      let lineType = null;
      if (isSolidLine) {
        lineType = TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_SOLID;
      } else {
        lineType = TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_DOTTED;
      }
      window.teduBoard.setGraphStyle({
        lineType,
        fillType: isFill
          ? TEduBoard.TEduBoardFillType.SOLID
          : TEduBoard.TEduBoardFillType.NONE,
        startArrowType,
        endArrowType
      });
    },

    /**
     * 工具类型
     * @param {*} toolType 画笔工具
     * @param {*} autoFitMode 自动拟合模式 0 不开启拟合 1 松手拟合 2 按住2s不动拟合
     */
    setPenToolType(toolType, autoFitMode = 0) {
      this.currentToolType = toolType;
      window.teduBoard.setToolType(toolType);

      if (autoFitMode === 0) {
        // 如果是不拟合
        window.teduBoard.enablePenAutoFit(false); // 关闭按住2s拟合
        window.teduBoard.setPenAutoFittingMode(
          TEduBoard.TEduBoardPenFittingMode.NONE
        ); // 关闭松手自动拟合
        window.teduBoard.setHandwritingEnable(
          this.settingItems[0].handwritingEnable
        ); // 笔锋以设置面板的值为准
      } else if (autoFitMode === 1) {
        // 如果是松手拟合
        window.teduBoard.enablePenAutoFit(false); // 关闭按住2s拟合
        window.teduBoard.setHandwritingEnable(false); // 关闭笔锋
        window.teduBoard.setPenAutoFittingMode(
          TEduBoard.TEduBoardPenFittingMode.AUTO
        ); // 开启松手自动拟合
      } else if (autoFitMode === 2) {
        // 如果是按住2s不动拟合
        window.teduBoard.setHandwritingEnable(false); // 关闭笔锋
        window.teduBoard.setPenAutoFittingMode(
          TEduBoard.TEduBoardPenFittingMode.NONE
        ); // 关闭松手自动拟合
        window.teduBoard.enablePenAutoFit(true, 2000); // 开启按住2s拟合
      }
    },

    setBrushThin(thin) {
      window.teduBoard.setBrushThin(thin);
    },

    setEraserSize(eraserSize) {
      window.teduBoard.setEraserSize(eraserSize);
    },

    setTextSize(size) {
      window.teduBoard.setTextSize(size);
    },

    setBrushColor(color) {
      window.teduBoard.setBrushColor(color.hex);
    },

    setTextColor(color) {
      window.teduBoard.setTextColor(color.hex);
    },

    setBackgroundColor(color) {
      clearTimeout(this.colorTask);
      this.colorTask = setTimeout(() => {
        const r = color.rgba.r;
        const g = color.rgba.g;
        const b = color.rgba.b;
        const a = color.rgba.a;
        const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a})`;
        window.teduBoard.setBackgroundColor(rgbaColor);
      }, 200);
    },

    setHighlighterColor(color) {
      const r = color.rgba.r;
      const g = color.rgba.g;
      const b = color.rgba.b;
      let a = Number(color.rgba.a.toFixed(1)).valueOf();
      a = Math.min(0.9, Math.max(a, 0.1));
      const highlighterColor = `rgba(${r}, ${g}, ${b}, ${a})`;
      window.teduBoard.setHighlighterColor(highlighterColor);
    },

    cleanAll(removeBackground = false) {
      window.teduBoard.clear(removeBackground);
    },

    useMathTool(tool = 1) {
      window.teduBoard.useMathTool(tool);
    },

    /**
     * 显示课件和元素的弹窗
     * @param {*} type
     */
    showCoursewareAndElementDialog(type) {
      if (type === "courseware") {
        this.$refs.coursewareDialog.show();
      } else if (type === "media") {
        this.$refs.mediaDialogRef.show();
      } else if (type === "background") {
        this.$refs.backgroundDialogRef.show();
      } else if (type === "watermark") {
        this.$refs.watermarkDialogRef.show();
      } else {
        this.$refs.elementDialogRef.show(type);
      }
    },

    showSettingPanelDialog() {
      this.$refs.settingDialogRef.show();
    },

    setCustomGraph(url) {
      window.teduBoard.addElement(
        TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_CUSTOM_GRAPH,
        url
      );
      this.showTip(
        "已经设置了自定义图形的url，请在页面上进行拖动绘制出自定义图形"
      );
    },

    setPiecewiseErasureEnable(enable) {
      window.teduBoard.setPiecewiseErasureEnable(enable);
    },

    showCustomGraph() {
      this.$refs.customGraphDialogRef.show();
    },

    onUpdateCustomGraph(url) {
      this.customGraphImages.push(url);
      this.setCustomGraph(url);
    },

    clearBackground() {
      const backgroundImage = window.teduBoard.getBackgroundImage();
      if (backgroundImage.type === 0) {
        // 图片背景
        window.teduBoard.setBackgroundImage("");
      } else {
        window.teduBoard.setBackgroundH5("");
      }
    },

    onUpdateSetting(settingItems) {
      this.settingItems = settingItems;
    },

    onSnapshot(option) {
      // 在截图回调事件TEduBoard.EVENT.TEB_SNAPSHOT处理截图的结果
      window.teduBoard.snapshot(option);
    },

    handleLineHeightChange(value) {
      window.teduBoard.setTextLineHeight(value);
    },

    changeAllPPTMediaPlaybackStatus(type, playstate) {
      const { pptMediaInfo = { videoIDList: [], audioIDList: [] } } =
        window.teduBoard.getFileInfo();
      const apiSuffix = type === "video" ? "Video" : "Audio";
      if (playstate === 1) {
        // playH5PPTVideo
        window.teduBoard[`playH5PPT${apiSuffix}`](
          pptMediaInfo[`${type}IDList`]
        );
      } else if (playstate === 2) {
        window.teduBoard[`pauseH5PPT${apiSuffix}`](
          pptMediaInfo[`${type}IDList`]
        );
      }
    }
  }
};
</script>

<style scoped>
.toolbar-container.disabled-event {
  pointer-events: none;
}
</style>
