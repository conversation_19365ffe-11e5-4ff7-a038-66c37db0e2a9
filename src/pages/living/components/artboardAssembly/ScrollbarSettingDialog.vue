<template>
  <div class="cursor-setting-dialog__container">
    <v-dialog v-if="showDialog" v-model="showDialog" width="500">
      <v-card>
        <v-card-title class="headline lighten-2"> 滚动条设置 </v-card-title>
        <v-card-text>
          <span>滑块颜色</span>
          <v-color-picker
            v-model="thumbColor"
            width="400"
            hide-inputs
            flat
            @update:color="handleScrollbarThumbColorChange"
          ></v-color-picker>
        </v-card-text>
        <v-card-text>
          <span>滑轨颜色</span>
          <v-color-picker
            v-model="trackColor"
            width="400"
            hide-inputs
            flat
            @update:color="handleScrollbarTrackColorChange"
          ></v-color-picker>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="showDialog = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showDialog: false,
      thumbColor: {
        r: 0,
        g: 0,
        b: 0,
        a: 0.12
      },
      trackColor: {
        r: 0,
        g: 0,
        b: 0,
        a: 0.06
      }
    };
  },
  created() {
    const thumbColorCache = sessionStorage.getItem("boardScrollbarThumbColor");
    if (thumbColorCache) {
      this.thumbColor = this.convertRgbaStringToObject(thumbColorCache);
    }
    const trackColorCache = sessionStorage.getItem("boardScrollbarTrackColor");
    if (trackColorCache) {
      this.trackColor = this.convertRgbaStringToObject(trackColorCache);
    }
  },
  methods: {
    show() {
      this.showDialog = true;
    },
    handleScrollbarThumbColorChange(color) {
      const r = color.rgba.r;
      const g = color.rgba.g;
      const b = color.rgba.b;
      const a = color.rgba.a;
      const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a})`;
      console.log("handleScrollbarThumbColorChange", rgbaColor);
      sessionStorage.setItem("boardScrollbarThumbColor", rgbaColor);
    },
    handleScrollbarTrackColorChange(color) {
      const r = color.rgba.r;
      const g = color.rgba.g;
      const b = color.rgba.b;
      const a = color.rgba.a;
      const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a})`;
      console.log("handleScrollbarTrackColorChange", rgbaColor);
      sessionStorage.setItem("boardScrollbarTrackColor", rgbaColor);
    },
    convertRgbaStringToObject(rgbaString) {
      const colors = ["r", "g", "b", "a"];
      const colorArr = rgbaString
        .slice(rgbaString.indexOf("(") + 1, rgbaString.indexOf(")"))
        .split(", ");

      // eslint-disable-next-line no-new-object
      const rgbaObject = new Object();

      colorArr.forEach((k, i) => {
        rgbaObject[colors[i]] = +k;
      });

      console.log(rgbaObject);
      return rgbaObject;
    }
  }
};
</script>
