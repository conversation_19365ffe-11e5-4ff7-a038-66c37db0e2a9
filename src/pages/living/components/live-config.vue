<template>
  <div class="post">
    <span v-if="item.userId !== doneTeacherId">
      <img
      src="@/assets/living/Mask group (1).png"
      alt=""
      class="stage"
      v-if="
        (type === 'loca' && doneDrawingAuthority) ||
        (type === 'farend' && item.student_role_status)
      "
    />
    </span>
    <img
      :src="
        (type === 'loca' && config.userAvatar) ||
        (type === 'farend' && item.avatar) ||
        require('@/assets/living/jin.png')
      "
      alt=""
      class="avatar"
      :style="(type === 'loca' ? (isVideoMuted && doneCameraAuthority) :type === 'farend' ? ((item.receiveVideo === true) && (item.student_camera_status ===true)) : true) ? '' :'z-index:3'
      "
      v-if="liveType && ((type === 'loca' && (!isVideoMuted || !doneCameraAuthority)) || 
      (type === 'farend'&& (item.userId == doneTeacherId ? !item.student_camera_status :(!item.receiveVideo || !item.student_camera_status))))"
    />
    <!-- :style="(type === 'loca' ? (isVideoMuted && doneCameraAuthority) :type === 'farend' ? ((item.receiveVideo === true) && (item.student_camera_status ===true)) : true) ? '' :'z-index:3'
      " -->
    <div
      class="audio_vlom"
      v-if="
        (type === 'loca' && isAudioMuted && liveType) ||
        (type === 'farend' &&
          ((item.student_microphone_status ? item.receiveAudio : liveType)&& liveType))
      "
    >
      <div
        v-for="(items, index) in 10"
        :key="index"
        class="RMS"
        :style="`${
          Math.ceil(greenAudio / 10) > index
            ? (type === 'loca' && doneTeacher && doneMicrophone) ||
              (type === 'farend' && item.student_microphone_status)
              ? 'background: #34FF41'
              : 'background: rgb(255 52 99)'
            : ''
        }`"
      ></div>
    </div>
    <div class="item_bottom">
      <div class="img_button" v-if="liveType" :style="item.userId == doneTeacherId ? 'display:flex':''">
        <img
          v-if="
            (type === 'loca' && doneTeacher) ||
            (type === 'farend' && item.student_microphone_status)
          "
          :src="
            (type === 'loca' && isAudioMuted) ||
            (type === 'farend' && item.receiveAudio)
              ? require('@/assets/living/Group 81.png')
              : require('@/assets/living/isgroup.png')
          "
          alt=""
        />
        <img
          v-else
          src="@/assets/living/wecom-temp-749-8ed44008ed7ad39051feef0c1462e1d8.png"
          alt=""
        />
        <!-- <p class="sutdent_name" v-if="doneTeacherId == item.userId">
          {{ doneTeacherName || "管理员" }}
        </p> -->
        <p class="sutdent_name" :style="item.userId  === doneTeacherId ? 'display: inline;width: 100%;overflow: inherit;':''">
          {{ type === "loca" ? config.userName : item.student_name }}
        </p>
      </div>
      <!-- 奖杯 -->
      <div
        class="trophy"
        v-if="liveType && doneTeacherId != item.userId && item.userId != '1'"
      >
        <img
          :src="
            (item.type == 'loca' && doneTreasureCount) ||
            (item.type == 'farend' && item.cupNumber) != 0
              ? require('@/assets/living/Mask group.png')
              : require('@/assets/living/Group 118.png')
          "
          alt=""
        />
        ×
        <span v-if="type === 'loca'">
          {{ doneTreasureCount }}
        </span>
        <span v-else>
          {{ item.cupNumber }}
        </span>
      </div>
    </div>
    <!-- <transition name="bounce"> -->
    <img
      src="@/assets/living/奖杯.png"
      alt=""
      class="student_trophy"
      v-if="liveType && item.reasureCount"
    />
    <!-- </transition> -->
    <div
      class="rudiu"
      v-if="
        (type === 'loca' && doneRaiseHand) ||
        (type === 'farend' && item.raiseHand)
      "
    >
      <img src="@/assets/living/Vector.png" alt="" />
    </div>
  </div>
</template>

<script>
// 视频遮罩
import liveApi from "@/api/living.js";
import config from "@/config";
export default {
  name: "live-config",
  props: {
    type: {
      type: String,
      default: "",
    },
    item: {},
    liveType: Boolean,
  },
  components: {},
  data() {
    return {
      info: "",
      stageti: true,
      config,
    };
  },
  // 计算属性
  computed: {
    isAudioMuted() {
      return this.$store.getters.doneAudioState;
    },
    isVideoMuted() {
      return this.$store.getters.doneVideoState;
    },
    audioLevel() {
      return this.$store.getters.doneAudioLevel;
    },
    doneOnStage() {
      return this.$store.getters.doneOnStage;
    },
    doneTreasureCount() {
      return this.$store.getters.doneTreasureCount;
    },
    greenAudio() {
      // console.log(this.type)
      if (this.type == "loca") {
        return this.audioLevel;
      } else {
        return this.item.volume;
      }
    },
    doneRaiseHand() {
      return this.$store.getters.doneRaiseHand;
    },
    doneTeacher() {
      return this.$store.getters.doneTeacher;
    },
    doneHandUpUser() {
      return this.$store.getters.doneHandUpUser;
    },
    doneDrawingAuthority() {
      return this.$store.getters.doneDrawingAuthority;
    },
    doneTeacherId() {
      return this.$store.getters.doneTeacherId;
    },
    doneTeacherName() {
      return this.$store.getters.doneTeacherName;
    },
    doneStandard() {
      return this.$store.getters.doneStandard;
    },
    doneMicrophone() {
      return this.$store.getters.doneMicrophone;
    },
    doneCamera() {
      return this.$store.getters.doneCamera;
    },
    doneTreasureSta() {
      return this.$store.getters.doneTreasureSta;
    },
    doneCameraAuthority() {
      return this.$store.getters.doneCameraAuthority;
    },

  },
  // 侦听器
  watch: {
    // doneRaiseHand: {
    //   handler(newVal, oldVal) {
    //     console.log(newVal, oldVal);
    //     if (newVal) {
    //       // 三秒后关闭举手状态
    //       setTimeout(() => {
    //         this.$bus.emit("cancelRaiseHand");
    //         this.$store.dispatch("UPDATE_RAISE_HAND_STATUS", false);
    //       }, 3000);
    //     }
    //   },
    //   deep:true,
    //   immediate: true,
    // },
    liveType: {
      handler(newVal, oldVal) {
        if (this.type == "loca" && newVal) {
          // this.getInfo();
        }
      },
    },
    // remoteList:{
    //   handler(newVal, oldVal) {
    //       this.getInfo();
    //   },
    // },
    "item.raiseHand": {
      handler(newVal, oldVal) {
        if (newVal) {
          // 三秒后关闭举手状态
          setTimeout(() => {
            this.item.raiseHand = false;
          }, 3000);
        }
      },
      deep: true,
    },
    "item.student_role_status": {
      handler(newVal, oldVal) {
        this.item.student_role_status = newVal;
      },
    },
    "item.student_microphone_status": {
      handler(newVal, oldVal) {
        this.item.student_microphone_status = newVal;
      },
    },
  },
  methods: {
    async getInfo() {
      let res = await liveApi.StudentsInfo({
        room_id: config.roomId,
        student_id: [
          this.item.userId == "localStream" ? config.userId : this.item.userId,
        ],
      });
      if (res.data.data) {
        //人员信息
        this.info = res.data.data[0];
        setTimeout(() => {
          this.item.cupNumber = this.info.trophy_num;
          // this.remoteStreamList.find((item) => {
          //   return item.userId == this.item.userId;
          // }).cupNumber = this.info.trophy_num;
          this.item.student_role_status = this.info.student_role_status;
          this.item.student_microphone_status = this.info.student_microphone_status;
          this.item.receiveAudio = this.info.microphone_status;
          this.item.student_camera_status = this.info.student_camera_status;
          this.$watch("item.cupNumber", this.tyophyHan,{deep:true});
        }, 500);
      }
    },
    // 切换摄像头
    changeCamera() {
      this.$store.commit("changeCamera");
    },
    closeStagetiOut() {
      setTimeout(() => {
        this.stageti = false;
      }, 3000);
      // 关闭
      clearTimeout(this.closeStagetiOut);
    },
    tyophyHan(newVal) {
      if(!newVal) return;
      this.item.cupNumber = newVal;
        this.item.reasureCount = true;
      setTimeout(() => {
        this.item.reasureCount = false;
      }, 3000);
    },
  },
  created() {},
  async mounted() {
    if (this.item.type == "loca") {
      console.log(this.doneMicrophone, "麦克风是否有是否可以上台");
      console.log(this.doneCamera, "摄像头是否可以上台");
      setTimeout(() => {
        this.closeStagetiOut();
      }, 500);
    } else {
      this.$nextTick(()=>{
          this.$bus.emit("stromRemote",this.item.userId);
        })
      if(this.doneTeacherId == this.item.userId) return;
      setTimeout(() => {
        this.$watch("item.cupNumber", this.tyophyHan);
      },500)
        

    }
  },
};
</script>

<style lang="scss" scoped>
.post {
  position: absolute;
  width: 100%;
  height: 100%;
  .avatar {
    width: 25%;
    // height: 73px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 1;
  }
  .audio_vlom {
    transform: rotate(270deg);
    position: absolute;
    bottom: 70px;
    /* height: 20px; */
    z-index: 3;
    left: -0.5%;
    .RMS {
      width: 5px;
      height: 10px;
      background: #ffffff;
      // border-radius: 9px;
      display: inline-block;
      margin-left: 2px;
    }
  }
  .rudiu {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3;
    img {
      width: 81px;
      // height: 81px;
    }
  }
  .student_trophy {
    position: absolute;
    width: 88px;
    height: 88px;
    left: 40%;
    top: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: bounce-in 3s linear infinite;
    z-index: 3;
    img {
      width: 100%;
      // height: 81px;
    }
  }
  .stage {
    width: 28px;
    height: 28px;
    position: absolute;
    top: 8px;
    right: 10px;
    z-index: 3;
  }
  .item_bottom {
    width: 100%;
    // height: 48px;
    position: absolute;
    bottom: 9px;
    left: 0;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 3;
    .trophy {
      font-size: 20px;
      font-weight: 500;
      color: #fff740;
      white-space: nowrap;
      width: 30%;
      text-align: right;
      img {
        width: 25px;
        height: 25px;
        // width: 25%;
      }
    }
    .sutdent_name {
      color: #fff;
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      white-space: nowrap;
      // overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: bottom;
      overflow: hidden;
      width: 70%;
      display: inline-block;
    }
    .img_button {
      display: inline-block;
      justify-items: center;
      // margin-right: 5px;
      width: 70%;
      align-items: flex-end;
      img {
        width: 30px;
        height: 30px;
        display: inline-block;
        // width: 22%;
        // height: 22%;
      }
    }
  }
}
@keyframes bounce-in {
  0% {
    transform: scale(3);
    opacity: 1;
  }
  90% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.bounce-enter-active {
  animation: bounce-in 2s;
}
.bounce-leave-active {
  animation: bounce-in 1s;
}
::v-deep video {
  z-index: 2;
}
</style>
