<template>
  <div v-if="doneOnStage" class="shar">
    <div
      v-for="(item, index) in remoteStreamList"
      :key="index"
      :id="item.userId + '_screen'"
      class="share_content"
    ></div>
  </div>
</template>

<script>
// 屏幕分享
export default {
  name: "com-sharing",
  props: {
    remoteStreamList: {
      type: Array,
      default: () => [],
    },
    doneShareScreen: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  data() {
    return {};
  },
  // 计算属性
  computed: {
    doneOnStage() {
      return this.$store.getters.doneOnStage;
    },
  },
  // 侦听器
  watch: {},
  methods: {},
  created() {},
  mounted() {},
};
</script>

<style lang="scss" scoped>
.shar {
  position: absolute;
  z-index: 99;
}
.share_content {
  width: 1456px;
  height: 936px;
  // height: 888px;
  background-color: pink;
}
</style>
