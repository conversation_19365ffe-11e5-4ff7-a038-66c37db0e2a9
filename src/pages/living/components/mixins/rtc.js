import TRTC from "trtc-sdk-v5";
import config from "@/config";
import { DeviceDetector } from "trtc-sdk-v5/plugins/device-detector";
export default {
  data() {
    return {
      client: null,
      localStream: null,
      remoteStreamList: [], //远端
      shareRemoteStream: [], //教师分享
      isJoining: false,
      isJoined: false,
      isPublishing: false,
      isPublished: false,
      isMutedVideo: false,
      isMutedAudio: false,
      isPlayingLocalStream: false,
      audioVolume: 0,
      tick: false,
      uplinkTRTC: null, // 用于检测上行网络质量
      downlinkTRTC: null, // 用于检测下行网络质量
      localStream: null, // 用于测试的流
      testResult: {
        // 记录上行网络质量数据
        uplinkNetworkQualities: [],
        // 记录下行网络质量数据
        downlinkNetworkQualities: [],
        average: {
          uplinkNetworkQuality: 0,
          downlinkNetworkQuality: 0,
        },
      },
    };
  },
  methods: {
    // 初始化客户端
    async initClient() {
      this.client = TRTC.create();
      console.log(`Client [${config.userId}] created.`);
      this.handleClientEvents();
      // TRTC.setLogLevel(1, true); // 日志输出
    },
    // 设置麦克风采集音量
    settingAudioCaptureVolume(val) {
      if (val > 0) {
        this.client.updateLocalAudio({
          option: { captureVolume: val, earMonitorVolume: 0 },
        });
      }
    },
    // 设置播放音量大小
    setAudioPlayoutVolume(val) {
      console.log("设置播放音量大小", val);
      if (val > 0) {
        this.$store.dispatch("SET_SPEAKER_VOLUME", val);
        this.client.setRemoteAudioVolume("*", val);
      }
    },
    async startLocalAudio() {
      await this.client.startLocalAudio({
        publish: false,
        mute: false,
        option: {
          microphoneId: this.activeMicrophoneId,
          earMonitorVolume: 0,
        },
      });
    },
    async startLocalVideo(dom, isMoveVideo = false) {
      console.log("startLocalVideo", this.client);
      console.log("startLocalVideo1", dom);
      try {
        this.client
          .startLocalVideo({
            publish: !isMoveVideo
              ? this.isVideoMuted &&
                this.doneCameraAuthority &&
                this.doneOnStage
              : false,
            mute: !this.isVideoMuted || !this.doneCameraAuthority,
            view: dom,
            option: {
              cameraId: this.activeCameraId,
              mirror: "both",
              fillMode: "cover",
              profile: "120p",
              mirror: false,
              frameRate: 30,
              bitrate: 200,
              qosPreference: "QOS_PREFERENCE_SMOOTH",
            },
          })
          .catch((error) => {
            console.log(error);
          });
      } catch (error) {}
    },

    destroyLocalStream() {
      this.client && this.client.destroy();
      this.client = null;
    },

    resumeStream(stream) {
      stream.resume();
    },

    async join() {
      if (this.isJoining || this.isJoined) {
        return;
      }
      this.isJoining = true;
      !this.client && (await this.initClient());
      try {
        await this.client.enterRoom({
          roomId: parseInt(config.roomId),
          sdkAppId: config.sdkAppId,
          userSig: config.userSig,
          userId: config.userId,
          autoReceiveAudio: true,
          scene: "rtc",
          role: "anchor",
          enableAutoPlayDialog: false, //关闭sdk自动播放
          // proxy: "wss://test.rtc.qq.com"
        });
        this.isJoining = false;
        this.isJoined = true;
        console.log(`Join room [${config.roomId}] success.`);
        // this.reportSuccessEvent("joinRoom");
        this.startGetAudioLevel();
      } catch (error) {
        this.isJoining = false;
        console.error("join room failed", error);
        console.log(
          `Join room ${config.roomId} failed, please check your params. Error: ${error.message}`
        );
        // this.reportFailedEvent("joinRoom", error);
        throw error;
      }
    },
    startPublish() {
      // setTimeout(() => {
      if (this.doneMicrophone && this.doneStandard !== 2) {
        this.client &&
          this.startLocalAudio(document.getElementById("localStream"));
      }
      if (this.doneCamera) {
        this.client &&
          this.startLocalVideo(document.getElementById("localStream")).catch(
            (error) => {
              console.log(error);
              if (
                error.extraCode === 5302 &&
                typeof error.handler === "function"
              ) {
                // 提示用户系统关闭了浏览器的摄像头 or 麦克风权限，即将跳转至系统权限设置 APP，请打开浏览器摄像头、麦克风权限。
                // 适用于 Windows 和 MacOS 系统。
                // error.handler();
              }
            }
          );
      }
      this.roomButton = true;
      // }, 500);
    },
    async publish() {
      // if (!this.isJoined || this.isPublishing || this.isPublished) {
      //   return;
      // }
      this.isPublishing = true;
      try {
        await this.client
          .updateLocalAudio({
            publish: !this.microphone_status,
            earMonitorVolume: 0,
          })
          .catch((error) => {
            console.log(error);
          });
        // }
        // if (this.doneCamera) {
        await this.client.updateLocalVideo({ publish: true }).catch((error) => {
          console.log(error);
        });
        this.setUpdateLocalVideo(!this.isVideoMuted);
        this.setUpdateLocalAudio(!this.isAudioMuted);
        // this.client && await this.startAiNlp();
        this.isPublishing = false;
        this.isPublished = true;
        console.log("LocalStream is published successfully.");
      } catch (error) {
        this.isPublishing = false;
        console.error("publish localStream failed", error);
        console.log(
          `LocalStream is failed to publish. Error: ${error.message}`
        );
        throw error;
      }
    },

    async unPublish() {
      // if (!this.liveType) {
      //   return;
      // }
      this.isUnPublish = false;
      try {
        // await this.client.unpublish(this.localStream);
        // await this.client.updateLocalVideo({ publish: false });
        // await this.client.updateLocalAudio({ publish: false });
        this.client && (await this.client.stopLocalVideo());
        this.client && (await this.client.stopLocalAudio());
        // this.localStream.close();
        this.isUnPublishing = false;
        this.isPublished = false;
        // this.reportSuccessEvent("unpublish");
      } catch (error) {
        this.isUnPublishing = false;
        console.error("unpublish localStream failed", error);
        console.error(
          `LocalStream is failed to unpublish. Error: ${error.message}`
        );
        // this.reportFailedEvent("unpublish", error);
        throw error;
      }
    },
    isUndefined(value) {
      return value === "undefined";
    },
    async setUpdateLocalAudio(type) {
      let typeAudio = type;
      // 如果本地没开的话  就不打开
      if (!this.isAudioMuted || !this.doneTeacher) {
        typeAudio = true;
      }
      this.client &&
        (await this.client
          .updateLocalAudio({
            mute: typeAudio,
            earMonitorVolume: 0,
            publish: true,
          })
          .catch((error) => {
            console.log(error);
          }));
    },
    async setUpdateLocalVideo(type) {
      let typeVideo = type;
      // 如果本地没开的话  就不打开
      if (!this.isVideoMuted || !this.doneCameraAuthority) {
        typeVideo = true;
      }
      this.client &&
        (await this.client
          .updateLocalVideo({ mute: typeVideo, publish: true })
          .catch((error) => {
            console.log(error);
          }));
    },
    async subscribe(remoteStream, config = { audio: true, video: true }) {
      try {
        await this.client.subscribe(remoteStream, {
          audio: this.isUndefined(config.audio) ? true : config.audio,
          video: this.isUndefined(config.video) ? true : config.video,
        });
        console.log(`Subscribe [${remoteStream.getUserId()}] success.`);
        // this.reportSuccessEvent("subscribe");
      } catch (error) {
        console.error(
          `subscribe ${remoteStream.getUserId()} with audio: ${
            config.audio
          } video: ${config.video} error`,
          error
        );
        console.log(`Subscribe ${remoteStream.getUserId()} failed!`);
        // this.reportFailedEvent("subscribe", error);
      }
    },

    async unSubscribe(remoteStream) {
      try {
        await this.client.unsubscribe(remoteStream);
        console.log(`unsubscribe [${remoteStream.getUserId()}] success.`);
        // this.reportSuccessEvent("unsubscribe");
      } catch (error) {
        console.error(`unsubscribe ${remoteStream.getUserId()} error`, error);
        console.log(`unsubscribe ${remoteStream.getUserId()} failed!`);
        this.reportFailedEvent("unsubscribe", error);
      }
    },

    async leave(reason = 0) {
      console.log("leave1");
      // if (!this.isJoined || this.isLeaving) {
      //   return;
      // }
      console.log("leave11");
      this.isLeaving = true;
      // this.client && this.destroyLocalStream();

      try {
        console.log("leave");
        this.client && (await this.client.exitRoom());
        this.isLeaving = false;
        this.isJoined = false;
        this.client && this.destroyLocalStream();
        // this.reportSuccessEvent("leaveRoom");
      } catch (error) {
        this.isLeaving = false;
        console.error("leave room error", error);
        console.error(`Leave room failed. Error: ${error.message}`);
        // this.reportFailedEvent("leaveRoom", error);
        throw error;
      }
    },

    // 关闭视频
    muteVideo() {
      console.log("关闭视频");
      // config.userAvatar
      this.client &&
        this.client.updateLocalVideo({ mute: true }).catch((err) => {
          console.log(err);
        });
    },

    // 关闭音频
    muteAudio() {
      this.client &&
        this.client
          .updateLocalAudio({ mute: true, earMonitorVolume: 0 })
          .catch((err) => {
            console.log(err);
          });
    },

    // 打开视频
    unmuteVideo() {
      this.client &&
        this.client.updateLocalVideo({ mute: false }).catch((err) => {
          console.log(err);
        });
    },

    // 打开音频
    unmuteAudio() {
      this.client &&
        this.client
          .updateLocalAudio({ mute: false, earMonitorVolume: 0 })
          .catch((err) => {
            console.log(err);
          });
    },
    switchDevice(type, deviceId) {
      console.log(type, deviceId);
      try {
        if (this.client) {
          if (type === "audio") {
            this.client.updateLocalAudio({
              option: { microphoneId: deviceId, earMonitorVolume: 0 },
            });
          } else if (type === "video") {
            this.client.updateLocalVideo({
              option: { cameraId: deviceId },
            });
          } else if (type === "speaker") {
            if (deviceId) {
              this.client.setCurrentSpeaker({
                speakerId: deviceId,
              });
            }
          }

          console.log(`Switch ${type} device success.`);
        }
      } catch (error) {
        console.error("switchDevice failed", error);
        console.log(`Switch ${type} device failed.`);
      }
    },

    startGetAudioLevel() {
      this.client.on("audio-volume", (event) => {
        event.result.forEach(({ userId, audioVolume }) => {
          if (audioVolume >= 0.02) {
            // console.log(`user ${this.userId} is speaking ${audioVolume}`);
            this.$store.dispatch("UPDATE_AUDIOVOLUME", audioVolume);
          } else {
            this.$store.dispatch("UPDATE_AUDIOVOLUME", 0);
          }
        });
      });
      this.client.enableAudioVolumeEvaluation(200);
    },
    // 开始本地流获取音量
    startGetLocalAudioLevel() {
      this.audioLevelInterval = setInterval(() => {
        const level =
          this.localStream && this.localStream.getAudioLevel() * 100;
        if (level >= 0.02) {
          console.log(`user ${this.userId} is speaking ${level}`);
          this.$store.dispatch("UPDATE_AUDIOVOLUME", level);
        } else {
          this.$store.dispatch("UPDATE_AUDIOVOLUME", 0);
        }
      }, 200);
    },
    // 停止获取本地流音量
    stopGetAudioLevel() {
      this.client && this.client.enableAudioVolumeEvaluation(-1);

      this.audioLevelInterval && clearInterval(this.audioLevelInterval);
      this.$store.dispatch("UPDATE_AUDIO_LEVEL", 0);
    },
    // 播放流
    playStream(stream, dom) {
      stream.play(dom).catch();
    },
    // 获取设备头设备列表
    async getCameras() {
      return await TRTC.getCameraList();
    },

    // 获取麦克风设备列表
    async getMicrophones() {
      return await TRTC.getMicrophoneList();
    },

    // 获取扬声器设备列表
    async getSpeakers() {
      return await TRTC.getSpeakerList();
    },

    playRemoteList() {
      this.remoteList.forEach((item) => {
        const view = `${item.userId}`;
        if (item.streamType === "main") {
          this.client &&
            this.client.startRemoteVideo({
              userId: item.userId,
              streamType: item.streamType,
              view,
            });
        }
      });
    },
    stromRemote(userId) {
      const view = `${userId}`;
      this.client &&
        this.client
          .startRemoteVideo({
            userId: userId,
            streamType: "main",
            view,
          })
          .catch((err) => {
            console.log(err, "视频无法播放");
          });
    },
    // ai降噪
    async startAiNlp() {
      await this.client.startPlugin("AIDenoiser", {
        assetsPath: "@/assets/denoiser-wasm.js", // 例：denoiser-wasm.js 文件存放在 assets 目录下
        sdkAppId: this.config.sdkAppId,
        userId: this.config.userId,
        userSig: this.config.userSig,
      });
    },
    // 关闭降噪
    async stopAiNlp() {
      await this.client.stopPlugin("AIDenoiser");
    },
    handleClientEvents() {
      console.log("handleClientEvents");
      this.client.on("error", (error) => {
        console.error(error);
        // alert(error);
        Toast("检测到系统错误，请重新进入直播间");
        // this.back();
      });
      this.client.on(TRTC.EVENT.AUDIO_VOLUME, (event) => {
        event.result.forEach(({ userId, volume }) => {
          const isMe = userId === ""; // 当 userId 为空时，代表本地麦克风音量。
          //   console.log(`user: ${userId} volume: ${volume}`)
          if (isMe) {
            // console.log(`my volume: ${volume}`);
            this.$store.dispatch("UPDATE_AUDIO_LEVEL", volume);
          } else {
            console.log(`user: ${userId} volume: ${volume}`);
            this.remoteList.forEach((item) => {
              if (item.userId === userId) {
                item.volume = volume;
              }
            });
          }
        });
      });
      this.client.enableAudioVolumeEvaluation(200);
      // 收到远端视频流
      this.client.on(
        TRTC.EVENT.REMOTE_VIDEO_AVAILABLE,
        ({ userId, streamType }) => {
          const remoteStreamIndex = this.remoteStreamList.findIndex(
            (item) => item.userId === userId
          );
          if (remoteStreamIndex === -1) {
            this.remoteStreamList.push({
              userId,
              streamType,
              receiveAudio: true, // 麦克风
              receiveVideo: true, // 摄像头
              hasLimit: false,
              onStage: true, // 上台状态
              student_microphone_status: true, // 老师介入的权限
              camera_status: true,
              raiseHand: false, // 举手
              student_role_status: false, //白板权限
              cupNumber: 0,
              userName: "",
              volume: 0,
              isShow: true,
              reasureCount: false,
              type: "farend",
            });
          }
          // 开启音量回调，并设置每 1000ms 触发一次事件
          this.client.enableAudioVolumeEvaluation(1000);
          // 为了播放视频画面，您需在 DOM 中放置一个 HTMLElement，可以是一个 div 标签，假设其 id 为 `${userId}_${streamType}`
          if (streamType === "main" && document.getElementById(userId)) {
            // setTimeout(() => {
            this.$nextTick(() => {
              const view = `${userId}`;
              this.client
                .startRemoteVideo({
                  userId,
                  streamType,
                  view,
                  option: {
                    samall: true,
                    receiveWhenViewVisible: true,
                    viewRoot: this.$refs.containers,
                  },
                })
                .catch((error) => {
                  console.error("startRemoteVideo error", error);
                });
            });
            // }, 1000);
            this.remoteList.find(
              (item) => item.userId === userId
            ).receiveVideo = true;
          }
          console.log(this.remoteStreamList, "alsjdhoaisbdljashdl");
          // trtc.startRemoteVideo({userId, streamType, view});

          // 删除屏幕分享需求
          // else {
          //   console.log('辅流')
          //     // 辅路视频流，一般是推屏幕分享的那路流。
          //     // 1. 在页面中放置一个 id 为 `${userId}_screen` 的 div 标签，用于在 div 标签内播放屏幕分享。业务侧可自定义 div 标签的 id，此处只是举例说明。
          //     // 2. 播放屏幕分享
          //     this.$store.dispatch("UPDATE_IS_SCREEN_SHARING", true);
          //     setTimeout(() => {
          //       this.client.startRemoteVideo({ userId, streamType, view: `${userId}_screen` });
          //     }, 500);

          // }
        }
      );
      // 在进房前监听 麦克风开启
      this.client.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, (event) => {
        const userId = event.userId;
        const streamType = event.streamType;
        const remoteStreamIndex = this.remoteStreamList.findIndex(
          (item) => item.userId === userId
        );
        if (remoteStreamIndex === -1) {
          this.remoteStreamList.push({
            userId,
            streamType,
            receiveAudio: true, // 麦克风
            receiveVideo: true, // 摄像头
            hasLimit: false,
            onStage: true, // 上台状态
            student_microphone_status: true, // 老师介入的权限
            camera_status: true,
            raiseHand: false, // 举手
            student_role_status: false, //白板权限
            cupNumber: 0,
            userName: "",
            volume: 0,
            isShow: true,
            reasureCount: false,
            type: "farend",
          });
        }
        this.remoteList.find(
          (item) => item.userId === userId
        ).receiveAudio = true;
      });
      this.client.on(TRTC.EVENT.KICKED_OUT, (error) => {
        console.error(
          `kicked out, reason:${error.reason}, message:${error.message}`
        );
        // error.reason 有以下几种情况
        // 'kick' 由于相同 userId 进相同房间，导致先进入的用户被踢。
        // 'banned' 被管理员移出房间
        // 'room-disband' 管理员解散了房间
        this.tick = true;
        if (error.reason === "kick") {
          // 用户被踢，请做相关处理
          // this.$bus.emit("outRoomDiag", "多端登陆账号");
        } else if (error.reason === "banned") {
          // 用户被管理员移出房间，请做相关处理
          this.$bus.emit("outRoomDiag", "你已被管理员移出房间");
        } else if (error.reason === "room_disband") {
          // 管理员解散了房间，请做相关处理
          // this.config.userSig = "";
          // this.roomTop = false;
          // this.$bus.emit("outRoomDiag", "课节已下课");
        }
      });
      // 当远程流被删除时触发，例如远程用户调用Client.unpublish
      this.client.on(TRTC.EVENT.REMOTE_USER_EXIT, async (event) => {
        console.log("remote user exsadklnljabs,nmbxz,cnmit", event);
        // const { stream: remoteStream } = event;
        const { userId } = event;
        // remoteStream.stop();
        // const index = this.remoteStreamList.indexOf(remoteStream);
        const index = this.remoteStreamList.findIndex(
          (item) => item.userId === userId
        );
        if (index >= 0) {
          this.remoteStreamList.splice(index, 1);
        }
      });

      this.client.on("stream-updated", (event) => {
        const { stream: remoteStream } = event;
        console.log(
          `type: ${remoteStream.getType()} stream-updated hasAudio: ${remoteStream.hasAudio()} hasVideo: ${remoteStream.hasVideo()}`
        );
        console.log(
          `RemoteStream updated: [${remoteStream.getUserId()}] audio:${remoteStream.hasAudio()}, video:${remoteStream.hasVideo()}.`
        );
      });

      this.client.on("mute-audio", (event) => {
        const { userId } = event;
        console.log(`${userId} mute audio`);
        console.log(`[${event.userId}] mute audio.`);
      });
      this.client.on("unmute-audio", (event) => {
        const { userId } = event;
        console.log(`${userId} unmute audio`);
        console.log(`[${event.userId}] unmute audio.`);
      });
      this.client.on("mute-video", (event) => {
        const { userId } = event;
        console.log(`${userId} mute video`);
        console.log(`[${event.userId}] mute video.`);
      });
      this.client.on("unmute-video", (event) => {
        const { userId } = event;
        console.log(`${userId} unmute video`);
        console.log(`[${event.userId}] unmute video.`);
      });

      this.client.on("connection-state-changed", (event) => {
        console.log(
          `RtcClient state changed to ${event.state} from ${event.prevState}`
        );
      });

      this.client.on("network-quality", (event) => {
        const { uplinkNetworkQuality, downlinkNetworkQuality } = event;
        console.log(
          `network-quality uplinkNetworkQuality: ${uplinkNetworkQuality}, downlinkNetworkQuality: ${downlinkNetworkQuality}`
        );
      });
      // 监听屏幕分享停止
      this.client.on(TRTC.EVENT.SCREEN_SHARE_STOPPED, () => {
        console.log("screen sharing was stopped");
        this.$store.dispatch("UPDATE_IS_SCREEN_SHARING", false);
      });
      this.client.on(TRTC.EVENT.PUBLISH_STATE_CHANGED, (event) => {
        console.log(
          `${event.mediaType} ${event.state} ${event.reason}slajdfljashdf`
        );
      });
      this.client.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, (event) => {
        console.log(event);
        const userId = event.userId;
        if (event.streamType === "main") {
          this.remoteList.find(
            (item) => item.userId === userId
          ).receiveVideo = false;
        }
      });
      this.client.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
        const userId = event.userId;
        // this.remoteList.find(
        //   (item) => item.userId === userId
        // ).receiveAudio = false;
      });
      this.client.on(TRTC.CONNECTION_STATE_CHANGED, (event) => {
        const prevState = event.prevState;
        const curState = event.state;
        console.log(
          `prevState = ${prevState}, curState = ${curState} 连接状态`
        );
      });
      this.client.on(TRTC.EVENT.ERROR, (error) => {
        // alert('发生错误', errors);
        console.log("trtc发生错误请重试-------", error);
        // 尝试重新进入
        this.handleEnder();
      });
      this.client.on(TRTC.EVENT.ERROR, (error) => {
        if (error.code === TRTC.ERROR_CODE.DEVICE_ERROR) {
          // 摄像头恢复采集失败
          if (error.extraCode === 5308) {
            // 引导用户检查设备后，调用 updateLocalVideo 传入 cameraId 重新采集。
            // trtc.updateLocalVideo({ option: { cameraId: '' }});
            this.switchDevice("video", this.activeCameraId);
          }
          // 麦克风恢复采集失败
          if (error.extraCode === 5309) {
            // 引导用户检查设备后，调用 updateLocalAudio 传入 microphoneId 重新采集。
            // trtc.updateLocalAudio({ option: { microphoneId: '' }});
            this.switchDevice("audio", this.activeMicrophoneId);
          }
        }
      });
      this.client.on(TRTC.EVENT.VIDEO_PLAY_STATE_CHANGED, (event) => {
        // 本地摄像头采集异常，此时 SDK 会尝试自动恢复采集，您可以引导用户检查摄像头是否正常。
        if (
          event.userId === "" &&
          event.streamType === TRTC.TYPE.STREAM_TYPE_MAIN &&
          (event.reason === "mute" || event.reason === "ended")
        ) {
          // 引导用户检查摄像头是否正常。
          // Toast("摄像头异常，请检查摄像头是否正常");
        }
      });
      this.client.on(TRTC.EVENT.AUDIO_PLAY_STATE_CHANGED, (event) => {
        // 本地麦克风采集异常，此时 SDK 会尝试自动恢复采集，您可以引导用户检查麦克风是否正常。
        if (
          event.userId === "" &&
          (event.reason === "mute" || event.reason === "ended")
        ) {
          // Toast("摄像头异常，请检查摄像头是否正常");
        }
      });
      // 插拔行为
      this.client.on(TRTC.EVENT.DEVICE_CHANGED, (event) => {
        // 设备插入
        if (event.action === "add") {
          if (event.type === "camera") {
            // 插入摄像头后，自动切换到新的摄像头
            console.log("插入摄像头后，自动切换到新的摄像头");
            this.switchDevice("video", event.device.deviceId);
            this.$store.dispatch("UPDATE_ACTIVE_CAMERA", event.device.deviceId);
          } else if (event.type === "microphone") {
            console.log(event.device, "device");
            this.switchDevice("audio", event.device.deviceId);
            this.$store.dispatch(
              "UPDATE_ACTIVE_MICROPHONE",
              event.device.deviceId
            );
          } else if (event.type === "speaker") {
            this.switchDevice("speaker", event.device.deviceId);
            this.$store.dispatch(
              "UPDATE_ACTIVE_SPEAKER",
              event.device.deviceId
            );
          }
        } else if (event.action === "remove") {
          if (event.type === "camera") {
            const currentCameraId = this.client
              .getVideoTrack()
              ?.getSettings()?.deviceId;
            if (event.device.deviceId === currentCameraId) {
              // 当前正在使用的摄像头被拔出。
              console.log("当前正在使用的摄像头被拔出。");
            }
          } else if (event.type === "microphone") {
            const currentMicId = this.client
              .getAudioTrack()
              ?.getSettings()?.deviceId;
            console.log(microphone, "qwe");
            if (event.device.deviceId === currentMicId) {
              // 当前正在使用的麦克风被拔出。
              console.log("当前正在使用的麦克风被拔出。");
            }
          } else if (event.type === "speaker") {
            const currentSpeakerId = this.getSpeakers()[0].deviceId;
            // console.log(currentSpeakerId,'asdlkjs,mdcn')
            this.switchDevice("speaker", currentSpeakerId);
          }
        }
        console.log(`${event.type}(${event.device.label}) ${event.action}`);
      });
    },
  },
  mounted() {
    this.$bus.on("stromRemote", this.stromRemote);

    // 移动端视频播放优化
    this.initMobileVideoSupport();

    TRTC.isSupported().then((checkResult) => {
      // 不支持使用 SDK，引导用户使用最新版的 Chrome 浏览器。
      if (!checkResult.result) {
        console.log("不支持使用 SDK，引导用户使用最新版的 Chrome 浏览器。");
      }
      // 不支持发布视频
      if (!checkResult.detail.isH264EncodeSupported) {
        console.log("不支持发布视频");
      }
      // 不支持拉视频
      if (!checkResult.detail.isH264DecodeSupported) {
        console.log("不支持拉视频");
      }
    });
  },
};
