// WebSocket
import config from "@/config";
import storage from "@/public/storage.js";
import { Toast } from "mint-ui";
export default {
  data() {
    return {
      socket: null,
      lockReconnect: false, //是否真正建立连接
      timeout: 5 * 1000, //5秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      // 连接次数
      connectCount: 0,
      socketStuts: true,
      socketRoor: true,
      // 超时时间
      heartTime: 9 * 1000,
    };
  },
  mounted() {
    // this.addNetworkListeners();
  },
  methods: {
    addNetworkListeners() {
      window.addEventListener("online", this.handleOnline, true);
      window.addEventListener("offline", this.handleOffline);
    },
    handleOnline() {
      this.socketStuts = true;
      console.log("重新连接");
      this.init();
    },
    handleOffline() {
      this.socketStuts = false;
      this.clearTimers();
      this.closeSocket();
      console.log("断开连接");
    },
    init() {
      const path =
        this.config.wsliveBaseUrl +
        `?live_token=${storage.$getStroage("liveToken")}&room_id=${
          this.config.roomId
        }`;
      if (typeof WebSocket === "undefined") {
        console.log("不支持socket");
      } else {
        // 实例化socket
        this.socket = new WebSocket(path);
        // 监听socket连接
        this.socket.onopen = this.open;
        // 监听socket错误信息
        this.socket.onerror = this.error;
        // 监听socket消息
        this.socket.onmessage = this.getMessage;
        //连接关闭
        this.socket.onclose = this.close;
        this.socketStuts = true;
      }
    },
    open() {
      //连接成功事件
      this.send(
        JSON.stringify({
          type: "register",
        })
      );
      this.send(
        JSON.stringify({
          type: "enter_send_question",
        })
      );
      //提示成功
      console.log("连接成功");
      this.enterRoomStatusText = "进入房间中...";
      // 进入直播间注册
      if (!this.liveType && this.socketRoor) {
        this.handleJoinRoom();
        this.socketStuts = true;
      }
      this.socketRoor = false;
      // 当连接成功后才可以进入直播间
      //开启心跳
      this.start();
    },
    error() {
      this.socketRoor = false;
      console.log("连接错误");
      this.$refs.loading.onHide();
      //重连
      this.reconnect();
    },
    async getMessage(msg) {
      let data = JSON.parse(msg.data);
      let dataObj = data.content;
      // 重置超时时间
      this.timeoutHandler();
      this.connectCount = 0;
      // 判断类型
      switch (data.type) {
        // case "heartbeat":
        //   //收到服务器信息，心跳重置
        //   this.reset();
        //   break;
        case "control_student":
          dataObj = JSON.parse(dataObj.body);
          // 解析数据
          //1 老师麦克风 3 台上学生麦克分禁言和开启、4、台上学生白板权限开启关闭  5、摄像头权限 6、全员下台
          if (dataObj.operate_type == 1) {
            this.remoteList.find(
              (item) => item.userId === this.doneTeacherId
            ).receiveAudio = dataObj.status;
          } else if (dataObj.operate_type == 4) {
            // if (dataObj.student_ids.includes(this.config.userId)) {
            //   this.$store.dispatch("UPDATE_DRAW_PERMISSION", dataObj.status);
            // }
            dataObj.student_ids.find((item) => {
              if (item == this.config.userId) {
                this.$store.dispatch("UPDATE_DRAW_PERMISSION", dataObj.status);
              } else {
                try {
                  this.remoteList.filter(
                    (items) => items.userId == item
                  )[0].student_role_status = dataObj.status;
                } catch (err) {
                  console.log(err);
                }
              }
            });
          } else if (dataObj.operate_type == 3) {
            dataObj.student_ids.find((item) => {
              if (item == this.config.userId) {
                this.$store.dispatch(
                  "UPDATE_TEACHER_PERMISSION",
                  dataObj.status
                );
                // this.$store.dispatch('UPDATE_VIDEO_STATE',dataObj.status);
                if (dataObj.status) {
                  // 话筒静音
                  this.setUpdateLocalAudio(false);
                } else {
                  this.setUpdateLocalAudio(true);
                }
              } else {
                // console.log(this.remoteList.filter(item =>dataObj.student_ids.filter(i => item.userId == i)),'asdasdasdsad');
                try {
                  this.remoteList.filter(
                    (items) => items.userId == item
                  )[0].student_microphone_status = dataObj.status;
                } catch (err) {
                  console.log(err);
                }
              }
            });
          } else if (dataObj.operate_type == 5) {
            // 摄像头
            dataObj.student_ids.find((item) => {
              if (item == this.config.userId) {
                this.$store.dispatch(
                  "UPDATE_CAMERA_PERMISSION",
                  dataObj.status
                );
                // this.$store.dispatch('UPDATE_VIDEO_STATE',dataObj.status);
                if (dataObj.status) {
                  // 关闭摄像头
                  this.setUpdateLocalVideo(false);
                } else {
                  this.setUpdateLocalVideo(true);
                }
              } else {
                // console.log(this.remoteList.filter(item =>dataObj.student_ids.filter(i => item.userId == i)),'asdasdasdsad');
                try {
                  this.remoteList.filter(
                    (items) => items.userId == item
                  )[0].receiveVideo = dataObj.status;
                  this.adjustStage();
                } catch (err) {
                  console.log(err);
                }
              }
            });
          } else if (dataObj.operate_type == 6) {
            // 全员下台
            if (dataObj.student_ids.includes(this.config.userId)) {
              this.$store.dispatch("UPDATE_STAGE_STATUS", dataObj.status);
              this.unPublish();
              setTimeout(() => {
                this.$store.dispatch(
                  "UPDATE_STAGE_STATUS",
                  dataObj.up_status == 1 ? true : false
                );
              }, 600);
              this.adjustStage();
              // const index = this.remoteList.findIndex((item) => item.userId === "localStream");
              // if (index >= 0) {
              //   this.remoteList.splice(index, 1);
              // }
            }
          } else if (dataObj.operate_type == 2) {
            console.log(this.doneTeacherId);
            // this.remoteList.filter(
            //   (items) => items.userId == this.doneTeacherId
            // )[0].receiveAudio = dataObj.status;
            this.remoteList.filter(
              (items) => items.userId == this.doneTeacherId
            )[0].student_camera_status = dataObj.status;
            console.log(this.remoteList);
          }
          break;
        case "send_question":
          // if(dataObj.operate_type){
          // 题库
          // if(this.config.userId == dataObj.student_id){
          if (dataObj.body != "") {
            this.$store.dispatch(
              "UPDATE_CHESS_BOARD_PARAM",
              JSON.parse(dataObj.body).question_id
            );
            this.$store.dispatch(
              "UPDATE_LAST_ANSWER",
              JSON.parse(dataObj.body).answer
            );
            this.$store.dispatch(
              "UPDATE_QUESTION_TIME",
              JSON.parse(dataObj.body).after_time
            );
            this.$store.dispatch("UPDATE_CHESS_BOARD_STATUS", true);
          }
          // }
          break;
        case "raise_hand":
          // 举手
          if (data.content.body == "") {
            return;
          }
          dataObj = JSON.parse(data.content.body);
          if (dataObj.data.status) {
            try {
              this.remoteList.filter(
                (item) => item.userId == dataObj.data.student_id
              )[0].raiseHand = true;
            } catch (err) {}
          }
          break;
        case "teacher_end_question":
          dataObj = JSON.parse(data.content.body);
          if (dataObj.end_status == "2") {
            this.$store.dispatch("UPDATE_CHESS_BOARD_STATUS", false);
          }
          break;
        case "trophy":
          // 奖杯
          dataObj = JSON.parse(data.content.body);
          this.$store.dispatch("UPDATE_TREASURE_STATUS", true);
          let is_first = dataObj.is_first;
          if (dataObj.list) {
            dataObj.list.find((item) => {
              if (item.student_id == this.config.userId && !is_first) {
                this.$nextTick(() => {
                  this.$refs.trophyCom.onShow();
                  this.$store.dispatch(
                    "UPDATE_GET_TREASURE_COUNT",
                    item.trophy_num
                  );
                });
              }
              // 所有人视频框都有奖杯 包括自己
              try {
                this.remoteList.filter(
                  (items) => items.userId == item.student_id
                )[0].cupNumber = item.trophy_num;
              } catch (err) {
                console.log(err);
              }
            });
          }
          break;
        case "lotto_result":
          // 中奖名单
          dataObj = JSON.parse(data.content.body);
          this.winListId = dataObj.lottos ?? [];
          this.viewsId = dataObj.views ?? [];
          this.$nextTick(() => {
            // this.$refs.callRollDialogs.handStart();
            this.$bus.emit("handStart");
            this.$refs.callRollDialogs.checkId = this.winListId[0];
          });
          break;
        case "lotto_list":
          dataObj = JSON.parse(data.content.body);
          this.lotteryList = dataObj ?? [];
          this.callRollDialogVisible = true;
          break;
        case "lotto_start":
          dataObj = JSON.parse(data.content.body);
          this.lotteryList = dataObj.list ?? [];
          this.winListId = dataObj.lottos ?? [];
          this.viewsId = dataObj.views ?? [];
          this.callRollDialogVisible = true;
          this.$nextTick(() => {
            setTimeout(() => {
              if (this.$refs.callRollDialogs) {
                // this.$refs.callRollDialogs.handStart();
                this.$refs.callRollDialogs.checkId = this.winListId[0];
                this.$refs.callRollDialogs.text = "开始";
                this.$bus.emit("handStart", "开始");
              }
            }, 500);
          });
          break;
        case "lotto_stop":
          dataObj = JSON.parse(data.content.body);
          if (dataObj.lotto_type == "close") {
            // 关闭点名器
            this.callRollDialogVisible = false;
          } else {
            // 结束抽奖

            this.$nextTick(() => {
              this.lotteryList = dataObj.data.list ?? [];
              this.winListId = dataObj.data.lottos ?? [];
              this.viewsId = dataObj.data.views ?? [];
              this.callRollDialogVisible = true;

              setTimeout(() => {
                if (this.$refs.callRollDialogs) {
                  this.$refs.callRollDialogs.checkId = this.winListId[0];
                  this.$refs.callRollDialogs.text = "暂停";
                  this.$bus.emit("handStart", "暂停");
                }
                // this.$bus.emit("handStart");
              }, 500);
            });
          }
          break;
        case "up_and_down":
          await this.adjustStage();
          // 老师控制上下台
          if (data.content.body != "") {
            dataObj = JSON.parse(data.content.body ?? []);
            if (dataObj.student_ids.find((i) => i == this.config.userId)) {
              // if(this.doneStandard !== 2 && this.$refs.loading){
              //   this.liveType = true;
              //   this.$refs.loading.onHide();
              //   await this.join();
              // }
              // await this.unPublish();
              if (dataObj.up_status == 1) {
                this.$nextTick(async () => {
                  // this.$refs.videoWalls.onHide();
                  this.client && (await this.client.stopLocalVideo());
                  await this.startPublish();
                });
              } else {
                await this.unPublish();
              }
              // 刚进来的时候 看是否有老师权限和自己的
              if (!this.camera_status || !this.doneCameraAuthority) {
                // this.$store.dispatch("UPDATE_VIDEO_STATE", false);
                this.muteVideo();
              } else if (!this.microphone_status || !this.doneTeacher) {
                this.muteAudio();
              }
              setTimeout(() => {
                this.$nextTick(async () => {
                  this.$store.dispatch(
                    "UPDATE_STAGE_STATUS",
                    dataObj.up_status == 1 ? true : false
                  );
                });
                // if(this.doneStandard == 2){
                this.$refs.videoWalls && this.$refs.videoWalls.onShow();
                // }
              }, 600);
            }
          }
          break;
        case "comment":
          // 禁言
          dataObj = JSON.parse(data.content.body);
          if (dataObj.comment_type === "single") {
            if (dataObj.student_id == this.config.userId) {
              this.$store.dispatch("UPDATE_MUTE_STATUS", dataObj.status);
            }
          } else {
            this.$store.dispatch("UPDATE_ALL_MUTE_STATUS", dataObj.status);
            this.$store.dispatch("UPDATE_MUTE_STATUS", dataObj.status);
          }

          break;
        case "send_question_machine":
          // 答题器
          dataObj = JSON.parse(data.content.body);
          this.$store.dispatch("UPDATE_ANSWER_STATUS", true);
          this.$store.dispatch("UPDATE_ANSWER_OPTION", dataObj);
          break;
        case "teacher_end_machine":
          this.$store.dispatch("UPDATE_ANSWER_STATUS", false);
          break;
        case "white_board":
          // 老师的白板尺寸同步学生端
          dataObj = JSON.parse(data.content.body);
          // 设置白板尺寸
          this.$store.dispatch("UPDATE_WHITE_BOARD_SIZE", dataObj.size);
          break;
        case "rotation":
          // 轮播状态
          dataObj = JSON.parse(data.content.body);
          // 设置轮播状态
          this.$store.dispatch("UPDATE_ROTATION_STATUS", dataObj.status);
          break;
        case "course_end":
          // 下课
          this.config.userSig = "";
          this.roomTop = false;
          this.outRoomDiag("课节已下课", true);
          this.lockReconnect = true;
          this.clearTimers();
          this.socket.close();
          break;
        case "teacher_coordinate_move":
          this.$nextTick(() => {
            this.$bus.emit("receive_teacher_coordinate_move", {
              top: JSON.parse(data.content.body).top,
              left: JSON.parse(data.content.body).left,
              with: JSON.parse(data.content.body).with,
              height: JSON.parse(data.content.body).height,
              window_height: JSON.parse(data.content.body).window_height,
              window_width: JSON.parse(data.content.body).window_width,
              teacher_camera_move: JSON.parse(data.content.body)
                .teacher_camera_move,
            });
          });
          break;
        // 麦克风状态
        case "change_owner_status":
          dataObj = JSON.parse(data.content.body);
          try {
            if (dataObj.operate_type === 1) {
              this.remoteList.filter(
                (items) => items.userId == dataObj.student_id
              )[0].receiveAudio = dataObj.status;
            } else {
              this.remoteList.filter(
                (items) => items.userId == dataObj.student_id
              )[0].receiveVideo = dataObj.status;
            }
          } catch (e) {
            console.log(e);
          }
          break;
        case "view_board":
          dataObj = JSON.parse(data.content.body);
          if (this.config.userId == dataObj.student_id) {
            this.$store.dispatch("UPDATE_LOOK_CAMERA", dataObj.status);
            if (dataObj.status) {
              // this.startLocalVideo("lookLocalStream",true)
              await this.setUpdateLocalVideo(false, "lookLocalStream");
              //  setTimeout(() => {
              // if(!this.isVideoMuted || !this.doneCameraAuthority){
              //   this.muteVideo("lookLocalStream");
              // }else{
              //   this.unmuteVideo("lookLocalStream");
              // }
              // },500)
            } else {
              this.client &&
                this.client.updateLocalVideo({
                  publish: false,
                  dom: "lookLocalStream",
                });
            }
          }
          break;
        default:
      }
    },
    // 发送消息给被连接的服务端
    send(params) {
      this.socket.send(params);
    },
    close() {
      console.log("socket已经关闭");
      //重连
      this.reconnect();
    },
    reconnect() {
      if (!this.liveType || !this.socketStuts) return;
      //重新连接
      var that = this;
      console.log(this.connectCount, " connectCount|||||||111");
      if (that.lockReconnect || this.connectCount > 11) {
        return;
      }
      that.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多
      that.timeoutnum && clearTimeout(that.timeoutnum);
      that.timeoutnum = setTimeout(function () {
        //新连接
        // that.init();
        if (storage.$getStroage("liveToken")) {
          that.init();
        }
        that.lockReconnect = false;
      }, 5000);
      this.connectCount++;
      if (this.connectCount > 10) {
        // Toast("网络错误，请重新进入");
        this.outRoomDiag("连接中断，请重新进入", false);
        this.$refs.loading.onHide();
      }
    },
    start() {
      // 不记录服务端心跳 每5秒发送一次心跳
      var self = this;
      self.timeoutObj && clearInterval(self.timeoutObj);
      self.timeoutObj = setInterval(() => {
        // 心跳超时
        self.socket.send(
          JSON.stringify({
            type: "heartbeat",
          })
        );
      }, 5000);
      this.timeoutHandler();
    },
    timeoutHandler() {
      // 如果在9秒内 没有重置定时器 那么就判断为断开链接
      let self = this;
      self.serverTimeoutObj && clearTimeout(self.serverTimeoutObj);
      self.serverTimeoutObj = setTimeout(function () {
        //超时关闭
        console.log("前端超时 关闭");
        self.closeSocket();
        self.reconnect();
      }, self.heartTime);
    },
    clearTimers() {
      this.timeoutObj && clearInterval(this.timeoutObj);
      this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj);
      this.timeoutnum && clearTimeout(this.timeoutnum);
    },
    closeSocket() {
      this.socket && this.socket.close();
    },
  },
  beforeDestroy() {
    this.clearTimers();
  },
};
