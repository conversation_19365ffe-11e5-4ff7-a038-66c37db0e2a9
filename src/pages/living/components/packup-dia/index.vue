<template>
  <!-- <div
    @mouseover="butShow = true"
    @mouseout="butShow = false"
    :class="butShow ? 'conit':'active_conit conit'"
    :style="{
      'background-image': `url(${require('@/assets/living/isCloe.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="conit_flex">
      <img
        :class="doneChat ? 'active' : ''"
        src="@/assets/living/liaotian.png"
        alt=""
        @click="$emit('openChat')"
      />
    </div>
  </div> -->
  <!-- <div class="conit" @mouseover="butShow = true" @mouseout="butShow = false">
    <img src="@/assets/living/Group 348.png" alt="" class="left_active" v-if="butShow"/>
    <img src="@/assets/living/isCloe.png" alt="" class="bg" />
    <img src="@/assets/living/liaotian.png" alt="" class="lt"  @click="$emit('openChat')"/>
  </div> -->
  <div class="tool_wrap" :style="doneDrawingAuthority ? 'top:0' : ''">
    <div class="tool_box">
      <div v-if="doneDrawingAuthority">
        <el-tooltip class="item" effect="dark" content="鼠标" placement="left">
          <div
            @mouseover="onHover(1)"
            @mouseout="hoverOut(1)"
            :style="{ backgroundColor: hoverIndex === 1 ? hoverColor : '' }"
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE
              )
            "
          >
            <img src="@/assets/living/鼠标.png" alt="" />
          </div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="选框" placement="left">
          <div
            @mouseover="onHover(2)"
            @mouseout="hoverOut(2)"
            :style="{ backgroundColor: hoverIndex === 2 ? hoverColor : '' }"
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT_SELECT
              )
            "
          >
            <img src="@/assets/living/框选.png" alt="" />
          </div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="画笔" placement="left">
          <div
            @mouseover="onHover(3)"
            @mouseout="hoverOut(3)"
            :style="{ backgroundColor: hoverIndex === 3 ? hoverColor : '' }"
            @click="
              setPenToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN
              )
            "
          >
            <img src="@/assets/living/画笔.png" alt="" />
          </div>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="橡皮擦"
          placement="left"
        >
          <div
            @mouseover="onHover(4)"
            @mouseout="hoverOut(4)"
            :style="{ backgroundColor: hoverIndex === 4 ? hoverColor : '' }"
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ERASER
              )
            "
          >
            <img src="@/assets/living/橡皮擦.png" alt="" />
          </div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="文字" placement="left">
          <div
            @mouseover="onHover(5)"
            @mouseout="hoverOut(5)"
            :style="{ backgroundColor: hoverIndex === 5 ? hoverColor : '' }"
            @click="
              setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_TEXT)
            "
          >
            <img src="@/assets/living/文字.png" alt="" />
          </div>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="激光笔"
          placement="left"
        >
          <div
            @mouseover="onHover(6)"
            @mouseout="hoverOut(6)"
            :style="{ backgroundColor: hoverIndex === 6 ? hoverColor : '' }"
            @click="
              setToolType(
                TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LASER
              )
            "
          >
            <img src="@/assets/living/激光笔.png" alt="" />
          </div>
        </el-tooltip>
        <!-- <el-tooltip class="item" effect="dark" content="清除画板" placement="left">
          <div
            @mouseover="onHover(7)"
            @mouseout="hoverOut(7)"
            :style="{ backgroundColor: hoverIndex === 7 ? hoverColor : '' }"
            @click="cleanAll()"
          >
            <img src="@/assets/living/清除.png" alt="" />
          </div>
        </el-tooltip> -->
        <!-- <el-tooltip class="item" effect="dark" content="课件" placement="left">
          <div
            @mouseover="onHover(8)"
            @mouseout="hoverOut(8)"
            :style="{ backgroundColor: hoverIndex === 8 ? hoverColor : '' }"
          >
            <img src="@/assets/living/课件.png" alt="" />
          </div>
        </el-tooltip> -->
        <el-tooltip class="item" effect="dark" content="截图" placement="left">
          <div
            @mouseover="onHover(9)"
            @mouseout="hoverOut(9)"
            :style="{ backgroundColor: hoverIndex === 9 ? hoverColor : '' }"
            @click="onSnapshot({ full: false })"
          >
            <img src="@/assets/living/截图.png" alt="" />
          </div>
        </el-tooltip>
      </div>
      <el-tooltip class="item" effect="dark" content="对话框" placement="left">
        <div
          @mouseover="onHover(10)"
          @mouseout="hoverOut(10)"
          :style="{ backgroundColor: hoverIndex === 10 ? hoverColor : '' }"
          @click="chat()"
        >
          <img src="@/assets/living/对话.png" alt="" />
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import { execFile } from "child_process";
// 白板
export default {
  name: "packup-dia",
  props: {
    doneChat: Boolean,
  },
  components: {},
  data() {
    return {
      butShow: false,
      currentToolType: TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE,
      toolbarClassName: "",
      dialog: false,
      thin: 50,
      eraserSize: 16,
      fontSize: 320,
      lineHeight: 1,
      hoverIndex: 0,
      hoverColor: "",
      // 只有该值才不会触发初次打开回调
      lineHeightItems: [
        // 1、1.15、1.3、1.5、2、3
        {
          text: "1",
          value: 1,
        },
        {
          text: "1.15",
          value: 1.15,
        },
        {
          text: "1.3",
          value: 1.3,
        },
        {
          text: "1.5",
          value: 1.5,
        },
        {
          text: "2",
          value: 2,
        },
        {
          text: "3",
          value: 3,
        },
      ],
      uploadTypeList: {
        courseware: {
          title: "课件资源",
        },
        media: {
          title: "多媒体资源",
        },
        background: {
          title: "背景资源",
        },
        element: {
          title: "白板元素",
        },
        watermark: {
          title: "水印元素",
        },
      },

      customGraphImages: [
        "https://test-1259648581.file.myqcloud.com/%E4%B8%89%E8%A7%92%E5%BD%A2.svg",
        "https://test-1259648581.file.myqcloud.com/%E6%8A%9B%E7%89%A9%E7%BA%BF_parabolic9.svg",
        "https://test-1259648581.file.myqcloud.com/%E8%8F%B1%E5%BD%A2.svg",
        "https://test-1259648581.file.myqcloud.com/%E6%B1%BD%E8%BD%A6.svg",
        "https://test-1259648581.file.myqcloud.com/%E7%83%A7%E6%9D%AF.svg",
        "https://test-1259648581.file.myqcloud.com/%E7%A3%81%E9%93%81.svg",
      ],
      colorTask: null, // 设置背景颜色的任务
    };
  },
  // 计算属性
  computed: {
    doneDrawingAuthority() {
      return this.$store.getters.doneDrawingAuthority;
    },
  },
  // 侦听器
  watch: {
    "$store.state.isTiwReady"(value) {
      if (value) {
        const teduBoard = window.teduBoard;
        // 监听元素位置更新事件
        teduBoard.on(
          TEduBoard.EVENT.TEB_BOARD_ELEMENT_POSITION_CHANGE,
          (data, status) => {
            if (
              status ===
              TEduBoard.TEduBoardPositionChangeStatus
                .TEDU_BOARD_POSITION_CHANGE_START
            ) {
              // 开始变化
              this.toolbarClassName = "disabled-event";
            } else if (
              status ===
              TEduBoard.TEduBoardPositionChangeStatus
                .TEDU_BOARD_POSITION_CHANGE_END
            ) {
              // 结束变化
              this.toolbarClassName = "";
            }
          }
        );
        // 监听新增元素
        teduBoard.on(
          TEduBoard.EVENT.TEB_ADDELEMENT,
          ({ id, type, userData }) => {
            // (id, type, userData, title) => {
            console.log("新增元素", id, type, userData);
            if (
              this.settingItems[0].graphicAutoSelect &&
              type ===
                TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_GEOMETRY
            ) {
              window.teduBoard.autoSelectedElement(id);
            }
          }
        );
      }
    },
    doneDrawingAuthority: {
      handler(newValue, oldValue) {
        if (!newValue) {
          this.setToolType(
            TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE
          );
        }
      },
    },
  },
  mounted() {
    // this.setToolType(this.currentToolType)
  },
  methods: {
    chat() {
      // this.$bus.$emit("chat", { showCompChat: true });
      this.$emit("openChat");
    },
    onHover(index) {
      this.hoverIndex = index;
      this.hoverColor = "#489AFF";
    },
    hoverOut() {
      this.hoverIndex = 0;
    },

    showTip(tip) {
      this.$toasted.show(tip);
    },

    showErrorTip(tip) {
      this.$toasted.error(tip);
    },

    formatColor(color) {
      const [r, g, b, a] = color.match(/[\d.]+/).map((i) => +i);
      return { r, g, b, a };
    },

    setToolType(
      toolType,
      isSolidLine = true,
      isFill = false,
      startArrowType = 1,
      endArrowType = 1
    ) {
      console.log(
        toolType,
        "setToolType:",
        isSolidLine,
        isFill,
        startArrowType,
        endArrowType
      );
      this.currentToolType = toolType;
      console.log(window.window);
      window.window.teduBoard.setToolType(toolType);
      let lineType = null;
      if (isSolidLine) {
        lineType = TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_SOLID;
      } else {
        lineType = TEduBoard.TEduBoardLineType.TEDU_BOARD_LINE_TYPE_DOTTED;
      }
      window.teduBoard.setGraphStyle({
        lineType,
        fillType: isFill
          ? TEduBoard.TEduBoardFillType.SOLID
          : TEduBoard.TEduBoardFillType.NONE,
        startArrowType,
        endArrowType,
      });
    },

    /**
     * 工具类型
     * @param {*} toolType 画笔工具
     * @param {*} autoFitMode 自动拟合模式 0 不开启拟合 1 松手拟合 2 按住2s不动拟合
     */
    setPenToolType(toolType, autoFitMode = 0) {
      this.currentToolType = toolType;
      window.teduBoard.setToolType(toolType);

      if (autoFitMode === 0) {
        // 如果是不拟合
        window.teduBoard.enablePenAutoFit(false); // 关闭按住2s拟合
        window.teduBoard.setPenAutoFittingMode(
          TEduBoard.TEduBoardPenFittingMode.NONE
        ); // 关闭松手自动拟合
        console.log(this.settingItems);
        window.teduBoard.setHandwritingEnable(
          this.settingItems[0].handwritingEnable
        ); // 笔锋以设置面板的值为准
      } else if (autoFitMode === 1) {
        // 如果是松手拟合
        window.teduBoard.enablePenAutoFit(false); // 关闭按住2s拟合
        window.teduBoard.setHandwritingEnable(false); // 关闭笔锋
        window.teduBoard.setPenAutoFittingMode(
          TEduBoard.TEduBoardPenFittingMode.AUTO
        ); // 开启松手自动拟合
      } else if (autoFitMode === 2) {
        // 如果是按住2s不动拟合
        window.teduBoard.setHandwritingEnable(false); // 关闭笔锋
        window.teduBoard.setPenAutoFittingMode(
          TEduBoard.TEduBoardPenFittingMode.NONE
        ); // 关闭松手自动拟合
        window.teduBoard.enablePenAutoFit(true, 2000); // 开启按住2s拟合
      }
    },

    setBrushThin(thin) {
      window.teduBoard.setBrushThin(thin);
    },

    setEraserSize(eraserSize) {
      window.teduBoard.setEraserSize(eraserSize);
    },

    setTextSize(size) {
      window.teduBoard.setTextSize(size);
    },

    setBrushColor(color) {
      window.teduBoard.setBrushColor(color.hex);
    },

    setTextColor(color) {
      window.teduBoard.setTextColor(color.hex);
    },

    setBackgroundColor(color) {
      clearTimeout(this.colorTask);
      this.colorTask = setTimeout(() => {
        const r = color.rgba.r;
        const g = color.rgba.g;
        const b = color.rgba.b;
        const a = color.rgba.a;
        const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a})`;
        window.teduBoard.setBackgroundColor(rgbaColor);
      }, 200);
    },

    setHighlighterColor(color) {
      const r = color.rgba.r;
      const g = color.rgba.g;
      const b = color.rgba.b;
      let a = Number(color.rgba.a.toFixed(1)).valueOf();
      a = Math.min(0.9, Math.max(a, 0.1));
      const highlighterColor = `rgba(${r}, ${g}, ${b}, ${a})`;
      window.teduBoard.setHighlighterColor(highlighterColor);
    },

    cleanAll(removeBackground = false) {
      window.teduBoard.clear(removeBackground);
    },

    useMathTool(tool = 1) {
      window.teduBoard.useMathTool(tool);
    },

    /**
     * 显示课件和元素的弹窗
     * @param {*} type
     */
    showCoursewareAndElementDialog(type) {
      if (type === "courseware") {
        this.$refs.coursewareDialog.show();
      } else if (type === "media") {
        this.$refs.mediaDialogRef.show();
      } else if (type === "background") {
        this.$refs.backgroundDialogRef.show();
      } else if (type === "watermark") {
        this.$refs.watermarkDialogRef.show();
      } else {
        this.$refs.elementDialogRef.show(type);
      }
    },

    showSettingPanelDialog() {
      this.$refs.settingDialogRef.show();
    },

    setCustomGraph(url) {
      window.teduBoard.addElement(
        TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_CUSTOM_GRAPH,
        url
      );
      this.showTip(
        "已经设置了自定义图形的url，请在页面上进行拖动绘制出自定义图形"
      );
    },

    setPiecewiseErasureEnable(enable) {
      window.teduBoard.setPiecewiseErasureEnable(enable);
    },

    showCustomGraph() {
      this.$refs.customGraphDialogRef.show();
    },

    onUpdateCustomGraph(url) {
      this.customGraphImages.push(url);
      this.setCustomGraph(url);
    },

    clearBackground() {
      const backgroundImage = window.teduBoard.getBackgroundImage();
      if (backgroundImage.type === 0) {
        // 图片背景
        window.teduBoard.setBackgroundImage("");
      } else {
        window.teduBoard.setBackgroundH5("");
      }
    },

    onUpdateSetting(settingItems) {
      this.settingItems = settingItems;
    },

    onSnapshot(option) {
      // 在截图回调事件TEduBoard.EVENT.TEB_SNAPSHOT处理截图的结果
      // window.teduBoard.snapshot(option);
      const { ipcRenderer, desktopCapturer } = require("electron");

      const isDevelopment = window.process.env.NODE_ENV !== "development";
      const path = require("path");
      const child_process = require("child_process");
      if (window.process.platform == "darwin") {
        //判断当前操作系统，"darwin" 是mac系统     "win32" 是window系统
        child_process.exec(
          `screencapture -W -c -U`,
          (error, stdout, stderr) => {
            console.log("308", error);
            if (!error) {
              //截图完成，在粘贴板中
            }
          }
        );
      } else {
        ipcRenderer.send('capture-screen');
        // let url = path.resolve(
        //   __dirname,
        //   "../../../../../../extraResources/qq/PrintScr.exe"
        // );
        // if (isDevelopment && !process.env.IS_TEST) {
        //   // 生产环境
        //   url = path.join(__dirname, "../../../../../../extraResources/qq/PrintScr.exe");
        // }
        // let screen_window = execFile(url);
        // screen_window.on("exit", (code) => {
        //   if (code) {
        //     // 　　　　　　　　this.clipboardParsing();
        //   }
        // });
      }

      // ipcRenderer.invoke("request-video-access").then((res)=>{
      //   if(res==='granted'){
      //     console.log(res)
      //     // ipcRenderer.send('capture-screen')
      //   }else{
      //     desktopCapturer.getSources({ types: [ 'screen'] }).then(async sources => {
      //       // for (const source of sources) {
      //       //   if (source.name === 'Electron') {
      //       //     mainWindow.webContents.send('SET_SOURCE', source.id)
      //       //     return
      //       //   }
      //       // }
      //     })
      //   }
      // })
    },

    handleLineHeightChange(value) {
      window.teduBoard.setTextLineHeight(value);
    },

    changeAllPPTMediaPlaybackStatus(type, playstate) {
      const { pptMediaInfo = { videoIDList: [], audioIDList: [] } } =
        window.teduBoard.getFileInfo();
      const apiSuffix = type === "video" ? "Video" : "Audio";
      if (playstate === 1) {
        // playH5PPTVideo
        window.teduBoard[`playH5PPT${apiSuffix}`](
          pptMediaInfo[`${type}IDList`]
        );
      } else if (playstate === 2) {
        window.teduBoard[`pauseH5PPT${apiSuffix}`](
          pptMediaInfo[`${type}IDList`]
        );
      }
    },
  },
};
</script>

<style scoped lang="less">
.tool_wrap {
  display: flex;
  justify-content: center;
  position: absolute;
  top: 20%;
  // transform: translateY(-50%);
  // top: 50%;
  // right: 1.5%;
  // position: fixed;
  .tool_box {
    width: 40%;
    border-radius: 60px;
    border: 4px solid #fff;
    background: #3178d2;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.25),
      0px 4px 8px 0px rgba(48, 124, 210, 0.5) inset;
    padding: 20px 0;
    cursor: pointer;
    img {
      width: 100%;
    }
  }
}
</style>
