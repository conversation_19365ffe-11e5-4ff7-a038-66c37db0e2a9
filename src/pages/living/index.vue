<template>
  <div
    class="live_wrap"
    :style="{
      'background-image': `url(${require('@/assets/living/backgr.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <!-- 顶部 -->
    <topFeature
      v-if="roomTop"
      :liveType="liveType"
      :config="config"
      @leave="back"
      @switchDevice="switchDevice"
      :liveData="liveDta"
      @outRoomDiag="outRoomDiag"
      :interval_start="interval_start"
      :interval_starting="interval_starting"
      :interval_end="interval_end"
    ></topFeature>
    <div>
      <div
        class="dog"
        :style="{
          'background-image': `url(${require('@/assets/living/dog.png')})`,
          'background-size': '100% 100%',
        }"
      ></div>
      <!-- 视频区域 -->
      <div :class="liveType ? (doneStandard == 1 ? 'live_flex' : 'live_flex fiex') : ''">
        <studentLiving
          v-if="config.userSig"
          @startLocalVideo="startLocalVideo"
          @startLocalAudio="startLocalAudio"
          @publish="publish"
          @adjustStage="adjustStage"
          @sendTotal="sendTotal"
          :doneStandard="doneStandard"
          :type="liveType"
          :remoteStreamList="remoteStreamList"
          :remoteList="remoteList"
          @startPublish="startPublish"
        ></studentLiving>
      </div>
      <div v-if="!liveType">
        <!-- 测试设备部分 -->
        <inspection @showType="handleEnder" :roomButton="roomButton" />
      </div>
      <div v-else>
        <div
          :class="doneStandard == 1 ? 'content_wrap' : 'content_wrap heig'"
          id="content_wrap"
        >
          <div
            :class="
              remoteStreamList.length > 0
                ? 'left_content student-livings_not_start'
                : 'left_content'
            "
          >
            <div class="flex">
              <div class="left_wrap">
                <!-- 题库 -->
                <!-- <div class="game_pos" > -->
                <game
                  v-if="doneChessBoard"
                  ref="game"
                  @handAnswer="handAnswer"
                  class="left_pos"
                  style="
                    z-index: 12;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                  "
                />
                <!-- </div> -->
                <!-- 点名器 -->
                <!-- <div class="game_pos" > -->
                <callRollDialog
                  :lotteryList="lotteryList"
                  :winListId="winListId"
                  :viewsId="viewsId"
                  ref="callRollDialogs"
                  v-if="callRollDialogVisible"
                />
                <!-- </div> -->
                <!-- 画板 远程接收教师端分享 -->
                <artboardAssembly
                  class="left_pos"
                  style="z-index: 11"
                  v-if="liveType && config.userSig"
                />
                <!-- 共享屏幕 -->
                <!-- <comSharing :remoteStreamList="remoteStreamList" :doneShareScreen="doneShareScreen" :key="doneShareScreen" class="shar"/> -->
              </div>
              <div class="right_wrap">
                <!-- 聊天 -->
                <div v-show="doneChat">
                  <forum
                    v-if="config.userSig"
                    @closeChat="closeChat"
                    :doneChat="doneChat"
                  ></forum>
                </div>
                <div class="but_coin">
                  <!-- 画板工具 -->
                  <packupDia @openChat="openChat" :doneChat="doneChat" />
                  <!-- 举手 -->
                  <featureButton @raiseHand="raiseHand" v-if="doneStandard == 1"></featureButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <classIsover ref="classIsover" @back="back" />
    <answeringMachine
      ref="answeringMachine"
      :liveType="liveType"
      @sendAnswer="sendAnswer"
      v-if="liveType && doneStartAnswer"
    />
    <trophyCom ref="trophyCom" />
    <loading ref="loading" json_data_url="living" :errText="enterRoomStatusText" />
    <videoWalls ref="videoWalls" v-if="liveType && doneStandard !== 2 && !doneOnStage" :remoteList="remoteList" @startLocalVideo="startLocalVideo" @updateLocalVideo="client.stopLocalVideo()"/>
  </div>
</template>
<script>
import rtc from "./components/mixins/rtc.js";
import socket from "./components/mixins/socket.js";
import { Toast, Indicator } from "mint-ui";
import LibGenerateTestUserSig from "@/utils/lib-generate-test-usersig.min.js";
import config from "@/config";
import topFeature from "./components/stamLive/top-feature";
import studentLivings1 from "./components/stamLive/student-livings1";
import featureButton from "./components/feature-button";
import studentLiving from "./components/stamLive/student-living";
import forum from "./components/forum";
import inspection from "./inspection";
import comSharing from "./components/com-sharing";
import liveApi from "@/api/living.js";
import ArtboardAssembly from "./components/artboardAssembly";
import classIsover from "./diag/class-isover.vue";
import game from "./components/game";
import packupDia from "./components/packup-dia";
import answeringMachine from "./diag/answering-machine.vue";
import trophyCom from "./diag/trophy-com.vue";
import callRollDialog from "./diag/call-roll-dialog.vue";
import loading from "./diag/live-loading.vue";
import videoWalls from "./diag/video-walls.vue";
let { ipcRenderer, contextBridge } = require("electron");
export default {
  mixins: [rtc, socket],
  data() {
    return {
      remoteStreamList: [
        {
          userId: "localStream",
          // streamType,
          receiveAudio: true, // 麦克风
          receiveVideo: true, // 摄像头
          hasLimit: false,
          onStage: true, // 上台状态
          student_microphone_status: true, // 老师介入的权限
          camera_status: true, // 摄像头权限
          raiseHand: false, // 举手
          student_role_status: false, //白板权限
          userName: "",
          volume: 0,
          type: "loca",
          cupNumber: 0,
          isShow: true,
          reasureCount: false,
        },
      ], // 远端小视频
      liveDta: null,
      config,
      liveType: false,
      camera: false,
      microphone: false,
      speaker: false,
      roomButton: false, //是否可以进入直播间
      interval_end: 0,
      interval_start: 0,
      interval_starting: 0,
      roomTop: false,
      camera_status: true,
      microphone_status: true,
      student_role_status: "",
      student_microphone_status: "",
      trophy_num: 0,
      lotteryList: [], //中奖人员
      callRollDialogVisible: false, //点名弹窗
      winListId: [], //中奖名单id
      viewsId: [],
      limitsauthority: true,
      student_camera_status: true,
      comment_status: true,
      remoteList: [], // 远端合并视频
      // 进房状态
      enterRoomStatusText: "正在加载中...",
      hiddenTime: null,
    };
  },
  components: {
    topFeature,
    // studentLivings,
    featureButton,
    studentLiving,
    forum,
    inspection,
    comSharing,
    ArtboardAssembly,
    classIsover,
    game,
    packupDia,
    answeringMachine,
    trophyCom,
    callRollDialog,
    loading,
    videoWalls
  },
  watch: {
    isAudioMuted: {
      handler: function (val) {
        // if(!this.doneTeacher){
        //   this.muteAudio();
        // }else{
        // if(!this.doneOnStage) return;
        if (!val || !this.doneTeacher) {
          // 话筒静音
          this.muteAudio();
        } else {
          this.unmuteAudio();
        }
        // }
      },
      immediate: true,
      deep: true,
    },
    isVideoMuted: {
      handler: function (val) {
        // if(!this.doneOnStage) return;
        if (!val || !this.doneCameraAuthority) {
          // 关闭摄像头
          this.muteVideo();
        } else {
          this.unmuteVideo();
        }
      },
      immediate: true,
      deep: true,
    },
    activeCameraId(val, oldVal) {
      if (oldVal) {
        this.switchDevice("video", val);
      }
    },
    activeMicrophoneId(val, oldVal) {
      if (oldVal) {
        this.switchDevice("audio", val);
      }
    },
    activeSpeakerId(val, oldVal) {
      if (oldVal) {
        this.switchDevice("speaker", val);
      }
    },
    doneOnStage: {
      handler: function (val) {
        if (!val) {
          // 关闭摄像头
          this.unPublish();
        } else {
          this.publish();
        }
      },
      // immediate: true,
      deep: true,
    },
    doneTeacher: {
      handler: function (val) {
        if (!val || !this.isAudioMuted) {
          this.muteAudio();
        } else {
          this.unmuteAudio();
        }
      },
    },
    doneCameraAuthority: {
      handler: function (val) {
        if (!val || !this.isVideoMuted) {
          this.muteVideo();
        } else {
          this.unmuteVideo();
        }
      },
    },
  },
  computed: {
    isAudioMuted() {
      return this.$store.getters.doneAudioState;
    },
    isVideoMuted() {
      return this.$store.getters.doneVideoState;
    },
    activeMicrophoneId() {
      return this.$store.getters.activeMicrophoneId;
    },
    activeSpeakerId() {
      return this.$store.getters.activeSpeakerId;
    },
    activeCameraId() {
      return this.$store.getters.activeCameraId;
    },
    doneShareScreen() {
      return this.$store.getters.doneShareScreen;
    },
    doneChat() {
      return this.$store.getters.doneChat;
    },
    doneChessBoard() {
      return this.$store.getters.doneChessBoard;
    },
    doneTeacher() {
      return this.$store.getters.doneTeacher;
    },
    doneOnStage() {
      return this.$store.getters.doneOnStage;
    },
    doneRaiseHand() {
      return this.$store.getters.doneRaiseHand;
    },
    doneStandard() {
      return this.$store.getters.doneStandard;
    },
    doneMicrophone() {
      return this.$store.getters.doneMicrophone;
    },
    doneCamera() {
      return this.$store.getters.doneCamera;
    },
    doneStartAnswer() {
      return this.$store.getters.doneStartAnswer;
    },
    doneCameraAuthority() {
      return this.$store.getters.doneCameraAuthority;
    },
    doneTeacherId() {
      return this.$store.getters.doneTeacherId;
    },
  },
  async mounted() {
    let that = this;
    this.liveDta = JSON.parse(this.$route.query.data);
    ipcRenderer.on("close", (res) => {
      if (this.$route.path === "/living") {
        this.back();
      }
      console.log("退出");
    });
    // 专注模式
    // ipcRenderer.send('set-winTop',true);
    // 请求摄像头
    let videoCame = await ipcRenderer.invoke("request-video-access");
    // 请求麦克风权限
    let mediaMic = await ipcRenderer.invoke("request-media-access");
    this.$store.dispatch(
      "UPDATE_MICROPHONE_STATUS",
      mediaMic == "granted" ? true : false
    );
    this.$store.dispatch("UPDATE_CAMERA_STATUS", videoCame == "granted" ? true : false);
    this.limitsauthority = videoCame || mediaMic ? true : false;
    await this.initClient();
    await this.getRoomInfo();
    this.startGetAudioLevel();
    this.$bus.on("initClient", this.initClient);
    // this.$bus.on("startLocalVideo", this.startLocalVideo);
    this.$bus.on("startLocalAudio", this.startLocalAudio);
    this.$bus.on("setAudioPlayoutVolume", this.setAudioPlayoutVolume);
    this.$bus.on("switchDevice", this.switchDevice);
    this.$bus.on("destroyLocalStream", this.destroyLocalStream);
    this.$bus.on("outRoomDiag", this.outRoomDiag);
    this.$bus.on("settingAudioCaptureVolume", this.settingAudioCaptureVolume);
    this.$bus.on("cancelRaiseHand", this.cancelRaiseHand);

    //   document.addEventListener('visibilitychange',function() {
    // if(document.visibilityState == 'hidden') {
    // 	//记录页面隐藏时间
    // 	that.hiddenTime = new Date().getTime();
    //   // this.socket = null;
    //   //   this.back();
    // } else {
    //   setTimeout(()=>{
    //     that.back();
    //   },300)
    // 	let visibleTime = new Date().getTime();
    // 	// 页面再次可见的时间-隐藏时间>1分钟,重连
    // 	if((visibleTime - this.hiddenTime) / 60000 > 1){
    // 		// 主动关闭连接
    // 		this.socket = null;
    //     this.back();
    // 		setTimeout(function(){
    // 			 this.handleEnder();
    //     },1500)
    // 	}else{
    // 		console.log('还没有到断开的时间')
    // 	}
    // }
    // })
  },
  beforeCreate() {
    this.$store.dispatch("RESET_CARTSTATE");
    // 如果process.env.VUE_APP_BASE_API 包含dev
    // if (!(process.env.VUE_APP_BASE_API.indexOf("dev") > -1)) {
    //   console.log = function () {};
    //   console.info = function () {};
    //   console.warn = function () {};
    //   console.error = function () {};
    // }
  },
  methods: {
    handleEnder() {
      // 进房之前建立连接
      // Indicator.open({
      //   text: this.enterRoomStatusText,
      //   spinnerType: 'fading-circle'
      // });
      this.enterRoomStatusText = "连接服务器中...";
      this.$refs.loading.onShow();
      if (this.$storage.$getStroage("liveToken")) {
        this.init();
      }
    },
    // 进入房间
    async handleJoinRoom() {
      this.enterRoomStatusText = "进入房间中...";
      this.roomButton = false;
      // await this.unPublish();
      // 进入直播间
      liveApi
        .StudentEnter({
          room_id: config.roomId,
          student_id: config.userId,
          up_status: this.doneOnStage ? 1 : 0,
          allow_up: this.limitsauthority,
          device_name:
            window.navigator.platform !== "Win64" || window.navigator.platform !== "Win32"
              ? window.navigator.platform
              : "",
          tg_student_name: config.tgStudentName,
        })
        .then(async (res) => {
          if (res.data.code === 0) {
            await this.join();
            this.liveType = true;
            this.$refs.loading.onHide();
            if (res.data.data.up_status == 1) {
              this.$nextTick(async () => {
                await this.startPublish("localStream");
                if (!this.camera_status || !this.doneCameraAuthority) {
                  // this.$store.dispatch("UPDATE_VIDEO_STATE", false);
                  this.muteVideo();
                } else if (!this.microphone_status || !this.doneTeacher) {
                  this.muteAudio();
                }
              });
            }
            this.$store.dispatch(
              "UPDATE_STAGE_STATUS",
              res.data.data.up_status == 1 ? true : false
            );
          } else {
            Toast({ message: res.data.message, duration: 3000 });
            this.roomButton = true;
            this.$refs.loading.onHide();
            this.socket.close();
            this.socketRoor = true;
          }
        })
        .catch((err) => {
          Toast("进入房间失败，请稍后再试");
          this.$refs.loading.onHide();
          this.roomButton = true;
          this.socket.close();
          this.socket = null;
          this.socketRoor = true;
        });
      // 这里是赋值设备信息 老师的权限等
      this.$store.dispatch("UPDATE_VIDEO_STATE", this.camera_status);
      this.$store.dispatch("UPDATE_AUDIO_STATE", this.microphone_status);
      this.$store.dispatch("UPDATE_DRAW_PERMISSION", this.student_role_status);
      this.$store.dispatch("UPDATE_TEACHER_PERMISSION", this.student_microphone_status);
      this.$store.dispatch("UPDATE_GET_TREASURE_COUNT", this.trophy_num);
      this.$store.dispatch("UPDATE_CAMERA_PERMISSION", this.student_camera_status);
      this.$store.dispatch("UPDATE_MUTE_STATUS", this.comment_status);
      // // 刚进来的时候 看是否有老师权限和自己的
      // if (!this.camera_status || !this.doneCameraAuthority) {
      //   // this.$store.dispatch("UPDATE_VIDEO_STATE", false);
      //   this.muteVideo();
      // } else if (!this.microphone_status || !this.doneTeacher) {
      //   this.muteAudio();
      // }
      // this.$store.dispatch("UPDATE_MICROPHONE_STATUS", this.microphone_status);
      // this.$store.dispatch("UPDATE_CAMERA_STATUS", this.camera_status);
      window.addEventListener("keydown", (event) => {
        if (event.keyCode === 9) {
          console.log("tabfalse13");
          event.preventDefault();
        }
      });
    },
    // 退出方法
    async back(val) {
      if (val != 1) {
        this.enterRoomStatusText = "退出教室中，请稍后...";
        this.$refs.loading.onShow();
      }
      this.roomTop = false;
      if (this.liveType && !this.tick) {
        try {
          await liveApi.StudentOuter({
            room_id: config.roomId,
            student_id: config.userId,
            up_status: this.doneOnStage ? 1 : 0,
          });
          this.socket = null;
        } catch (err) {
          Toast("退出失败，请稍后再试");
          this.$refs.loading.onHide();
        }
      }
      await this.unPublish();
      await this.leave();
      this.config.userSig = "";
      this.$store.dispatch("RESET_CARTSTATE");
      this.liveType = false;
      // ipcRenderer.send('set-winTop',false)
      if (val != 1) {
        this.$router.push({
          path: "/",
          query: {
            defaultIndex: 1,
            currentIndex: Date.now()
          },
        });
      }
    },
    // 获取房间信息
    async getRoomInfo() {
      let res = await liveApi.StudentCome({ room_id: this.liveDta.index });
      if (res.code == 1) {
        this.outRoomDiag(res.data.message);
        return;
      }
      config.roomId = this.liveDta.index;
      config.userId = res.data.data.student_id;
      config.userSig = res.data.data.sign;
      config.userName =
        this.maskPhoneNumber(res.data.data.student_name);
      config.tgStudentName = res.data.data.student_name;
      config.roomName = this.liveDta.name;
      config.userAvatar = res.data.data.avatar;
      config.group_id = this.liveDta.group_id;
      // 公开课 与 1v0
      let courseType =
        this.liveDta.course_type == 2 ? 2 : this.liveDta.stage_number == 0 ? 2 : 1;
      this.$store.dispatch(
        "UPDATE_TEACHER",
        this.liveDta.teacher.id + this.liveDta.index
      );
      this.$store.dispatch("UPDATE_TEACHER_NAME", this.liveDta.teacher.name);
      this.$store.dispatch("UPDATE_IS_STANDARD", courseType);
      const roomInfo = {
        room_id: this.liveDta.index,
        student_id: config.userId,
      };
      // 获取上一次麦克风 摄像头 开关
      let { data ,code,message} = await liveApi.StudentRoomInfo({
        ...roomInfo,
      });
      if (code == 1) {
        this.outRoomDiag(message);
        return;
      }
      this.interval_end = data.data.interval_end;
      this.interval_start = data.data.interval_start;
      this.interval_starting = data.data.interval_starting;
      this.camera_status = data.data.camera_status;
      this.microphone_status = data.data.microphone_status;
      this.student_role_status = data.data.student_role_status;
      this.student_microphone_status = data.data.student_microphone_status;
      this.student_camera_status = data.data.student_camera_status;
      this.trophy_num = data.data.trophy_num;
      this.comment_status = data.data.comment_status;
      this.$store.dispatch("UPDATE_WHITE_BOARD_SIZE", data.data.white_board_size);
      this.$store.dispatch("UPDATE_ALL_MUTE_STATUS", data.data.is_all_comment);
      this.classRoomStatus();
      this.roomTop = true;
      await this.adjustStage();
      this.startPublish();
    },
    // 判断上课的进程
    classRoomStatus() {
      if (this.interval_start > 0) {
        // 未到上课时间
        this.$store.dispatch("UPDATE_LIVE_STAGE", "not_started");
      } else if (
        this.interval_start === 0 &&
        this.interval_starting > 0 &&
        this.interval_end === 0
      ) {
        this.$store.dispatch("UPDATE_LIVE_STAGE", "broadcast-starts");
        // 正在上课
      } else if (this.interval_start === 0 && this.interval_end > 0) {
        // 延堂
        this.$store.dispatch("UPDATE_LIVE_STAGE", "yeondo");
      } else {
        this.$store.dispatch("UPDATE_LIVE_STAGE", "ended");
      }
    },
    goRoom() {
      // this.publish();
      // 进入直播间
      this.$store.dispatch("UPDATE_STAGE_STATUS", true);
    },
    outRoomDiag(text, type = false) {
      this.$refs.classIsover.onShow(text, type);
      this.socket = null;
    },
    // 操作直播间
    async handleRoom(type) {
      if (type == 1) {
        // this.startPublish();
        this.goRoom();
      }
    },
    closeChat() {
      this.$store.dispatch("UPDATE_CHAT_BOX", false);
    },
    openChat() {
      this.$store.dispatch("UPDATE_CHAT_BOX", !this.doneChat);
    },
    // 题库
    handAnswer(val) {
      console.log(val, "val");
      let answerData = {
        room_id: config.roomId,
        student_id: config.userId,
        ...val,
      };
      if (answerData.answer_result != "") {
        this.sendTotal({
          type: "question_bank_answer",
          content: JSON.stringify(answerData),
        });
      }
    },
    // 奖杯
    raiseHand() {
      this.sendTotal({
        type: "raise_hand",
        content: JSON.stringify({
          status: true,
          student_id: this.config.userId,
        }),
      });
    },
    // 取消举手
    cancelRaiseHand() {
      if (!this.doneRaiseHand) return;
      this.sendTotal({
        type: "raise_hand",
        content: JSON.stringify({
          status: false,
          student_id: config.userId,
        }),
      });
    },
    // 推送答题信息
    sendAnswer(val) {
      let answerData = {
        student_name: config.tgStudentName,
        student_id: config.userId,
        room_id: config.roomId,
        id: val.id,
        answer: val.answer,
        correct: val.correct,
        correct_answer: val.correct_answer,
        interval_unix: val.interval_unix,
        option: val.option,
        up_status: this.doneOnStage ? 1 : 2,
        operate_type: val.operate_type,
      };
      this.sendTotal({
        type: "receive_question_machine",
        content: JSON.stringify(answerData),
      });
    },
    // 推送
    sendTotal(data) {
      this.send(
        JSON.stringify({
          ...data,
        })
      );
    },
    // 调整台上人的顺序
    async adjustStage() {
      let that = this;
      let res = await liveApi.UpStudentList({ room_id: config.roomId });
      if (!res.data.data) {
        res.data.data = [];
      }
      // if (this.liveType) {
      let index = res.data.data.findIndex((item) => item.student_id == config.userId);
      if (index !== -1) {
        res.data.data[index].student_id = "localStream";
        // res.data.data.splice(index, 1, "localStream");
        this.$store.dispatch(
          "UPDATE_TEACHER_PERMISSION",
          res.data.data[index].student_microphone_status
        );
        this.$store.dispatch("UPDATE_VIDEO_STATE", res.data.data[index].camera_status);
        this.$store.dispatch(
          "UPDATE_AUDIO_STATE",
          res.data.data[index].microphone_status
        );
        this.$store.dispatch(
          "UPDATE_DRAW_PERMISSION",
          res.data.data[index].student_role_status
        );
        this.$store.dispatch(
          "UPDATE_GET_TREASURE_COUNT",
          res.data.data[index].trophy_num
        );
        this.$store.dispatch(
          "UPDATE_CAMERA_PERMISSION",
          res.data.data[index].student_camera_status
        );
        this.$store.dispatch("UPDATE_MUTE_STATUS", res.data.data[index].comment_status);
      } else {
      }
      this.remoteList = res.data.data.map((item, index) => {
        // if (item.student_id === "localStream") {
        //   return this.remoteStreamList.find((item1) => item1.userId == item.student_id);
        // } else {
        return {
          userId: item.student_id,
          default: true,
          // // streamType,
          receiveAudio: item.microphone_status, // 麦克风
          receiveVideo: item.camera_status, // 摄像头
          hasLimit: false,
          onStage: true, // 上台状态
          student_microphone_status: item.student_microphone_status, // 老师介入的权限
          raiseHand: false, // 举手
          student_role_status:
            this.doneTeacherId === item.student_id ? false : item.student_role_status, //白板权限
          student_name: item.student_name,
          volume: 0,
          type: item.student_id === "localStream" ? "loca" : "farend",
          student_camera_status: item.student_camera_status,
          cupNumber: item.trophy_num,
          avatar: item.avatar,
          isShow: true,
          reasureCount: false,
        };
        // }
      });
      if (!this.liveType) {
        this.remoteList.push({
          userId: "localStream",
          default: true,
          // streamType,
          receiveAudio: true, // 麦克风
          receiveVideo: true, // 摄像头
          hasLimit: false,
          onStage: true, // 上台状态
          student_microphone_status: true, // 老师介入的权限
          camera_status: true, // 摄像头权限
          raiseHand: false, // 举手
          student_role_status: false, //白板权限
          userName: "",
          volume: 0,
          type: "loca",
          cupNumber: 0,
          isShow: true,
          reasureCount: false,
        });
      }
      // } else {
      //   this.remoteList = this.remoteStreamList;
      // }
    },
    maskPhoneNumber(phone) {
      console.log(phone);
      let phoneNumber = phone;
      const phoneRegex = /^(\d{3})\d{4}(\d{4})/;
      const mask = "$1****$2";
      // 检查是否为11位数字
      if (phoneRegex.test(phoneNumber)) {
        // 隐藏中间4位数
        return phoneNumber.replace(phoneRegex, mask);
      } else {
        // 如果不是11位数字，则返回原始号码
        return phoneNumber;
      }
    },
  },
  async created() {
    //  // 获取设备列表并更新到全局
    const cameraList = await this.getCameras();
    const microphoneList = await this.getMicrophones();
    const speakerList = await this.getSpeakers();
    this.$store.dispatch("UPDATE_ACTIVE_CAMERA", cameraList[0]);
    this.$store.dispatch("UPDATE_ACTIVE_MICROPHONE", microphoneList[0]);
    this.$store.dispatch("UPDATE_ACTIVE_SPEAKER", speakerList[0]);
  },
  beforeDestroy() {
    // 销毁bus事件
    this.leave();
    this.$bus.off("initClient");
    // this.$bus.off("startLocalVideo");
    this.$bus.off("setAudioPlayoutVolume");
    this.$bus.off("destroyLocalStream");
    this.$bus.off("startLocalAudio");
    this.$bus.off("outRoomDiag");
    this.$bus.off("settingAudioCaptureVolume");
  },
};
</script>
<style lang="less" scoped>
.live_wrap {
  width: 1920px;
  height: 100vh;
  background-color: #f6f6fc;
  background: url("@/assets/living/backgr.png") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  overflow: hidden;
  .dog {
    position: absolute;
    left: 0;
    bottom: 0;
    // z-index: 9999999999;
    width: 35%;
    height: 54%;
  }
  .fiex {
    position: fixed;
    z-index: 12;
    // width: 100%;
    // height: 100%;
  }
  .live_flex {
    display: flex;
    justify-content: center;
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // margin-left: 20px;
  }
  .heig {
    height: calc(100vh - 150px) !important;
  }
  .content_wrap {
    width: 1920px;
    height: calc(100vh - 370px);
    // margin: 24px;
    .shar {
      position: absolute;
      z-index: 1;
    }
    .student-livings {
      width: 324px;
      height: 194px;
      margin-right: 16px;
    }
    .student-livings_not_start {
      width: 100%;
      height: calc(100% - 40px);
    }
    .left_content {
      width: 100%;
      height: 100%;
      // height: 1336px;
      .flex {
        display: flex;
        justify-content: space-between;
        height: 100%;
        .left_wrap {
          width: 100%;
          // background:red;
          position: relative;
          .game_pos {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 12;
          }
          .left_pos {
            position: absolute;
            top: 0;
          }
        }
        .right_wrap {
          position: absolute;
          top: 30%;
          right: 0;
          z-index: 16;
          display: contents;
          display: flex;
          height: 806px;
        }
        .but_coin {
          width: 150px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          justify-content: space-evenly;
        }
      }
    }
    .right_content {
      width: 480px;
      height: 1336px;
    }
  }
}
/deep/video {
  z-index: 2;
  position: relative;
}
/deep/.vdr.active:before {
  outline: none !important;
}
</style>
