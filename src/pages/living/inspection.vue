<template>
    <!-- 设备检测页面 -->
    <div class="page">
      <div class="conit">
        <div class="title">
          <p>设备检测</p>
        </div>
        <div class="form">
          <div class="item">
            <p for="dropdown">摄像头：</p>
            <selectDiec :deviceType="'camera'" ></selectDiec>
          </div>
          <div class="item">
            <p for="dropdown">麦克风：</p>
            <selectDiec :deviceType="'microphone'" ></selectDiec>
            <div class="flex">
              <img class="img" src="@/assets/living/mic.png" alt="">
              <div class="bo">
                <div v-for="(item, index) in 25" :key="index"  class="RMS" :style="`${Math.ceil(audioLevel / 4) > index ? 'background: #FFD242' : ''}`"></div>
              </div>
            </div>
          </div>
          <div class="item">
            <p for="dropdown">扬声器：</p>
            <selectDiec :deviceType="'speaker'"></selectDiec>
            <div class="flex sliderWrap">
              <img class="img images" style="margin-right:3px" src="@/assets/living/smal.png" alt="">
              <audio id="myAudio">
              <source src="xxx" type="audio/mpeg">
              </audio>
              <!-- @input="getLoudIput" @mouseup="loudMput" -->
              <!-- <input type="range" id="volume" name="volume" min="0" max="100" v-model="loudVolume"> -->
              <el-slider v-model="loudVolume" style="width:100%" :show-tooltip="false"></el-slider>
              <img class="img images" style="margin-left:3px" src="@/assets/living/gour.png" alt="">
            </div>
          </div>
        </div>
        <div class="but"> 
          <button v-preventReClick="goRoom" :disabled="!roomButton" :style="{background:!roomButton ? '#c3bdbd' :''}">进入教室</button>
        </div>
      </div>
    </div>
</template>
<script>
import selectDiec from "./components/selectDiec";
export default {
  name: 'inspection',
  components: {
    selectDiec
  },
  props:{
    roomButton:{
      type:Boolean,
      default:true
    }
  },
  data () {
    return {
      data:null, // 直播间信息
      loudVolume:100
    }
  },
  // 计算属性
  computed: {
    audioLevel() {
      return this.$store.getters.doneAudioLevel;
    },
    microphoneVolume() {
      return this.$store.getters.doneSpeakerVolume;
    }
  },
  // 侦听器
  watch: {
    // microphoneVolume: {
    //   handler: function (val) {
    //     console.log(val);
    //     // 设置采集麦克风音量
    //     this.settingAudioCaptureVolume(Number(val));
    //   },
    //   deep: true
    // },
    loudVolume: {
      handler: function (val) {
        if (val) {
          console.log(val);
          this.$bus.emit('setAudioPlayoutVolume',(Number(val)))
        }
      },
      deep: true
    },
  },
  methods: {
    goRoom(){
      this.$emit('showType');
    },
  },
  created () {
  },
  mounted () {
  }
  }
  </script> 
  
  <style lang='scss' scoped>
  .page{
    position: relative;
    z-index:10;
    .conit{
      position: absolute;
      top: 106px;
      left: 58px;
      width: 600px;
      // height: 640px;
      // border: 1px solid #000;
      background: #fff;
      border-radius: 10px;

      .title{
        width: 100%;
        height: 71.27px;
        background: #438EFF;
        color: #fff;
        border-radius: 10px 10px 0 0;
        font-size: 28px;
        line-height: 38.4px;
        letter-spacing: 0px;
        text-align: center;
        font-weight: 600;
        display: flex;
        align-items: center;
        p{
          padding-left: 20px;
        }
      }
      .form{
        font-weight: 500;
        font-size: 26px;
        color: #438EFF;
        padding: 20px;
        .item{
          line-height: 18.48px;
          margin-top: 50px;
          p{
            margin-bottom: 20px;
            margin-top: 20px;
          }
          .flex{
            margin-top: 20px;
            display: flex;
            align-items: center;
            
            input{
              height: 20px;
              width: 100%;
            }
            .img{
              width: 24px;
              height: 28px;
            }
            .images{
              width: 28px;
            }
            .bo{
              
              .RMS{
                width: 8px;
                height: 20px;
                background: #D9D9D9;
                border-radius: 9px;
                display: inline-block;
                margin-left: 12px;
              }
            }
          }
        }
      }
      .but{
        margin-top: 60px;
        padding: 20px;
          button{
            width: 100%;
            height: 66px;
            font-size: 26px;
            line-height: 22.4px;
            font-weight: 500;
            background: #438EFF;
            color: #FFFFFF;
            border-radius: 5px;
            border: none;
          }
        }
        .sliderWrap {
          width: 100%;
          ::v-deep .el-slider__runway {
            height: 20px;
            border-radius: 200px;
            margin:0;
            .el-slider__button-wrapper {
              width: 50px;
              height: 50px;
              top: -15px
            }
            .el-slider__button {
              width: 24px;
              height: 24px;
              background-color: #1bbd86;
              border: 2px solid #fff;
            }
            .el-slider__bar {
              position: relative;
              height: 20px;
              background-image: linear-gradient(
                270deg,
                #17ba82 -0.99%,
                #33c493 101.16%
              );
              border-radius: 100px;
            }
            .el-slider__bar::after {
              position: absolute;
              content: "";
              width: 100%;
              height: 10px;
              background-color: #fff;
              left: 0;
              top: -6px;
              transform: rotate(-0.5deg);
            }
            .el-slider__bar::before {
              position: absolute;
              content: "";
              width: 100%;
              height: 10px;
              background-color: #fff;
              left: 0;
              bottom: -6px;
              transform: rotate(0.5deg);
            }
          }
        }
    }
  }
  </style>
  
  