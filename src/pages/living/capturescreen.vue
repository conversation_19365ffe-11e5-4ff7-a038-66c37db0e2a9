<template>
  <span>
    <div id="js-bg" class="bg"></div>
    <div id="js-mask" class="mask"></div>
    <canvas id="js-canvas" class="image-canvas"></canvas>
    <div id="js-size-info" class="size-info"></div>
    <div id="js-toolbar" class="toolbar">
      <div class="iconfont icon-zhongzhi" id="js-tool-reset"></div>
      <div class="iconfont icon-xiazai" id="js-tool-save"></div>
      <div class="iconfont icon-guanbi" id="js-tool-close"></div>
      <div class="iconfont icon-duihao" id="js-tool-ok"></div>
    </div>
  </span>
</template>

<script>
export default {
  name: "capturescreen",
  props: {},
  components: {},
  data() {
    return {};
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {},
  created() {},
  mounted() {
    /**
     * Created by xujian1 on 2018/10/4.
     */

    const { ipcRenderer, clipboard, nativeImage, remote } = require("electron");

    const fs = require("fs");
    console.log("123");
    const { getScreenSources } = require("@/lib/desktop-capturer");
    const { CaptureEditor } = require("@/lib/capture-editor");
    const { getCurrentScreen } = require("@/lib/utils");
    const $canvas = document.getElementById("js-canvas");
    const $bg = document.getElementById("js-bg");
    const $sizeInfo = document.getElementById("js-size-info");
    const $toolbar = document.getElementById("js-toolbar");

    const $btnClose = document.getElementById("js-tool-close");
    const $btnOk = document.getElementById("js-tool-ok");
    const $btnSave = document.getElementById("js-tool-save");
    const $btnReset = document.getElementById("js-tool-reset");

    // const audio = new Audio();
    // audio.src = "./assets/audio/capture.mp3";

    const currentScreen = getCurrentScreen();
    console.log(currentScreen);
    // 右键取消截屏
    document.body.addEventListener(
      "mousedown",
      (e) => {
        if (e.button === 2) {
          window.close();
        }
      },
      true
    );

    // console.time('capture')
    getScreenSources({}, (imgSrc) => {
      console.log(imgSrc);
      // console.timeEnd('capture')

      let capture = new CaptureEditor($canvas, $bg, imgSrc);

      let onDrag = (selectRect) => {
        $toolbar.style.display = "none";
        $sizeInfo.style.display = "block";
        $sizeInfo.innerText = `${selectRect.w} * ${selectRect.h}`;
        if (selectRect.y > 35) {
          $sizeInfo.style.top = `${selectRect.y - 30}px`;
        } else {
          $sizeInfo.style.top = `${selectRect.y + 10}px`;
        }
        $sizeInfo.style.left = `${selectRect.x}px`;
      };
      capture.on("start-dragging", onDrag);
      capture.on("dragging", onDrag);

      let onDragEnd = () => {
        if (capture.selectRect) {
          ipcRenderer.send("capture-screen", {
            type: "select",
            screenId: currentScreen.id,
          });
          const { r, b } = capture.selectRect;
          $toolbar.style.display = "flex";
          $toolbar.style.top = `${b + 15}px`;
          $toolbar.style.right = `${window.screen.width - r}px`;
        }
      };
      capture.on("end-dragging", onDragEnd);

      ipcRenderer.on("capture-screen", (e, { type, screenId }) => {
        if (type === "select") {
          if (screenId && screenId !== currentScreen.id) {
            capture.disable();
          }
        }
      });

      capture.on("reset", () => {
        $toolbar.style.display = "none";
        $sizeInfo.style.display = "none";
      });

      $btnClose.addEventListener("click", () => {
        ipcRenderer.send("capture-screen", {
          type: "close",
        });
        window.close();
      });

      $btnReset.addEventListener("click", () => {
        capture.reset();
      });

      let selectCapture = () => {
        if (!capture.selectRect) {
          return;
        }
        let url = capture.getImageUrl();
        remote.getCurrentWindow().close();
        console.log(remote)
        // audio.play();
        // audio.onended = () => {
        //   window.close();
        // };
        clipboard.writeImage(nativeImage.createFromDataURL(url));
        ipcRenderer.send("capture-screen", {
          type: "complete",
          url,
        });
      };
      $btnOk.addEventListener("click", selectCapture);

      $btnSave.addEventListener("click", () => {
        let url = capture.getImageUrl();

        remote.getCurrentWindow().hide();
        remote.dialog.showSaveDialog(
          {
            filters: [
              {
                name: "Images",
                extensions: ["png", "jpg", "gif"],
              },
            ],
          },
          (path) => {
            if (path) {
              // eslint-disable-next-line no-buffer-constructor
              fs.writeFile(
                path,
                new Buffer(url.replace("data:image/png;base64,", ""), "base64"),
                () => {
                  ipcRenderer.send("capture-screen", {
                    type: "complete",
                    url,
                    path,
                  });
                  window.close();
                }
              );
            } else {
              ipcRenderer.send("capture-screen", {
                type: "cancel",
                url,
              });
              window.close();
            }
          }
        );
      });

      window.addEventListener("keypress", (e) => {
        if (e.code === "Enter") {
          selectCapture();
        }
      });
    });
  },
};
</script> 

<style scope>
@import "@/lib/assets/iconfont/iconfont.css";

html,
body,
div {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

.bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.image-canvas {
  position: absolute;
  display: none;
  z-index: 1;
}

.size-info {
  position: absolute;
  color: #ffffff;
  font-size: 12px;
  background: rgba(40, 40, 40, 0.8);
  padding: 5px 10px;
  border-radius: 2px;
  font-family: Arial Consolas sans-serif;
  display: none;
  z-index: 2;
}

.toolbar {
  position: absolute;
  color: #343434;
  font-size: 12px;
  background: #f5f5f5;
  padding: 5px 10px;
  border-radius: 4px;
  font-family: Arial Consolas sans-serif;
  display: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
  z-index: 2;
  align-items: center;
}

.toolbar .iconfont {
  font-size: 24px;
  padding: 2px 5px;
}
</style>
