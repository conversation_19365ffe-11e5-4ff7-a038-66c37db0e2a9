<template>
    <div class="bg" v-if="clasOver">
        <div class="sz">
            <img src="@/assets/living/xin.png" alt="" class="bei">
            <img src="@/assets/living/thby.png" alt="" class="trophy">
            <img src="@/assets/living/路径 13.png" alt="" class="itema">
            <img src="@/assets/living/路径 5.png" alt="" class="itema">
            <img src="@/assets/living/路径 14.png" alt="" class="itema">
            <img src="@/assets/living/路径 15.png" alt="" class="itema">
            <img src="@/assets/living/路径 29.png" alt="" class="itema">
            <img src="@/assets/living/路径 31.png" alt="" class="itema">
            <img src="@/assets/living/路径 32.png" alt="" class="itema">
            <img src="@/assets/living/Fill 45.png" alt="" class="itema">
            <img src="@/assets/living/Fill 17.png" alt="" class="itema">
        </div>
    </div>
</template>

<script>
export default {
  name: 'trophy-com',
  props: {},
  components: {},
  data () {
    return {
        clasOver:false,
    }
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    onShow(){
        this.clasOver = true;
        setTimeout(()=>{
            this.clasOver = false;
        },3000);
    },
    getBack(){
        this.$emit('back');
        this.clasOver = false;
    }
  },
  created () {
  },
  mounted () {
  },
  beforeDestroy () {
      clearTimeout(this.onShow);
  }
}
</script> 

<style lang='scss' scoped>
.bg{
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  .sz{
    width: 784px;
    height: 786px;
    position: relative;
    top: 237px;
    left: 568px;
    border-radius: 15px;
    // background: url('@/assets/living/wecom-temp-127771-4f3033ab8beae0fdddba3ea0e492a10e.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    justify-content: space-evenly;
    .trophy{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        background-size: 100% 100%;
    }
    .bei{
        width: 110%;
        height: 110%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        background-size: 100% 100%;
        animation: rotate 8s linear infinite;
        
    }
    @keyframes rotate {
        from {
            transform: rotate(0deg); /* 开始旋转 */
        }
        to {
            transform: rotate(360deg); /* 持续旋转 */
        }
    }
    .itema{
        width: 40px;
        height: auto;
        position: absolute;
        z-index: 3;
        animation: rotates 8s linear infinite;
    }
    img:nth-child(3){
        top: 7.464844vw;
        left: 2vw;
    }
    img:nth-child(4){
        top: 0.464844vw;
        left: 2vw;
    }
    img:nth-child(5){
        top: 3.464844vw;
        left: 8vw;
    }
    img:nth-child(6){
        top: 20.464844vw;
        left: -4vw;
    }
    img:nth-child(7){
        top: 10.464844vw;
        right: 2vw;
    }
    img:nth-child(8){
        top: 0.464844vw;
        right: 15vw;
    }
    img:nth-child(9){
        top: 0.464844vw;
        right: 0vw;
    }
    img:nth-child(10){
        top: 8.464844vw;
        right: -5vw;
    }
    img:nth-child(11){
        top: 15.464844vw;
        right: -5vw;
    }
    @keyframes rotates {
        from {
            bottom: 100px;
        }
        to {
            top: 800px;
        }
    }
    .text{
        position: absolute;
        top: 250px;
        font-size: 30px;
        font-weight: bold;
    }
    .class-but{
        position: absolute;
        top: 400px;
        img{
            width: 240px;
            height: 80px;
            cursor: pointer;
        }
        // position: absolute;
        // bottom: 100px;
        
    }
  }
}
</style>
