<template>
    <div v-if="clasOver" class="bg">
        <div class="sz" :style="{
        'background-image': `url(${require('@/assets/living/wecom-temp-127771-4f3033ab8beae0fdddba3ea0e492a10e.png')})`,
        'background-size': '100% 100%',
        }">
            <div class="text">
                {{ errText }}
            </div>
            <div class="class-but" @click="getBack">
                <img src="@/assets/living/queren.png" alt="">
            </div>
        </div>
    </div>
</template>

<script>
import config from "@/config";
export default {
  name: 'class-isover',
  props: {},
  components: {},
  data () {
    return {
        clasOver:false,
        errText:"结课已下课",
        indexShowAppraise:false
    }
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    onShow(text,type){
        this.errText = text;
        this.clasOver = true;
        this.indexShowAppraise = type;
        this.$emit('back',1);
    },
    getBack(){
        this.clasOver = false;
        this.$router.push({
        path: "/",
        query: {
          defaultIndex: 1,
          currentIndex: Date.now(),
          roomid:this.indexShowAppraise ? config.roomId : null
        },
      });
    }
  },
  created () {
  },
  mounted () {
  },
}
</script> 

<style lang='scss' scoped>
.bg{
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  .sz{
    width: 784px;
    height: 606px;
    position: relative;
    top: 237px;
    left: 568px;
    border-radius: 15px;
    background: url('@/assets/living/wecom-temp-127771-4f3033ab8beae0fdddba3ea0e492a10e.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    justify-content: space-evenly;
    .text{
        position: absolute;
        top: 250px;
        font-size: 30px;
        font-weight: bold;
    }
    .class-but{
        position: absolute;
        top: 400px;
        img{
            width: 240px;
            height: 80px;
            cursor: pointer;
        }
        // position: absolute;
        // bottom: 100px;
        
    }
  }
}
</style>
