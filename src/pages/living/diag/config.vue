<template>
    <div v-if="yarDiag" class="bg">
        <div class="sz">
            <div class="lay">
              <div class="title item">
                设置
              </div>
              <div class="close item" @click="close">
                <!-- <img src="" alt=""> -->
              </div>
            </div>
            <div class="table">
              <div class="tab">
                <div :class="name == '视频' ? 'tabitem active':'tabitem'" @click="tab('视频')">
                  <div class="flex">
                    <img class="img" :src="name == '视频' ? `${require('@/assets/living/video.png')}`:`${require('@/assets/living/videonow.png')}`" alt="">
                    视频
                  </div>
                </div>
                <div :class="name == '音频' ? 'tabitem active':'tabitem'" @click="tab('音频')">
                  <div class="flex">
                    <img class="img" :src="name == '音频' ? `${require('@/assets/living/auit.png')}`:`${require('@/assets/living/auitnow.png')}`" alt="">
                    音频
                  </div>
                  
                </div>
              </div>
              <div class="body">
                  <div v-show="name=='视频'" class="item">
                    <p class="color">效果预览</p>
                    <div
                      id="localVideo"
                    ></div>
                    <div class="items">
                      <p class="color">本地摄像头</p>
                      <selectDiec :deviceType="'camera'"></selectDiec>
                      <!-- <select id="dropdown" v-model="config.came" @change="getCam">
                        <option :value="item.deviceId" v-for="(item,index) in config.cameraList" :key="index">{{ item.label }}</option>
                      </select> -->
                    </div>
                  </div>
                  <div v-show="name === '音频'" class="item">
                    <p class="color">麦克风</p>
                    <selectDiec :deviceType="'microphone'"></selectDiec>
                    <div class="flex">
                      <img class="img" src="@/assets/living/mic.png" alt="">
                      <div class="bo">
                        <div v-for="(item, index) in 25" :key="index" class="RMS" :style="`${Math.ceil(audioLevel / 4) > index ? 'background: #FFD242' : ''}`"></div>
                      </div>
                    </div>
                    <div class="flex sliderWrap">
                        <img class="img" style="margin-right:3px" src="@/assets/living/smal.png" alt="">
                        <el-slider v-model="setAudioCaptureVolume" style="width:100%" :show-tooltip="false"></el-slider>
                        <img class="img" style="margin-left:3px" src="@/assets/living/gour.png" alt="">
                      </div>
                    <div class="items">
                      <p class="color">扬声器</p>
                      <selectDiec :deviceType="'speaker'"></selectDiec>
                      <div class="flex sliderWrap">
                        <img class="img" style="margin-right:3px" src="@/assets/living/smal.png" alt="">
                        <el-slider v-model="loudVolume" style="width:100%" :show-tooltip="false"></el-slider>
                        <img class="img" style="margin-left:3px" src="@/assets/living/gour.png" alt="">
                      </div>
                    </div>
                    <!-- <div class="items">
                      <p class="switch">
                        <el-switch
                        v-model="reduction"
                      >
                      </el-switch>
                      背景音降噪（建议大多数场景下开启）
                      </p>
                      <p class="switch">
                        <el-switch
                        v-model="musicians"
                      >
                      </el-switch>
                      音乐模式（建议专业场景中开启，如声乐和乐器的演奏）
                      </p>
                    </div> -->
                  </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import TRTC from "trtc-sdk-v5";
import RTCAIDenoiser from 'rtc-ai-denoiser';
import selectDiec from "../components/selectDiec";
import rtc from "../components/mixins/rtc"
export default {
  name: 'config',
  props: {

  },
  components: {
    selectDiec
  },
  mixins: [rtc],
  data () {
    return {
      yarDiag:false,
      name:'视频',
      setAudioCaptureVolume:100,
      loudVolume:100,
      localStream:null,//初始化
      value1:false,
      reduction:true,
      musicians:true,
    }
  },
  // 计算属性
  computed: {
    audioLevel() {
      return this.$store.getters.doneAudioLevel;
    },
    microphoneVolume() {
      return this.$store.getters.doneSpeakerVolume;
    },
    activeMicrophoneId() {
      return this.$store.getters.activeMicrophoneId;
    },
    activeSpeakerId() {
      return this.$store.getters.activeSpeakerId;
    },
    activeCameraId() {
      return this.$store.getters.activeCameraId;
    },
    isVideoMuted() {
      return this.$store.getters.doneCamera;
    },
    doneCameraAuthority() {
      return this.$store.getters.doneCameraAuthority;
    },
    doneMicrophone() {
      return this.$store.getters.doneMicrophone;
    },

  },
  // 侦听器
  watch: {
    setAudioCaptureVolume: {
      handler: function (val) {
        if (val) {
          this.$store.dispatch("UPDATE_AUDIOVOLUME", val);
          console.log(val);
          // 设置采集麦克风音量
          // this.settingAudioCaptureVolume(Number(val));
          this.$bus.emit('settingAudioCaptureVolume', Number(val))
        }
      },
      deep: true
    },
    loudVolume: {
      handler: function (val) {
        if (val) {
          this.$bus.emit("setAudioPlayoutVolume", val);
          // this.setAudioPlayoutVolume(val);
          this.$store.dispatch("SET_SPEAKER_VOLUME", val);
        }
      },
      deep: true
    },
    captureAudioVolume: {
      deep: true,
      handler(val) {
        this.setAudioVolume = val;
      }
    },
    activeMicrophoneId: {
      immediate: true,
      handler(val) {
        if (this.deviceType === "microphone") {
          this.activeDeviceId = val;
        }
      }
    },
    activeSpeakerId: {
      immediate: true,
      handler(val) {
        if (this.deviceType === "speaker") {
          this.activeDeviceId = val;
        }
      }
    },
    activeCameraId: {
      immediate: true,
      handler(val) {
        this.switchDevice("video", val);
        // if (this.deviceType === "camera") {
          console.log("activeCameraId", val);
          this.activeDeviceId = val;
        // }
      }
    },
  },
  methods: {
    onShow(){
      this.yarDiag = true;
      this.loudVolume = this.microphoneVolume;
      navigator.mediaDevices.addEventListener(
        "devicechange",
        this.initDeviceList
      );
      
      this.tab(this.name);
      this.initClient();
      this.$nextTick(()=>{
        this.startLocalVideo(document.getElementById("localVideo"))
      })
    },
    tab(name){
      this.name = name;
    },
    async initDeviceList() {
      this.deviceList = await this.getDeviceList(this.deviceType);
    },
    async getDeviceList() {
      switch (this.deviceType) {
        case "camera":
          this.deviceList = await TRTC.getCameraList();
          break;
        case "microphone":
          this.deviceList = await TRTC.getMicrophoneList();
          break;
        case "speaker":
          this.deviceList = await TRTC.getSpeakerList();
          break;
        default:
          break;
      }
    },
    close(){
      this.yarDiag = false;
      this.destroyLocalStream();
      // navigator.mediaDevices.removeEventListener(
      //   "devicechange",
      //   this.initDeviceList
      // );
    }

  },
  created () {
    console.log(this.$bus)
  },
  mounted () {
    this.getDeviceList();
  },
  beforeDestroy() {
    this.destroyLocalStream();
    // navigator.mediaDevices.removeEventListener(
    //   "devicechange",
    //   this.initDeviceList
    // );
  }
}
</script> 

<style lang='scss' scoped>
.bg{
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  .sz{
    width: 884px;
    // height: 706px;
    position: absolute;
    top: 237px;
    left: 568px;
    border-radius: 15px;
    background-color: white;
    box-shadow: -0.5px 1px 10px #5d5a5a;
    .lay{
      width: 100%;
      height: 80px;
      font-size: 30px;
      font-weight: 600;
      background:#438EFF;
      color: white;
      display: flex;
      justify-content: revert;
      align-items: center;
      justify-content: space-between;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      // padding: 10px;
      .close{
        width: 42px;
        height: 42px;
        background: url("@/assets/living/close.png") no-repeat;
        background-size: 100% 100%;
        padding: 0;
        cursor: pointer;
        // img{
        //   width: 42px;
        //   height: 42px;
        // }
      }
    }
    .table{
      display: flex;
      // height: 650px;;
      .tab{
        width: 200px;
        height: 120px;
        font-size: 30px;
        margin-top: 30px;
        .tabitem{
          width: 100%;
          height: 56px;
          line-height: 56px;
          text-align: center;
          color: #8392A6;
          display: flex;
          justify-content: center;
          font-weight: 500;
          // background-color: #F7F7F7;
          &.active{
            color: white;
            background: #438EFF;
            font-weight: 500;
            // border-bottom: 2px solid #438EFF;
        }
        .flex{
          display: flex;
          align-items: center;
          .img{
            width: 35px;
            height: 35px;
            margin-right: 10px;
            margin-left: 10px;
          }
        }
      }
    }
    .body{
      border-left: 0.2px solid #E6E6E6;
      height: 700px;
      .color{
        font-size: 25px;
        font-weight: 500;
        color:#438EFF;
        margin-top: 10px;
        margin-bottom: 10px;
      }
      #localVideo{
        width: 100%;
        height: 404px;
      }
      .item{
        width: 700px;
        // height: 306px;
        .items{
          margin-top: 40px;
          .switch{
            font-size: 26px;
            font-weight: bold;
            color:#8392A6;
            margin-top: 20px;
          }
        }
        ::v-deep video{
          border-radius: 9.6px;
          object-fit:fill !important;
        }
        .flex{
            margin-top: 20px;
            display: flex;
            align-items: center;
            
            input{
              height: 20px;
              width: 100%;
            }
            .img{
              width: 24px;
              height: 24px;
            }
            .bo{
              
              .RMS{
                width: 8px;
                height: 20px;
                background: #D9D9D9;
                border-radius: 9px;
                display: inline-block;
                margin-left: 12px;
              }
            }
          }
      }
      .sliderWrap {
          width: 100%;
          ::v-deep .el-slider__runway {
            height: 20px;
            border-radius: 200px;
            margin:0;
            .el-slider__button-wrapper {
              width: 50px;
              height: 50px;
              top: -15px
            }
            .el-slider__button {
              width: 24px;
              height: 24px;
              background-color: #1bbd86;
              border: 2px solid #fff;
            }
            .el-slider__bar {
              position: relative;
              height: 20px;
              background-image: linear-gradient(
                270deg,
                #17ba82 -0.99%,
                #33c493 101.16%
              );
              border-radius: 100px;
            }
            .el-slider__bar::after {
              position: absolute;
              content: "";
              width: 100%;
              height: 10px;
              background-color: #fff;
              left: 0;
              top: -6px;
              transform: rotate(-0.5deg);
            }
            .el-slider__bar::before {
              position: absolute;
              content: "";
              width: 100%;
              height: 10px;
              background-color: #fff;
              left: 0;
              bottom: -6px;
              transform: rotate(0.5deg);
            }
          }
          // ::v-deep .el-slider__runway::after {
          //   position: absolute;
          //   content: "";
          //   width: 100%;
          //   height: 10px;
          //   background-color: #fff;
          //   left: 0;
          //   top: -6px;
          //   transform: rotate(-0.7deg);
          // }
          // ::v-deep .el-slider__runway::before {
          //   position: absolute;
          //   content: "";
          //   width: 100%;
          //   height: 10px;
          //   background-color: #fff;
          //   left: 0;
          //   bottom: -6px;
          //   transform: rotate(0.7deg);
          // }
        }
    }
  }
    .item{
      padding: 20px 20px 20px 40px;
    }
  }
}
::v-deep .el-switch{
  width: 40px;
  height: 20px;
  line-height: 20px;
  margin-right: 10px;
  .el-switch__core{
    width: 40px !important;
    height: 20px;
  }
  .el-switch__core:after{
    top: -3px;
    left: 0px;
    width: 15px;
    height: 15px;
  }
}
::v-deep .is-checked{
  .el-switch__core::after{
    left: 66%;
    margin-left: 0;
  }
}
::v-deep select{
  background-position: 600px center !important;
} 
</style>
