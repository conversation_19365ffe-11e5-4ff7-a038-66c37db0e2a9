<template>
  <div v-show="loading" class="bg">
    <div class="sz">
      <div class="login">
        <div ref="LottieAnimation" :style="{ width, height }"></div>
        <div class="text">
          {{ errText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "live-loading",
  props: {
    errText: {
      type: String,
      default: "正在加载中...",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    // data.json文件的路径，
    json_data_url: {
      required: true,
      type: String,
      default: () => null,
    },
    autoplay: {
      type: Boolean,
      default: true,
    },
    loop: {
      type: Boolean,
      default: true,
    },
  },
  components: {},
  data() {
    return {
      loading: false,
    };
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    onShow() {
      this.loading = true;
    },
    onHide() {
      this.loading = false;
    },
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      const { loop, autoplay, json_data_url } = this;
      const url =
        location.origin + "/lottie_animation/" + json_data_url + "/data.json";
      const container = this.$refs.LottieAnimation;
      // eslint-disable-next-line no-undef
      const anim = lottie.loadAnimation({
        container,
        renderer: "svg",
        loop,
        autoplay,
        // animationData只能加载本地json，优先级高于path
        // animationData: props.jsonData,
        // 拼接导入地址，使用时只需要传入项目public目录下对应的动画目录
        path: url,
      });
      this.$emit("init", anim);
    });
  },
};
</script> 

<style lang='scss' scoped>
.bg {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  .sz {
    width: 784px;
    height: 606px;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    // left: 568px;
    border-radius: 15px;
    // background: url('@/assets/living/wecom-temp-127771-4f3033ab8beae0fdddba3ea0e492a10e.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    justify-content: space-evenly;
    .login {
      width: 35%;
      height: 50%;
      position: relative;
      background: white;
      border-radius: 12px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .text {
      position: absolute;
      top: 250px;
      width: 100%;
      text-align: center;
      font-size: 25px;
      font-weight: bold;
      color: rgb(72, 141, 232);
    }
    .class-but {
      position: absolute;
      top: 400px;
      img {
        width: 240px;
        height: 80px;
        cursor: pointer;
      }
      // position: absolute;
      // bottom: 100px;
    }
  }
}
</style>
