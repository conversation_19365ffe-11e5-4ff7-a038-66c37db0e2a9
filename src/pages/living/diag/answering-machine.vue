<template>
  <div class="bg">
    <VueDragResize
    :h="'auto'"
        :w="'auto'"
        :isActive="true"
        :isDraggable="true"
        :isResizable="false"
        :y="y ? y :(screenHeight * 1) / 2"
        :x="x ? x : (screenWidth * 1) / 15"
        v-on:resizing="resize"
        v-on:dragging="resize"
        :parentLimitation="true"
        :z="999"
      >
    <div
      class="sz"
      :style="{
        'background-image': `url(${require('@/assets/living/wecom-temp-76533-e0252221f03ccace70f9706d28160379.png')})`,
        'background-size': '100% 100%',
      }"
    >
      <div
        class="awer_bg"
        :style="{
          'background-image': `url(${require('@/assets/living/awbg.png')})`,
          'background-size': '100% 100%',
        }"
      >
        <div class="timer">
          {{ diffTime }}
        </div>
        <div class="tab_sele">
          <div
            class="tab_item"
            v-for="(item, index) in doneAnswerOption.option"
            :key="index"
            @click.stop="hanTab(index)"
           @touchend="hanTab(index)"
            @touchcancel="hanTab(index)"
          >
            <div :class="active == item ? 'item_bod active' : 'item_bod'">
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <div class="class-but" @click.stop="getBack" @touchend="getBack"
      @touchcancel="getBack">
        <img v-if="disabled" src="@/assets/living/huise.png" alt="">
        <img
        v-else
          :src="
            answerStatus
              ? require('@/assets/living/Group 346.png')
              : require('@/assets/living/aw.png')
          "
          alt=""
        />
      </div>
    </div>
  </VueDragResize>
  </div>
</template>

<script>
import VueDragResize from "vue-drag-resize";
export default {
  name: "answering-machine",
  props: {
    liveType: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    VueDragResize
  },
  data() {
    return {
      // list: [
      //   {
      //     value: "A",
      //     label: "A",
      //   },
      //   {
      //     value: "B",
      //     label: "B",
      //   },
      //   {
      //     value: "C",
      //     label: "C",
      //   },
      //   {
      //     value: "D",
      //     label: "D",
      //   },
      //   {
      //     value: "D",
      //     label: "D",
      //   },
      // ],
      active: "",
      timer: null,
      diffTime: "00:00:00",
      answerStatus: true,
      interval_unix:0,
      // 禁用按钮
      disabled: false,
      screenHeight: 0,
      screenWidth: 0,
      x:"",
      y:110
    };
  },
  // 计算属性
  computed: {
    doneQuestionId() {
      return this.$store.getters.doneQuestionId;
    },
    doneAnswerOption() {
      return this.$store.getters.doneAnswerOption;
    },
  },
  // 侦听器
  watch: {
    doneStartAnswer: {
      async handle(newVal, oldVal) {
        if (newVal && this.liveType) {
          // 开始计时
          this.startTimer();
        }
      },
      deep: true,
    },
    active:{
      handler(newVal, oldVal) {
        if (!this.answerStatus) {
          this.disabled = false;
        }
      },
      deep: true,
    },
    doneAnswerOption:{
      handler(newVal, oldVal) {
        if (newVal) {
          this.active = newVal.answer;
          // this.answerStatus = true;
          // this.disabled = true;
        }
      },
      deep: true,
    }
  },
  methods: {
    getBack() {
      if(this.disabled) return;
      if (this.active) {
        this.answerStatus = false;
        this.disabled = true;
        // clearInterval(this.timer);
        // this.$store.dispatch("UPDATE_ANSWER_STATUS", false);
      
      this.$emit("sendAnswer", {
        id: this.doneAnswerOption.question_id,
        answer: this.active,
        correct:this.doneAnswerOption.correct_answer ?  this.doneAnswerOption.correct_answer === this.active : true ,
        correct_answer: this.doneAnswerOption.correct_answer,
        interval_unix: this.interval_unix,
        option: this.doneAnswerOption.option,
      });
    }
    },
    hanTab(index) {
      this.active = this.doneAnswerOption.option[index];
    },
    startTimer() {
      let remainingSeconds = this.doneAnswerOption.after_time % 3600;
      let seconds = this.doneAnswerOption.after_time != 0 ? remainingSeconds % 60 : 0;
      let minutes = this.doneAnswerOption.after_time != 0 ? Math.floor(remainingSeconds / 60) : 0;
      let hours =this.doneAnswerOption.after_time != 0 ? Math.floor(this.doneAnswerOption.after_time / 3600) : 0;
      this.timer = setInterval(() => {
        seconds++;
        if (seconds === 60) {
          seconds = 0;
          minutes++;
        }
        if (minutes === 60) {
          minutes = 0;
          hours++;
        }
        // 格式化时间
        const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
          this.interval_unix = this.convertTimeToSeconds(hours, minutes, seconds);
        this.diffTime = formattedTime;
      }, 1000);

      seconds++;
      if (seconds === 60) {
        seconds = 0;
        minutes++;
      }
      if (minutes === 60) {
        minutes = 0;
        hours++;
      }
      // 格式化时间
      const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
      // 更新计时器显示
      this.diffTime = formattedTime;
    },
    convertTimeToSeconds(hours, minutes, seconds) {  
      return (hours * 3600) + (minutes * 60) + seconds;  
    },
    resize(newRect) {
      this.width = newRect.width;
      this.height = newRect.height;
      this.top = newRect.top;
      this.left = newRect.left;
    },
    // 改变位置
    changePosition(x, y) {
      this.x = x;
      this.y = y;
    },
  },
  created() {
    this.screenHeight = document.body.clientHeight;
    this.screenWidth = document.body.clientWidth;
  },
  mounted() {
    this.active = this.doneAnswerOption.answer;
    this.answerStatus = this.doneAnswerOption.answer ? false : true;
    this.disabled = this.doneAnswerOption.answer ? true : false
    this.startTimer();
  },
};
</script>

<style lang="scss" scoped>
.bg {
  width: 100%;
  height: 100%;
  // background: rgba(0, 0, 0, 0.5);
  // position: fixed;
  // top: 0;
  // left: 0;
  // z-index: 15;
  .sz {
    width: 324px;
    // height: 783px;
    // position: relative;
    // top: 237px;
    // left: 768px;
    border-radius: 15px;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 100px;
    min-height: 25vw;
    .awer_bg {
      width: 70%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 20px;
      margin: 50% auto 30px auto;
    }
    .timer {
      font-size: 30px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    .tab_sele {
      width: 100%;
      font-size: 50px;
      font-weight: bold;
      color: #feb415;
      border-top: 0.6px solid #feb415;
      .tab_item {
        width: 50%;
        text-align: center;
        display: inline-block;
        margin-top: 20px;
        .item_bod {
          width: 66px;
          height:66px;
          font-size:54px;
          cursor: pointer;
          border: 1px solid #feb415;
          border-radius: 50%;
          background: #ffdba4;
          margin: 0 auto;
          line-height: 60px;
        }
        .active {
          border: 1px solid #e0f5ff;
          color: #e0f5ff;
          background: linear-gradient(180deg, #30cdff 0%, #2eb1ff 100%);
        }
      }
    }
    .class-but {
      // position: absolute;
      // top: 580px;
      // left: 30%;
      display: flex;
      justify-content: center;
      img {
        width: 200px;
        height: 80px;
        cursor: pointer;
      }
      // position: absolute;
      // bottom: 100px;
    }
  }
}
</style>
