<template>
  <div class="scroll_table">
    <VueDragResize
      :h="'auto'"
      :w="'auto'"
      :isActive="true"
      :isDraggable="true"
      :isResizable="false"
      :y="10"
      :x="(screenWidth * 1) / 2"
      v-on:resizing="resize"
      v-on:dragging="resize"
      :parentLimitation="true"
      :z="999"
    >
      <div
        class="scroll"
        :style="{
          'background-image': `url(${require('@/assets/living/抽奖背景.png')})`,
        }"
      >
        <div style="display: inline-block" class="roll">
          <vue-seamless-scroll
            v-show="!showDown"
            ref="a"
            :data="tableList"
            class="seamless-warp"
            style="width: 100%"
            :class-option="{
              step: step,
              limitMoveNum: limitMoveNum,
              hoverStop: false,
              direction: 1,
              openWatch: true,
            }"
          >
            <el-table
              id="top"
              :show-header="false"
              :data="tableList"
              class="table_scroll"
            >
              <el-table-column
                v-for="(item, index) in columns"
                :key="index + 'i'"
                :label="item.label"
                :prop="item.prop"
              />
            </el-table>
          </vue-seamless-scroll>
          <el-table
            v-show="showDown"
            id="top1"
            :show-header="false"
            :data="winList"
            class="table_scroll"
          >
            <el-table-column
              v-for="(item, index) in columns"
              :key="index + 'i'"
              :label="item.label"
              :prop="item.prop"
            >
              <template slot-scope="scope">
                <div :class="{ check: scope.row.student_id === checkId }">
                  {{ scope.row.student_name }}
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <div
        @click="handStart"
        class="start"
        :style="{
          'background-image':
            text === '开始'
              ? `url(${require('@/assets/living/开始点名.png')})`
              : `url(${require('@/assets/living/结束点名.png')})`,
          'background-size': '100% 100%'
        }"
      ></div> -->
        </div>
      </div>
    </VueDragResize>
  </div>
</template>
<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import config from "@/config";
import VueDragResize from "vue-drag-resize";
export default {
  data() {
    return {
      showDown: false,
      text: "开始",
      step: 0,
      columns: [{ prop: "student_name", label: "姓名" }],
      winList: [],
      tableList: [],
      limitMoveNum: 0,
      checkId: -1,
      callRollDialog: false,
      screenHeight: 0,
      screenWidth: 0,
    };
  },
  components: { vueSeamlessScroll, VueDragResize },
  watch: {},
  computed: {},
  props: {
    // 抽奖人员
    lotteryList: {
      type: Array,
      default: () => [],
    },
    // 中奖人员
    winListId: {},
    // 最后7个人
    viewsId: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    this.screenHeight = document.body.clientHeight;
    this.screenWidth = document.body.clientWidth;
    this.getList();
    this.$bus.on("handStart", this.handStart);
  },
  methods: {
    async getList() {
      this.tableList = this.lotteryList ?? [];
      this.limitMoveNum = this.tableList.length;
    },
    // 获取中奖人 以及中奖展示其余人
    async getWinNum() {
      this.checkId = this.winListId[0];
      this.winList = this.viewsId ?? [];
      console.log(this.winList);
      // 当房间人数大于7
      // if (this.tableList.length > 7) {
      //   // const findItem = this.tableList.find(
      //   //   (item) => item.student_id === this.checkId
      //   // );
      //   // this.winList = this.noRepeatFilter(
      //   //   this.viewsId,
      //   //   7,
      //   //   this.viewsId.findIndex((item) => item.student_id === this.checkId)
      //   // );
      //   this.winList = this.viewsId ?? [];
      // } else {
      //   this.winList = this.tableList;
      // }

      console.log(this.tableList);
    },
    noRepeatFilter(arr, num, index) {
      const newArr = JSON.parse(JSON.stringify(arr));
      newArr.splice(index, 1);
      console.log("newArr", newArr);
      const resultData = [];
      for (let i = 0; i < num; i++) {
        const random = Math.floor(Math.random() * newArr.length);
        resultData.push(newArr[random]);
        newArr.splice(random, 1);
      }
      return resultData;
    },

    handStart() {
      if (this.text === "开始") {
        this.getWinNum();
        this.showDown = false;
        this.step = 1;
        this.$refs.a._startMove();
        this.text = "暂停";
      } else {
        this.getWinNum();
        this.showDown = true;
        this.$refs.a._stopMove();
        this.text = "开始";
        this.step = 0;
      }
    },
    resize(newRect) {
      this.width = newRect.width;
      this.height = newRect.height;
      this.top = newRect.top;
      this.left = newRect.left;
    },
  },
  beforeDestroy() {
    this.$bus.off("handStart");
  },
};
</script>
<style lang="less" scoped>
.scroll_table {
  width: 100%;
  height: 100%;
  // width: 40vw;
  //   height: 34vw;
  // background-size: contain;
  // background-repeat: no-repeat;
  // position: fixed;
  // right: 106px;
  // bottom: 18.765625vw;
  // z-index: 999;
  .scroll {
    width: 470px;
    height: 500px;
    background-size: contain;
    background-repeat: no-repeat;
    // position: fixed;
    // right: 106px;
    // bottom: 18.765625vw;
    // z-index: 999;
  }
  ::v-deep .table .el-table__body-wrapper {
    display: none;
  }
  ::v-deep .el-table__body-wrapper {
    height: 100% !important;
    border-radius: 2vh;
    overflow-x: none;
  }
  .el-table__body-wrapper::-webkit-scrollbar {
    display: none;
  }
  .roll {
    position: absolute;
    top: 6.8vh;
    left: 4vw;
    width: 25vh;
    // height: 92px;
    height: 17vw;
  }
  .seamless-warp {
    height: 17vw;
    overflow: hidden;
    border-radius: 3.5vh;
    ::v-deep .table_scroll .el-table__header-wrapper {
      display: none;
    }
  }
}
::v-deep .el-table {
  padding: 0 !important;
  width: 100%;
  background-color: transparent;
  margin-left: 4%;
  height: 100% !important;
  .cell {
    text-align: center;
    color: #435588;
    font-size: 2vh !important;
    font-style: normal;
    font-weight: 500;
    height: 100%;
    padding: 0;
    border-radius: 56px;
    line-height: 5vh;
    .check {
      height: 100%;
      border: 6px solid #ff7272;
      line-height: 5vh;
      border-radius: 56px;
      background: linear-gradient(90deg, #ffecab 0%, #ffdc65 100%) !important;
    }
  }
}
::v-deep .el-table__body {
  width: 91% !important;
  // height:92px;
  //-webkit-border-horizontal-spacing: 13px;  // 水平间距
  -webkit-border-vertical-spacing: 8px; // 垂直间距
  tr {
    border-radius: 56px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 1) 0%,
      rgba(197, 231, 255, 1) 100%
    ) !important;
  }
}

::v-deep .el-table--border::after,
.el-table--group::after,
.el-table::before {
  height: 0;
}
::v-deep .el-table__row {
  // height: 6vh;
}
::v-deep col {
  width: 11.789063vw !important;
}
::v-deep .el-table__row > td {
  border-radius: 56px;
}
::v-deep .el-table__row:hover > td {
  background: linear-gradient(
    90deg,
    #d7e5ff 0%,
    rgba(215, 229, 255, 0) 100%
  ) !important;
}
::v-deep .el-table .el-table__cell {
  padding: 0 !important;
  border: none;
}
.start {
  position: absolute;
  bottom: -9.5vh;
  left: 9vh;
  width: 17vh;
  height: 6vh;
  cursor: pointer;
}
.is-leaf {
  border: none !important;
}
</style>
