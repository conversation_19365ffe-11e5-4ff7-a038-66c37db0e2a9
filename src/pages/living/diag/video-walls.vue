<template>
  <VueDragResize
    :h="'auto'"
    :w="'auto'"
    :isActive="true"
    :isDraggable="true"
    :isResizable="false"
    :z="999"
    :y="remoteList.length >= 3 ? 70 : 25"
    :x="screenWidth - 100"
    v-on:resizing="resize"
    v-on:dragging="resize"
    :parentLimitation="true"
  >
    <div id="lookLocalStream" class="video-box"></div>
  </VueDragResize>
</template>

<script>
import VueDragResize from "vue-drag-resize";
export default {
  name: "videoWalls",
  props: {
    remoteList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    VueDragResize,
  },
  data() {
    return {
      screenHeight: 0,
      screenWidth: 0,
    };
  },
  computed: {
    doneOnStage() {
      return this.$store.getters.doneOnStage;
    },
  },
  watch: {
    doneOnStage: {
      handler: function (val) {
        if (!val) {
          //   this.onShow();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    resize(newRect) {
      this.width = newRect.width;
      this.height = newRect.height;
      this.top = newRect.top;
      this.left = newRect.left;
    },
    onShow() {
      this.$nextTick(() => {
        this.$emit("startLocalVideo", "lookLocalStream", true);
      })
    },
    onHide() {
      this.$emit("updateLocalVideo");
    },
  },
  created() {},
  mounted() {
    this.screenHeight = document.body.clientHeight;
    this.screenWidth = document.body.clientWidth;
    this.onShow();
  },
};
</script>

<style lang="scss" scoped>
.video-box {
  width: 392px;
  height: 232px;
  // min-width: 392px;
  // min-height: 232px;
  margin-right: 3%;
  background-color: #3c3c3c;
  border-radius: 18px;
  overflow: hidden;
  position: relative;
  animation: spread 1s 1 alternate;
}
@keyframes spread {
  0% {
    transform: scale(0); /* 开始时缩小到原来的一半 */
  }
  100% {
    transform: scale(1); /* 结束时放大到原来的大小 */
  }
}
</style>
