<template>
  <div
    class="course-index"
    :style="{
      'background-image': `url(${require('@/assets/index/course_bg.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="back" :style="{
      'background-image': `url(${require('@/assets/index/back.png')})`,
      'background-size': '100% 100%',
    }" @click="back"></div>
    <div class="content">
      <videoCourse></videoCourse>
    </div>
  </div>
</template>

<script>
import videoCourse from "@/components/Index/videoCourse";

export default {
  name: "courseIndexWrap",
  data() {
    return {
    };
  },
  components: {
    videoCourse,
  },
  computed: {
    defaultIndex() {
      return this.$route.query.defaultIndex;
    }
  },
  methods: {
    back(){
      this.$store.commit("setImitateRoute", null);
    }
  },
};
</script>

<style scoped lang="less">
.course-index {
  width: 100vw;
  height: 100vh;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 40px;
    left: 56px;
    z-index: 10;
  }
}
</style>
