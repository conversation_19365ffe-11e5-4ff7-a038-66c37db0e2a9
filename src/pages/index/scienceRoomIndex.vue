<template>
  <div
    class="learn-hall-index"
    :style="{
      'background-image': `url(${require('@/assets/index/living.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div
      class="back"
      @click="back"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div class="tabs">
      <div class="play" @click="handPlayback">
        <img src="@/assets/living/playt.png" alt="" />
      </div>
    </div>
    <livingList v-if="isliveShow" :list="list" @getLiveList="getLiveList"></livingList>
  </div>
</template>
<script>
import config from "@/config";
import livingList from "@/components/Index/livingList.vue";
import liveApi from "@/api/living";
import moment from "moment";
export default {
  data() {
    return {
      list: [],
      pageInatin: {
        end_time: "",
        // page: 1,
        // page_num: 0,
        // page_size: 10,
        sort: "",
        start_time: "",
        // type:"live"
      },
      new_status: {},
      pageTotal: 0,
      enterRoomStatusText: "正在进入房间，请稍等...",
      isliveShow: false,
    };
  },
  created() {},
  mounted() {
    if (!this.$storage.$getStroage("liveToken")) {
      this.LiveToken();
      console.log("获取token");
    } else {
      this.getLiveList();
    }
    this.$bus.on("LiveToken", this.LiveToken);
  },
  methods: {
    back() {
      this.$store.commit("setImitateRoute", null);
    },
    async LiveToken() {
      try {
        let res = await liveApi.LiveToken();
        console.log("res", res);
        this.$storage.$setStroage("liveToken", res.data.token);
        this.getLiveList();
      } catch (err) {
        console.log(err);
      }
    },
    async getLiveList() {
      const today = moment().format("YYYY-MM-DD");
      this.pageInatin.start_time = today;
      this.pageInatin.end_time = today;
      try {
        let res = await liveApi.LiveCourseList({ ...this.pageInatin });
        this.list = res.data.code == 0 ? res.data.data.results : [];
        this.pageTotal = res.data.code == 0 ? res.data.data.count : 0;
        this.isliveShow = true;
      } catch (err) {
        this.$bus.emit("LiveToken");
      }
    },
    handPlayback() {
      this.$router.push({ name: "playbackPage" });
    },
  },
  components: {
    livingList,
  },
  beforeDestroy() {
    this.$bus.off("LiveToken", this.LiveToken);
  }
};
</script>
<style lang="less" scoped>
.learn-hall-index {
  width: 100vw;
  height: 100vh;
  padding-top: 48px;
  box-sizing: border-box;
  position: relative;
  .tabs-box {
    display: flex;
    position: relative;
    margin: 0 12px;
  }
  .back {
    position: absolute;
    top: 40px;
    left: 56px;
    width: 120px;
    height: 120px;
    z-index: 100;
  }
  .tabs{
    width: 100%;
    height:253px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .play {
    width: 300px;
    // height: 100px;
    
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
  }
  }
  
}
</style>
