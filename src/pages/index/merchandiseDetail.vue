<template>
  <div class="merchandise-detail">
    <div class="header">
      <div class="back" @click="back"></div>
      <span class="jcyt600">{{details.title}}</span>
    </div>
    <div class="service" @click="toService">
      <img src="@/assets/index/serve.png" />
    </div>
    <img :src="details.banner_list[0].url" class="block-img" v-if="details.banner_list"/>
    <div class="content">
      <p class="title jcyt500">{{details.title}}</p>
      <p class="describle jcyt400">{{details.description}}</p>
      <div class="flex-row justify-between">
        <p class="flex-row aligns-center">
          <span class="sale-price jcyt500">¥{{parseInt(details.current_price / 100)}}</span>
          <span class="price jcyt500" v-if="details.origin_price!=0">原价{{parseInt(details.origin_price)}}</span>
        </p>
        <div class="quota">
          <span class="jcyt400">剩余名额<i class="jcyt500">{{details.display_remain_seats}}</i></span>
        </div>
      </div>
    </div>
    <span class="service-title jcyt500">服务</span>
    <div class="info">
      <img :src="item.url" v-for="(item,index) in details.info_pictures" :key="index"/>
    </div>
    <div class="modal" v-if="closeVisible"></div>
    <div class="service-alert" :style="{'background-image': `url(${require('@/assets/index/bg.png')})`}" v-if="closeVisible">
      <img class="close" src="../../assets/index/cha.png" @click="close"/>
      <p class="title jcyt500">咨询客服</p>
      <img :src="details['cs_group']['before_sale_cs']['avatar']['url']" class="avatar"/>
      <p class="s-title jcyt500">{{details['cs_group']['before_sale_cs']['name']}}</p>
      <p class="t-title jcyt500">请扫码添加客服微信咨询哦~</p>
      <img :src="details['cs_group']['before_sale_cs']['qr_code']['url']" class="qrcode"/>
    </div>
  </div>
</template>
<script>
import merchandiesApi from "@/api/merchandiesZone";
export default {
  data(){
    return {
      details: {},
      closeVisible: false
    }
  },
  mounted(){
    let id = this.$route.query.id;
    this.getDetails(id);
  },
  methods:{
    back() {
      this.$router.push({
        path: "/",
      });
    },
    async getDetails(id){
      let res = await merchandiesApi.getMerchandiseInfoAPI({"merchandiseDetail_id": id});
      this.details = res.data;
    },
    toService(){
      __bl.sum("咨询客服");
      this.closeVisible = true;
    },
    close(){
      this.closeVisible = false;
    }
  }
}
</script>
<style lang="less" scoped>
.flex-row{
  display: flex;
  flex-direction: row;
}
.merchandise-detail {
  height: 100vh;
  overflow-y: scroll;
}
.service {
  position: fixed;
  right: 0;
  top: 260px;
  width: 160px;
  background-color: #fff;
  border-top-left-radius: 90px;
  border-bottom-left-radius: 90px;
  box-shadow: 0 15px 15px 0 rgba(117, 117, 117, 0.2);
  height: 130px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  img {
    width: 100px;
    margin-left: 10px;
  }
}
.aligns-center {
  align-items: center;
}
.block-img {
  display: block;
  width: 100vw;
  object-fit: contain;
}
.header {
  height: 80px;
  width: 100%;
  box-shadow: 0 5px 8px 0 rgba(94, 94, 94, .5);
  font-size: 28px;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  text-align: center;
  line-height: 80px;
}
.back{
  position: absolute;
  left: 20px;
  top: 30px;
  content: "";
  display: inline-block;
  height: 15px;
  width: 15px;
  border-width: 0 0 4px 4px;
  border-color: #000;
  border-style: solid;
  transform: matrix(0.71, 0.71, -.71, 0.71, 0, 0);
  -webkit-transform: matrix(0.71, 0.71, -.71, 0.71, 0, 0);
  cursor: pointer;
}
.title {
  font-size: 42px;
  color: #333333;
}
.describle {
  font-size: 26px;
  margin-top: 15px;
  margin-bottom: 35px;
  color: #333333;
}
.justify-between {
  justify-content: space-between;
}
.content {
  padding: 30px 20px;
}
.sale-price {
  font-size: 42px;
  color: #FF4438;
  font-weight: bold;
  margin-right: 10px;
}
.price {
  font-size: 24px;
  color: #B2B2B2;
  text-decoration: line-through;
}
.quota {
  padding: 0 25px;
  height: 60px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  background: #F5F5F7;
  color: #333333;
  margin-right: -20px;
  font-size: 26px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  i {
    font-style: normal;
    color: #FFA400;
    font-weight: bold;
  }
}
.service-title {
  font-size: 26px;
  padding: 20px 30px;
  height: 40px;
  display: inline-block;
  color: #88888D;
  border-top: 1px solid #f0f0f0;
  width: 100%;
}
.info {
  // padding: 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width:100%;
    display: block;
  }
}
.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}
.service-alert {
  width: 670px;
  height: 850px;
  border-radius: 55px;
  background-size: cover;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 11;
  .close {
    width: 36px;
    height: 36px;
    position: absolute;
    right: 56px;
    top: 32px;
    cursor: pointer;
  }
  .title {
    margin-top: 45px;
    font-size: 36px;
    color: #fff;
    text-align: center;
  }
  .avatar {
    width: 136px;
    height: 136px;
    border-radius: 50%;
    display: block;
    margin: 20px auto;
  }
  .s-title {
    font-size: 36px;
    color: #333333;
    text-align: center;
  }
  .t-title {
    margin-top: 20px;
    color: #BEBEBE;
    font-size: 28px;
    text-align: center;
  }
  .qrcode {
    width: 320px;
    height: 320px;
    display: block;
    margin: 20px auto;
  }

}
</style>