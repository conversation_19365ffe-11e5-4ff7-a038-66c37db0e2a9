<template>
  <div class="study-room-index" :style="{
    'background-image': `url(${require('@/assets/index/study_room_bg.png')})`,
    'background-size': '100% 100%',
  }">
    <div
      class="back"
      @click="back"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="title jcyt600" :style="{
        'background-image': `url(${require('@/assets/index/study_room_title.png')})`,
        'background-size': '100% 100%'
      }"></div>
      <div class="content flex-row">
        <div class="item" @click="goQuestion">
          <img src="@/assets/index/study_room_pic1.png" />
        </div>
        <div class="item" @click="goTo">
          <img src="@/assets/index/study_room_pic2.png" />
        </div>
        <div class="item" @click="goEvaluation">
          <img src="@/assets/index/study_room_pic3.png" />
        </div>
      </div>
  </div>
</template>
<script>
export default {
  data(){
    return {

    }
  },
  methods: {
    back(){
      this.$store.commit("setImitateRoute", null);
    },
    goTo() {
      this.$router.push("/aiSelect");
    },
    goQuestion() {
      this.$router.push({
        path: "/questionBank"
      });
    },
    goEvaluation() {
      __bl.sum("评测");
      // Toast("研发中~敬请期待");
      this.$router.push("/evaluationIndex");
    },
  }
}
</script>
<style lang="less" scoped>
.study-room-index {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  padding-top: 60px;
  .back {
    position: absolute;
    top: 40px;
    left: 56px;
    width: 120px;
    height: 120px;
    z-index: 300;
    cursor: pointer;
  }
  .title {
    width: 288px;
    height: 104px;
    margin: 0 auto;
  }
  .content {
    padding: 0 136px;
    margin-top: 96px;
  }
  .item {
    box-shadow: rgba(0, 33, 135, .5) 0 16px 30px 0;
    width: 480px;
    height: 620px;
    border-radius: 72px;
    img {
      width: 100%;
    }
  }
  .item + .item {
    margin-left: 104px;
  }
}

</style>