<template>
  <div class="learn-hall-index" :style="{
    'background-image': `url(${require('@/assets/index/learn_hall_bg.png')})`,
    'background-size': '100% 100%',
  }">
    <div
      class="back"
      @click="back"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="tabs">
      <ul class="tabs-box"
      >
        <div class="line" id="line"></div>
        <li
          v-for="(item, index) in tabList"
          :key="index"
          class="tab jcyt600"
          :id="`tab${index}`"
          :class="{ 'tab-current': index == currentIndex }"
          @click="clickTab(index)"
        >
          {{ item }}
        </li>
      </ul>
    </div>
    <merchandise-list v-if="allCourseList.length > 0"
      :list="allCourseList[currentIndex]['data']"
      :tabs="tabList[currentIndex]"
    ></merchandise-list>
  </div>
</template>
<script>
import merchandiseList from '@/components/Index/merchandiseList.vue';
import config from "@/config";
import merchandiesApi from "@/api/merchandiesZone";

export default {
  data(){
    return {
      allCourseList: [],
      tabList: [],
      isAirSchool: "",
      currentIndex: 0,
    }
  },
  created() {
    this.isAirSchool = config.isAirSchool;
  },
  mounted(){
    this.getList();
  },
  methods: {
    back(){
      this.$store.commit("setImitateRoute", null);
    },
    async getList(){
      this.$store.commit("setApiLoading", true);
      let res = await merchandiesApi.merchandiseListAPI({
        show_type: "h5",
        app_business: this.isAirSchool ? "airSchool" : "higo"
      });
      const { data } = res;
      this.tabList = data.map((item) => item.name);
      this.allCourseList = data;
      this.$nextTick(() => {
        this.setLineWidth(0);
      })
      this.$store.commit("setApiLoading", false);
    },
    setLineWidth(index){
      let line = document.getElementById("line");
      let li = document.getElementsByClassName("tab");
      line.style.width = li[index].offsetWidth + "px";
      let num = 0;
      for(let i = 0; i < index ;i++) {
        num += li[i].offsetWidth;
      }
      line.style.left = num + "px";
    },
    clickTab(index) {
      this.currentIndex = index;
      this.setLineWidth(index);
    },
  },
  components: {
    merchandiseList
  }
}
</script>
<style lang="less" scoped>
.learn-hall-index {
  width: 100vw;
  height: 100vh;
  padding-top: 48px;
  box-sizing: border-box;
  position: relative;
  .tabs-box {
    display: flex;
    position: relative;
    margin: 0 12px;
  }
  .tabs {
    min-width: 272px;
    height: 112px;
    line-height: 112px;
    border-radius: 56px;
    background-image: linear-gradient(to bottom, #F8F9FF, #E4E9FF);
    box-shadow: #96ACF1 0 -2px 4px 1px inset;
    display: flex;
    text-align: center;
    position: relative;
    margin: 0 auto 120px;
    font-size: 40px;
    clear: both;
    padding: 12px 0;
    box-sizing: border-box;
    max-width: 816px;
    overflow-x: scroll;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    .tab {
      height: 88px;
      line-height: 88px;
      cursor: pointer;
      color: #4C5777;
      z-index: 9;
      padding: 0 44px;
      white-space: nowrap;
      box-sizing: border-box;
    }
    .line {
      position: absolute;
      z-index: 2;
      height: 88px;
      transition: all 0.3s;
      margin-left: 0;
      border-radius: 56px;
      left: 12px;
      background-image: linear-gradient(to bottom, #30C3FF, #00BAFF);
      box-shadow: rgba(99, 158, 198, .4) 0 3px 2px 0, #00A1EE 0 -6px 3px 0 inset;
    }
    .tab-current {
      color: #fff;
      height: 88px;
      margin-left: 0;
      border-radius: 56px;
      // background-image: linear-gradient(to bottom, #30C3FF, #00BAFF);
      // box-shadow: rgba(99, 158, 198, .4) 0 3px 2px 0, #00A1EE 0 -6px 3px 0 inset;
    }
  }
  .back {
    position: absolute;
    top: 40px;
    left: 56px;
    width: 120px;
    height: 120px;
    z-index: 100;
  }
}
</style>