<template>
  <div class="know-nie" :class="{'bg-color': currentIndex === 1}">
    <img class="back" src="@/assets/index/返回.png" @click="back"/>
    <div class="title">
      <ul class="title-ul">
        <div class="line" :class="currentIndex === 0 ? 'first' : 'second'"></div>
        <li v-for="(item,index) in tabs" :key="index" @click="changeTab(index)">
          <span :class="{'active': currentIndex === index}" class="jcyt500">{{item}}</span>
        </li>
      </ul>
    </div>
    <div class="main" :class="{'main-1' : currentIndex === 1}">
      <template v-if="currentIndex === 0">
        <img src="@/assets/index/knowNie1.png" class="pic1"/>
        <img src="@/assets/index/knowNie2.png" />
        <img src="@/assets/index/knowNie3.png" />
        <img src="@/assets/index/knowNie4.png" />
        <img src="@/assets/index/knowNie5.png" />
        <img src="@/assets/index/knowNie6.png" />
        <img src="@/assets/index/knowNie7.png" />
      </template>
      <template v-else-if="currentIndex === 1">
        <div class="content">
          <div v-for="(item,index) in allSchool" :key="index">
            <div class="part jcyt600">{{index}}</div>
            <div class="part-th">
              <div class="part-td part-td-w-1 jcyt600">校区</div>
              <div class="part-td part-td-w-2 jcyt600">详细地址</div>
              <div class="part-td part-td-w-3 jcyt600">咨询电话</div>
            </div>
            <div class="part-tr" v-for="(item1,index1) in item" :key="index1">
              <div class="part-td part-td-w-1 jcyt500">{{item1.branch_name}}</div>
              <div class="part-td part-td-w-2 jcyt500">{{item1.address}}</div>
              <div class="part-td part-td-w-3 jcyt500">{{item1.contact}}</div>
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>
<script>
import userApi from "@/api/user";
export default {
  data(){
    return {
      tabs: [ "了解聂道", "校区地址"],
      currentIndex: 0,
      allSchool: {}
    }
  },
  mounted(){
    if(this.currentIndex == 1) {
      this.getList();
    }
  },
  methods:{
    async getList(){
      let res = await userApi.branchSchoolList();
      this.allSchool = res.data;
    },
    back(){
      this.$router.push({
        path: "/",
        query: {
          defaultIndex: 0,
        },
      });
    },
    changeTab(index) {
      this.currentIndex = index;
      if(this.currentIndex == 1) {
        this.getList();
      }
    }
  }
}
</script>
<style lang="less" scoped>
.know-nie {
  height: 100vh;
  overflow-y: scroll;
}
.back {
  width: 86px;
  height: 86px;
  cursor: pointer;
  position: absolute;
  left: 30px;
  top: 30px;
  z-index: 10;
}
.title {
  position: absolute;
  z-index: 10;
  left: 50%;
  top: 35px;
  margin-left: -208px;
}
.title-ul {
  border-radius: 30px;
  box-shadow: 0 4px 8px 0 rgba(255, 136, 0, .2);
  width: 554px;
  height: 80px;
  position: relative;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  li {
    width: 277px;
    text-align: center;
    height: 80px;
    line-height: 80px;
    font-size: 42px;
    font-weight: bold;
    display: inline-block;
    background: #fff;
    cursor: pointer;
    span {
      position: relative;
      z-index: 12;
      color: #D5783E;
    }
    span.active {
      color: #ffffff;
    }
  }
}
.line {
  position: absolute;
  top: 0px;
  width: 277px;
  height: 80px;
  margin-left: 0;
  transition: all 0.3s;
  background: linear-gradient(to bottom, #FFAA33, #FF7900);
  z-index: 12;
}
.first {
  left: 0px;
}
.second {
  left: 277px;
}
.main {
  position: relative;
  img {
    width: 100vw;
    object-fit: contain;
    display: block;
  }
  .pic1 {
    margin-top: -150px;
  }
}
.bg-color {
  background-color: #FFDE8D;
}
.main-1 {
  background-color: #FFFBE8;
  border-radius: 27px;
  padding: 33px;
  margin-top: 200px;
  .content {
    border: 3px dashed #F3CA63;
    border-radius: 15px;
    padding: 26px;
    // width: 100%;
    // height: 1px;
    // background-image: linear-gradient(to right, #F3CA63 0%, #F3CA63 50%, transparent 50%);
    // background-size: 50px 1px;
    // background-repeat: repeat-x;
  }
  .part {
    height: 106px;
    width: 100%;
    border-radius: 21px;
    background-color: #FF9734;
    color: #fff;
    text-align: center;
    font-size: 56px;
    line-height: 106px;
    margin-top: 10px;
  }
  div.part:first-child {
    margin-top: 0;
  }
  .part-th, .part-tr {
    display: flex;
    flex-direction: row;
    margin-top: 10px;
    justify-content: space-between;
  }
  .part-td {
    height: 114px;
    text-align: center;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }
  .part-th .part-td {
    background-color: #FFF1A9;
    border-radius: 18px;
    color: #FF7D0E;
    font-size: 42px;
  }
  .part-td-w-1 {
    width: 19.8%;
  }
  .part-td-w-2 {
    width: 44%;
  }
  .part-td-w-3 {
    width: 35.7%;
  }
  .part-tr .part-td {
    background-color: #FFF7CF;
    border-radius: 18px;
    color: #494037;
    font-size: 29px;
    
  }
}
</style>