<template>
  <div class="howToLearn">
    <img src="../../../assets/index/返回_白.png" class="back" @click="back"/>
    <img src="../../../assets/index/pad1.png" class="main"/>
    <img src="../../../assets/index/pad2.png" class="main"/>
    <img src="../../../assets/index/pad3.png" class="main"/>
    <img src="../../../assets/index/pad4.png" class="main"/>
    <img src="../../../assets/index/pad5.png" class="main"/>
  </div>
</template>
<script>
export default {
  methods: {
    back(){
      this.$router.push({
        path: "/",
        query: {
          defaultIndex: 0,
        },
      });
    }
  }
}
</script>
<style lang="less" scoped>
.howToLearn {
  overflow-y: scroll;
  height: 100vh;
  position: relative;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    z-index: 10;
    border-radius: 50%;
    cursor: pointer;
  }
  .main {
    width: 100vw;
    object-fit: contain;
    display: block;
  }
}
</style>