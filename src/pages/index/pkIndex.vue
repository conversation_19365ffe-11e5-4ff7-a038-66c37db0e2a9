<template>
  <div class="pk-index">
    <!-- <div class="newCanvas land">
      <canvas id="canvas"></canvas>
    </div> -->
    <img src="@/assets/index/pk_bg.png" class="pk-bg"/>
    <div class="newCanvas">
      <canvas id="pkBgCanvas"></canvas>
    </div>
    <div class="content">
      <div
      class="back"
      @click="back"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
      <div class="title">
        <img src="@/assets/index/pk_title.png" />
      </div>
      <div class="select flex-row" @click="goWatchHall">
        <div class="select-left">
          <div class="select_icon" :style="{
            'background-image': `url(${require('@/assets/index/watch.png')})`,
            'background-size': 'cover'
          }" ></div>
        </div>
        <span class="jcyt600 label">观战大厅</span>
      </div>
      <img src="@/assets/index/pk_change_clothes.png" @click="goMatch()" class="change-clothes"/>
      <div class="box flex-row">
        <div class="left">
          <div class="name jcyt600"  :style="{
            'background-image': `url(${require('@/assets/index/pk_index_title.png')})`,
            'background-size': 'cover'
          }">
            {{username}}
          </div>
          <div class="newCanvas" :class="gender === 'female' ? 'girl' : 'boy'">
            <canvas id="peopleCanvas"></canvas>
          </div>
          <div class="boy-shadow"></div>
          <img src="@/assets/index/pk_station.png" class="station"/>
        </div>
        <div class="right flex-column">
          <div class="ranking" @click="goMatch('seasonIndex')" :style="{
                'background-image': `url(${require('@/assets/index/pk_pk_bg.png')})`,
                'background-size': '100% 100%'}">
            <div class="newCanvas ranking-canvas">
              <canvas id="rankingCanvas"></canvas>
            </div>
            <div class="text jcyt500 special">S{{rankInfo.season_index}}赛季</div>
          </div>
          <div class="ranking nie" @click="goMatch('nwpMatch')" :style="{
                'background-image': `url(${require('@/assets/index/pk_nie_bg.png')})`,
                'background-size': '100% 100%'}">
            <div class="newCanvas nie-canvas">
              <canvas id="nieCanvas"></canvas>
            </div>
            <div class="text jcyt500">限时开启</div>
          </div>
          <div class="ranking solo" @click="goMatch('rule')" :style="{
                'background-image': `url(${require('@/assets/index/pk_solo_bg.png')})`,
                'background-size': '100% 100%'}">
            <div class="newCanvas solo-canvas">
              <canvas id="soloCanvas"></canvas>
            </div>
            <div class="text jcyt500 special">全天开启</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { setWH, createRive } from "@/public/setCanvas";
import { Toast } from 'mint-ui';
import mineApi from "@/api/mine";
export default {
  data(){
    return {
      bgRive: "",
      peopleRive: "",
      rankingRive: "",
      nieRive: "",
      soloRive: "",
      username: "",
      rankInfo: {},
      gender: ""
    }
  },
  mounted(){
    setWH("pkBgCanvas");
    setWH("peopleCanvas");
    setWH("rankingCanvas");
    setWH("nieCanvas");
    setWH("soloCanvas");

    this.gender = this.$storage.$getStroage("gender");
    this.bgRive = createRive("pkBgCanvas", "pk_bg.riv");
    this.peopleRive = createRive("peopleCanvas", this.gender === "female" ? "girl.riv" : "boy.riv");

    
    this.rankingRive = createRive("rankingCanvas", "pk_btn.riv");
    this.nieRive = createRive("nieCanvas", "nie_btn.riv");
    this.soloRive = createRive("soloCanvas", "solo_btn.riv");
    this.username = this.$storage.$getStroage("username");
    this.getSeasonInfo();
  },
  created(){
    window.addEventListener("resize", this.onResize);
  },
  beforeDestroy() {
		window.removeEventListener("resize", this.onResize);
	},
  methods: {
    onResize() {
      this.peopleRive.resizeToCanvas();
      this.bgRive.resizeToCanvas();
      this.rankingRive.resizeToCanvas();
      this.nieRive.resizeToCanvas();
      this.soloRive.resizeToCanvas();
		},
    back(){
      this.$store.commit("setImitateRoute", null);
    },
    goWatchHall(){
      this.$router.push("/watchingHall");
    },
    goMatch(route) {
      if (route) {
        this.$router.push(`/${route}`);
      } else {
        Toast({message: "研发中~敬请期待", duration: 1000 });
      }
    },
    async getSeasonInfo(){
      let res = await mineApi.getSeasonInfo();
      this.rankInfo = res.data;
    }
  },
}
</script>
<style lang="less" scoped>
.pk-index {
  .content {
    width: 100vw;
    height: 100vh;
    z-index: 10;
    position: relative;
  }
  .back {
    position: absolute;
    top: 40px;
    left: 56px;
    width: 120px;
    height: 120px;
    z-index: 100;
  }
  .select {
    height: 80px;
    width: 280px;
    margin-left: 40px;
    background-image: linear-gradient(to bottom, #FFFFFF, #DAF4FF);
    border-radius: 44px;
    box-shadow: #8BB6E1 0 -3px 3px 0 inset, 0 4px 8px 0 rgba(0, 33, 135, .5);
    box-sizing: border-box;
    text-align: center;
    font-size: 36px;
    cursor: pointer;
    display: flex;
    position: absolute;
    right: 48px;
    top: 60px;
    .label {
      margin-left: 22px;
      line-height: 80px;
      color: #294584;
      padding-right: 30px;
    }
    .select_icon {
      width: 49px;
      height: 52px;
      margin-top: 18px;
      margin-left: 28px;
    }
    .select-left {
      width: 84px;
      height: 80px;
      border-radius: 44px 0 0 44px;
      background: #3168E7;
      position: relative;
      box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      &::after {
        content:"";
        width: 0; height: 0;
        border-color: transparent #3168E7; /*上下颜色 左右颜色*/
        border-width: 0 0 80px 20px;
        border-style: solid;
        position: absolute;
        top: 0;
        left: 84px;
        box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      }
    }
  }
  .title {
    padding-top: 64px;
    text-align: center;
    width: 100%;
    height: 121px;
    img {
      width: 376px;
      height: 121px;
    }
  }
  .left {
    position: relative;
    margin-left: 225px;
  }
  .right {
    align-items: flex-end;
    padding-top: 20px;
  }
  .change-clothes {
    width: 144px;
    height: 144px;
    position: absolute;
    top: 208px;
    left: 44px;
    z-index: 50;
    cursor: pointer;
  }
  .boy {
    width: 295px;
    height: 554.41px;
    left: 92px;
    top: 75px;
    z-index: 12;
  }
  .girl {
    width: 565.44px;
    height: 580px;
    left: -33px;
    top: 63px;
    z-index: 12;
  }
  .boy-shadow {
    background: rgba(0, 0, 0, .35);
    filter: blur(3.5px);
    width: 210.14px;
    height: 56.03px;
    position: absolute;
    border-radius: 50%;
    bottom: 50px;
    left: 148px;
    z-index: 11;
  }
  .box {
    width: 100%;
    justify-content: space-between;
    padding: 35px 80px 0;
    box-sizing: border-box;
  }
  .station {
    width: 512px;
    height: 236px;
    position: absolute;
    bottom: -35.6px;
    left: 0;
    z-index: 10;
  }
  .ranking {
    width: 644px;
    height: 220px;
    position: relative;
  }
  .nie {
    margin-right: 40px;
  }
  .solo {
    margin-right: 80px;
  }
  .ranking-canvas {
    width: 333.75px;
    height: 267px;  //1.25
    top: -63px;
    left: 20px;
  }
  .nie-canvas {
    width: 383.81px;
    height: 267px;  //1.4375
    top: -63px;
    left: unset;
    right: -22px;
  }
  .solo-canvas {
    width: 299.2px;
    height: 220px; //1.36
    top: -10px;
    left: 22px;
  }
  .name {
    width: 320px;
    height: 56px;
    line-height: 56px;
    color: #fff;
    font-size: 32px;
    position: absolute;
    top: 0;
    left: 79px;
    z-index: 100;
    text-align: center;
  }
  .text {
    font-size: 32px;
    line-height: 44px;
    color: #fff;
    width: 309px;
    height: 44px;
    text-align: center;
    position: absolute;
    bottom: 55px;
    left: -13px;
  }
  .special {
    right: -13px;
    left: unset;
  }
}
.pk-bg {
  position: absolute;
  width: 3856px;
  height: 1359px;
  left: -1050px;
  top: -70px;
}
</style>