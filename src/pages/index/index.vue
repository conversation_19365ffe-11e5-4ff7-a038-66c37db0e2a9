<template>
  <div class="indexWrap">
    <div class="indexWrap" v-show="imitateRoute == null">
      <div class="newCanvas land">
        <canvas id="canvas"></canvas>
      </div>
      <div class="newCanvas">
        <canvas id="bgCanvas"></canvas>
      </div>
      <div class="content">
        <IndexHeader v-if="reload"></IndexHeader>
        <div class="money flex-row">
          <!-- <div class="bg bg-right flex-row items-center" :style="{
            'background-image': `url(${require('@/assets/index/money_bg.png')})`,
            'background-size': '100% 100%',
          }">
            <img src="@/assets/index/coin.png" class="icon"/>
            <div class="num jcyt600">111</div>
          </div>
          <div class="bg flex-row items-center" :style="{
            'background-image': `url(${require('@/assets/index/money_bg.png')})`,
            'background-size': '100% 100%',
          }">
            <img src="@/assets/index/dimand.png" class="icon"/>
            <div class="num jcyt600">111</div>
            <img src="@/assets/index/add.png" class="add"/>
          </div> -->
        </div>
        <div class="icon-top-right flex-row">
          <img
            src="@/assets/index/shop.png"
            class="img-h-144"
            @click="toShop"
          />
          <img
            src="@/assets/index/lesson_guide.png"
            class="img-h-144"
            @click="goHowToLearn"
          />
          <img
            src="@/assets/index/know_company.png"
            class="img-h-144"
            @click="goKnowNie"
          />
        </div>
        <div class="icon-right flex-column">
          <img
            src="@/assets/index/settings.png"
            class="img-h-144"
            @click="settings"
          />
          <img
            src="@/assets/index/notice.png"
            class="img-h-144"
            @click="openNotice"
          />
        </div>
        <div class="icon-bottom flex-row">
          <img
            src="@/assets/index/mine.png"
            class="img-h-160"
            @click="toMineDetail"
          />
          <img
            src="@/assets/index/mission.png"
            class="img-h-160"
            @click="toMission"
          />
          <!-- <img src="@/assets/index/ranking.png" class="img-h-160" /> -->
        </div>
        <div class="message flex-row" @click="noticeFlag = true">
          <div class="title jcyt600">系统</div>
          <div
            class="label jcyt500"
            v-html="noticeContent ? noticeContent : '暂无消息'"
          ></div>
        </div>
        <div class="study-room" @click="toPage('studyRoom')"></div>
        <div class="school-hall" @click="toPage('courseIndex')"></div>
        <div class="technology" @click="toPage('science')"></div>
        <div class="pet" @click="toPage()"></div>
        <div class="learn-hall" @click="toPage('learnHall')"></div>
        <div class="pk" @click="toPage('pk')"></div>
        <img src="@/assets/living/liveRoom.png" class="liveRoom" alt="" />
      </div>
      <mt-dialog :visible.sync="showDialog"></mt-dialog>
      <notice
        v-if="noticeFlag"
        :isOpen="noticeFlag"
        :content="noticeContent"
        @close="noticeFlag = false"
      ></notice>
      <settings
        v-if="openSettings"
        :isOpen="openSettings"
        @cancel="openSettings = false"
        @really="openSettings = false"
      ></settings>
    </div>
    <study-room-index v-if="imitateRoute == 'studyRoom'"></study-room-index>
    <learn-hall-index v-if="imitateRoute == 'learnHall'"></learn-hall-index>
    <pk-index v-if="imitateRoute == 'pk'"></pk-index>
    <course-index v-if="imitateRoute == 'courseIndex'"></course-index>
    <div class="newCanvas loading" :class="{ 'set-index': isLoading }">
      <canvas id="loadingCanvas"></canvas>
    </div>
    <science-room-index v-if="imitateRoute == 'science'"></science-room-index>
    <img src="@/assets/living/appraise.png" alt="" v-show="false" />
  </div>
</template>

<script>
import IndexHeader from "@/components/Index/IndexHeader";
import dialog from "../dialog/playSuccessDialog";
import { Toast } from "mint-ui";
import config from "@/config";
import userApi from "@/api/user";
import gameApi from "@/api/game";
import { setWH, createRive } from "@/public/setCanvas";
// import tips from "@/components/tips/tips";
import studyRoomIndex from "./studyRoomIndex";
import learnHallIndex from "./learnHallIndex";
import pkIndex from "./pkIndex";
import courseIndex from "./courseIndex";
import notice from "@/components/tips/notice";
import informApi from "@/api/inform";
import settings from "@/components/tips/settings.vue";
import scienceRoomIndex from "./scienceRoomIndex.vue";
export default {
  name: "indexWrap",
  data() {
    return {
      showDialog: false,
      isAirSchool: "",
      rive: "",
      bgRive: "",
      loadingRive: "",
      isLoading: false,
      noticeFlag: false,
      messageList: [],
      openSettings: false,
      timer1: null,
      timer2: null,
      reload: false,
      noticeContent: ""
    };
  },
  components: {
    IndexHeader,
    "mt-dialog": dialog,
    //tips,
    studyRoomIndex,
    learnHallIndex,
    pkIndex,
    courseIndex,
    notice,
    settings,
    scienceRoomIndex
  },
  computed: {
    // loadingRive(){
    //   return this.$store.getters.getLoadingRive;
    // },
    imitateRoute() {
      return this.$store.getters.getImitateRoute;
    }
  },
  watch: {
    imitateRoute(val) {
      if (val == null && this.rive) {
        this.rive.play();
        this.bgRive.play();
      }
    },
    $route(to, from) {
      if (to.path == "/" && from.path == "/smsLogin") {
        this.reload = false;
        this.initInfo();
        this.getMaintain();
        this.createBg();
        this.createLoading();
        setTimeout(() => {
          this.reload = true;
        }, 50);
      }
    }
  },
  methods: {
    openNotice() {
      this.noticeFlag = true;
    },
    goKnowNie() {
      __bl.sum("了解聂道");
      this.$router.push("/knowNie");
    },
    goHowToLearn() {
      __bl.sum("上课指南");
      this.$router.push("/howToLearn");
    },
    toMineDetail() {
      this.$router.push("/mine");
    },
    toShop() {
      Toast({ message: "研发中~敬请期待", duration: 1000 });
    },
    toMission() {
      Toast({ message: "研发中~敬请期待", duration: 1000 });
    },
    settings() {
      // Toast("研发中~敬请期待");
      this.openSettings = true;
    },
    toPage(router) {
      if (typeof router == "undefined") {
        Toast({ message: "研发中~敬请期待", duration: 1000 });
        return;
      }
      if (router == "pk") {
        // this.$router.push('/' + router);
        // this.loadingRive.stop();
        this.loadingRive.reset();
        this.loadingRive.play();
        this.isLoading = true;
        this.timer1 = setTimeout(() => {
          if (this.timer1) {
            clearTimeout(this.timer1);
          }
          this.$store.commit("setImitateRoute", router);
        }, 3000);
        this.timer2 = setTimeout(() => {
          if (this.timer2) {
            clearTimeout(this.timer2);
          }
          this.loadingRive.pause();
          this.isLoading = false;
          this.rive.pause();
          this.bgRive.pause();
        }, 5000);
      } else {
        this.$store.commit("setImitateRoute", router);
        this.rive.pause();
        this.bgRive.pause();
      }
    },
    getEvaluationFrom() {
      let res = this.$storage.$getStroage("evaluationFrom");
      return res == "study" ? 1 : 0;
    },
    async getMaintain() {
      let res = await gameApi.getMaintain();
      if (res.data.is_maintain) {
        Toast({
          message: "对弈维护中",
          duration: 3000
        });
      }
    },
    onResize() {
      this.rive.resizeToCanvas();
      this.bgRive.resizeToCanvas();
      this.loadingRive.resizeToCanvas();
    },
    async createLoading() {
      // this.isLoading = true;
      await setWH("loadingCanvas");
      this.loadingRive = await createRive("loadingCanvas", "loading.riv");
      this.loadingRive.pause();
      // this.isLoading = false;
    },
    async createBg() {
      let tmp = this.imitateRoute;
      this.$store.commit("setImitateRoute", null);
      await setWH("canvas");
      await setWH("bgCanvas");
      this.rive = await createRive("canvas", "land.riv");
      this.bgRive = await createRive("bgCanvas", "land_bg.riv");
      this.$store.commit("setImitateRoute", tmp);
    },
    initInfo() {
      informApi.GetMessageList().then((res) => {
        this.messageList = res.data;
      });
      informApi.GetMessageToast().then((res) => {
        this.noticeContent = res.data.content ?? "";
        if (res.data.content && res.data.is_read == false) {
          this.openNotice();
        }
      });
    }
  },
  created() {
    this.isAirSchool = config.isAirSchool;
    window.addEventListener("resize", this.onResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.onResize);
  },

  mounted() {
    this.initInfo();
    this.getMaintain();
    this.createBg();
    this.createLoading();
    this.reload = true;
  }
};
</script>

<style scoped lang="less">
.indexWrap {
  width: 100vw;
  height: 100vh;

  .land {
    z-index: 2;
  }
  .content {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 10;
    .img-h-144 {
      width: 144px;
      height: 144px;
    }
    .img-h-160 {
      width: 144px;
      height: 160px;
    }
    .icon-top-right {
      position: absolute;
      right: 40px;
      top: 32px;
      z-index: 10;
      img + img {
        margin-left: 24px;
      }
    }
    .icon-right {
      position: absolute;
      right: 32px;
      top: 40%;
      z-index: 10;
      img + img {
        margin-top: 24px;
      }
    }
    .icon-bottom {
      position: absolute;
      right: 32px;
      bottom: 40px;
      z-index: 10;
      img + img {
        margin-left: 32px;
      }
    }
    .message {
      width: 416px;
      height: 120px;
      border-radius: 16px;
      background: rgba(0, 0, 0, 0.65);
      position: absolute;
      z-index: 10;
      bottom: 40px;
      left: 30px;
      .title {
        width: 72px;
        height: 36px;
        border-radius: 8px;
        color: #fff;
        font-size: 26px;
        margin: 20px 12px 0 16px;
        text-align: center;
        line-height: 36px;
        background-image: linear-gradient(to bottom, #ff6772 0%, #ef515b 100%);
      }
      .label {
        margin-top: 20px;
        margin-bottom: 20px;
        width: 300px;
        color: #fff;
        font-size: 27px;
        line-height: 38px;
        word-break: break-all;
        overflow-y: scroll;
      }
      ::-webkit-scrollbar {
        width: 0px;
      }
    }
    .money {
      position: absolute;
      top: 40px;
      right: 28.75%;
      .bg {
        width: 280px;
        height: 72px;
        box-shadow: rgba(0, 23, 52, 0.3) 0 2px 2px 0;
        border-radius: 33px;
      }
      .icon {
        width: 52px;
        height: 52px;
        margin-left: 10px;
      }
      .add {
        width: 52px;
        height: 52px;
        margin-right: 10px;
        border-radius: 50%;
        box-shadow: rgba(0, 54, 97, 0.5) 0px 2px 4px 0;
      }
      .num {
        font-size: 30px;
        line-height: 72px;
        width: 156px;
        text-align: center;
        color: #fff;
        font-size: 30;
      }
      .bg-right {
        margin-right: 16px;
      }
    }
  }

  .study-room {
    // background: #000;
    width: 384px;
    height: 354px;
    left: 329px;
    top: 155px;
    position: absolute;
    transform: rotateX(60deg) rotateZ(50deg);
    z-index: 11;
    cursor: pointer;
  }
  .school-hall {
    // background: rgb(19, 110, 170);
    width: 404px;
    height: 354px;
    left: 719px;
    top: 265px;
    position: absolute;
    transform: rotateX(60deg) rotateZ(50deg);
    z-index: 11;
    cursor: pointer;
  }
  .technology {
    // background: rgb(102, 19, 255);
    width: 524px;
    height: 284px;
    right: 280px;
    top: 215px;
    position: absolute;
    transform: rotateX(60deg) rotateZ(50deg);
    z-index: 11;
    cursor: pointer;
  }
  .pet {
    // background: rgb(71, 255, 185);
    width: 364px;
    height: 364px;
    left: 450px;
    top: 425px;
    position: absolute;
    transform: rotateX(60deg) rotateZ(50deg);
    z-index: 11;
    cursor: pointer;
  }
  .learn-hall {
    // background: rgb(255, 71, 240);
    width: 364px;
    height: 364px;
    left: 720px;
    bottom: 90px;
    position: absolute;
    transform: rotateX(60deg) rotateZ(50deg);
    z-index: 11;
    cursor: pointer;
  }
  .pk {
    // background: rgb(248, 36, 36);
    width: 364px;
    height: 364px;
    right: 500px;
    bottom: 245px;
    position: absolute;
    transform: rotateX(60deg) rotateZ(50deg);
    z-index: 11;
    cursor: pointer;
  }
  .loading {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -10;
  }
  .set-index {
    z-index: 2000;
  }
}
.liveRoom {
  position: absolute;
  top: 122px;
  right: 600px;
  width: 70px;
  height: 250px;
}
</style>
