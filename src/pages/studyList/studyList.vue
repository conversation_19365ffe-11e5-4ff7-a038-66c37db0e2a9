<template>
  <div
    class="study_list"
    :style="{
      'background-image': `url(${require('@/assets/questionBank/题库列表背景.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div class="top_row">
      <div
        class="back"
        @click="goBack"
        :style="{
          'background-image': `url(${require('@/assets/index/back.png')})`,
          'background-size': '100% 100%',
        }"
      ></div>
      <div class="title jcyt600">学习报告</div>
      <div></div>
    </div>
    <div class="list_box">
      <div class="nav_type">
        <div
          class="nav_report jcyt500"
          @click="changeType(1)"
          :class="{ nav_active: chooseIndex == 1 }"
        >
          周度报告
        </div>
        <div
          class="nav_report jcyt500"
          @click="changeType(2)"
          :class="{ nav_active: chooseIndex == 2 }"
        >
          月度报告
        </div>
      </div>
      <div class="list_ul">
        <div
          class="list_li"
          v-for="(item, index) in matchList"
          :key="index"
          @click="goDetail(item.id)"
        >
          <div
            class="report_img"
            :style="{
              'background-image': `url(${item.image_src})`,
              'background-size': '100% 100%',
            }"
          ></div>
          <div class="report_info">
            <div class="name jcyt500">{{ item["report_name"] }}</div>
            <div class="time jcyt400">完成学习：{{ item["lesson_done"] }}课</div>
            <div class="time jcyt400">生成时间：{{ item["end_date"] }}</div>
          </div>
        </div>
      </div>
      <div class="page_box">
        <div style="display: flex">
          <div
            class="page_btn margin_2 jcyt500"
            :style="{ opacity: page == 1 ? '0.5' : '1' }"
            @click="switchPaging('1')"
          >
            首页
          </div>
          <div
            class="page_btn jcyt500"
            :style="{ opacity: page == 1 ? '0.5' : '1' }"
            @click="switchPaging('2')"
          >
            上一页
          </div>
        </div>
        <div class="page_text jcyt500">共{{ page }}/{{ total }}页</div>
        <div style="display: flex">
          <div
            class="page_btn margin_2 jcyt500"
            :style="{ opacity: page == total || total == 0 ? '0.5' : '1' }"
            @click="switchPaging('3')"
          >
            下一页
          </div>
          <div
            class="page_btn jcyt500"
            :style="{ opacity: page == total || total == 0 ? '0.5' : '1' }"
            @click="switchPaging('4')"
          >
            尾页
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mineApi from "@/api/mine.js";
export default {
  data() {
    return {
      page: 1,
      total: 0,
      limit: 6,
      chooseIndex: 1,
      matchList: [],
    };
  },
  mounted() {
    if (this.type) {
      this.chooseIndex = this.type == "week" ? 1 : 2;
    }
    this.initList();
  },
  computed: {
    type() {
      return this.$route.query.type;
    },
  },
  watch: {},
  methods: {
    changeType(index) {
      this.chooseIndex = index;
      this.initList();
    },
    initList() {
      this.$store.commit("setApiLoading", true);
      mineApi
        .StudyReportList({
          page: this.page,
          limit: this.limit,
          date_type: this.chooseIndex == 1 ? "week" : "month",
        })
        .then((res) => {
          this.matchList = res.data.results;
          if (res.data.count % 8 == 0) {
            this.total = res.data.count / 8;
          } else {
            this.total = parseInt(res.data.count / 8) + 1;
          }
          this.$store.commit("setApiLoading", false);
        });
    },
    switchPaging(val) {
      if (val == 1) {
        this.page = 1;
      } else if (val == 2) {
        if (this.page > 1) {
          this.page--;
        }
      } else if (val == 3) {
        if (this.page < this.total) {
          this.page++;
        }
      } else if (val == 4) {
        this.page = this.total;
      }
      this.initList();
    },
    goBack() {
      this.$router.push("/mine");
    },
    goDetail(id) {
      this.$router.push({
        path: "studyReportDetail",
        query: {
          date_type: this.chooseIndex == 1 ? "week" : "month",
          id: id,
        },
      });
    },
  },
};
</script>

<style lang="less">
.study_list {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .top_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .back {
      width: 120px;
      height: 120px;
      position: absolute;
      top: 24px;
      left: 40px;
      z-index: 100;
    }
    .title {
      font-size: 52px;
      color: #ffffff;
      text-align: center;
      line-height: 64px;
      padding-top: 80px;
      width: 100%;
    }
    .margin_1 {
      margin: 0 50px 0 0;
    }
  }
  .list_box {
    width: 1824px;
    height: 832px;
    background: #ffffff;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 8px 0 #e4f3ff;
    border-radius: 40px;
    margin: 48px auto;
    position: relative;
    .nav_type {
      position: absolute;
      top: -96px;
      right: 60px;
      display: flex;
      .nav_report {
        width: 232px;
        height: 96px;
        line-height: 96px;
        background-image: linear-gradient(180deg, #fffcf2 6%, #fff5e7 94%);
        box-shadow: inset 0 8px 8px 0 #ffffff, inset 0 -8px 16px 0 #ffe8c8;
        border-radius: 40px 40px 0 0;
        font-size: 40px;
        color: #a93b00;
        text-align: center;
      }
      .nav_active {
        background-image: linear-gradient(180deg, #fed018 6%, #fdb100 94%);
        box-shadow: inset 0 8px 8px 0 #ffe570, inset 0 -8px 16px 0 #ff9b08;
        color: #ffffff;
      }
    }
    .list_ul {
      padding: 32px 32px 120px 32px;
      box-sizing: border-box;
      overflow-y: scroll;
      width: 100%;
      height: 832px;
      &::-webkit-scrollbar {
        width: 0 !important;
      }
      .list_li {
        width: 864px;
        height: 232px;
        background: #e3f5fd;
        border-radius: 32px;
        float: left;
        margin: 0 32px 32px 0;
        padding: 32px;
        box-sizing: border-box;
        display: flex;
        .report_img {
          width: 168px;
          height: 168px;
          border-radius: 24px;
          background: pink;
          margin-right: 32px;
        }
        .report_info {
          .name {
            font-size: 40px;
            color: #333333;
            margin-bottom: 40px;
            line-height: 48px;
          }
          .time {
            font-size: 28px;
            color: #6e95b5;
            line-height: 40px;
          }
        }
      }
      .list_li:nth-child(2n) {
        margin-right: 0;
      }
    }
    .page_box {
      width: 1824px;
      height: 120px;
      background: #ffe8c8;
      border-radius: 0 0 40px 40px;
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 48px;
      box-sizing: border-box;
      .page_btn {
        width: 224px;
        height: 80px;
        background-image: linear-gradient(180deg, #fffcf2 6%, #fff5e7 94%);
        box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.1), inset 0 8px 6px 0 #ffffff,
          inset 0 -8px 8px 0 #ffe8c8;
        border-radius: 40px;
        font-size: 32px;
        color: #b65600;
        letter-spacing: 0;
        text-align: center;
        line-height: 80px;
        cursor: pointer;
      }
      .margin_2 {
        margin-right: 32px;
      }
      .page_text {
        font-size: 32px;
        color: #b65600;
        letter-spacing: 0;
        text-align: center;
      }
    }
  }
}
</style>