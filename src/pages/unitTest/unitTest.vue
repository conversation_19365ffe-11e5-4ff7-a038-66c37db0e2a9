<template>
  <div
    class="unitTest"
    :style="{
      'background-image': `url(${require('@/assets/unitTest/下棋页面背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="unit-wrap">
      <div
        class="unit_box"
      >
        <div class="title jcyt600" :style="{'background-image': `url(${require('@/assets/evaluation/title_bg.png')})`}">{{ matchInfo.name }}</div>
        <div class="column_one">
          <div class="pie"></div>
          <div class="one_text jcyt500">
            测评内容：
            <span class="one_text_2 jcyt400">{{ matchInfo.rule_text }}</span>
          </div>
        </div>
        <div class="column_one">
          <div class="pie"></div>
          <div class="one_text jcyt500">
            测评时长：
            <span class="one_text_2 jcyt400">{{ matchInfo.time_text }}</span>
          </div>
        </div>
        <div class="pop_btn jcyt600">温馨提示</div>
        <div class="pop_text jcyt400" v-html="matchInfo.tip"></div>
        <div
          class="star_btn jcyt600"
          @click="starUnit"
        >
          {{ matchInfo.status == "not_start" ? "开始测评" : "继续测评" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import unitTestApi from "@/api/unitTest";
export default {
  data() {
    return {
      matchInfo: "",
      matchId: "",
      unitStatus: {}
    };
  },
  computed: {
    course_id() {
      return this.$route.query.course_id;
    },
    lesson_id() {
      return this.$route.query.lesson_id;
    },
    url_from() {
      return this.$route.query.from;
    },
    type() {
      return this.$route.query.type;
    }
  },
  mounted() {
    this.matchId = this.$route.query.matchId;
    unitTestApi.GetUnitTestRule({ match_id: this.matchId }).then((res) => {
      this.matchInfo = res.data;
      if (res.data.status == "is_start") {
        unitTestApi.Status({ exam_id: this.matchInfo.exam_id }).then((res) => {
          this.unitStatus = res.data ?? {};
        });
      }
    });
  },
  methods: {
    starUnit() {
      if (
        this.matchInfo["status"] != null &&
        this.matchInfo["status"] == "cant_start"
      ) {
        return null;
      } else {
        if (this.matchInfo["status"] == "not_start") {
          if (this.$route.query.from == "lessonList") {
            let obj = {
              group_id: Number(this.matchId),
              course_id: Number(this.course_id)
            };
            unitTestApi.PostCreatExam(obj).then((res) => {
              this.$router.push({
                path: "/unitTestPlay",
                query: {
                  course_id: this.course_id,
                  lesson_id: this.lesson_id,
                  matchId: res.data,
                  questionIndex: "1",
                  from: this.url_from ?? ""
                }
              });
            });
          } else {
            let obj = {
              group_id: Number(this.matchId),
              course_id: Number(this.course_id),
              lesson_id: Number(this.lesson_id)
            };
            unitTestApi.PostCreatExam(obj).then((res) => {
              this.$router.push({
                path: "/unitTestPlay",
                query: {
                  course_id: this.course_id,
                  lesson_id: this.lesson_id,
                  matchId: res.data,
                  questionIndex: "1",
                  from: this.url_from ?? ""
                }
              });
            });
          }
        } else {
          this.goGame();
        }
      }
    },
    goGame() {
      if (
        (this.unitStatus["game_status"] == "is_start" ||
          this.unitStatus["game_status"] == "not_start") &&
        this.unitStatus["current_game_index"] != 0
      ) {
        this.$router.push({
          path: "/unitTest/game",
          query: {
            course_id: this.course_id,
            lesson_id: this.lesson_id,
            matchId: this.matchInfo.exam_id,
            gameIndex: this.unitStatus["current_game_index"],
            from: this.url_from ?? ""
          }
        });
      } else if (
        this.unitStatus["question_status"] == "is_start" &&
        this.unitStatus["game_status"] == "not_start"
      ) {
        this.$router.push({
          path: "/unitTestPlay",
          query: {
            course_id: this.course_id,
            lesson_id: this.lesson_id,
            matchId: this.matchInfo.exam_id,
            questionIndex: this.unitStatus["current_question_index"],
            from: this.url_from ?? ""
          }
        });
      }
    },
    goBack() {
      if (this.url_from == "report") {
        this.$router.push({
          path: "/unitTestReport",
          query: {
            course_id: this.course_id,
            lesson_id: this.lesson_id,
            matchId: this.matchId ,
            type: this.type
          }
        });
      } else if (this.url_from == "lessonList") {
        this.$router.push({
          path: "/lessonList",
          query: {
            course_id: this.course_id
          }
        });
      } else if (this.url_from == "lessonInfo") {
        this.$router.push({
          path: "/lessonInfo",
          query: {
            lesson_id: this.lesson_id,
            course_id: this.course_id
          }
        });
      }else{
        this.$router.push({
          path: "/lessonList",
          query: {
            course_id: this.course_id
          }
        });
      }
    }
  }
};
</script>

<style lang="less">
.unitTest {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    z-index: 200;
  }
  .unit-wrap {
    width: 1728px;
    height: 840px;
    background: linear-gradient(to bottom, #FCD24C,#F6BF26, #F9C93D);
    box-shadow: rgba(39, 151, 147, .55) 0 10px 20px 0, #FFEBAF 0 10px 20px 0 inset, #E39D1E 0 -10px 20px 0 inset;
    margin:  184px auto 0;
    position: relative;
    box-sizing: border-box;
    padding: 80px 40px 40px 40px;
    border-radius: 100px;
  }
  .unit_box {
    background: #FFFCF9;
    width: 100%;
    height: 100%;
    border-radius: 80px;
    padding-top: 56px;
    box-sizing: border-box;
    .title {
      background-size: 100% 100%;
      font-size: 56px;
      color:#fff;
      min-width: 728px;
      height: 168px;
      margin: 0 auto;
      text-align: center;
      line-height: 130px;
      position: absolute;
      top: -100px;
      left: 50%;
      transform: translateX(-50%);
      padding: 0 140px;
      box-sizing: border-box;
      text-shadow: 0 -2px 6px #ff3900;
    }
    .column_one {
      display: flex;
      margin-left: 145px;
      align-items: center;
      margin-bottom: 24px;
      .pie {
        width: 32px;
        height: 32px;
        border-radius: 16px;
        background: #ffdfa4;
        margin-right: 16px;
      }
      .one_text {
        font-size: 40px;
        color: #333333;
        letter-spacing: 0;
        .one_text_2 {
          font-size: 40px;
          color: #a16007;
          letter-spacing: 0.71px;
        }
      }
    }
  }
  .pop_btn {
    width: 208px;
    height: 80px;
    margin: 56px 0 32px 145px;
    background: #ff7748;
    border-radius: 40px;
    font-size: 36px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    line-height: 80px;
  }
  .pop_text {
    margin-left: 145px;
    margin-right: 145px;
    font-size: 36px;
    color: #a16007;
    letter-spacing: 0px;
    line-height: 64px;
    letter-spacing: 0.1px;

  }
  .star_btn {
    width: 640px;
    height: 104px;
    background-image: linear-gradient(180deg, #2fceff 0%, #2da7ff 100%);
    border: 7.2px solid #ffffff;
    box-shadow: 0 4px 7px 0 rgba(0, 0, 0, 0.1), inset 0 -7px 0 0 #149eee;
    border-radius: 54px;
    font-size: 40px;
    color: #ffffff;
    text-align: center;
    line-height: 84px;
    text-shadow: 0 4px 4px rgba(20, 158, 238, 0.85);
    margin: 48px auto;
    cursor: pointer;
    box-sizing: border-box;
  }
}
</style>
