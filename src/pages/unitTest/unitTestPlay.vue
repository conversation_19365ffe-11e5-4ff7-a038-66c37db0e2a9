<template>
  <div
    class="exerciseDetail"
    :style="{
      'background-image': `url(${require('@/assets/exerciseDetail/下棋页面背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="content" v-if="reload">
      <div class="left">
        <move
          ref="move"
          :question="exercise_detail"
          @change_answer_result="changeAnswerResult"
          @show_confirm_button="showConfirmButton"
          v-if="exercise_detail.type === 'play_on_board'"
        ></move>
        <choice
          ref="choice"
          :question="exercise_detail"
          v-if="exercise_detail.type === 'multiple_choice'"
        ></choice>
        <culture
          ref="culture"
          :question="exercise_detail"
          v-if="exercise_detail.type === 'culture'"
        ></culture>
      </div>
      <div class="right">
        <div class="question_type_wrap">
          <div
            class="question_time"
            v-if="exercise_list.question_status == 'is_start'"
          >
            <div
              class="count_down jcyt500"
              :style="{
                'background-image': `url(${require('@/assets/unitTest/时间倒计时.png')})`,
                'background-size': '100% 100%'
              }"
            >
              {{ s_to_hs(count_down) }}
            </div>
            <div class="question_type jcyt500">
              {{ question_index }}/{{ question_id_list.length }}
            </div>
          </div>
          <div class="question_type" v-else>
            {{
              exercise_detail.type === "play_on_board"
                ? "落子题"
                : exercise_detail.type === "multiple_choice"
                ? "选择题"
                : "文化题"
            }}
            {{ question_index }}/{{ question_id_list.length }}
          </div>
          <div
            class="back"
            @click="back"
            :style="{
              'background-image': `url(${require('@/assets/exerciseDetail/返回.png')})`,
              'background-size': '100% 100%'
            }"
          ></div>
        </div>
        <div class="question_content">
          <img
            @click="changeVoiceStatus"
            class="voice"
            :src="
              open_voice
                ? require('@/assets/exerciseDetail/打开声音.png')
                : require('@/assets/exerciseDetail/关闭声音.png')
            "
          />
          <div class="line-top">
            <div class="issue-wrap">
              <div class="issue-title jcyt600">{{ exercise_detail.name }}</div>
              <!-- 选择题 -->
              <choice-issue
                v-if="exercise_detail.type !== 'play_on_board'"
                :issue_answer="exercise_detail.answer"
                :option_list="exercise_detail.choices"
                :show_answer="show_answer"
                :user_answer="user_answer"
                :answer_result="answer_result"
                :is_redo="is_redo"
                @change_answer_result="changeAnswerResult"
              ></choice-issue>
              <!-- 落子题 -->
              <div v-else>
                <p class="tips jcyt600" v-if="answer_result == 'not_answer'">
                  请在棋盘上落子
                </p>
                <p
                  class="tips jcyt600"
                  v-if="answer_result === 'is_right'"
                  style="color: #34cc67"
                >
                  答对了，你真棒！
                </p>
                <p
                  class="tips jcyt600"
                  v-if="answer_result === 'is_wrong'"
                  style="color: #ff5f69"
                >
                  答错了，继续加油！
                </p>
              </div>
            </div>
            <div class="confirm_wrap">
              <div
                class="confirm_move"
                v-if="exercise_detail.type != 'cultrue' && is_show_confirm_button"
                @click="confimMove"
                :style="{
                  'background-image': `url(${require('@/assets/exerciseDetail/确认落子.png')})`,
                  'background-size': '100% 100%'
                }"
              ></div>

              <div
                class="open_answer"
                @click="openAnswer()"
                v-if="
                  show_answer &&
                  exercise_detail.type == 'play_on_board' &&
                  is_show_check_answer_button
                "
                :style="{
                  'background-image': `url(${require('@/assets/exerciseDetail/查看答案.png')})`,
                  'background-size': '100% 100%'
                }"
              ></div>
              <div
                class="close_answer"
                @click="closeAnswer"
                v-if="
                  show_answer &&
                  exercise_detail.type == 'play_on_board' &&
                  is_show_check_answer_button == false &&
                  is_show_confirm_button == false
                "
                :style="{
                  'background-image': `url(${require('@/assets/exerciseDetail/关闭答案.png')})`,
                  'background-size': '100% 100%'
                }"
              ></div>
            </div>
          </div>
          <div class="line"></div>
          <div class="threeButton" v-if="show_answer">
            <div
              @click="goPrev"
              class="prev"
              :class="question_index == 1 ? 'prev_dis' : ''"
              :style="{
                'background-image':
                  question_index == 1
                    ? `url(${require('@/assets/exerciseDetail/上一题小灰.png')})`
                    : `url(${require('@/assets/exerciseDetail/上一题小.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="redo"
              @click="goRedo"
              :style="{
                'background-image': `url(${require('@/assets/exerciseDetail/重做中.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="next"
              @click="goNext"
              :style="{
                'background-image':
                  question_id_list.length == question_index
                    ? `url(${require('@/assets/exerciseDetail/下一题小灰.png')})`
                    : `url(${require('@/assets/exerciseDetail/下一题小.png')})`,
                'background-size': 'cover'
              }"
            ></div>
          </div>
          <div v-else class="button_wrap">
            <div
              class="next_wrap"
              :class="answer_result == 'not_answer' ? 'next_wrap_dis' : ''"
              v-if="question_id_list.length > question_index"
              @click="goNext"
              :style="{
                'background-image':
                  answer_result == 'not_answer'
                    ? `url(${require('@/assets/exerciseDetail/下一题大灰.png')})`
                    : `url(${require('@/assets/exerciseDetail/下一题大.png')})`,
                'background-size': '100% 100%'
              }"
            ></div>
            <div
              class="last_wrap"
              :class="answer_result == 'not_answer' ? 'last_wrap_dis' : ''"
              v-if="question_id_list.length == question_index"
              @click="goGame"
              :style="{
                'background-image':
                  answer_result == 'not_answer'
                    ? `url(${require('@/assets/exerciseDetail/下一关灰.png')})`
                    : `url(${require('@/assets/exerciseDetail/下一关.png')})`,
                'background-size': 'cover'
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <speak ref="speak"></speak>
    <timeoutDialog ref="timeout" :matchId="matchId"></timeoutDialog>
  </div>
</template>

<script>
import choiceIssue from "@/components/choiceIssue/choiceIssue";
import move from "@/components/question/move";
import choice from "@/components/question/choice";
import culture from "@/components/question/culture";
import speak from "@/components/question/speak";
import unitTestApi from "@/api/unitTest";
import timeoutDialog from "./components/timeoutDialog.vue";
export default {
  data() {
    return {
      sgf: "",
      matchId: "",
      exercise_list: {},
      question_id_list: [], //问题列表
      question_index: 0, //第几题
      exercise_detail: {}, //当前问题详情
      answer_result: "not_answer", //答题结果
      show_answer: false, //整个答题结束 显示答案
      user_answer: "", //用户的答案
      is_redo: false, //是否重做
      is_show_check_answer_button: true,
      is_show_confirm_button: false,
      board_click: false,
      reload: false,
      answer_sgf: "",
      status: "",
      open_voice: true,
      timer: "",
      count_down: "0"
    };
  },
  components: { choiceIssue, move, choice, culture, speak, timeoutDialog },
  computed: {
    course_id() {
      return this.$route.query.course_id;
    },
    lesson_id() {
      return this.$route.query.lesson_id;
    },
    url_from() {
      return this.$route.query.from;
    },
    type() {
      return this.$route.query.type;
    }
  },
  watch: {
    question_index(new_str) {
      unitTestApi
        .Question({ exam_id: this.matchId, index: new_str })
        .then((res) => {
          this.exercise_detail = res.data;
          if (this.open_voice) {
            this.$refs.speak.play(this.exercise_detail.name);
          }
          this.sgf = this.exercise_detail["sgf"];
          this.status = this.exercise_detail["status"];
          this.reload = true;
          if (this.exercise_detail["status"] != "not_answer") {
            this.answer_result = this.exercise_detail["status"];
            this.exercise_detail["type"] == "play_on_board"
              ? (this.exercise_detail["sgf"] =
                  this.exercise_detail["answer_sgf"])
              : ((this.user_answer = this.exercise_detail["answer_choice"]),
                (this.answer_result = this.exercise_detail["status"]));
          }
        });
    }
  },
  mounted() {
    this.matchId = this.$route.query.matchId;
    this.question_index = this.$route.query.questionIndex;
    this.getExerciseInfo();
    this.open_voice =
      JSON.parse(this.$storage.$getStroage("open_voice")) ?? true;
  },
  destroyed() {
    clearInterval(this.timer);
  },
  methods: {
    getExerciseInfo() {
      unitTestApi.Status({ exam_id: this.matchId }).then((res) => {
        this.exercise_list = res.data ?? [];
        this.question_id_list = res.data["question_history"];
        if (this.exercise_list["game_status"] != "not_start") {
          this.show_answer = this.exercise_list["question_status"] == "is_end";
        }
        this.count_down = this.exercise_list.question_left_time;
        if (
          this.exercise_list.question_left_time == 0 &&
          this.exercise_list.question_status != "is_end" &&
          this.exercise_list.question_status != "is_pass" &&
          this.exercise_list.question_status != "not_pass"
        ) {
          this.$refs.timeout.open();
        }

        if (this.timer) {
          clearInterval(this.timer);
        }
        if (this.exercise_list.question_status == "is_start") {
          this.timer = setInterval(() => {
            this.getExerciseInfo();
          }, 1000);
        } else {
          clearInterval(this.timer);
        }
      });
    },
    s_to_hs: function (s) {
      if (!s) {
        s = 0;
      }
      var h;
      h = Math.floor(s / 60);
      s = s % 60;
      h += "";
      s += "";
      h = h.length == 1 ? "0" + h : h;
      s = s.length == 1 ? "0" + s : s;
      return h + ":" + s;
    },
    back() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      if (
        this.exercise_list.match_status == "is_pass" ||
        this.exercise_list.match_status == "not_pass"
      ) {
        this.$router.push({
          path: "/unitTestReport",
          query: {
            lesson_id: this.lesson_id,
            course_id: this.course_id,
            matchId: this.exercise_list.group_id,
            type: this.type ?? ""
          }
        });
      } else {
        this.$router.push({
          path: "/unitTest",
          query: {
            course_id: this.course_id,
            lesson_id: this.lesson_id,
            matchId: this.exercise_list.group_id,
            type: this.type ?? ""
          }
        });
      }
    },
    changeAnswerResult(e) {
      if (this.exercise_detail["type"] == "play_on_board") {
        this.answer_result = e.answer_result;
        this.answer_sgf = e.answer_sgf;
        this.is_show_confirm_button = false;
        if (this.is_show_check_answer_button == true) {
          this.sendAnswer("");
        }
      } else {
        this.user_answer = e.user_answer;
        this.answer_result = e.answer_result;
        this.is_show_confirm_button = false;
        this.exercise_detail["type"] == "culture"
          ? this.$refs.culture.changeResultDialog(e.answer_result)
          : this.$refs.choice.changeResultDialog(e.answer_result);
        if (this.is_show_check_answer_button == true) {
          this.sendAnswer(e.user_answer);
        }
      }
    },
    sendAnswer(choice) {
      unitTestApi
        .PostSendAnswer({
          answer_sgf:
            this.exercise_detail["type"] == "play_on_board"
              ? this.answer_sgf
              : "",
          answer_choice: choice,
          status: this.answer_result,
          index: Number(this.question_index),
          exam_id: Number(this.matchId)
        })
        .then((res) => {
          this.exercise_detail["answer_sgf"] = this.answer_sgf;
          if (this.question_id_list.length == this.question_index) {
            clearInterval(this.timer);
          }
        });
    },
    openAnswer() {
      this.exercise_detail["status"] = this.answer_result = "not_answer";
      this.$refs.move.open_answer(this.sgf);
      this.is_show_check_answer_button = false;
    },
    closeAnswer() {
      this.exercise_detail["status"] = this.answer_result = this.status;
      this.$refs.move.close_answer(this.exercise_detail["answer_sgf"]);
      this.is_show_check_answer_button = true;
    },
    goNext() {
      if (this.question_index < this.question_id_list.length) {
        this.exercise_detail = {};
        this.answer_result = "not_answer";
        this.user_answer = "";
        this.is_show_check_answer_button = true;
        this.question_index = parseInt(this.question_index) + 1;
        this.$refs.speak.cancle();
      }
    },
    goPrev() {
      if (this.question_index > 1) {
        this.exercise_detail = {};
        this.answer_result = "not_answer";
        this.user_answer = "";
        this.is_show_check_answer_button = true;
        this.question_index = parseInt(this.question_index) - 1;
        this.$refs.speak.cancle();
      }
    },
    goRedo() {
      this.$refs["move"].replay();
      this.exercise_detail["status"] = "not_answer";
      this.answer_result = "not_answer";
      this.user_answer = "";
      this.is_show_check_answer_button = null;
      if (this.exercise_detail["type"] != "culture") {
        this.$refs.move.update_sgf(this.sgf);
      }
    },
    showConfirmButton() {
      this.is_show_confirm_button = true;
    },
    confimMove() {
      this.$refs.move.confirm_move();
      this.is_show_confirm_button = false;
    },
    changeVoiceStatus() {
      this.open_voice = !this.open_voice;
      if (this.open_voice) {
        this.$storage.$setStroage("open_voice", true);
        this.$refs.speak.play(this.exercise_detail.name);
      } else {
        this.$storage.$setStroage("open_voice", false);
        this.$refs.speak.cancle();
      }
    },
    goGame() {
      if (this.timer) {
          clearInterval(this.timer);
        }
      this.$router.push({
        path: "/unitTest/game",
        query: {
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          matchId: this.matchId,
          gameIndex: 1,
          from: this.url_from ?? ""
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
.exerciseDetail {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  .content {
    width: 1504px;
    height: 1000px;
    display: flex;
    justify-content: space-between;
    .left {
      width: 1000px;
      height: 1000px;
      background: #f7a448;
      border: 16px solid #fee194;
      box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.1);
      border-radius: 40px;
      position: relative;
      box-sizing: border-box;
    }
    .right {
      width: 488px;
      height: 1000px;
      background-color: greenyellow;
      margin-left: 16px;
      border-radius: 30px;
      background-image: linear-gradient(
        180deg,
        #5296f7 0%,
        #2e79ff 10%,
        #2e79ff 100%
      );
      box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.1), inset 0 -12px 0 0 #0062e1;

      .question_type_wrap {
        width: 448px;
        height: 80px;
        margin: 16px 24px 16px 16px;
        display: flex;
        justify-content: space-between;
        .question_type {
          width: 230px;
          height: 80px;
          line-height: 75px;
          background: rgba(49, 77, 189, 0.5);
          border: 1.51px solid rgba(49, 77, 189, 0.3);
          box-shadow: inset 0 0 9px 0 #314dbd;
          border-radius: 45px;
          font-size: 32px;
          color: #ffffff;
          box-sizing: border-box;
          text-align: center;
        }
        .question_time {
          display: flex;
          .count_down {
            width: 210px;
            height: 80px;
            margin-right: 20px;
            padding-left: 60px;
            text-align: center;
            line-height: 80px;
            font-size: 32px;
            color: #ffdd4e;
            box-sizing: border-box;
          }
        }

        .back {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          box-shadow: 0 4px 6px 0 rgba(0, 99, 255, 0.5);
          cursor: pointer;
        }
      }

      .question_content {
        width: 488px;
        height: 888px;
        background: #ffffff;
        box-shadow: inset 0 -12px 15px 0 #e1f1fc;
        border-radius: 29.76px;
        text-align: center;
        .voice {
          width: 80px;
          height: 80px;
          margin-top: 40px;
          cursor: pointer;
        }
        .line-top {
          height: 488px;
        }
        .issue-wrap {
          width: 416px;
          // height: 290px;
          margin: 29px auto 0;
          .issue-title {
            width: 416px;
            font-size: 32px;
            line-height: 40px;
            // height: 56px;
            color: #333333;
            text-align: justify;
            margin-bottom: 32px;
            overflow: hidden;
            text-overflow: ellipsis; /* 超出部分省略号 */
            word-break: break-all; /* break-all(允许在单词内换行。) */
            display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
            -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
            -webkit-line-clamp: 2;
          }
          .tips {
            font-size: 32px;
            color: #00BAFF;
            text-align: left;
          }
        }
        .confirm_wrap {
          // width: 388px;
          // height: 128px;
          margin: 40px auto 0;

          .confirm_move {
            width: 360px;
            height: 110px;
            cursor: pointer;
            margin: 0 auto;
          }
          .open_answer {
            width: 340px;
            height: 110px;
            cursor: pointer;
            margin: 0 auto;
          }

          .close_answer {
            width: 340px;
            height: 110px;
            cursor: pointer;
            margin: 0 auto;
          }
        }

        .line {
          width: 416px;
          border-top: 2px dashed #c9c9c9;
          margin: 35px auto;
        }
        .threeButton {
          width: 448px;
          height: 128px;
          margin: 0 auto;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          .prev {
            width: 108px;
            height: 108px;
            cursor: pointer;
          }
          .prev_dis {
            width: 108px;
            height: 108px;
            pointer-events: none;
          }
          .redo {
            width: 200px;
            height: 108px;
            cursor: pointer;
            margin-top: -4px;
          }
          .next {
            width: 108px;
            height: 108px;
            cursor: pointer;
          }
        }
        .button_wrap {
          width: 360px;
          margin: 0 auto;
          .next_wrap {
            width: 360px;
            height: 110px;

            margin: 0 auto;
            cursor: pointer;
          }
          .next_wrap_dis {
            pointer-events: none;
          }
          .last_wrap {
            width: 360px;
            height: 110px;

            margin: 0 auto;
            cursor: pointer;
          }
          .last_wrap_dis {
            pointer-events: none;
          }
        }
      }
    }
  }
}
</style>
