<template>
  <div
    class="unitTestReport"
    :style="{
      'background-image': `url(${require('@/assets/unitTest/下棋页面背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div
      class="report_box"
      :style="{
        'background-image': `url(${require('@/assets/unitTest/单元评测报告背景.png')})`,
        'background-size': '100% 100%'
      }"
    >
      <img class="left-balloon" v-if="matchStatus.status == 'is_pass'" src="@/assets/unitTest/气球左.png"/>
      <img class="right-balloon" v-if="matchStatus.status == 'is_pass'" src="@/assets/unitTest/气球右.png"/>
      <div
        class="title jcyt600"
        v-if="
          (matchStatus.type == 'unit' && matchStatus.star > 0) ||
          (matchStatus.final == 'final' && matchStatus.status == 'is_pass')
        "
        :style="{
          'background-image': `url(${require('@/assets/unitTest/pad通过.png')})`,
          'background-size': '100% 100%'
        }"
      >
        {{ matchStatus.name }}
      </div>
      <div
        v-else
        class="title"
        :style="{
          'background-image': `url(${require('@/assets/unitTest/pad未通过.png')})`,
          'background-size': '100% 100%'
        }"
      >
        {{ matchStatus.name }}
      </div>
      <div class="pass" v-if="matchStatus.type == 'final'">
        <img
          src="@/assets/unitTest/pass.png"
          v-if="matchStatus.status == 'is_pass'"
        />
        <img src="@/assets/unitTest/not_pass.png" v-else />
      </div>
      <div class="top_row">
        <div class="number jcyt600">{{ matchStatus.score }}分</div>
        <div class="star_row" v-if="matchStatus.type == 'unit'">
          <div
            class="star"
            v-for="(item, i) in matchStatus.star"
            :key="i"
          ></div>
          <div v-if="matchStatus.star != 5" style="display: flex">
            <div
              class="star_dis"
              v-for="(item, i) in 5 - matchStatus.star"
              :key="i"
            ></div>
          </div>
        </div>
        <div class="title_min jcyt500" v-if="matchStatus.type == 'unit'">
          {{ matchStatus["star_text"] }}
        </div>
        <div class="title_min jcyt500" v-if="matchStatus.type == 'final'">
          {{ matchStatus[
          'status'] ==
          'is_pass'
          ? matchStatus[
          'pass_text']
          : matchStatus[
          'not_pass_text'] }}
        </div>
      </div>

      <div class="one_conten">
        <div class="one_title jcyt600">答题详情</div>
        <div class="one_row">
          <div class="pie_green"></div>
          <div class="text_1 jcyt500" style="margin-right: 96px">正确</div>
          <div class="pie_red"></div>
          <div class="text_1 jcyt500">错误</div>
        </div>
        <div class="pie_box">
          <div
            class="pie_right jcyt500"
            v-for="(item, index) in matchStatus.question_result"
            :key="index"
            :style="{ background: item ? '#37d27c' : '#ff6461' }"
            @click="goPlay(index)"
          >
            {{ index + 1 }}
          </div>
        </div>
        <div class="one_bottom">
          <div class="smile jcyt500"></div>
          点击编号可查看题目详情
        </div>
      </div>
      <div class="two_conten">
        <div class="two_left">
          <div class="two_title jcyt500">答题时长</div>
          <div class="time_text jcyt500">
            {{
              matchStatus.question_time
                ? parseInt(matchStatus.question_time / 60)
                : 0
            }}
            <span class="unit jcyt500">分钟</span>
          </div>
        </div>
        <div class="line"></div>
        <div class="two_right">
          <div class="two_title jcyt500">正确率</div>
          <div class="time_text jcyt500">{{ questionCorrect }}%</div>
        </div>
      </div>
      <div class="one_conten">
        <div class="one_title jcyt600">对弈详情</div>
        <div class="one_row">
          <div class="pie_green"></div>
          <div class="text_1 jcyt500" style="margin-right: 96px">胜利</div>
          <div class="pie_red"></div>
          <div class="text_1 jcyt500">失败</div>
        </div>
        <div class="pie_box1">
          <div
            class="pie_right jcyt500"
            v-for="(item, index) in matchStatus.game_result"
            :key="index"
            :style="{ background: item ? '#37d27c' : '#ff6461' }"
            @click="goGame(index)"
          >
            {{ index + 1 }}
          </div>
        </div>
        <div class="one_bottom">
          <div class="smile jcyt500"></div>
          点击编号可查看对弈详情
        </div>
      </div>
      <div class="two_conten">
        <div class="two_left">
          <div class="two_title jcyt500">对弈时长</div>
          <div class="time_text jcyt500">
            {{
              matchStatus.game_time ? parseInt(matchStatus.game_time / 60) : 0
            }}
            <span class="unit jcyt500">分钟</span>
          </div>
        </div>
        <div class="line"></div>
        <div class="two_right">
          <div class="two_title jcyt500">对弈胜率</div>
          <div class="time_text jcyt500">{{ matchStatus.game_rating }}%</div>
        </div>
      </div>
    </div>
    <div class="btn_row">
      <div class="left_btn jcyt600" @click="dialogOn">测评记录</div>
      <div class="right_btn_dis jcyt600" v-if="!matchStatus.can_click_next">
        再测一次
      </div>
      <div class="right_btn jcyt600" v-else @click="goUnitTestStar">再测一次</div>
    </div>
    <div class="dialog" v-if="isDialogOn">
      <div class="dialog_row">
        <div class="dialog_box">
          <div class="dialog_title jcyt600">
            {{ matchStatus.type == "unit" ? "单元测评" : "期末测试" }}
          </div>
          <div class="ul">
            <div
              class="li"
              v-for="(item, i) in orderList"
              :key="i"
              :class="order_index == i ? 'li_active' : ''"
              @click="orderHandle(item, i)"
            >
              <div>
                <div class="li_title jcyt500">{{ item.name }}</div>
                <div class="li_time jcyt400">
                  测试时间：{{ item.created_at.slice(0, 10) }}
                </div>
              </div>
              <div class="pitch_on"></div>
              <div class="star_row" v-if="matchStatus.type == 'unit'">
                <div
                  class="star"
                  v-for="(child, t) in item.star"
                  :key="t"
                ></div>
                <div v-if="item.star != 5" style="display: flex">
                  <div
                    class="star_dis"
                    v-for="(child, t) in 5 - item.star"
                    :key="t"
                  ></div>
                </div>
              </div>
              <div v-else class="pass_type">
                <img
                  src="../../assets/unitTest/pass.png"
                  v-if="item.status == 'is_pass'"
                />
                <img src="../../assets/unitTest/not_pass.png" v-else />
              </div>
            </div>
          </div>
        </div>
        <div
          class="dialog_close"
          @click="dialogClose"
          :style="{
            'background-image': `url(${require('@/assets/login/关闭按钮.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import unitTestApi from "@/api/unitTest";
export default {
  data() {
    return {
      examId: "",
      matchStatus: {},
      orderList: [],
      questionCorrect: "",
      matchId: "",
      isDialogOn: false,
      order_index: 0
    };
  },
  computed: {
    course_id() {
      return this.$route.query.course_id;
    },
    lesson_id() {
      return this.$route.query.lesson_id;
    },
    type() {
      return this.$route.query.type;
    }
    // url_from() {
    //   return this.$route.query.from;
    // }
  },
  watch: {
    examId(new_str) {
      this.initReport(new_str);
    }
  },
  mounted() {
    this.matchId = this.$route.query.matchId;
    unitTestApi.GetExamId({ group_id: this.matchId }).then((res) => {
      this.examId = res.data;
    });
  },
  methods: {
    initReport(val) {
      unitTestApi.GetReport({ exam_id: val }).then((res) => {
        this.matchStatus = res.data;
        var rightNum = 0;
        for (let i = 0; i < this.matchStatus.question_result.length; i++) {
          if (this.matchStatus.question_result[i]) {
            rightNum++;
          }
        }
        this.questionCorrect = (
          (rightNum * 100) /
          this.matchStatus.question_result.length
        ).toFixed(0);
      });
    },
    goPlay(index) {
      var num = index + 1;
      this.$router.push({
        path: "/unitTestPlay",
        query: {
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          matchId: this.examId,
          questionIndex: num,
          type: this.type,
          from:'report'
        }
      });
    },
    goUnitTestStar() {
      this.$router.push({
        path: "/unitTest",
        query: {
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          matchId: this.matchStatus.group_id,
          type: this.type,
          from:'report'
        }
      });
    },
    dialogOn() {
      this.isDialogOn = true;
      unitTestApi
        .GetReportList({ group_id: this.matchStatus.group_id })
        .then((res) => {
          this.orderList = res.data;
        });
    },
    orderHandle(item, val) {
      this.order_index = val;
      this.isDialogOn = false;
      this.initReport(item.exam_id);
    },
    dialogClose() {
      this.isDialogOn = false;
    },
    goGame(index) {
      this.$router.push({
        path: "/watchingGame",
        query: {
          game_id:this.matchStatus['game_ids'][index],
          from_url: "/unitTestReport",
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          matchId:  this.matchId ,
          type: this.type,
        }
      })
    },
    goBack() {
      if (this.type == "final") {
        this.$router.replace({
          path: "/lessonList",
          query: {
            course_id: this.course_id
          }
        });
      } else {
        this.$router.replace({
          path: "/lessonInfo",
          query: {
            course_id: this.course_id,
            lesson_id: this.lesson_id
          }
        });
      }
    }
  }
};
</script>

<style lang="less">
.unitTestReport {
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 0!important;
  }
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    cursor: pointer;
    z-index: 20;
  }
  .report_box {
    width: 1688px;
    height: 3210px;
    margin: 392px auto 0;
    position: relative;
    padding-top: 1px;
    .title {
      width: 1088px;
      height: 544px;
      position: absolute;
      top: -392px;
      right: 300px;
      font-size: 56px;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      line-height: 64px;
      text-shadow: 0 -2px 6px #ff3900;
      padding-top: 400px;
      box-sizing: border-box;
    }
    .pass {
      width: 250px;
      height: 250px;
      position: absolute;
      top: 188px;
      right: 196px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .top_row {
      // width: 1608px;
      height: 406px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 120px;
    }
    .number {
      font-size: 128px;
      color: #ff4e4e;
      text-align: center;
      line-height: 136px;
      margin-bottom: 22px;
      display: inline-block;
    }
    .star_row {
      display: flex;
      justify-content: center;
      margin-bottom: 32px;
      .star {
        width: 56px;
        height: 56px;
        background: url("@/assets/unitTest/已完成_star.png") no-repeat;
        background-size: 100% 100%;
      }
      .star_dis {
        width: 56px;
        height: 56px;
        background: url("@/assets/unitTest/未完成_star.png") no-repeat;
        background-size: 100% 100%;
      }
    }
    .title_min {
      font-size: 32px;
      color: #ff6461;
      text-align: center;
      line-height: 40px;
    }
    .one_conten {
      width: 1480px;
      //   height: 928px;
      background: #fff8e5;
      border-radius: 70px;
      margin: 200px auto 40px;
      position: relative;
      padding-top: 1px;
      padding-bottom: 80px;
      box-sizing: border-box;
      .one_title {
        width: 416px;
        height: 104px;
        background: #ffba19;
        border-radius: 52px;
        font-size: 48px;
        color: #ffffff;
        text-align: center;
        line-height: 104px;
        position: absolute;
        top: -52px;
        left: 532px;
      }
      .one_row {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 136px;
        margin-bottom: 80px;
        .pie_green {
          width: 32px;
          height: 32px;
          background: #37d27c;
          border-radius: 16px;
          margin-right: 16px;
        }
        .pie_red {
          width: 32px;
          height: 32px;
          background: #ff6461;
          border-radius: 16px;
          margin-right: 16px;
        }
        .text_1 {
          font-size: 32px;
          color: #333333;
          line-height: 32px;
        }
      }
      .pie_box {
        padding: 0px 92px;
        width: 1296px;
        height: 488px;
        margin-bottom: 80px;
      }
      .pie_box1 {
        padding: 0px 92px;
        width: 1296px;
        height: 120px;
        margin-bottom: 80px;
      }
      .pie_right {
        background: #37d27c;
        width: 120px;
        height: 120px;
        border-radius: 60px;
        font-size: 64px;
        color: #ffffff;
        letter-spacing: 0;
        text-align: center;
        line-height: 120px;
        float: left;
        margin-right: 48px;
        margin-bottom: 64px;
        cursor: pointer;
      }
      .pie_right:nth-child(8n) {
        margin: 0;
      }
      .one_bottom {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32px;
        color: #c87e18;
        line-height: 32px;
        .smile {
          width: 32px;
          height: 32px;
          background: url("../../assets/unitTest/笑脸.png") no-repeat;
          background-size: 100% 100%;
          margin-right: 16px;
        }
      }
    }
    .two_conten {
      width: 1480px;
      height: 296px;
      background: #fff8e5;
      border-radius: 70px;
      margin: 0 auto 0;
      display: flex;
      align-items: center;
      padding: 0 230px;
      box-sizing: border-box;
      .two_title {
        width: 280px;
        height: 80px;
        background: #ffe9c8;
        border-radius: 45px;
        font-size: 40px;
        color: #c87e18;
        text-align: center;
        line-height: 80px;
      }
      .line {
        background: #f4e0bd;
        width: 2px;
        height: 168px;
        margin: 0 226px;
      }
      .time_text {
        font-size: 72px;
        color: #333333;
        text-align: center;
        margin-top: 24px;
        .unit {
          font-size: 36px;
          color: #666666;
        }
      }
    }
  }
  .btn_row {
    display: flex;
    justify-content: center;
    margin-top: 80px;
    margin-bottom: 148px;
    .left_btn {
      width: 560px;
      height: 120px;
      background-image: linear-gradient(180deg, #fed018 0%, #fdb100 100%);
      border: 8px solid #ffffff;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #ff9b08;
      border-radius: 60px;
      font-size: 48px;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      line-height: 104px;
      text-shadow: 0 4px 4px rgba(255, 155, 8, 0.85);
      margin-right: 60px;
      cursor: pointer;
      box-sizing: border-box;
    }
    .right_btn {
      width: 560px;
      height: 120px;
      background-image: linear-gradient(180deg, #2fceff 0%, #2da7ff 100%);
      border: 8px solid #ffffff;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #149eee;
      border-radius: 60px;
      font-size: 48px;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      line-height: 104px;
      text-shadow: 0 4px 4px rgba(20, 158, 238, 0.85);
      cursor: pointer;
      box-sizing: border-box;
    }
    .right_btn_dis {
      width: 560px;
      height: 120px;
      background-image: linear-gradient(180deg, #e6e6e6 0%, #b3b3b3 100%);
      border: 8px solid #ffffff;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 0 0 #969696;
      border-radius: 60px;
      font-size: 48px;
      color: #8c8c8c;
      letter-spacing: 0;
      text-align: center;
      line-height: 104px;
      box-sizing: border-box;
      text-shadow: 0 4px 4px rgba(255, 255, 255, 0.15);
    }
  }
  .dialog {
    width: 100vw;
    height: 100vh;
    position: fixed;
    background: rgba(0, 0, 0, 0.6);
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .dialog_row {
      display: flex;
      justify-content: center;
    }
    .dialog_box {
      width: 1152px;
      height: 800px;
      border-radius: 50px;
      background: #ffffff;
      box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1), inset 0 -8px 30px 0 #ccfaff;
      margin-left: 112px;
      .dialog_title {
        font-size: 48px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        margin: 64px 0;
      }
      .ul {
        height: 560px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 0!important;
        }
        .li_active {
          border: 4px solid #ffa132;
          box-sizing: border-box;
          position: relative;
          .pitch_on {
            width: 57px;
            height: 52px;
            background: url("@/assets/unitTest/选中.png") no-repeat;
            background-size: 100% 100%;
            position: absolute;
            bottom: -1px;
            right: 0;
          }
        }
        .li {
          width: 976px;
          height: 200px;
          background: #f7f9fb;
          border-radius: 40px;
          margin: 0 auto 24px;
          overflow: hidden;
          display: flex;
          box-sizing: border-box;
          justify-content: space-between;
          .li_title {
            font-size: 40px;
            color: #333333;
            line-height: 48px;
            margin: 48px 0 16px 48px;
          }
          .li_time {
            font-size: 32px;
            color: #999999;
            margin-left: 48px;
            line-height: 40px;
          }
          .star_row {
            display: flex;
            justify-content: center;
            margin-bottom: 32px;
            margin-top: 52px;
            margin: 52px 48px 32px 0;
            .star {
              width: 40px;
              height: 40px;
              background: url("@/assets/unitTest/star.png") no-repeat;
              background-size: 100% 100%;
              margin-right: 8px;
            }
            .star_dis {
              width: 40px;
              height: 40px;
              background: url("@/assets/unitTest/star_dis.png") no-repeat;
              background-size: 100% 100%;
              margin-right: 8px;
            }
          }
          .pass_type {
            width: 152px;
            height: 152px;
            margin: 24px 48px 0 0;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    .dialog_close {
      width: 96px;
      height: 96px;
      margin-left: 40px;
    }
  }
  .left-balloon{
    position: absolute;
    left: 0;
    top: -146px;
    width: 362px;
    height: 302px;
  }
  .right-balloon {
    position: absolute;
    right: 0;
    top: -146px;
    width: 362px;
    height: 302px;
  }
}
</style>
