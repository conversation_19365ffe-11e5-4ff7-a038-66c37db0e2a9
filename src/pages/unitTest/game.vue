<template>
  <div
    class="gameWrap"
    :style="{
      'background-image': `url(${require('@/assets/exerciseDetail/下棋页面背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="wifi">
      <wifiWrap :game_end="game_end"></wifiWrap>
    </div>
    <div class="content">
      <div class="left">
        <board
          ref="board_play"
          id="board"
          :sgf="sgf"
          :board_click="board_click"
          :userTurn="user_turn"
          @turn="change_turn"
          @setup="change_setup"
          @captured="change_captured"
          @chess_move="chess_move"
        />
      </div>
      <div class="right">
        <div class="game_type_wrap">
          <gameRule :status="status" ref="is_rule_show"></gameRule>
          <div class="game_index jcyt600">第 {{ setup }} 手</div>
          <div class="fuc_wrap">
            <div
              class="reload"
              @click="goReload()"
              :style="{
                'background-image': `url(${require('@/assets/game/刷新.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="rule"
              @click="changeRuleShow()"
              :style="{
                'background-image': is_rule_show
                  ? `url(${require('@/assets/game/查看规则.png')})`
                  : `url(${require('@/assets/game/规则.png')})`,
                'background-size': 'cover'
              }"
            ></div>
            <div
              class="back"
              @click="back"
              :style="{
                'background-image': `url(${require('@/assets/exerciseDetail/返回.png')})`,
                'background-size': 'cover'
              }"
            ></div>
          </div>
        </div>
        <gamePeople
          :game_end="game_end"
          :status="status"
          :black_enter="black_enter"
          :white_enter="white_enter"
          :ws_left_time="ws_left_time"
          :black_left_time="black_left_time"
          :white_left_time="white_left_time"
          :hum_captured="hum_captured"
          :ai_captured="ai_captured"
          :lose_reason="lose_reason"
          :time_c="time_c"
          :win_side="win_side"
        ></gamePeople>
        <div
          class="drop_countdown jcyt500"
          v-if="game_end == false && status.enable_move_time == 1"
        >
          落子倒计时：{{ s_to_hs(drop_left_time) }}
        </div>
        <functionButton
          :game_end="game_end"
          :active="active"
          :confirm_status="confirm_status"
          :status="status"
          :setup="setup"
          :user_player="user_player"
          :win_side="win_side"
          :lose_reason="lose_reason"
          :white_captured="ai_captured"
          :black_captured="hum_captured"
          :end_step="end_step"
          :black_score="black_score"
          :white_score="white_score"
          :game_length="game_history.length"
          @check_tab="check_tab"
          @confirm_move="confirm_move"
          @update_pass="update_pass"
          @resign="game_end == false && $refs.confirm_dialog.open()"
          @check_area_score="check_area_score"
          @changeStepStyle="changeStepStyle"
          @goToStep="goToStep"
          @show_step="show_step"
          :game_index="gameIndex"
        ></functionButton>
      </div>
    </div>
    <confirmResignDialog
      ref="confirm_dialog"
      @confirm_resign="confirm_resign"
      @cancel_resign="cancel_resign"
    ></confirmResignDialog>
    <winDialog
      :status="status"
      ref="gameSuccessDialog"
      :win_side="win_side"
      :lose_reason="lose_reason"
      :white_captured="ai_captured"
      :black_captured="hum_captured"
      :black_score="black_score"
      :white_score="white_score"
      :eInfo="eInfo"
      :gameIndex="gameIndex"
      :type="'unitest'"
      @continueGame="continueGame"
      @checkReport="checkReport"
      :can_go_button="can_go_button"
    ></winDialog>
    <loseDialog
      :status="status"
      ref="gameFailDialog"
      :win_side="win_side"
      :lose_reason="lose_reason"
      :white_captured="ai_captured"
      :black_captured="hum_captured"
      :black_score="black_score"
      :white_score="white_score"
      :eInfo="eInfo"
      :gameIndex="gameIndex"
      :type="'unitest'"
      @continueGame="continueGame"
      @checkReport="checkReport"
      :can_go_button="can_go_button"
    ></loseDialog>
    <peaceDialog
      :status="status"
      ref="peaceDialog"
      :win_side="win_side"
      :lose_reason="lose_reason"
      :white_captured="ai_captured"
      :black_captured="hum_captured"
      :black_score="black_score"
      :white_score="white_score"
      :eInfo="eInfo"
      :gameIndex="gameIndex"
      :type="'unitest'"
      @continueGame="continueGame"
      @checkReport="checkReport"
      :can_go_button="can_go_button"
    ></peaceDialog>
    <starDialog
      :status="status"
      :game_history="game_history"
      :gameIndex="gameIndex"
      ref="starGameDialog"
      @starGame="starGame"
    ></starDialog>
  </div>
</template>

<script>
import functionButton from "@/components/game/functionButton";
import confirmResignDialog from "@/components/game/confirmResignDialog";
import starDialog from "@/components/game/starDialog";
import winDialog from "@/components/game/winDialog";
import loseDialog from "@/components/game/loseDialog";
import peaceDialog from "@/components/game/peaceDialog";
import gameRule from "@/components/game/gameRule";
import gamePeople from "@/components/game/gamePeople";
import wifiWrap from "@/components/game/gameWifi";
import gameApi from "@/api/game";
import board from "@/components/game/board";
import unitTestApi from "@/api/unitTest";
import { Toast } from "mint-ui";
import storage from "@/public/storage.js";
import zip from "../../public/zip";
import "@/public/wgo/wgo.min";
import "@/public/wgo/wgo.player.min";
import tool from "@/public/tool";
import AwaitLock from "await-lock";
import websocket from "@/api/websocket";
export default {
  name: "gameWrap",
  data() {
    return {
      active: 1,
      is_rule_show: false,
      player: "",
      move_audio: "",
      mark_list: [],
      board_click: false,
      status: {},
      request_timer: null,
      hum_captured: 0,
      ai_captured: 0,
      last_mark: "",
      last_move: {
        x: "",
        y: "",
        turn: ""
      },
      confirm_status: false,
      apply_score_number: "",
      areaScoreing: false,
      showMoveNumber: false,
      user_id: "",
      send_data: {},
      user_hash: "",
      user_turn: 0,
      setup: 0,
      sgf: "",
      timer: "",
      timer1: "",
      ws_left_time: {},
      turn: 1,
      user_player: false,
      time_c: 1,
      move_c: 1,
      win_side: "",
      lose_reason: "",
      game_end: false,
      black_left_time: "",
      white_left_time: "",
      drop_left_time: "",
      end_step: 0,
      dis_game_id: "",
      black_enter: false,
      white_enter: false,
      black_score: 0,
      white_score: 0,
      ownership: [], //数目结束标志位
      game_id: "",
      game_history: [],
      eInfo: {},
      last_msg_index: 0,
      miss_msg_locker: new AwaitLock(),
      handler_locker: new AwaitLock(),
      can_go_button: false,
      queue: []
    };
  },
  components: {
    functionButton,
    board,
    confirmResignDialog,
    winDialog,
    loseDialog,
    peaceDialog,
    gameRule,
    gamePeople,
    starDialog,
    wifiWrap
  },

  computed: {
    course_id() {
      return this.$route.query.course_id;
    },
    lesson_id() {
      return this.$route.query.lesson_id;
    },
    matchId() {
      return this.$route.query.matchId;
    },
    gameIndex() {
      return this.$route.query.gameIndex;
    },
    url_from() {
      return this.$route.query.from;
    },
    wsMessage() {
      return this.$store.getters.getMessage;
    },
    wsReadyStats() {
      return this.$store.getters.getWsReadyStats;
    },
    type() {
      return this.$route.query.type;
    }
  },
  watch: {
    wsReadyStats: {
      handler(new_stats, old_stats) {
        if (new_stats === 1) {
          this.$nextTick(() => {
            this.$socket.send({
              message_type: "bind_group",
              data: { group_id: "game:" + this.game_id }
            });
          });
        }
        if (new_stats === 0) {
          // alert("网络连接失败，请检查网络设置");
        }
      },
    },
    wsMessage: async function (newValue, oldValue) {
      await this.onMessage(newValue);
    },

    status: {
      handler(new_object) {
        gameApi.PlaySgf(this.send_data).then((res) => {
          var data = JSON.parse(zip.unzipStr(res.data));
          this.sgf = data.sgf;
          gameApi
            .CanPlay(this.send_data)
            .then((res) => {
              if (res.status == 200) {
                this.turn = res.data.next_color ?? 0;
                this.can_play();
              }
            })
            .catch((err) => {});
        });
        this.turn = this.time_c = new_object.turn;
        if (new_object.black_user_hash === this.user_hash) {
          this.user_turn = 1;
        }
        if (new_object.white_user_hash === this.user_hash) {
          this.user_turn = 2;
        }
        if (new_object.is_end === false) {
          //
        } else {
          this.game_end = true;
          this.end_step = new_object.step;
          this.win_side = new_object.win === 1 ? "black" : "white";
          this.drop_left_time = new_object.now_move_time;
          this.black_score = new_object.black_score;
          this.white_score = new_object.white_score;
          this.black_enter = new_object.black_enter;
          this.white_enter = new_object.white_enter;
          this.check_result(new_object);
        }
        this.black_left_time =
          new_object.now_black_time > 0
            ? new_object.now_black_time
            : new_object.black_time;
        this.white_left_time =
          new_object.now_white_time > 0
            ? new_object.now_white_time
            : new_object.white_time;

        this.apply_score_number = new_object.territory_step;
        this.check_user_player();
      },
      deep: true
    }
  },
  methods: {
    async onMessage(val) {
      let msg = val.data;
      let index = val.index;
      switch (val["group"]) {
        case `game:${this.game_id}`:
          if (this.miss_msg_locker.acquired) {
            this.queue.push(val);
            return;
          }
          if (this.last_msg_index === 0) {
            this.last_msg_index = index - 1;
          }
          if (this.last_msg_index + 1 < index) {
            if (this.miss_msg_locker.tryAcquire()) {
              let totalMissMsgCount = index - this.last_msg_index - 1;
              for (let i = 1; i <= totalMissMsgCount; i++) {
                await this.handlerMissMsg(this.last_msg_index + i);
              }
              this.handler(msg);
              this.last_msg_index = index;
              this.doMySelf();
              this.miss_msg_locker.release();
            }
          } else {
            this.handler(msg);
            this.last_msg_index = index;
          }
          break;
        default:
          if (val.data.message_type === "unittest:game_status") {
            this.handler(msg);
            return;
          }
          break;
      }
    },
    async handlerMissMsg(msg_index) {
      await websocket
        .GetMissGroupWebSocketMsg({
          index: msg_index,
          group_id: `game:${this.game_id}`
        })
        .then((res) => {
          let data = res.data.data.message;
          let msg = zip.unzipStr(data);
          this.handler(JSON.parse(msg));
        }).catch(() => {
          window.location.reload();
        });
    },
    handler(msg) {
      switch (msg.message_type) {
        case "time":
          let data = msg.data;
          this.time_c = data.c;
          this.ws_left_time = data;
          this.time_down();
          break;
        case "move_time":
          this.move_c = msg.data.color;
          if (this.timer1) {
            clearInterval(this.timer1);
          }
          if (this.status.enable_move_time == 1) {
            this.drop_left_time = msg.data.left_time;
            this.dropTimeReGetCountdown(this.drop_left_time);
          }

          this.can_play();
          break;
        case "user_status":
          if (msg.data.user_hash === this.status.black_user_hash) {
            if (
              (msg.data.enter_status && msg.data.online_status) ||
              this.status.is_end
            ) {
              this.black_enter = true;
            } else {
              this.black_enter = false;
            }
          } else if (msg.data.user_hash === this.status.white_user_hash) {
            if (
              (msg.data.enter_status && msg.data.online_status) ||
              this.status.is_end
            ) {
              this.white_enter = true;
            } else {
              this.white_enter = false;
            }
          }
          break;
        case "move":
          if (msg.data.c !== this.user_turn) {
            this.$refs.board_play.update_board({
              x: msg.data.x,
              y: msg.data.y,
              turn: msg.data.c === 2 ? -1 : 1
            });
          }
          this.turn = msg.data.turn === 1 ? 2 : 1;
          this.can_play();
          break;
        case "pass":
          this.$refs.board_play.update_pass();
          this.turn = msg.data.c == 1 ? 2 : 1;
          Toast(this.turn === 1 ? "白方停一手" : "黑方停一手");
          this.can_play();
          break;
        case "start_byomi":
          this.start_byomi = msg.data.c === 1 ? "black" : "white";
          break;
        case "unittest:game_status":
          this.can_go_button = true;
          this.eInfo.match_status = msg.data.match_status;
          break;
        case "end":
          this.game_end = true;
          this.end_step = msg.data.step;
          this.black_score = msg.data.b_score;
          this.white_score = msg.data.w_score;
          this.active = 1;
          this.check_result(msg.data);
          this.end_game_popup(msg.data);
          if (msg.data.owner_ship) {
            this.ownership = msg.data.owner_ship;
            this.owner_ship();
          }
          if (this.timer) {
            clearInterval(this.timer);
          }
          if (this.timer1) {
            clearInterval(this.timer1);
          }

          this.$socket.send({
            message_type: "unbind_group",
            data: { group_id: "game:" + this.game_id }
          });

          break;
      }
    },
    doMySelf() {
      let val = this.queue.shift();
      if (val) {
        let msg = val.data;
        let index = val.index;
        this.handler(msg);
        this.last_msg_index = index;
      }
      if (this.queue.length > 0) {
        this.doMySelf();
      }
    },
    owner_ship() {
      tool.OwnerShip(this.ownership, this.$refs.board_play.player);
    },
    remove_owner_ship() {
      if (this.ownership.length > 0) {
        tool.RemoveOwnerShip(this.ownership, this.$refs.board_play.player);
        this.ownership = [];
      }
    },
    check_user_player() {
      if (this.status.black_user_hash === this.user_hash) {
        this.user_player = true;
        return;
      }
      if (this.status.white_user_hash === this.user_hash) {
        this.user_player = true;
        return;
      }
      this.user_player = false;
    },
    continueGame() {
      console.log("continueGame");
      this.cancel();
      this.$router.push({
        query: {
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          matchId: this.matchId,
          gameIndex: parseInt(this.gameIndex) + 1,
          from: this.url_from ?? ""
        }
      });
      window.location.reload();
    },
    goReload() {
      window.location.reload();
    },
    checkReport() {
      this.$router.push({
        path: "/unitTestReport",
        query: {
          course_id: this.course_id,
          lesson_id: this.lesson_id,
          matchId: this.eInfo.group_id,
          type: this.type
        }
      });
    },
    starGame() {
      if (this.$socket.conn) {
        this.$nextTick(() => {
          if (this.$socket.conn.ws.readyState === 1) {
            this.$socket.send({
              message_type: "bind_group",
              data: { group_id: "game:" + this.game_id }
            });
          }
        });
        if (this.user_turn == 1) {
          if (!this.status.black_enter) {
            gameApi.EnterBoard(this.send_data);
          }
        }
        if (this.user_turn == 2) {
          if (!this.status.white_enter) {
            gameApi.EnterBoard(this.send_data);
          }
        }
        this.drop_left_time = this.status.now_move_time;
        this.black_left_time =
          this.status.now_black_time > 0
            ? this.status.now_black_time
            : this.status.black_time;
        this.white_left_time =
          this.status.now_white_time > 0
            ? this.status.now_white_time
            : this.status.white_time;
        if (this.timer1) {
          clearInterval(this.timer1);
        }
        if (this.timer) {
          clearInterval(this.timer);
        }
        if (this.status.turn == 1) {
          this.reGetCountdown(this.black_left_time);
        } else if (this.status.turn == 2) {
          this.reGetCountdown(this.white_left_time);
        }
        this.dropTimeReGetCountdown(this.drop_left_time);
      }
      this.$refs.starGameDialog.close();
    },

    check_result(data) {
      this.win_side =
        data.win == 1 ? "black" : data.win == 2 ? "white" : data.win;
      if (this.win_side == 3) {
        this.lose_reason = data.win_result;
      } else {
        if (data.win_result === "Abstain") {
          this.lose_reason = "Abstain";
          return;
        }
        var str = data.win_result.substring(
          data.win_result.indexOf("+") + 1,
          data.win_result.length
        );
        if (str.indexOf("C")) {
          var num = str.substring(1, str.length);
          data.win == 1
            ? (this.black_captured = num)
            : (this.white_captured = num);
        }
        this.lose_reason =
          str.indexOf("C") > -1 || str == "O"
            ? "captured"
            : str == "R"
            ? "resign"
            : str == "T"
            ? "time_out"
            : parseInt(str) > 0
            ? "area_score"
            : str == "L"
            ? "withdraw"
            : "Draw";
      }
    },
    reGetCountdown(new_time) {
      this.timer = setInterval(() => {
        if (new_time > 0) {
          new_time--;
          this.time_c == 2
            ? (this.white_left_time = new_time)
            : (this.black_left_time = new_time);
        }
      }, 1000);
    },
    dropTimeReGetCountdown(new_time) {
      this.timer1 = setInterval(() => {
        if (new_time > 0) {
          new_time--;
          this.drop_left_time = new_time;
        }
      }, 1000);
    },
    changeRuleShow() {
      this.is_rule_show = !this.is_rule_show;
      this.is_rule_show
        ? this.$refs.is_rule_show.open()
        : this.$refs.is_rule_show.close();
    },
    time_down() {
      if (this.time_c == 1) {
        if (
          this.ws_left_time.black_main_time == 0 &&
          this.ws_left_time.black_byo_yomi > 0
        ) {
          if (this.timer) {
            clearInterval(this.timer);
            this.black_left_time = this.ws_left_time.black_byo_yomi_time;
          }
          this.reGetCountdown(this.ws_left_time.black_byo_yomi_time);
        } else {
          if (this.timer) {
            clearInterval(this.timer);
            this.black_left_time = this.ws_left_time.black_main_time;
          }
          this.reGetCountdown(this.ws_left_time.black_main_time);
        }
      } else if (this.time_c == 2) {
        if (
          this.ws_left_time.white_main_time == 0 &&
          this.ws_left_time.white_byo_yomi > 0
        ) {
          if (this.timer) {
            clearInterval(this.timer);
            this.white_left_time = this.ws_left_time.white_byo_yomi_time;
          }
          this.reGetCountdown(this.ws_left_time.white_byo_yomi_time);
        } else {
          if (this.timer) {
            clearInterval(this.timer);
            this.white_left_time = this.ws_left_time.white_main_time;
          }
          this.reGetCountdown(this.ws_left_time.white_main_time);
        }
      }
    },
    update_pass: function () {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }

      if (this.board_click === true) {
        this.handler_locker.tryAcquire();
        if (this.last_mark) {
          this.$refs.board_play.remove_chess_mark();
          this.last_mark = null;
        }
        this.confirm_status = false;
        this.board_click = false;

        gameApi
          .Pass(Object.assign(this.send_data, { c: this.turn }))
          .then((res) => {
            if (res.status == 200) {
              // Toast(this.turn === 1 ? "黑方停一手" : "白方停一手");
              this.turn = res.data.next_color;
            } else {
              this.board_click = true;
              Toast("停一手失败");
            }
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          })
          .catch(() => {
            this.board_click = true;
            Toast("停一手失败");
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          });
      } else {
        Toast("还没轮到你哦～");
      }
    },
    s_to_hs: function (s) {
      if (!s) {
        s = 0;
      }
      var h;
      h = Math.floor(s / 60);
      s = s % 60;
      h += "";
      s += "";
      h = h.length == 1 ? "0" + h : h;
      s = s.length == 1 ? "0" + s : s;
      return h + ":" + s;
    },
    change_captured: function (event) {
      this.hum_captured = event.B;
      this.ai_captured = event.W;
    },
    change_setup: function (event) {
      this.setup = event;
    },
    change_turn: function (event) {
      this.turn = event == -1 ? 2 : 1;
    },

    can_play: function () {
      if (
        this.user_player &&
        this.turn === 1 &&
        this.user_id == this.status.black_user_id
      ) {
        this.board_click = true;
      } else if (
        this.user_player &&
        this.turn === 2 &&
        this.user_id == this.status.white_user_id
      ) {
        this.board_click = true;
      }
    },
    chess_move: function (ob) {
      if (this.last_mark) {
        this.$refs.board_play.remove_chess_mark();
      }
      this.last_mark = ob;
      this.confirm_status = true;
      this.$refs.board_play.add_chess_mark(this.last_mark);
    },
    confirm_move: function () {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }
      this.handler_locker.tryAcquire();
      this.board_click = false;
      this.confirm_status = false;
      if (this.last_mark) {
        this.play_move(this.last_mark);
      }
    },

    check_sgf_same(status_sgf, board_sgf) {
      this.$refs.board_play.loadSgf(status_sgf);
    },
    play_move(ob) {
      if ("x" in ob && "y" in ob) {
        gameApi
          .HumsMove(
            Object.assign(this.send_data, { x: ob.x, y: ob.y, c: this.turn })
          )
          .then((res) => {
            this.$refs.board_play.repain_chess_mark(this.last_mark);
            this.$refs.board_play.update_board(this.last_mark);
            //this.board_click = false;
            //this.confirm_status = false;
            this.last_move = this.last_mark;
            this.last_mark = null;
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          })
          .catch((error) => {
            this.board_click = true;
            this.confirm_status = false;
            this.last_mark = null;
            console.log("落子失败");
            Toast(error.err);
            if (this.handler_locker.acquired) {
              this.handler_locker.release();
            }
          });
      }
    },

    check_tab(active_index) {
      this.active = active_index;
    },
    back() {
      if (
        this.eInfo.match_status == "is_pass" ||
        this.eInfo.match_status == "not_pass"
      ) {
        this.$router.push({
          path: "/unitTestReport",
          query: {
            lesson_id: this.lesson_id,
            course_id: this.course_id,
            matchId: this.matchId,
            type: this.type ?? ""
          }
        });
      } else {
        this.$router.push({
          path: "/unitTest",
          query: {
            course_id: this.course_id,
            lesson_id: this.lesson_id,
            matchId: this.eInfo.group_id,
            type: this.type ?? ""
          }
        });
      }

      // if (this.url_from == "unitTestReport") {
      //   this.$router.push({
      //     path: "/unitTestReport",
      //     query: {
      //       lesson_id: this.lesson_id,
      //       course_id: this.course_id,
      //       matchId: this.matchId
      //     }
      //   });
      // } else {
      //   if (this.from == "Ai") {
      //     this.$router.push("/aiSelect");
      //   } else {
      //     this.$router.push({
      //       path: "/lessonInfo",
      //       query: {
      //         lesson_id: this.lesson_id,
      //         course_id: this.course_id
      //       }
      //     });
      //   }
      // }
    },

    getStatus() {
      gameApi
        .GetPlayInfo(this.send_data)
        .then((res) => {
          this.status = JSON.parse(zip.unzipStr(res.data));
        })
        .catch(() => {});
    },

    confirm_resign() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }
      this.handler_locker.tryAcquire();
      gameApi
        .Resign(this.send_data)
        .then((res) => {
          if (this.handler_locker.acquired) {
            this.handler_locker.release();
          }
          this.$refs.confirm_dialog.close();
        })
        .catch((error) => {
          if (this.handler_locker.acquired) {
            this.handler_locker.release();
          }
          this.$refs.confirm_dialog.close();
        });
    },
    cancel_resign() {
      if (this.handler_locker.acquired) {
        this.handler_locker.release();
      }
    },
    end_game_popup(new_object) {
      this.$refs.board_play.hideLastMove();
      this.$refs.board_play.remove_anim_chess_mark();
      if (this.last_mark) {
        this.$refs.board_play.remove_chess_mark();
      }
      this.isFinished = true;
      this.board_click = false;
      if (
        ( this.win_side === 3 &&
        this.lose_reason == "Draw" &&
        this.user_player) || 
        ( this.win_side === 4 &&
        this.lose_reason == "Abstain" &&
        this.user_player)
       
      ) {
        this.$refs.peaceDialog.open();
      } else if (
        this.status.black_user_id == this.user_id &&
        this.win_side === "black"
      ) {
        this.$refs.gameSuccessDialog.open();
      } else if (
        this.status.white_user_id == this.user_id &&
        this.win_side === "white"
      ) {
        this.$refs.gameSuccessDialog.open();
      } else if (
        this.status.black_user_id != this.user_id &&
        this.win_side === "black" &&
        this.user_player
      ) {
        this.$refs.gameFailDialog.open();
      } else if (
        this.status.white_user_id != this.user_id &&
        this.win_side === "white" &&
        this.user_player
      ) {
        this.$refs.gameFailDialog.open();
      }
      this.$refs.is_rule_show.close();
      this.is_rule_show = false;
    },
    check_area_score() {
      if (this.handler_locker.acquired) {
        Toast("点击太快啦！");
        return;
      }

      if (this.game_end == false) {
        if (this.areaScoreing == false) {
          this.areaScoreing = true;
          if (
            this.setup >= this.apply_score_number &&
            this.board_click === true
          ) {
            this.board_click = false;
            this.handler_locker.tryAcquire();
            gameApi
              .AreaScore(this.send_data)
              .then((res) => {
                if (this.last_mark) {
                  this.$refs.board_play.remove_chess_mark();
                }
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
              })
              .catch((error) => {
                if (this.handler_locker.acquired) {
                  this.handler_locker.release();
                }
              });
          } else if (
            this.setup < this.apply_score_number &&
            this.board_click === true
          ) {
            Toast(`请在${this.apply_score_number}手后申请数子`);
          } else if (this.board_click === false) {
            Toast("请等待对手落子后再数目");
          }
          this.areaScoreing = false;
        }
      }
    },
    changeStepStyle(num) {
      this.remove_owner_ship();
      if (num == 0) {
        if (this.setup > 0) {
          this.$refs.board_play.move_to_first();
        }
      } else if (num == -5) {
        if (this.setup >= 5) {
          this.$refs.board_play.goTo(-5);
        }
      } else if (num == -1) {
        if (this.setup >= 1) {
          this.$refs.board_play.move_to_previous();
        }
      } else if (num == 1) {
        if (this.end_step >= this.setup + 1) {
          this.$refs.board_play.move_to_next();
        }
      } else if (num == 5) {
        if (this.end_step >= this.setup + 5) {
          this.$refs.board_play.goTo(5);
        }
      } else if (num == 361) {
        if (this.end_step != this.setup) {
          this.$refs.board_play.move_to_last();
        }
      }
    },
    goToStep(e) {
      this.$refs.board_play.goToStep(e);
    },
    show_step() {
      if (!this.showMoveNumber) {
        this.$refs.board_play.show_move_number();
      } else {
        this.$refs.board_play.close_move_number();
      }
      this.showMoveNumber = !this.showMoveNumber;
    },
    cancel() {
      this.$socket.send({
        message_type: "unbind_group",
        data: { group_id: "game:" + this.dis_game_id }
      });
    },
    initStatus() {
      unitTestApi.Status({ exam_id: this.matchId }).then((res) => {
        this.eInfo = res.data;
        this.game_history = res.data["game_history"];
        if (parseInt(this.gameIndex) > 0) {
          this.game_id =
            res.data["game_history"][this.gameIndex - 1]["game_id"];
        } else {
          this.gameIndex = res.data["game_history"].length - 1;
          this.game_id = res.data["game_history"].last["game_id"];
        }
        this.dis_game_id = parseInt(this.game_id);
        this.send_data = {
          game_id: parseInt(this.game_id),
          user_hash: this.user_id + ":1",
          user_id: this.user_id
        };
        if (this.eInfo.game_status != "is_end") {
          this.$refs.starGameDialog.open();
        }
        this.getStatus();
      });
    }
  },
  mounted() {
    this.handler_locker.tryAcquire();
    this.handler_locker.release();
    this.dis_game_id = parseInt(this.game_id);
    this.user_id = storage.$getStroage("userId");
    this.user_hash = `${this.user_id}:1`;
    // this.dis_game_id = parseInt(this.game_id);
    // this.send_data = {
    //   game_id: parseInt(this.game_id),
    //   user_hash: this.user_id + ":1",
    //   user_id: this.user_id
    // };
    this.move_audio = document.getElementById("move-audio");
    this.initStatus();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.timer1) {
      clearInterval(this.timer1);
    }
    this.cancel();
  }
};
</script>

<style scoped lang="less">
.gameWrap {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  vertical-align: middle;
  .wifi {
    position: absolute;
    right: 16px;
    top: 40px;
    display: flex;
    z-index: 10;
    flex-direction: row-reverse;
  }

  .content {
    // width: 2016px;
    // height: 1344px;
    padding-top: 40px;
    display: flex;
    justify-content: center;
    margin: 0 auto;
    .left {
      width: 1000px;
      height: 1000px;
      background: #f7a448;
      border: 16px solid #fee194;
      box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.1);
      border-radius: 40px;
      position: relative;
      box-sizing: border-box;
    }

    .right {
      width: 488px;
      height: 1000px;
      margin-left: 16px;
      background: rgba(255, 255, 255, 0.5);
      border: 2px solid rgba(255, 255, 255, 0.5);
      box-shadow: 0 4px 15px 0 rgba(0, 0, 0, 0.1);
      border-radius: 30px;
      box-sizing: border-box;

      .game_type_wrap {
        width: 488px;
        height: 104px;
        padding: 16px 20px 16px 16px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        background-image: linear-gradient(180deg, #ffffff 6%, #c6ecff 87%);
        box-shadow: 0 4px 15px 0 #5bb9e7, inset 0 -3px6px 0 #e1f1fc;
        border-radius: 30px 30px 0 0;
        position: relative;
        align-items: center;

        .game_index {
          width: 144px;
          height: 72px;
          line-height: 72px;
          text-align: center;
          background: rgba(198, 236, 255, 0.5);
          border: 1.49px solid rgba(108, 206, 255, 0.3);
          box-shadow: inset 0 0 9px 0 #6cceff;
          border-radius: 37.2px;
          font-size: 26.79px;
          color: #344770;
        }

        .fuc_wrap {
          // width: 304px;
          // height: 92px;
          display: flex;

          .back {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
            margin-left: 12px;
          }

          .reload {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
          }

          .rule {
            width: 64px;
            height: 64px;

            border-radius: 50%;
            box-shadow: 0 3px 6px 0 rgba(91, 185, 231, 0.5);
            cursor: pointer;
            margin-left: 12px;
          }
        }
      }

      .drop_countdown {
        width: calc(100% - 32px);
        height: 64px;
        line-height: 64px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 32px;
        font-size: 32px;
        color: #ffffff;
        text-align: center;

        margin: 16px auto 0;
      }
    }
  }
}
</style>
