<template>
  <transition name="van-fade">
    <div class="wrap" v-if="enable_dialog">
      <div
        class="account-dialog"
        @click="$emit('close_dialog')"
        :style="{
          background: `url(${require('@/assets/mine/account.png')}) center no-repeat
      transparent`,
          'background-size': 'contain'
        }"
      >
        <div class="buttons">
          <div class="confirm jcyt500" @click="confirm_cancel()">注销账号</div>
          <div class="cancle jcyt500" @click="close()">再想想</div>
        </div>
      </div>
    </div>
  </transition>
</template>
<script>
import mineApi from "@/api/mine";
export default {
  name: "rightDialog",
  props: {
    enable_dialog: {
      type: Boolean
    },
    mobile: {},
    code: {}
  },
  watch: {
    enable_dialog: {
      handler: function (data) {
        console.log(data);
      },
      deep: true
    }
  },
  methods: {
    close() {
      this.$emit("closeAccount");
    },
    confirm_cancel() {
      mineApi
        .DelNumberAPi({
          username: this.mobile,
          sms_code: this.code
        })
        .then((res) => {
          if (res.data["id"] != null) {
            this.$router.push("/delNumberDown");
          }
        });
    }
  }
};
</script>
<style scoped lang="less">
.wrap {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  z-index: 20001;
  top: 0;
  left: 0;
}
.account-dialog {
  position: absolute;
  width: 944px;
  height: 792px;
  top: 50%;
  left: 50%;
  margin-left: -472px;
  margin-top: -396px;

  z-index: 20001;
  .buttons {
    width: 720px;
    height: 112px;
    margin: 600px auto 0px;
    display: flex;
    justify-content: space-between;
    .confirm {
      width: 344px;
      height: 104px;
      background: #ffffff;
      border: 3.78px solid #00bdff;
      border-radius: 56.7px;
      font-size: 38px;
      color: #00bdff;
      text-align: center;
      line-height: 96px;
      cursor: pointer;
      box-sizing: border-box;
    }
    .cancle {
      width: 344px;
      height: 104px;
      background: #08ccfd;
      border-radius: 56.7px;
      font-size: 38px;
      color: #ffffff;
      text-align: center;
      line-height: 104px;
      cursor: pointer;
      margin-left: 96px;
    }
  }
}
</style>
