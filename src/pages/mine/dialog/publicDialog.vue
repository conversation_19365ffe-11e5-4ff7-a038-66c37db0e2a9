<template>
  <div class="dialog" v-if="visible">
    <div
      class="dialog-container"
      :class="{ customPadding: isCustomPadding }"
      :style="{
        width: customStyleWidth * 0.04882812 + 'vw',
        padding: customStylePadding * 0.04882812 + 'vw'
      }"
    >
      <div
        class="close-icon"
        @click="close()"
        v-if="showClose"
        :style="{
          'background-image': `url(${require('@/assets/mine/关闭按钮.png')})`,
          'background-size': '100% 100%'
        }"
      ></div>
      <div class="dialog-title"
      :style="{
        fontSize: customStylefontSize * 0.04882812 + 'vw',
      
      }"
      >{{ title }}</div>
      <div class="content">
        <slot></slot>
      </div>
      <slot name="footer">
        <div class="confirm-button" @click="confirmBtn">
          {{ confirmText }}
        </div></slot
      >
    </div>
  </div>
</template>
<script>
export default {
  props: {
    visible: Boolean,
    title: {
      type: String
    },
    confirmText: {
      type: String,
      default: "确认"
    },
    isCustomPadding: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: true
    },
    customStyleWidth: {
      type: Number,
      default: 1152
    },
    customStylePadding: {
      type: Number
    },
    customStylefontSize: {
      type: Number
    },
  },
  data() {
    return {};
  },
  methods: {
    close() {
      this.$emit("update:visible", false);
    },
    confirmBtn() {
      this.$emit("confirm");
      //   this.close();
    }
  },
  mounted() {
    console.log(this.customStyleWidth, "customwidth");
  }
};
</script>
<style lang="less" scoped>
.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
  .dialog-container {
    width: 1152px;
    height: auto;
    background: #ffffff;
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 110px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 64px 176px;
    .close-icon {
      width: 96px;
      height: 96px;
      position: absolute;
      top: 0;
      right: -112px;
      border-radius: 50%;
    }
    .dialog-title {
      font-size: 56px;
      line-height: 64px;
      color: #333333;
      font-weight: bold;
      box-sizing: border-box;
      margin-bottom: 64px;
    }
    .content {
      width: 100%;
      height: auto;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      .avactar-container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      .avatar {
        width: 160px;
        height: 160px;
        border: 10px solid #fff;
        border-radius: 50%;
        background-size: 100% 100%;
        box-shadow: 0px 4px 12px 0px rgba(32, 47, 66, 0.06);
        margin-bottom: 48px;
        position: relative;
      }
      .select {
        border-color: #00baff;
      }
      .disabled {
        filter: grayscale(100%);
      }
    }
    .content::-webkit-scrollbar {
      width: 0px;
    }
    .confirm-button {
      margin-top: 16px;
      width: 800px;
      height: 120px;
      text-align: center;
      line-height: 120px;
      font-size: 48px;
      color: #ffffff;
      border-radius: 60px;
      background: linear-gradient(
        to bottom,
        rgba(45, 213, 255),
        rgba(0, 204, 255)
      );
      box-shadow: 0px 8px 8px 0px #96e6ff inset, 0px -8px 8px 0px #00bdff inset;
      font-weight: bold;
    }
  }
  .customPadding {
    padding-left: 88px;
    padding-right: 88px;
  }
}
</style>
