<template>
  <div class="dialog" v-show="showMask">
    <div class="dialog-container">
      <div
        class="close-icon"
        @click="close()"
        :style="{
          'background-image': `url(${require('@/assets/mine/关闭按钮.png')})`,
          'background-size': '100% 100%',
        }"
      ></div>
      <div class="dialog-title jcyt600">更换头像</div>
      <div class="content">
        <div class="avactar-container">
          <div
            class="avatar"
            v-for="(item, index) in avatarList"
            v-bind:key="index"
            @click="selectAvatar(item.state, index)"
            :style="{
              'background-image': 'url(' + item.url + ')',
            }"
            :class="{
              select: isSelect == index && item.state,
              disabled: !item.state,
            }"
          >
            <div
              v-show="isSelect == index && item.state"
              class="selectIcon"
              :style="{
                'background-image': `url(${require('@/assets/mine/选中背景.png')})`,
                'background-size': '100% 100%',
              }"
            ></div>
          </div>
        </div>
      </div>
      <div class="confirm-button jcyt600" @click="submitAvatar">确认</div>
    </div>
  </div>
</template>

<script>
import mineApi from "@/api/mine";
export default {
  props: {
    value: {}, //类型包括defalut默认，danger危险，confirm确认，
  },

  data() {
    return {
      showMask: false,
      avatarList: [],
      isSelect: 0,
    };
  },
  methods: {
    closeMask() {
      this.showMask = false;
    },
    selectAvatar(state, index) {
      if (state) {
        this.isSelect = index;
      }
    },
    submitAvatar() {
      // mineApi
      //   .ChangeAvatar({ avatar: this.avatarList[this.isSelect].url })
      //   .then(() => {
          // this.avatarList = res.data;
          this.$emit("confirm", this.avatarList[this.isSelect].url);
          this.closeMask();
        // });
    },
    close() {
      this.$emit("cancel");

      this.closeMask();
    },
  },

  mounted() {
    this.showMask = this.value;
    mineApi.GetAvatarListAPi().then((res) => {
      this.avatarList = res.data;
    });
  },

  watch: {
    value(newVal) {
      this.showMask = newVal;
    },

    showMask(val) {
      this.$emit("input", val);
    },
  },
};
</script>

<style lang="less" scoped>
.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  .dialog-container {
    width: 1200px;
    height: 800px;
    background: #ffffff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 102px;
    display: flex;
    align-items: center;
    flex-direction: column;
    .close-icon {
      width: 96px;
      height: 96px;
      position: absolute;
      top: 0;
      right: -112px;
      border-radius: 50%;
    }
    .dialog-title {
      font-size: 52px;
      line-height: 64px;
      color: #333333;
      box-sizing: border-box;
      margin: 63px 0 57px;
    }
    .content {
      width: 888px;
      height: 504px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      .avactar-container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

      }

      .avatar {
        width: 160px;
        height: 160px;
        border: 10px solid #fff;
        border-radius: 50%;
        background-size: 100% 100%;
        box-shadow: 0px 4px 12px 0px rgba(32, 47, 66, 0.06);
        margin-bottom: 48px;
        margin-right: 56px;
        position: relative;

      }
      .avatar:nth-child(4n){
      margin-right:0
      }

      .select {
        border-color: #00baff;
      }
      .selectIcon {
        position: absolute;
        width: 50px;
        height: 50px;
        top: -10px;
        right: -10px;
        border-radius: 50%;
      }
      .disabled {
        filter: grayscale(100%);
      }
    }
    .content::-webkit-scrollbar {
      width: 0px;
    }
    .confirm-button {
      margin-top: 48px;
      width: 576px;
      height: 88px;
      text-align: center;
      line-height: 88px;
      font-size: 38px;
      color: #ffffff;
      border-radius: 43.2px;
      margin-bottom: 72px;
      background: linear-gradient(
        to bottom,
        rgba(45, 213, 255),
        rgba(0, 204, 255)
      );
      box-shadow:0 3px 5px 0 rgba(0, 56, 79, .1), 0px 5px 5px 0px #96e6ff inset, 0px -5px 5px 0px #00bdff inset;
    }
  }
}
</style>