<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt500">消息详情</div>
      <div class="center-box">
        <div class="center-container">
          <div>
            <div v-html="informDetail?.content" class="jcyt500"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
// import mineApi from "@/api/mine";
import informApi from "@/api/inform";
import { Indicator } from "mint-ui";
export default {
  name: "mineIndex",
  components: {},
  data() {
    return {
      informDetail: "",
      apiDone: false,
    };
  },
  created() {
    if (this.$route.query.messageId) {
      this.init(this.$route.query.messageId);
    }
    Indicator.open({
      text: "加载中...",
      //文字
      spinnerType: "fading-circle",
      //样式
    });
  },
  destroyed() {
    Indicator.close();
  },
  computed: {},
  methods: {
    init(id) {
      informApi.GetMessageInfo({ message_id: id }).then((res) => {
        this.apiDone = true;
        Indicator.close();
        this.informDetail = res.data;
      });
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="less">
::-webkit-scrollbar {
  width: 0px;
}
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    background-size: cover;
    z-index: 9;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    margin-top: 103px;
    margin-bottom: 49px;
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }

  .center-box {
    width: 100%;
    flex: 1;
    display: flex;
    justify-content: center;
    .center-container {
      width: 1680px;
      height: 784px;

      display: flex;
      box-sizing: border-box;
      flex-direction: column;
      align-items: center;
      border-radius: 50px;
      background: #ffffff;
      box-shadow: 0 0px 50px 0px rgba(124, 143, 166, 0.1);
      padding: 60px 76px;
      > div {
        width: 100%;
        height: 100%;
        overflow: auto;

        > div {
          width: 100%;
          height: auto;
          font-size: 26px;
          line-height: 48px;
          //   :deep(p) {
          //     margin-bottom: 44px;
          //   }
        }
      }
    }
  }
}
.fading-circle {
  color: rgba(100, 49, 191, 255);
}
</style>
