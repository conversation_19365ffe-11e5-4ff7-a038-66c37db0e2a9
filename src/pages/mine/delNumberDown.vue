<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt500">注销账号</div>
      <div class="center-box">
        <div class="center-container">
          <div>
            <div
              class="avatar-container"
              @click="openMask()"
              :style="{
                'background-image': `url(${require('@/assets/mine/默认头像.png')})`,
                'background-size': '100% 100%'
              }"
            >
              <img class="avatar" :src="info.avatar || ''" />
            </div>
          </div>
          <div class="field-content">
            <div class="field-container">
              <span class="field-label jcyt500">账号</span>
              <span class="divider"></span>
              <input placeholder="请输入学生姓名" v-model="info.mobile" readonly class="jcyt500"/>
            </div>
            <div class="field-container">
              <span class="field-label jcyt500">姓名</span>
              <span class="divider"></span>
              <input placeholder="请输入学生姓名" v-model="info.name" readonly class="jcyt500"/>
            </div>
          </div>
          <div class="time jcyt600">{{ h }}:{{ m }}:{{ s < 10 ? "0" + s : s }}</div>
        </div>
      </div>
      <div class="submit jcyt600" @click="findCode()">找回账号</div>
    </div>
  </div>
</template>

<script>
import mineApi from "@/api/mine";
import { parseTime } from "./date";
import { Toast } from "mint-ui";

export default {
  name: "mineIndex",
  components: {},
  data() {
    return {
      info: {},
      accountInfo: {},
      d: "",
      h: "",
      m: "",
      s: "",
      sum_h: "",
      timer:null
    };
  },
  created() {
    this.initInfo();
  },
  computed: {},
  methods: {
    findCode() {
      mineApi.RevokeAPi().then((res) => {
        clearTimeout(this.timer);
        Toast("账号找回成功");
      });
      this.$router.push("/");
    },
    goBack() {
        clearTimeout(this.timer);
      this.$router.push("/systemSetting");
    },
    initInfo() {
      mineApi.GetInfo().then((res) => {
        this.info = res.data;
        this.mobile = this.info.mobile;
      });
      mineApi.DelNumberDownAPi().then((res) => {
        this.accountInfo = res.data;
   
        this.countTime();
      });
    },

    countTime: function () {
      // 获取当前时间
      var date = new Date();
      var now = date.getTime();
      //设置截止时间
      var endDate = new Date(parseTime(this.accountInfo.logoff_time));
      var end = endDate.getTime();
      //时间差
      var leftTime = end - now;
      console.log(leftTime);
      //定义变量 d,h,m,s保存倒计时的时间
      if (leftTime >= 0) {
        this.d = Math.floor(leftTime / 1000 / 60 / 60 / 24);
        this.h = Math.floor((leftTime / 1000 / 60 / 60) % 24);
        this.m = Math.floor((leftTime / 1000 / 60) % 60);
        this.s = Math.floor((leftTime / 1000) % 60);
        this.sum_h = this.d * 24 + this.h;
      }
      // console.log(this.s);
      //递归每秒调用countTime方法，显示动态时间效果
      clearTimeout(this.timer);
      this.timer = setTimeout(this.countTime, 1000);
    }
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    margin-top: 103px;
    margin-bottom: 55px;
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }
  .submit {
    width: 576px;
    height: 88px;
    border-radius: 43.2px;
    background: linear-gradient(
      to bottom,
      rgba(45, 213, 255),
      rgba(0, 204, 255)
    );
    box-shadow: rgba(0, 56, 79, 0.1) 0 3px 5px 0, 0px 5px 5px 0px #96e6ff inset, 0px -5px 5px 0px #00bdff inset;
    text-align: center;
    line-height: 88px;
    font-size: 38px;
    color: #ffffff;
    opacity: 0.8;
    margin-top: 32px;
  }

  .center-box {
    width: 1448px;
    height: 684px;
    border-radius: 36px;
    background: #ffffff;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 43px 72px;
    box-sizing: border-box;
    .center-container {
      width: 1304px;
      height: 598px;
    }
    .avatar-container {
      width: 176px;
      height: 176px;
      margin: 0 auto;
      border: 8px solid #fff;
      border-radius: 50%;
      background-size: 100% 100%;
      //   margin-bottom: 40px;
      position: relative;
      box-shadow: 0 10px 16px 0px rgba(0, 124, 199, 0.1);
      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
      .avatar-camera {
        width: 56px;
        height: 56px;
        position: absolute;
        bottom: -10px;
        right: -10px;
        border: 10px solid #fff;
        border-radius: 50%;
      }
    }
    .field-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      height: 96px;
      margin-top: 32px;
    }
    .time {
      width: 100%;
      height: 238px;
      line-height: 238px;
      background: rgba(22, 195, 255, 0.08);
      border-radius: 38px;
      font-size: 106px;
      color: #6e95b5;
      text-align: center;
      margin-top: 40px;
    }
  }
}
.field-container {
  box-sizing: border-box;
  width: 632px;
  height: 96px;
  border-radius: 52.8px;
  background-color: #f6f8fb;
  line-height: 48px;
  padding: 28px 42px 28px 42px;
  display: flex;
  align-items: center;
  .field-label {
    font-size: 36px;
    line-height: 96px;
    color: #333333;
    width: 72px;
  }
  .divider {
    display: inline-block;
    height: 41.14px;
    width: 1.76px;
    background-color: rgba(191, 192, 197, .25);
    margin: 0 26.76px 0 26.36px;
  }
  input {
    border: none;
    background-color: #f6f8fb;
    font-size: 36px;
    line-height: 48px;
    outline: none;
    width: 420px;
  }
  .pull-down {
    width: 48px;
    height: 48px;
  }
}
</style>
