<template>
  <div class="living-timetable" :style="{
      'background-image': `url(${require('@/assets/index/首页背景.png')})`,
      'background-size': '100% 100%',
    }">
      <div class="flex-row living-top">
        <div
          class="back"
          @click="goBack"
          :style="{
            'background-image': `url(${require('@/assets/back/返回_白.png')})`,
            'background-size': '100% 100%'
          }"
        >
        </div>
        <p class="title">直播课</p>
        <div class="right-time flex-row items-center content-center">
          <div style="position:relative;width: 100%" @click="changeMonth">
            <span>{{month}}月</span>
            <img src="@/assets/questionBank/Fill.png" />
          </div>
        </div>
      </div>
      <div class="living-course">
      <swiper
        class="swiper swiper-no-swiping"
        :options="swiperOption"
        ref="Swiper"
      >
        <swiper-slide v-for="(item, index) in list" :key="index">
          <div class="living-course-pc swiper-item" @click="toHtml(item)">
            <img :src="item.cover_pic" class="course-pic" />
            <p class="title">{{ item.name }}</p>
            <div class="middle flex-row content-between">
              <div class="teacher flex-row items-center">
                <img :src="item.teacher_avatar" />
                <p class="name">{{ item.teacher_name }}</p>
              </div>
              <p class="name">{{ formatValue(item.student_numb) }}人</p>
            </div>
            <div class="bottom flex-row content-between">
              <p class="time">
                {{
                  `${getDate(item.estimated_start_time)} 周${getWeek(
                    item.estimated_start_time
                  )}`
                }}
                <span class="special">{{
                  getTime(item.estimated_start_time)
                }}</span>
              </p>
              <div
                class="button flex-row items-center content-center"
                :style="{
                  'background-image':
                    'url(' +
                    require(
                      item.status == 3
                      ? '../../assets/study/btn_bg_living.png'
                      : item.status <= 2
                      ? item.can_enter ? '../../assets/study/btn_bg_living.png' : '../../assets/study/btn_bg_start.png'
                      : '../../assets/study/btn_bg_restart.png') +
                    ')'
                }"
              >
                <img
                  :src="
                    require(item.status == 3
                      ? '../../assets/study/status_living.png'
                      : item.status <= 2
                      ? '../../assets/study/status_start.png'
                      : '../../assets/study/status_restart.png')
                  "
                  :class="item.status == 3 ? 'img1' : 'img2'"
                />
                <span>{{
                  item.status == 3
                    ? "直播中"
                    : item.status == 2 || item.status == 1
                    ? "即将开始"
                    : "课程回放"
                }}</span>
              </div>
            </div>
          </div>
        </swiper-slide>
        <swiper-slide> </swiper-slide>
        <swiper-slide v-if="list.length % 3 == 0"> </swiper-slide>
        <div
          class="swiper-button-prev"
          slot="button-prev"
          :style="{
            'background-image': `url(${require('@/assets/index/左箭头.png')})`,
            'background-size': 'contain'
          }"
        ></div>
        <div
          class="swiper-button-next"
          slot="button-next"
          :style="{
            'background-image': `url(${require('@/assets/index/右箭头.png')})`,
            'background-size': 'contain'
          }"
        ></div>
      </swiper>
      <div class="modal" v-if="dialogVisible"></div>
      <div class="dialog" v-if="dialogVisible">
        <img
          src="../../assets/study/圆叉.png"
          class="dialog-clear"
          @click="dialogVisible = false"
        />
        <p class="title">输入房间密码</p>
        <div class="box">
          <input
            class=""
            v-model="pwd"
            placeholder="请输入房间密码"
            type="password"
          />
          <img
            src="../../assets/study/清除.png"
            class="clear"
            v-if="pwd != ''"
            @click="pwd = ''"
          />
        </div>
        <button
          class="button"
          :style="{
            'background-image':
              'url(' + require('../../assets/study/login_btn.png') + ')'
          }"
          @click="openHtml"
        >
          确认
        </button>
      </div>
    </div>
    <mt-datetime-picker
        type="date"
        ref="picker"
        v-model="pickerDate"
        year-format="{value} 年"
        month-format="{value} 月"
        @confirm="handleConfirm"
        :startDate="startDate"
      >
      </mt-datetime-picker>
      <div class="no-Message-container" v-if="list.length == 0">
        <img class="no-Message" src="@/assets/nothing/暂无课程.png" />
        <p class="no-message-content">目前暂无直播课表</p>
      </div>
  </div>
</template>
<script>
import studyApi from "@/api/study";
import timeFormat from "@/public/timeFormat";
import { Toast } from "mint-ui";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import "swiper/css/swiper.min.css";
import { ipcRenderer } from "electron";
import moment from 'moment';
import { sha256 } from "js-sha256";
import { DatetimePicker } from "mint-ui";
import zip from "@/public/zip";

export default {
  data(){
    return {
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 0,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        }
      },
      ticket: "",
      pwd: "",
      dialogVisible: false,
      list: [],
      year: "",
      month: "",
      pickerDate: new Date(),
      startDate: new Date("2020-01-01"),
    }
  },
  mounted(){
    var nowTime = moment().utc();
    this.year =  nowTime.get("year");
    this.month = nowTime.get("month") + 1;
    this.getList();
  },
  components: { swiper, swiperSlide,"mt-datetime-picker": DatetimePicker },
  methods: {
    goBack() {
      this.$router.push({
        path: "/mine",
      });
    },
    getTime(time) {
      return timeFormat.GetCustTime(time * 1000, "HH:mm");
    },
    formatValue(num) {
      var res = num.toString().replace(/\d+/, function (n) {
        // 先提取整数部分
        return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
          return $1 + ",";
        });
      });
      return res;
    },
    getWeek(time) {
      let week = timeFormat.GetCustTime(time * 1000, "E");
      let arr = ["一", "二", "三", "四", "五", "六", "日"];
      return arr[week - 1];
    },
    getDate(time) {
      return timeFormat.GetCustTime(time * 1000, "MM月DD日");
    },
    async getList() {
      
      let n_month = this.month < 10 ? '0' + this.month.toString() : this.month.toString();
      let res = await studyApi.getMyLivingCourseApi({year: this.year, month: n_month});
      this.list = res.data.data == null ? [] : res.data.data;
      this.list.forEach((item,index) => {
        if(item.status == 2) {
          let nowTime = moment().utc().valueOf();
          let canEnterTime = item.student_early_entry_time * 60 * 1000;
          let estimatedTime = item.estimated_start_time * 1000;
          item.can_enter = nowTime >= estimatedTime - canEnterTime;
        }
        this.$set(this.list, index, item);
      })

    },
    async getTicket(id) {
      let res = await studyApi.getLivingCourseTicketApi({ live_id: id });
      this.ticket = res.data.data.ticket;
      this.dialogVisible = false;
      __bl.sum("直播课空中课堂学习");
      // window.open(
      //   "https://class.ushu.com/classroom/studentGetToken.html?ticket=" +
      //     this.ticket,
      //     "_blank",
      //     'width=' + (window.screen.availWidth - 10) + ',height=' + (window.screen.availHeight - 70) + ', top = 0, left = 0, status = no'
      // );
      ipcRenderer.send("openWindow", "https://class.ushu.com/classroom/studentGetToken.html?ticket=" + this.ticket);
      const os = require('os');
      if(os.platform() == 'darwin') {
        try {
          const mediaDevices = navigator.mediaDevices;
          const stream = await mediaDevices.getUserMedia({
            video: true,
            audio: true
          });
          // 进行摄像头和麦克风的操作
        } catch (err) {
          console.log('无法访问摄像头或麦克风');
        }
      }
    },
    toHtml(item) {
      if (item.status > 2 || (item.status == 2 && item.can_enter)) {
          if (item.status == 4) {
            if (item.url_list.length > 0 &&
                item.url_list != null && item.url_list[0] != "") {
              // 打开直播 嵌入aliyun
              window.localStorage.setItem("ty_url_list", item.url_list.toString());
              let winURL = process.env.NODE_ENV !== "production" ? `http://localhost:8080` : `app://./index.html`;
              ipcRenderer.send("openWindow", winURL + "#/livingCourseReplay");
            } else {
              Toast("暂无回放");
            }
          } else {
            if (item.type == 2) {
              this.dialogVisible = true;
              this.pwd = "";
              this.id = item.id;
              this.itemPwd = item.passwd;
            } else {
              this.getTicket(item.id);
            }
          }
      }
    },
    openHtml() {
      let p = sha256(`${this.pwd}_eStarGo2019`);
      if (p == this.itemPwd) {
        this.getTicket(this.id);
      } else {
        Toast("密码输入错误");
      }
    },
    changeMonth(){
      this.$refs.picker.open();
    },
    handleConfirm(){
      var ntime = moment(this.pickerDate);
      this.month = ntime.get("month") + 1;
      this.$forceUpdate();
      this.year = ntime.get("year");
      this.getList();
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .picker-slot:nth-child(3) {
  display: none;
}
.back {
  width: 128px;
  height: 128px;
  position: absolute;
  top: 80px;
  left: 50px;
}
.living-top .title {
  font-size: 56px;
  color: white;
  margin-top: 80px;
  text-align: center;
  width: 100%;
}

.right-time {
  width: 286px;
  height: 88px;
  border-radius: 44px;
  background: #fff;
  font-size: 40px;
  color: #333333;
  font-weight: bold;
  position: absolute;
  right: 40px;
  top: 80px;
  text-align: center;
  img {
    width: 30px;
    height: 32px;
    position: absolute;
    right: 40px;
    top: 10px;
  }
}

.living-timetable {
  width: 100vw;
  height: 100vh;
}
.living-course {
  margin: 0 80px;
  padding-top: 248px;
}
::-webkit-scrollbar {
  width: 0;
}
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.content-between {
  justify-content: space-between;
}
.content-center {
  justify-content: center;
}
.living-course-pc {
  width: 620px;
  height: 624px;
  border-radius: 50px;
  background-color: #fff;
  box-shadow: 0 10px 50px 0 rgba(132, 184, 198, 0.25);
  padding: 24px;
  box-sizing: border-box;
  cursor: pointer;
  .course-pic {
    width: 572px;
    height: 288px;
    border-radius: 40px;
    object-fit: cover;
  }
  .title {
    font-size: 48px;
    color: #333333;
    font-weight: bold;
    margin-top: 32px;
  }
  .middle {
    margin-top: 16px;
  }
  .teacher {
    img {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-right: 16px;
    }
  }
  .name {
    font-size: 26px;
    color: #999999;
  }
  .time {
    font-weight: bold;
    color: #333333;
    font-size: 32px;
    .special {
      display: block;
    }
  }
  .button {
    width: 224px;
    height: 80px;
    border-radius: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    cursor: pointer;
    span {
      font-size: 32px;
      color: #fff;
      font-weight: bold;
      margin-left: 14px;
    }
  }
  .bottom {
    margin-top: 32px;
  }
  .img1 {
    width: 32px;
    height: 30px;
  }
  .img2 {
    width: 40px;
    height: 40px;
  }
}
.dialog {
  width: 1152px;
  height: 744px;
  border-radius: 110px;
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  margin: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  z-index: 20;
  .title {
    font-size: 56px;
    font-weight: bold;
    color: #333333;
    padding-top: 64px;
    text-align: center;
  }
  input {
    border: none;
    background: #f6f8fb;
    border-radius: 60px;
    height: 120px;

    margin-left: 176px;
    width: 740px;
    padding-left: 60px;
    font-size: 40px;
    color: #333333;
    &::after {
      border: none;
    }
    outline: none;
  }
  input:focus {
    border: none;
  }
  .button {
    width: 800px;
    height: 120px;
    border-radius: 60px;
    background-size: cover;
    background-repeat: no-repeat;
    margin-top: 80px;
    border: none;
    font-size: 40px;
    color: #ffffff;
    margin-left: 176px;
    outline: none;
    cursor: pointer;
  }
  .clear {
    position: absolute;
    right: 216px;
    top: 34px;
    height: 56px;
    width: 56px;
    cursor: pointer;
  }
  .box {
    position: relative;
    margin-top: 120px;
  }
}
.dialog-clear {
  position: absolute;
  right: -120px;
  width: 96px;
  height: 96px;
  z-index: 30;
  cursor: pointer;
}
.modal {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100vh;
  width: 100vw;
}
.swiper-button-prev {
  width: 148px;
  height: 148px;

  left: 40px;
  margin-top: -74px;
  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}
.swiper-button-prev::after {
  content: "";
}
.swiper-button-next {
  width: 148px;
  height: 148px;

  right: 40px;
  bottom: 10px;
  margin-top: -74px;
  border-radius: 50%;

  box-shadow: 0 12px 20px 0 rgba(0, 0, 0, 0.1);
}
.swiper-button-next::after {
  content: "";
}
.swiper-slide {
  width: 620px!important;
  margin-right: 60px;
}
.no-Message-container {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.no-Message {
  width: auto;
  height: 600px;
  margin-top: 30px;
}
.no-message-content {
  font-size: 30px;
  color: #b2b2b2;
}
::v-deep .mint-popup-bottom {
  top: 500px;
}
</style>
