<template>
  <div class="alipayer">
    <aliplayer :urlList="urlList" ref="player"></aliplayer>
    <div class="tool" :class="{'tool-pull' : isPull}">
      <div class="list" v-if="isPull">
        <div class="list-item" v-for="(item, index) in urlList" :key="index" @click="changeVideo(index)" v-if="index > 0" :class="{'is-play': index == $refs.player.index}">{{getString(item)}}</div>
      </div>
      <img src="@/assets/course/more.png" class="arrow" :class="{'arrow-rotate' : isPull}" @click="isPull = !isPull"/>
    </div>
  </div>
</template>

<script>
import aliplayer from "@/components/Index/aliplayer.vue";
import  zip  from "@/public/zip";
export default {
  data(){
    return {
      urlList: [],
      isPull: false
    }
  },
  created(){
    this.urlList = window.localStorage.getItem("ty_url_list").split(",");
  },
  methods: {
    getString(item){
      var s = item.length > 24 ? item.substring(item.length - 17) : item;
      return s;
    },
    changeVideo(index){
      this.$refs.player.moveTo(index);
    }
  },
  components: {
    aliplayer
  },
  destroyed(){
    window.localStorage.removeItem("ty_url_list");
  }
}
</script>
<style lang="less" scoped>
.aliplayer {
  width: 100vw;
  height: 100vh;
  position: relative;
}
.tool {
  width: 1.6vw;
  height: 100vh;
  position: absolute;
  z-index: 10;
  background-color: rgba(0, 0, 0, .4);
  display: flex;
  flex-direction: row;
  right: 0;
  top: 0;
  cursor: pointer;
}
.arrow {
  width: 1.6vw;
  height: 1.6vw;
  align-self: center;
  transform: rotate(180deg);
  filter: brightness(0);
}
.arrow-rotate {
  transform: rotate(0);
}
.tool-pull {
  width: 15vw;
}
.list {
  display: flex;
  flex-direction: column;
  padding: 0 1vw;
  margin-left: 0.1vw;
  width: calc(100% - 1.6vw);
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 0;
  }
  .list-item {
    font-size: 24px;
    color: white;
    text-align: left;
    height: 5vh;
    line-height: 5vh;
  }
  .is-play {
    color: #31BFFF;
  }
}
</style>