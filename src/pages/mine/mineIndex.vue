<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/我的背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/back/返回_白.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="main-box">
      <div class="left-box">
        <div
          @click="updateInformation('information')"
          class="avatar"
          :style="{
            'background-image': 'url(' + info.avatar + ')'
          }"
        >
          <img
            v-if="info.gender === 'female'"
            class="avatar-sex"
            src="@/assets/mine/女.png"
          />
          <img
            v-if="info.gender !== 'female'"
            class="avatar-sex"
            src="@/assets/mine/男.png"
          />
        </div>
        <div class="name-container">
          <span class="name jcyt600">{{ info.name }}</span>

          <img
            @click="updateInformation('information')"
            class="name-icon"
            src="@/assets/mine/铅笔_pad.png"
          />
        </div>
        <div class="type jcyt400">{{ info.school_nick_name }}</div>
        <div class="left-bottom-box">
          <div class="study-time">
            <span class="jcyt400">学习时长</span>
            <div class="type-num jcyt500">{{ info.reg_time || 0 }}<span class="jcyt400">天</span></div>
          </div>
          <div class="line"></div>
          <div class="match-num">
            <span class="jcyt400">对局次数</span>
            <div class="type-num jcyt500">{{ gameTime }}<span class="jcyt400">局</span></div>
          </div>
        </div>
      </div>
      <div class="right-box">
        <div class="study-center">
          <div class="center-title jcyt600">学习中心</div>
          <ul>
            <li>
              <img
                src="@/assets/mine/sgf.png"
                @click="updateInformation('chessboard')"
              />
              <span class="jcyt500">我的棋谱</span>
            </li>
            <li v-if="liveCount > 0">
              <img
                src="@/assets/mine/livingTimetable_pad.png"
                @click="updateInformation('livingCourseTimetable')"
              />
              <span class="jcyt500">直播课表</span>
            </li>
            <li>
              <img
                src="@/assets/mine/学习报告_pad.png"
                @click="updateInformation('studyList')"
              />
              <span class="jcyt500">学习报告</span>
            </li>
            <li>
              <img
                src="@/assets/mine/复盘报告_pad.png"
                @click="updateInformation()"
              />
              <span class="jcyt500">复盘报告</span>
            </li>
          </ul>
        </div>
        <div class="function-center">
          <div class="center-title jcyt600">功能中心</div>
          <ul>
            <li>
              <img
                src="@/assets/mine/我的收藏_pad.png"
                @click="updateInformation('myCollect')"
              />
              <span class="jcyt500">我的收藏</span>
            </li>
            <li>
              <img
                src="@/assets/mine/我的订单_pad.png"
                @click="updateInformation('mineOrder')"
              />
              <span class="jcyt500">我的订单</span>
            </li>
            <li>
              <div class="message-box" @click="updateInformation('message')">
                <img src="@/assets/mine/我的消息pad.png" />
                <div v-if="unread_message" class="unread-message-box"></div>
              </div>
              <span class="jcyt500">我的消息</span>
            </li>
            <li>
              <img
                src="@/assets/mine/系统设置_pad.png"
                @click="updateInformation('systemSetting')"
              />
              <span class="jcyt500">系统设置</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mineApi from "@/api/mine";
import informApi from "@/api/inform";
import { Toast } from "mint-ui";
import config from "@/config";

export default {
  name: "mineIndex",
  data() {
    return {
      active: "",
      info: {},
      count: 0,
      unread_message: false,
      liveCount: 0,
      gameTime: 0
    };
  },
  created() {
    mineApi.GetInfo().then((res) => {
      this.info = res.data;
      this.liveCount = config.isAirSchool ? res.data.live_count : 0;
    });
    mineApi.GameCountAPi().then((res) => {
      this.gameTime = res.data.count;
    });
    this.initMessage();
  },
  components: {
    // userInfo,
    // gameInfo,
    // accountSetting,
    // levelInfo,
    // learningList,
  },
  computed: {
    // personal_information() {
    //   return this.$store.getters.doneGetPersonalInformation;
    // },
  },
  methods: {
    // change_active: function (active) {
    //   console.log(this.$refs.gameInfo);
    //   // uni.redirectTo({
    //   //   url: `/pages/mine/index?active=${active}`,
    //   // });
    // },
    initMessage() {
      informApi.GetMessageList().then((res) => {
        if (res.data.length != 0) {
          this.unread_message = res.data.some((t) => {
            return t.is_read == false;
          });
        }
      });
    },
    goBack() {
      this.$router.push("/");
    },
    updateInformation(route) {
      let a =
        route == "information"
          ? "编辑信息"
          : route == "chessboard"
          ? "我的棋谱"
          : route == "studyList"
          ? "学习报告"
          : route == "myCollect"
          ? "我的收藏"
          : route == "mineOrder"
          ? "我的订单"
          : route == "message"
          ? "我的消息"
          : route == "systemSetting"
          ? "系统设置"
          : "";
      __bl.sum(a);
      if (route) {
        this.$router.push("/" + route);
      } else {
        Toast({message: "研发中~敬请期待", duration: 1000 });
      }
    },
    goStudyReport() {
      this.$router.push("/studyReportDetail");
    }
  },
  onBackPress() {
    // uni.reLaunch({
    //   url: "/",
    // });
    return true;
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  margin-top: 256px;
  // margin: 0 176px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  .left-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    align-items: center;
    flex: 50%;
    margin-right: 128px;
    padding-left: 296px;
    box-sizing: border-box;
    .avatar {
      width: 176px;
      height: 176px;
      border: 8px solid #fff;
      background-size: 100% 100%;
      margin-bottom: 32px;
      border-radius: 50%;
      box-shadow: rgba(0, 124, 199, .1) 0 10px 16px 0;
      position: relative;
      .avatar-sex {
        width: 44.8px;
        height: 44.8px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
    .name-container {
      margin-bottom: 8px;
      display: flex;
      align-items: flex-end;
      position: relative;
      .name {
        font-size: 44px;
        line-height: 51.2px;
      }
      .name-icon {
        width: 56px;
        height: 56px;
        position: absolute;
        right: -64px;
      }
    }
    .type {
      font-size: 26px;
      color: #999999;
      line-height: 32px;
    }
    .left-bottom-box {
      width: 616px;
      height: 224px;
      background-color: rgba(22, 195, 255, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 32px;
      text-align: center;
      margin-top: 72px;
      .line {
        height: 108.32px;
        width: 1.61px;
        background: #deeef3;
      }
      .study-time {
        flex: 50%;
      }
      .type-num {
        font-size: 52px;
        line-height: 56px;
        color: #333333;
        margin-top: 16px;
        span {
          margin-left: 18.19px;
        }
      }
      .match-num {
        flex: 50%;
      }
      span {
        font-size: 30px;
        line-height: 38.4px;
        color: #999999;
      }
    }
  }
  .right-box {
    flex: 50%;
    ul {
      list-style: none;
      display: flex;
      margin-top: 48px;
    }
    li {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .function-center {
      li {
        margin-right: 56px;
        align-self: center;
      }
      img {
        width: 104px;
        height: 104px;
        margin-bottom: 8px;
      }
      span {
        font-size: 26px;
        line-height: 32px;
        color: #666666;
      }
    }
    .message-box {
      position: relative;
      height: 112px;
      .unread-message-box {
        position: absolute;
        right: 7px;
        top: 7px;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        border: 2px solid #fff;
        background-color: #fe4f37;
      }
    }
    .study-center {
      margin-bottom: 96px;
      li {
        margin-right: 24px;
      }
      img {
        width: 128px;
        height: 128px;
        margin-bottom: 4px;
      }
      span {
        font-size: 26px;
        line-height: 32px;
        color: #666666;
      }
    }
    .center-title {
      font-size: 32px;
      line-height: 38.4px;
      color: #333333;
    }
  }
}
</style>
