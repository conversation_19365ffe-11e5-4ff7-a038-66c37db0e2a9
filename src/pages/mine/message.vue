<template>
  <div class="container"  :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%',
    }">
    <div class="back" @click="goBack" :style="{
      'background-image': `url(${require('@/assets/index/back.png')})`,
      'background-size': '100% 100%',
    }"></div>
    <div class="main-box">
      <div class="title jcyt500">我的消息</div>
      <div class="center-box">
        <div class="center-container">
          <div class="no-Message-container" v-if="messageList.length == 0">
            <img class="no-Message" src="@/assets/nothing/暂无课程.png" />
            <p class="no-message-content jcyt500">暂无消息</p>
          </div>
          <div v-else>
            <div
              v-for="(item, index) in messageList"
              v-bind:key="index"
              class="message-container"
              @click="goDetail(item.id.toString())"
            >
              <div class="message-pic" :style="{
      'background-image': `url(${require('@/assets/mine/informIcon.png')})`,
      'background-size': '100% 100%',
    }">
                <div v-if="!item.is_read" class="unread-tip"></div>
              </div>
              <div class="message-box">
                <div class="message-title-container">
                  <div class="message-title jcyt500">{{ item.title }}</div>
                  <div class="message-date jcyt400">
                    {{ item.publish_time.substring(0, 10) +
                          ' ' +
                          item.publish_time.substring(11, 16) }}
                  </div>
                </div>
                <div class="message-intro jcyt400">{{ item.abstract }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
// import mineApi from "@/api/mine";
import informApi from "@/api/inform";
import { Indicator } from "mint-ui";
export default {
  name: "mineIndex",
  components: {},
  data() {
    return {
      messageList: [],
      apiDone: false,
    };
  },
  created() {
    this.initInfo();
    Indicator.open({
      text: "加载中...",
      //文字
      spinnerType: "fading-circle",
      //样式
    });
    },
  destroyed() {
    Indicator.close();
  },
  computed: {},
  methods: {
    initInfo() {
      this.$store.commit("setApiLoading", true);
      informApi.GetMessageList().then((res) => {
        this.apiDone = true;
        Indicator.close();
        this.messageList = res.data;
        this.$store.commit("setApiLoading", false);
      });
    },
    goDetail(id) {
      this.$router.push(`/informDetail?messageId=${id}`);
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    z-index: 9;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    margin-top: 103px;
    margin-bottom: 49px;
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }

  .center-box {
    width: 100%;
    flex: 1;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 0 !important;
    }

    .center-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .message-container {
        width: 1592px;
        height: 232px;
        background-color: #ffffff;
        border-radius: 32px;
        box-shadow: 0 0px 40px 0px rgba(124, 143, 166, 0.1);
        margin-bottom: 40px;
        padding: 24px 52px 24px 24px;
        box-sizing: border-box;
        display: flex;
        align-items: center;

        .message-pic {
          width: 184px;
          height: 184px;
          border-radius: 32.2px;
          margin-right: 44px;
          position: relative;
          .unread-tip {
            position: absolute;
            right: 0px;
            top: 0px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 1.61px solid #fff;
            background-color: #fe4f37;
          }
        }
        .message-box {
          flex: 1;
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          .message-title-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            .message-title {
              font-size: 38px;
              line-height: 42px;
              color: #333333;
              width: 760px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .message-date {
              font-size: 26px;
              line-height: 42px;
              color: #999999;
            }
          }
          .message-intro {
            margin-top: 21px;
            width: 100%;
            height: 84px;
            font-size: 28px;
            color: #999999;
            line-height: 42px;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
    .no-Message-container {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
    }
    .no-Message {
      width: auto;
      height: 442px;
      margin-top: 60px;
      margin-bottom: 31px;
      margin-left: 70px;
    }
    .no-message-content {
      font-size: 36px;
      color: #3A3B3B;
    }
  }
}
.fading-circle {
  color: rgba(100, 49, 191, 255);
}
</style>
