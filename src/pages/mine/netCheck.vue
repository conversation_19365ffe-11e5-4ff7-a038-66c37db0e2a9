<template>
  <div class="net-check" :style="{
        'background-image': `url(${require('@/assets/course/课程背景.png')})`,
        'background-size': '100% 100%',
      }">
    <img src="@/assets/index/back.png" class="back" @click="goBack"/>
    <div class="title jcyt500">
      <span>网络检测</span>
    </div>
    <div class="content flex-row items-center content-center">
      <div class="left-img">
        <div class="inner-text flex-column content-center">
          <span class="check-value jcyt500" :class="{'percent': result == 'inCheck'}"> {{result == 'inCheck' ? checkPercent : getResult(result)}}<i v-if="result == 'inCheck'">%</i></span>
          <span class="status jcyt500">{{ result == 'inCheck' ? '正在检测' : '检测结果' }}</span>
        </div>
        <img :src="result == 'inCheck' ? require('@/assets/mine/转圈pad.png') :
                    result == 'perfect' ? require('@/assets/mine/优秀.png') :
                    result == 'normal' ? require('@/assets/mine/一般.png') :
                    require('@/assets/mine/不佳.png')"
            :style="{'transform': result == 'inCheck' ? `rotate(${checkCircleRotate}deg)` : `rotate(0deg)`,
                    '-webkit-transform': result == 'inCheck' ? `rotate(${checkCircleRotate}deg)` : `rotate(0deg)`}"/>
      </div>
      <div class="line"></div>
      <div class="right">
        <div class="flex-row item">
          <span class="item-label jcyt500">网络类型</span>
          <span class="item-value jcyt500">{{ netType }}</span>
        </div>
        <div class="flex-row item">
          <span class="item-label jcyt500">主站延迟</span>
          <span class="item-value jcyt500" :class="{'special': (result != 'inCheck' && result != 'perfect' && result != 'normal')}">{{ delayHttp }}ms</span>
        </div>
      </div>
    </div>
    <div class="again jcyt500" :style="{
        'background-image': `url(${ result == 'inCheck' ? require('@/assets/login/login_btn_dis.png') : require('@/assets/login/login_btn.png')})`,
        'background-size': '100% 100%',
      }" @click="checkStart">
      <span>重新检测</span>
    </div>
  </div>
</template>
<script>
// import networkApi from "@/api/network";
import userApi from "@/api/user";
import moment from "moment";
// import ping from "ping";
export default {
  data(){
    return {
      netType: "",
      delayHttp: 0,
      resultLevel: {
        normal: 100,
        frozen: 200,
      },
      result: "",
      alreadyRequestDone: 0,
      alreadyPingDone: 0,
      timer: null,
      checkPercent: 0,
      checkCircleRotate: 0
    }
  },
  mounted(){
    this.getType();
    this.checkStart();
  },
  methods: {
    getType(){
      console.log(navigator);
      var connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      var type = connection.type || connection.effectiveType;
      this.netType = type;
    },
    goBack(){
      this.$router.go(-1)
    },
    checkStart(){
      if(this.result == 'inCheck') {
        return false;
      }
      this.result = "inCheck";
      this.delayHttp = 0;
      this.getAppVersion();
      //this.netWorkHealth();
      this.timer = setInterval(() => {
        if(this.checkPercent >= 100) {
          this.checkDone();
        } else {
          var alreadyDone = this.alreadyRequestDone
          //  this.alreadyPingDone > this.alreadyRequestDone
          //   ? this.alreadyRequestDone
            // : this.alreadyPingDone;
          var tempTop = alreadyDone * 20;
          this.checkCircleRotate += Math.PI * 4;
          if (this.checkPercent < tempTop) {
            this.checkPercent += 5;
          }
        }
      }, 100);
    },
    getResult(result) {
      return result == 'perfect' ? '优秀' : result == 'normal' ? '一般' : '不佳';
    },
    async getAppVersion(){
      var delaySum = 0;
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      delaySum += await this.httpTest();
      this.delayHttp = Math.ceil(delaySum / 5);
    },
    async httpTest() {
      var justNow = new Date().getTime();
      var response = moment().utc().valueOf();
      //await networkApi.getNetwork();
      var difference_value =
          parseInt(response) - justNow; //.data['data']['t']
      var res = await userApi.meshPing({'request_time': justNow});
      this.alreadyRequestDone += 1;
      return res.data['latency'] - difference_value;
    },
    checkDone(){
      if(this.timer) {
        clearInterval(this.timer);
      }
      this.checkPercent = 0;
      this.alreadyRequestDone = 0;
      this.checkCircleRotate = 0;
      this.alreadyPingDone = 0;
      this.result = this.delayHttp > this.resultLevel.frozen ? "frozen" :
                    this.delayHttp > this.resultLevel.normal ? "normal" : "perfect";
    },
    // netWorkHealth(){
    //   var pingServe = "higo-api.elf-go.com";
    //   var delaySum = 0;
    //   for (let i = 0; i < 5; i++) {
    //     ping.promise.probe(pingServe).then((data) => {
    //       this.alreadyPingDone = this.alreadyPingDone + 1;
    //       if (!data.alive) {
    //         delaySum += 1000;
    //       } else {
    //         delaySum += data.time;
    //       }

    //       if (i === 4) {
    //         const delayPing = Math.ceil(delaySum / 5)
    //         // console.log('平均延迟：', delayPing)
    //       }
    //     });
    //   }
    // }
  }
}
</script>
<style lang="less" scoped>
@keyframes rotate-anima {
  0% {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
  }
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.content-center {
  justify-content: center;
}
.net-check {
  height: 100vh;
  width: 100vw;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    z-index: 100;
    cursor: pointer;
  }
  .title {
    font-size: 52px;
    color: #333;
    text-align: center;
    padding-top: 103px;
  }
  .content {
    width: 1528px;
    height: 680px;
    box-sizing: border-box;
    padding: 0 40px;
    background: #fff;
    border-radius: 32px;
    box-shadow: 0 0 50px 0 rgba(124, 143, 166, 0.1);
    margin: 49px auto 0;
  }
  .left-img {
    position: relative;
    width: 520px;
    height: 520px;
    margin-right: 119.5px;
    img {
      width: 100%;
      height: 100%;
    }
    .check-value {
      color: #3D5066;
      font-size: 104px;
      line-height: 104px;
      text-align: center;
      i {
        font-size: 48px;
        font-style: normal;
        font-weight: normal;
      }
    }
    .percent {
      line-height: 49.6px;
    }
    .status {
      font-size: 26px;
      color: #999;
      text-align: center;
      margin-top: 10px;
      line-height: 32px;
    }
    .inner-text {
      position: absolute;
      top: 0;
      left: 0;
      width: 520px;
      height: 520px;
      z-index: 10;
    }
  }
  .line {
    height: 600px;
    width: 1.1px;
    background: #EEEEF1;
  }
  .right {
    width: 560px;
    height: 352px;
    margin-left: 103.5px;
  }
  .item {
    width: 100%;
    height: 96px;
    padding: 0 48px 0 40px;
    background: #F6F8FB;
    border-radius: 48px;
    box-sizing: border-box;
    line-height: 96px;
    justify-content: space-between;
    .item-label {
      font-size: 32px;
      color: #3D5066;
    }
    .item-value {
      font-size: 32px;
      color: #7C8FA6;
    }
    .special {
      color: #FF6461;
    }
  }
  .item + .item {
    margin-top: 32px;
  }
  .again {
    height: 88px;
    width: 576px;
    color: #fff;
    line-height: 88px;
    font-size: 38px;
    text-align: center;
    margin: 32px auto 0;
    box-shadow: rgba(0, 56, 79, .1) 0 3px 5px 0;
    border-radius: 43.2px;
  }
  .rotate {
    animation: rotate-anima 2.5s linear infinite;
    -webkit-animation: rotate-anima 2.5s linear infinite;
  }
}
</style>