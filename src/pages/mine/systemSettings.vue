<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt500">系统设置</div>
      <div class="center-box">
        <div class="center-container">
          <div class="field-container">
            <div class="field-box mobile-field-box">
              <span class="field-label jcyt500">手机号</span>
              <!-- <div class="bind-box" @click="mobileBindVisible = true">
                <span>绑定</span>
              </div> -->
              <div
                v-if="info?.mobile == null || info?.mobile == ''"
                class="bind-box jcyt500"
                :style="{
                  'background-image': `url(${require('@/assets/mine/系统设置button.png')})`,
                  'background-size': '100% 100%'
                }"
                @click="mobileBindVisible = true"
              >
                <span>绑定</span>
              </div>
              <div
                class="mobile-box jcyt500"
                v-if="info?.mobile !== null && info?.mobile !== ''"
              >
                <span>{{
                  this.info?.mobile !== null && this.info?.mobile !== ""
                    ? is_show_phone
                      ? this.info?.mobile
                      : this.info?.mobile?.replace(
                          /(\d{3})\d{4}(\d{4})/,
                          "$1****$2"
                        )
                    : ""
                }}</span>
                <div
                  @click="is_show_phone = !is_show_phone"
                  class="mobile-icon"
                >
                  <img v-if="!is_show_phone" src="@/assets/login/睁眼.png" />
                  <img v-if="is_show_phone" src="@/assets/login/闭眼.png" />
                </div>
                <div
                  v-if="
                    info?.mobile !== null &&
                    info?.mobile !== '' &&
                    info?.study_card !== null &&
                    info?.study_card !== ''
                  "
                  class="bind-box relieve-bind-box jcyt500"
                  :style="{
                    'background-image': `url(${require('@/assets/mine/系统设置button.png')})`,
                    'background-size': '100% 100%'
                  }"
                  @click="relieve('手机号')"
                >
                  <span>解绑</span>
                </div>
              </div>
            </div>
            <!-- <span class="divider"></span> -->
            <div class="field-box mobile-field-box">
              <span class="field-label jcyt500">学习卡号</span>
              <div
                v-if="info?.study_card == null || info?.study_card == ''"
                class="bind-box jcyt500"
                :style="{
                  'background-image': `url(${require('@/assets/mine/系统设置button.png')})`,
                  'background-size': '100% 100%'
                }"
                @click="studyBindVisible = true"
              >
                <span>绑定</span>
              </div>
              <div
                class="mobile-box jcyt500"
                v-if="info?.study_card !== null && info?.study_card !== ''"
              >
                <span>{{ this.info.study_card || "" }}</span>
                <div
                  v-if="
                    info?.mobile !== null &&
                    info?.mobile !== '' &&
                    info?.study_card !== null &&
                    info?.study_card !== ''
                  "
                  class="bind-box relieve-bind-box jcyt500"
                  :style="{
                    'background-image': `url(${require('@/assets/mine/系统设置button.png')})`,
                    'background-size': '100% 100%'
                  }"
                  @click="relieve('学习卡')"
                >
                  <span>解绑</span>
                </div>
              </div>
            </div>
            <!-- <span class="divider"></span> -->
            <div class="field-box flex-container">
              <span class="field-label jcyt500">密码设置</span>
              <span
                class="field-icon"
                @click="changePassword"
                :style="{
                  'background-image': `url(${require('@/assets/mine/箭头.png')})`,
                  'background-size': '100% 100%'
                }"
              ></span>
            </div>
          </div>
          <div class="field-container">
            <!-- <div class="field-box mobile-field-box">
              <span class="field-label">护眼模式</span>
              <div @click="safeEye = !safeEye" class="eye-icon">
                <img v-if="!safeEye" src="@/assets/mine/switch_关.png" />
                <img v-if="safeEye" src="@/assets/mine/switch_开.png" />
              </div>
            </div> -->
            <!-- <span class="divider"></span> -->
            <div class="field-box flex-container mobile-field-box"  @click="checkNetwork">
              <span class="field-label jcyt500">网络检测</span>
              <span
                class="field-icon"
                :style="{
                  'background-image': `url(${require('@/assets/mine/箭头.png')})`,
                  'background-size': '100% 100%'
                }"
              ></span>
            </div>
            <div class="field-box flex-container">
              <span class="field-label jcyt500">用户反馈</span>
              <span
                class="field-icon"
                @click="userRebackVisible = true"
                :style="{
                  'background-image': `url(${require('@/assets/mine/箭头.png')})`,
                  'background-size': '100% 100%'
                }"
              ></span>
            </div>
          </div>
          <div class="field-container field-container-bottom">
            <div class="field-box mobile-field-box">
              <span class="field-label jcyt500">关于我们</span>
              <div class="mobile-box jcyt500">{{ tyVersion.version }}</div>
            </div>
            <!-- <span class="divider"></span> -->
            <div
              class="field-box mobile-field-box"
              @click="goTo('userAgreement')"
            >
              <span class="field-label jcyt500">用户协议</span>
              <span
                class="field-icon"
                :style="{
                  'background-image': `url(${require('@/assets/mine/箭头.png')})`,
                  'background-size': '100% 100%'
                }"
              ></span>
            </div>
            <div
              class="field-box flex-container"
              @click="goTo('privateAgreement')"
              :class="{ 'mobile-field-box': settingInfo?.is_logoff_open == 1 }"
            >
              <span class="field-label jcyt500">隐私协议</span>
              <span
                class="field-icon"
                :style="{
                  'background-image': `url(${require('@/assets/mine/箭头.png')})`,
                  'background-size': '100% 100%'
                }"
              ></span>
            </div>
            <!-- <span class="divider"></span> -->
            <div
              v-if="settingInfo?.is_logoff_open == 1"
              @click="goCancelAccount()"
              class="field-box flex-container"
            >
              <span class="field-label jcyt500">注销账号</span>
              <span
                class="field-icon"
                :style="{
                  'background-image': `url(${require('@/assets/mine/箭头.png')})`,
                  'background-size': '100% 100%'
                }"
              ></span>
            </div>
          </div>
          <div class="submit jcyt600" @click="logOut()">退出登录</div>
          <div class="company jcyt400">
            <p>Copyright@2019-{{ getYear() }}</p>
            <p>北京弈友围棋文化传播有限公司</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 绑定手机号 -->
    <mt-dialog
      :visible.sync="mobileBindVisible"
      @confirm="mobileConfirm"
      title="绑定手机号"
      confirmText="确认"
    >
      <template>
        <div class="mobile-container">
          <input
            v-model="mobile"
            type="number"
            placeholder="请输入手机号"
            @change="canSubmit"
            class="jcyt500"
          />
        </div>
        <div class="code-container">
          <div class="mobile-container code-box">
            <input
              type="text"
              placeholder="请输入验证码"
              v-model="input_smsCode"
              maxlength="6"
              @change="changeMobileCode($event)"
              class="jcyt500"
            />
            <div
              v-if="cleanCode"
              class="clean-code-icon"
              :style="{
                'background-image': `url(${require('@/assets/login/清除.png')})`,
                'background-size': '100% 100%',
              }"
              @click="clickCleanCode('mobile')"
            ></div>
          </div>
          <button
            @click="getSms"
            class="mobile-container jcyt500"
            :style="{ color: isEnabledGetSms ? '#00BDFF' : '#BFC1C5' }"
          >
            {{ codeCountdownStr }}
          </button>
        </div>
      </template></mt-dialog
    >
    <!-- 绑定学习卡 -->
    <mt-dialog
      :visible.sync="studyBindVisible"
      @confirm="studyConfirm"
      title="绑定学习卡号"
      confirmText="确认"
    >
      <template>
        <div class="mobile-container">
          <input
            v-model="studyCard"
            type="number"
            placeholder="请输入学习卡号"
            @change="changeStudyCard($event)"
            class="jcyt500"
          />
        </div>
        <div class="mobile-container">
          <input
            v-model="studyCardPassword"
            type="number"
            placeholder="请输入学习卡密码"
            class="jcyt500"
          />
        </div> </template
    ></mt-dialog>
    <!-- 修改密码 -->
    <mt-dialog
      :visible.sync="passwordVisible"
      @confirm="passwordConfirm"
      title="修改密码"
      confirmText="确认修改"
    >
      <template>
        <div class="mobile-container">
          <input
            v-model="mobile"
            type="number"
            placeholder="请输入手机号"
            readonly
            class="jcyt500"
          />
        </div>
        <div class="code-container">
          <div class="mobile-container code-box">
            <input
              type="text"
              placeholder="请输入验证码"
              maxlength="6"
              v-model="code"
              @change="changeCode($event)"
              class="jcyt500"
            />
            <div
              v-if="cleanCode"
              class="clean-code-icon"
              :style="{
                'background-image': `url(${require('@/assets/login/清除.png')})`,
                'background-size': '100% 100%',
              }"
              @click="clickCleanCode('pass')"
            ></div>
          </div>
          <button
            @click="getUpdateSms"
            class="mobile-container jcyt500"
            :style="{ color: isEnabledGetSms ? '#00BDFF' : '#BFC1C5' }"
          >
            {{ codeCountdownStr }}
          </button>
        </div>
        <div class="mobile-container password-container">
          <input
            v-model="newPassword"
            :type="is_show_pass ? 'password' : 'text'"
            placeholder="请输入新密码(6～10位数字和字母)"
            @change="changePass($event)"
            class="jcyt500"
          />
          <div @click="is_show_pass = !is_show_pass" class="mobile-icon">
            <img v-if="!is_show_pass" src="@/assets/login/睁眼.png" />
            <img v-if="is_show_pass" src="@/assets/login/闭眼.png" />
          </div>
          <div class="mint-message jcyt500" v-if="displayMint">
            密码格式错误，请输入6~10位数字和字母
          </div>
        </div>
      </template></mt-dialog
    >
    <!-- 用户反馈 -->
    <mt-dialog
      :visible.sync="userRebackVisible"
      @confirm="userRebackConfirm"
      title="用户反馈"
      confirmText="提交"
    >
      <template>
        <div class="reback-container">
          <p class="reback-field-label jcyt500">问题或建议</p>
          <ty-textarea
            :value.sync="reback"
            placeholder="请填写10个字以上的问题描述以便我们提供更好的帮助"
            :maxlength="200"
          ></ty-textarea>
        </div> </template
    ></mt-dialog>
    <mt-dialog :visible.sync="relieveVisible" :showClose="false">
      <template>
        <div class="relieve-container">
          <p>{{ content }}解绑后</p>
          <p>将无法使用该{{ content }}登录</p>
        </div>
      </template>
      <template #footer>
        <div class="footer-container">
          <div class="footer-confirm-button" @click="confirmRelieve">确认</div>
          <div class="footer-cancel-button" @click="relieveVisible = false">
            取消
          </div>
        </div>
      </template>
    </mt-dialog>
  </div>
  <!-- </div> -->
</template>

<script>
import mineApi from "@/api/mine";
import userApi from "@/api/user";
import loginApi from "@/api/login";
// import dialogBar from "./dialog/changeAvatar";
import { Toast } from "mint-ui";
import dialog from "./dialog/publicDialog";
import textarea from "@/component/counter-textarea";
import { sha256 } from "js-sha256";
const tyVersion = require("../../../package.json");
import config from "@/config.js";
export default {
  name: "mineIndex",
  components: {
    "mt-dialog": dialog,
    "ty-textarea": textarea
  },
  data() {
    return {
      sendVal: "",
      is_show_phone: false,
      info: {},
      safeEye: false,
      settingInfo: {},
      mobileBindVisible: false, //手机绑定
      studyBindVisible: false, //学习卡绑定
      passwordVisible: false, //密码设置
      userRebackVisible: false, //用户反馈
      isEnabledGetSms: true,
      codeCountdownStr: "获取验证码",
      is_show_pass: true, //密码显示切换
      input_smsCode: "", //绑定手机验证码
      mobile: "",
      smsCode: "",
      expireTime: 60,
      isEnabledSubmit: false, //登录按钮状态
      isEnabledMobileSubmit: false, //绑定手机号按钮状态
      studyCard: "",
      studyCardPassword: "",
      password: "",
      reback: "",
      relieveVisible: false,
      content: "",
      code: "",
      cleanCode: false,
      newPassword: "",
      displayMint: false, //是否显示密码错误提示
      tyVersion: {}
    };
  },
  created() {
    this.initInfo();
    this.isAirSchool = config.isAirSchool;

    mineApi.GetSettingLogin().then((res) => {
      this.settingInfo = res.data;
    });
    this.tyVersion = tyVersion;
  },
  computed: {},
  methods: {
    initInfo() {
      mineApi.GetInfo().then((res) => {
        this.info = res.data;
      });
    },
    getYear() {
      var date = new Date();
      return date.getFullYear();
    },
    changeStudyCard() {},
    studyConfirm() {
      if (!this.studyCard || !this.studyCardPassword) {
        Toast(`请输入学习卡${!this.studyCard ? "号" : "密码"}`);
        return;
      }
      if (!this.isStudyCardExp(this.studyCard)) {
        Toast("请输入正确的学习卡号");
        return;
      }
      mineApi
        .BindStudyCard({
          card_numb: this.studyCard,
          card_password: this.studyCardPassword
        })
        .then((res) => {
          if (res.status && res.status === 200) {
            this.studyBindVisible = false;
            Toast({
              message: "绑定成功",
              duration: 800,
              position: "top",
              className: "successToast"
            });
            this.initInfo();
          } else if (res.error_code) {
            Toast({
              message: res.error_code,
              position: "top",
              className: "successToast"
            });
          }
        });
    },
    relieve(content) {
      this.content = content;
      this.relieveVisible = true;
    },
    changeCode(value) {
      if (!value.isEmpty) {
        this.cleanCode = true;
      } else {
        this.cleanCode = false;
      }
      this.passCanSubmit();
    },
    changeMobileCode(value) {
      if (!value.isEmpty) {
        this.cleanCode = true;
      } else {
        this.cleanCode = false;
      }
      this.canSubmit();
    },
    passCanSubmit() {
      if (
        this.newPassword != "" &&
        this.isPasswordExp(this.newPassword) &&
        this.code != ""
      ) {
        this.isEnabledSubmit = true;
      } else {
        this.isEnabledSubmit = false;
      }
    },
    goTo(route) {
      this.$router.push(`/${route}`);
    },
    canSubmit() {
      if (this.isPhoneExp(this.mobile) && this.smsCode != "") {
        this.isEnabledMobileSubmit = true;
      } else {
        this.isEnabledMobileSubmit = false;
      }
    },
    changePassword() {
      this.passwordVisible = true;
      this.mobile = this.info.mobile;
    },
    //密码设置
    passwordConfirm() {
      if (!this.isEnabledSubmit) {
        return null;
      } else {
        loginApi
          .ResetPassword({
            username: this.mobile,
            sms_code: sha256(`${this.code}_eStarGo2019`),
            password: this.newPassword
          })
          .then((res) => {
            if (res.status == 200) {
              Toast("密码设置成功");

              this.newPassword = "";
              this.smsCode = "";
              this.code = "";
              this.passwordVisible = false;
            }
          });
      }
    },
    changePass(e) {
      this.displayMint = !this.isPasswordExp(e.target.value);
      this.passCanSubmit();
    },
    //用户反馈
    userRebackConfirm() {
      if (this.reback.length == 0) {
        Toast({
          message: "请填写问题或建议",
          duration: 800,
          position: "top",
          className: "successToast",
        });
      } else {
        mineApi.FeedBack({ content: this.reback }).then((res) => {
          if (res.status == 200) {
            Toast({
              message: "提交成功",
              duration: 800,
              position: "top",
              className: "successToast",
            });
            this.userRebackVisible = false;
          }
        });
      }
    },
    isPhoneExp(str) {
      return RegExp("^1([0-9]{10})$").test(str);
    },
    isStudyCardExp(str) {
      return new RegExp(
        "^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9]{6,8}$"
      ).test(str);
    },
    isPasswordExp(str) {
      let pattern = /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9]{6,10}$/;
      return pattern.test(str);
    },
    clickCleanCode(type) {
      if (type == "mobile") {
        this.input_smsCode = "";
      } else {
        this.code = "";
      }
      this.cleanCode = false;
    },
    getSms() {
      if (!this.isEnabledGetSms) {
        return null;
      } else {
        if (!this.mobile) {
          Toast("请输入手机号");
          return null;
        } else if (!this.isPhoneExp(this.mobile)) {
          Toast("请输入正确的手机号");
          return null;
        }
        mineApi
          .BindMobileSendSMS({
            mobile: this.mobile,
          })
          .then((res) => {
            if (res.status == 200) {
              this.smsCode = res.data.sms_code;
              this.expireTime = res.data.expire_time;
              this.reGetCountdown();
              this.isEnabledGetSms = false;
            } else if (res.error_code) {
              Toast(res.error_code);
            }
          });
      }
    },
    getUpdateSms() {
      if (!this.isEnabledGetSms) {
        return null;
      } else {
        loginApi
          .ResetPasswordCode({
            username: this.mobile
          })
          .then((res) => {
            if (res.status == 200) {
              this.smsCode = res.data.sms_code;
              this.expireTime = res.data.expire_time;
              this.reGetCountdown();
              this.isEnabledGetSms = false;
            } else if (res.error_code) {
              Toast(res.error_code);
            }
          });
      }
    },
    reGetCountdown() {
      //   let tt = setTimeout(function () {
      if (this.expireTime >= 1) {
        this.expireTime--;
        this.codeCountdownStr = `${this.expireTime}s后重发`;
        setTimeout(this.reGetCountdown, 1000);
      } else {
        this.expireTime = 0;
        this.codeCountdownStr = "获取验证码";
        this.isEnabledGetSms = true;

        //   clearTimeout(tt);
      }
      //   }, 1000);
    },
    mobileConfirm() {
      if (this.isEnabledMobileSubmit) {
        mineApi
          .bindMobileApi({
            mobile: this.mobile,
            sms_code: sha256(`${this.input_smsCode}_eStarGo2019`)
          })
          .then(() => {
            this.mobileBindVisible = false;
            Toast("绑定成功");
            this.initInfo();
          });
        // .catchError(
        //     (err) {
        //       String _errText = err.toString();
        //       _errText = _errText.replaceAll("Exception: ", "");
        //       showToast(_errText,
        //           textStyle: TextStyle(
        //               color: Colors.white,
        //               fontSize: Responsive.isMobile(context) ? 16 : 30));
        //     },
        //   );
      }
    },
    goBack() {
      this.$router.push("/mine");
    },
    confirmRelieve() {
      if (this.content == "手机号") {
        userApi.OffBindMobile().then((res) => {
          this.initInfo();
          this.relieveVisible = false;
        });
      } else {
        userApi.OffBindStudyCard().then((res) => {
          this.initInfo();
          this.relieveVisible = false;
        });
      }
    },
    logOut() {
      if(this.$socket) {
        this.$socket.close();
      }
      this.$storage.$removeStroage("userId");
      this.$storage.$removeStroage("user_toekn");
      this.$storage.$removeStroage("liveToken");
      this.$router.push("/smsLogin");
    },
    goCancelAccount() {
      mineApi.DelNumberDownAPi().then((res) => {
        if (res.data["logoff_time"] > 0) {
          this.$router.push("/delNumberDown");
        } else {
          this.$router.push("/cancelAccount");
        }
      });
    },
    checkNetwork(){
      this.$router.push("/netCheck");
    }
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    margin-top: 103px;
    margin-bottom: 41px;
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }

  .center-box {
    width: 100%;
    flex: 1;
    overflow: auto;

    .center-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .field-container {
      width: 1384px;
      min-height: 112px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 32px;
      margin-bottom: 32px;
      padding: 39px 58px;
      box-shadow: rgba(124, 143, 166, .1) 0 0 40px 0;
    }

    .mobile-field-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1.1px solid #dddee4;
      padding-bottom: 32.25px;
      margin-bottom: 32.25px;
    }
    .flex-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .field-box {
      width: 1268px;
      .field-label {
        font-size: 32px;
        line-height: 40px;
        color: #333333;
      }

      .field-icon {
        width: 48px;
        height: 48px;
        color: #ccc;
        font-size: 48px;
      }

      .bind-box {
        width: 136px;
        height: 62px;
        border-radius: 28.8px;
        line-height: 59px;
        text-align: center;
        span {
          font-size: 28.8px;
          color: #ffffff;
        }
      }
      .relieve-bind-box {
        margin-left: 20px;
      }
      .mobile-box {
        display: flex;
        align-items: center;
        font-size: 40px;
        line-height: 56px;
        color: #999999;
        font-weight: 400;
      }
      .eye-icon {
        width: 128px;
        height: 72px;
        border-radius: 36px;
        img {
          width: 128px;
          height: 72px;
        }
      }
    }

    input {
      border: none;
      background-color: #ffffff;
      font-size: 40px;
      line-height: 48px;
      outline: none;
      width: 440px;
    }
    .field-container-bottom {
      height: auto;
      margin-bottom: 40px;
    }
    // .divider {
    //   display: inline-block;
    //   width: 1264.98px;
    //   height: 1px;
    //   background-color: #dddee4;
    //   //   margin: 0 30px;
    // }
  }
  .center-box::-webkit-scrollbar {
    width: 0px;
  }
  .mobile-icon {
    padding-left: 24px;
    width: 56px;
    height: 56px;
  }
  img {
    width: 56px;
    height: 56px;
  }
  .submit {
    width: 576px;
    height: 88px;
    border-radius: 43.2px;
    background: linear-gradient(
      to bottom,
      rgba(45, 213, 255),
      rgba(0, 204, 255)
    );
    box-shadow: rgba(0,56,79,.1) 0 3px 5px 0, 0px 5px 5px 0px #96e6ff inset, 0px -5px 5px 0px #00bdff inset;
    text-align: center;
    line-height: 88px;
    font-size: 38px;
    color: #ffffff;
    opacity: 0.8;
    margin-bottom: 31px;
  }
  .company {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 26px;
    color: #8ba6b6;
    p {
      line-height: 38px;
    }
    margin-bottom: 54px;
  }
}
.mobile-container {
  width: 100%;
  border-radius: 54px;
  height: 104px;
  box-sizing: border-box;
  padding: 0 48px;
  background-color: #f6f8fb;
  margin-bottom: 32px;

  input {
    border: none;
    font-size: 36px;
    line-height: 48px;
    outline: none;
    width: 100%;
    background-color: #f6f8fb;
    height: 104px;
  }
}
.mobile-icon {
  padding-left: 24px;
  width: 56px;
  height: 56px;
}
img {
  width: 56px;
  height: 56px;
}
.code-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .code-box {
    width: 552px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .clean-code-icon {
      width: 56px;
      height: 56px;
    }
  }
  button {
    width: 288px;
    border: none;
    font-size: 36px;
    color: #00bdff;
    box-sizing: border-box;
    line-height: 104px;
    margin-left: 24px;
  }
}
.password-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  .mint-message {
    position: absolute;
    bottom: -40px;
    left: 30px;
    color: #ff5868;
    font-size: 24px;
  }
}
.reback-container {
  margin-bottom: 48px;
}
.reback-field-label {
  font-size: 36px;
  line-height: 48px;
  color: #333333;
  margin-bottom: 25px;
}
.relieve-container {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  font-size: 40px;
  color: #333;
  font-weight: bold;
  margin-bottom: 65px;
}
.footer-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    width: 312px;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    font-weight: 500;
  }
  .footer-confirm-button {
    border: 6px solid #00bdff;
    color: #00bdff;
  }
  .footer-cancel-button {
    background: #08ccfd;
    color: #fff;
  }
}
.successToast {
  font-size: 30px;
  color: #ffffff;
}
::v-deep .dialog .dialog-container {
  width: 1080px!important;
  min-height: 648px;
  border-radius: 80px;
  box-sizing: border-box;
  padding: 72px 104px 80px;
  box-shadow: #CCFAFF 0 -7px 27px 0 inset, rgba(0, 0, 0, 0.1) 0 11px 36px 0;
  .dialog-title {
    margin-bottom: 55px;
    font-family: jcyt600w;
  }
  .confirm-button {
    width: 864px;
    height: 104px;
    line-height: 104px;
    font-family: jcyt500w;
    font-size: 38px;
  }
  input::-webkit-input-placeholder {
    color:#BFC1C5;
  }
}
</style>
