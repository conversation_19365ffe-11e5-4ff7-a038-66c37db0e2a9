<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt600">个人信息</div>
      <div class="center-box">
        <div>
          <div
            class="avatar-container"
            @click="openMask()"
          >
            <img class="avatar" :src="info.avatar || require('@/assets/mine/默认头像.png')" />
            <div
              class="avatar-camera"
              :style="{
                'background-image': `url(${require('@/assets/mine/相机.png')})`,
                'background-size': '100% 100%',
              }"
            ></div>
          </div>
        </div>
        <div class="filed-box">
          <div class="field-content">
            <div class="field-container">
              <span class="field-label jcyt600">姓名</span>
              <span class="divider"></span>
              <input placeholder="请输入学生姓名" v-model="info.name" class="jcyt500"/>
            </div>
            <div class="field-container" @click="sexVisible = true">
              <span class="field-label jcyt600">性别</span>
              <span class="divider"></span>
              <input placeholder="请选择" readonly v-model="sex" class="jcyt500"/>
              <div
                class="pull-down"
                :style="{
                  'background-image': `url(${require('@/assets/mine/下拉.png')})`,
                  'background-size': '100% 100%',
                }"
              ></div>
            </div>
          </div>
          <div class="field-content">
            <div class="field-container" @click="openPicker()">
              <span class="field-label jcyt600">生日</span>
              <span class="divider"></span>
              <input placeholder="请选择" readonly v-model="birthday" class="jcyt500"/>
              <div
                class="pull-down"
                :style="{
                  'background-image': `url(${require('@/assets/mine/下拉.png')})`,
                  'background-size': '100% 100%',
                }"
              ></div>
            </div>
            <div class="field-container" @click="openAddressPicker()">
              <span class="field-label jcyt600">地区</span>
              <span class="divider"></span>
              <input placeholder="请选择" v-model="region" readonly class="jcyt500"/>
              <div
                class="pull-down"
                :style="{
                  'background-image': `url(${require('@/assets/mine/下拉.png')})`,
                  'background-size': '100% 100%',
                }"
              ></div>
            </div>
          </div>
          <div>
            <div class="field-container bottom-field-container">
              <span class="field-label jcyt600">校区</span>
              <span class="divider"></span>
              <input
                placeholder="北京刘家窑校区"
                disabled
                v-model="info.branch_name"
                class="jcyt500"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="submit jcyt600" @click="sumbitInformation()">确认</div>
    </div>
    <dialog-bar
      v-model="sendVal"
      v-on:cancel="clickCancel()"
      @confirm="clickConfirm($event)"
      dangerText="Delete"
    >
    </dialog-bar>
    <!-- <mt-popup
      v-model="sexVisible"
      position="bottom"
      class="mint-popup"
      style="width: 100%; height: 30%"
    >
      <p class="addChooseButton">
        <span @click.stop="cancelp(1)">取消</span
        ><span @click.stop="cancelp(2)">确定</span>
      </p>
      <mt-picker
        :slots="slots"
        @change="onValuesChange"
        :visible-item-count="5"
        :show-toolbar="false"
        ref="picker"
        value-key="name"
      ></mt-picker>
    </mt-popup> -->
    <!-- <div class="sexPicker"> -->
    <mt-popup class="mint-popup sex-popup" v-model="sexVisible" position="bottom">
      <p class="addChooseButton">
        <span class="cancelBut jcyt500" @click="cancelp(1)">取消</span>
        <span class="confirmBut jcyt500" @click="cancelp(2)">确认</span>
      </p>
      <mt-picker
        :slots="slots"
        @change="onValuesChange"
        :visible-item-count="3"
        :show-toolbar="false"
        v-model="sexPicker"
        value-key="name"
        :itemHeight="80 / 1920 * w"
      ></mt-picker>
    </mt-popup>
    <div class="datePicker">
      <mt-datetime-picker
        type="date"
        ref="picker"
        v-model="pickerDate"
        year-format="{value} 年"
        month-format="{value} 月"
        date-format="{value} 日"
        @confirm="handleConfirm"
        :startDate="startDate"
        :visible-item-count="3"
      >
      </mt-datetime-picker>
    </div>
    <mt-popup class="mint-popup" v-model="addressVisible" position="bottom">
      <p class="addChooseButton">
        <span class="cancelBut jcyt500" @click="cancel()">取消</span>
        <span class="confirmBut jcyt500" @click="save()">确认</span>
      </p>
      <mt-picker
        ref="addressPicker"
        :slots="addressSlots"
        value-key="label"
        @change="changeCity"
        :visible-item-count="3"
        :itemHeight="80 / 1920 * w"
      ></mt-picker>
    </mt-popup>
  </div>
  <!-- </div> -->
</template>

<script>
import mineApi from "@/api/mine";
import dialogBar from "./dialog/changeAvatar";
import { Picker, Popup, DatetimePicker } from "mint-ui";
import moment from "moment";
import { Toast } from "mint-ui";
import area from "@/plugins/area";
export default {
  name: "mineIndex",
  components: {
    "dialog-bar": dialogBar,
    "mt-picker": Picker,
    "mt-popup": Popup,
    "mt-datetime-picker": DatetimePicker,
  },
  data() {
    return {
      active: "",
      info: {},
      count: 0,
      sendVal: false,
      sexVisible: false, //选择器的显示与影藏
      region: "",
      sex: "",
      sexPicker: "",
      pickerDate: "",
      slots: [
        {
          flex: 1,
          values: ["男", "女"],
          className: "slot1",
          textAlign: "center",
        },
      ],
      birthday: "", //出生日期
      startDate: new Date("1968-01-01"),
      addressVisible: false,
      addressStr: "",
      regionList: [],
      mrpId: "", //省id
      mrcid: "", //市id
      mrrid: "", //区id
      addressSlots: [
        {
          flex: 1,
          defaultIndex: 1,
          values: area, //省份数组
          className: "slot1",
          textAlign: "center",
        },
        {
          flex: 1,
          values: [],
          className: "slot2",
          textAlign: "center",
        },
        {
          flex: 1,
          values: [],
          className: "slot3",
          textAlign: "center",
        },
      ],
      w: 1
    };
  },
  created() {
    if (this.fromUrl == "portrait") {
      this.openMask();
    }
    this.initAddress("110000", "110100", "110101");
    mineApi.GetInfo().then((res) => {
      this.info = res.data;
      this.sex = res.data.gender
        ? res.data.gender === "female"
          ? "女"
          : "男"
        : "";
      this.slots[0].defaultIndex = this.sex === "男" ? 0 : 1;
      this.birthday = res.data.birthday.toString().substring(0, 10);
      this.regionList = res.data.region;
      this.initAddress(res.data.region);
    });
    // mineApi.GetSgfList({ page: 1 }).then((res) => {
    //   this.count = res.data.count;
    // });
    this.w = document.body.clientWidth;
  },
  computed: {
    fromUrl() {
      return this.$route.query.fromUrl;
    },
  },
  methods: {
    initAddress(regionArr) {
      let province, city, regions;
      area.map((item, index) => {
        if (item.value === regionArr[0] + "") {
          this.mrpId = item.value;
          province = item.label;
          this.addressSlots[0].defaultIndex = index;
          this.addressSlots[1].values = item.children;
          //   this.addressSlots[1].defaultIndex = item.children.findIndex(
          //     (item) => {
          //       item.value === region[1]+'';
          //     }
          //   );
          item.children.map((item, index) => {
            if (item.value === regionArr[1] + "") {
              this.mrcid = item.value;
              city = item.label;
              this.addressSlots[1].defaultIndex = index;
              this.addressSlots[2].values = item.children;
              this.addressSlots[2].defaultIndex = item.children.findIndex(
                (item3) => {
                  if (item3.value === regionArr[2] + "") {
                    regions = item3.label;
                    this.mrrid = item3.value;
                    return true;
                  }
                }
              );
            }
          });
        }
      });
      this.region = province + city + regions;
    },
    openMask() {
      this.sendVal = true;
    },
    clickCancel() {
      console.log("点击了取消");
    },
    clickConfirm(val) {
      this.info.avatar = val;
      console.log(val, "avatar");
    },
    goBack() {
      this.$router.push("/mine");
    },
    cancelp(index) {
      // const _this = this;
      if (index === 2) {
        this.sex = this.sexPicker;
        this.sexVisible = false;
      } else {
        this.sexVisible = false;
        this.sexPicker = this.sex;
        // _this.sex = "";
      }
    },
    save() {
      this.region = this.addressStr;
      this.regionList = [
        parseInt(this.mrpId),
        parseInt(this.mrcid),
        parseInt(this.mrrid),
      ];
      this.addressVisible = false;
    },
    openAddressPicker() {
      this.addressVisible = true;
      this.initAddress(this.regionList);
    },
    cancel() {
      this.addressStr = "";
      this.mrpId = this.regionList[0];
      this.mrcid = this.regionList[1];
      this.mrrid = this.regionList[2];
      this.addressVisible = false;
    },
    onValuesChange(picker, values) {
      //console.log(values)
      this.sexPicker = values[0];
      //   this.sexVisible = false;
    },
    openPicker() {
      this.pickerDate = new Date(this.birthday);
      this.$refs.picker.$refs.picker.itemHeight = 80 / 1920 * this.w;
      this.$refs.picker.open();
    },
    sumbitInformation() {
      if (
        this.info.name == "" ||
        this.sex == "" ||
        this.sex == null ||
        this.birthday == null ||
        this.birthday == "" ||
        this.regionList[0] == 0 ||
        this.regionList[0] == "" ||
        this.regionList[0] == null ||
        this.regionList.isEmpty
      ) {
        Toast("请完善信息");
      }
      mineApi
        .ChangeAvatar({ avatar: this.info.avatar }).then((res)=>{
          mineApi
        .UpdateInformation({
          name: this.info.name, //姓名
          birthday: this.birthday, //生日 yyyy-mm-dd
          region: this.regionList, //区域
          gender: this.sex == "女" ? "female" : "male", //性别
          school: 1,
          branch: 1,
        })
        .then((res) => {
          console.log(res, "update");
          if (res.status == 200) {
            //   if (globalVar.isRedirect) {
            //     globalVar.isRedirect = false;
            //     Routes.navigateTo(context, globalVar.redirectPath,
            //         params: globalVar.redirectParam, clearStack: true);
            //   }

            //   if (widget.type == 'change') {
            //     Routes.goBack(context, MinePadView());
            //   } else {
            this.goBack();
            //   }
          }
        });
        });
      
    },
    handleConfirm(data) {
      //   console.log(data);

      this.birthday = moment(data).format("YYYY-MM-DD"); //获取的时间为时间戳，getdata是自己写的一个转换时间的方法
    },
    changeCity(picker, val) {
      console.log(val);
      if (this.mrpId !== val[0].value) {
        picker.setSlotValues(1, val[0].children); //只要有一级数据就绑二级数据
        this.mrpId = val[0].value;
        // this.selectAddress = values;
      }
      if (val[1] && this.mrcid !== val[1].value) {
        picker.setSlotValues(2, val[1].children);
        this.mrcid = val[1].value; //只要有一级数据就绑二级数据
      }
      this.mrrid = val[2] && val[2].value;
      if (val[0] && val[1] && val[2]) {
        this.addressStr = val[0]["label"] + val[1]["label"] + val[2]["label"];
      }
    },
  },
  onBackPress() {
    // uni.reLaunch({
    //   url: "/",
    // });
    return true;
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 104px;
  .title {
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }
  .center-box {
    width: 1680px;
    height: 696px;
    border-radius: 32px;
    background: #ffffff;
    box-shadow: rgba(124, 143, 166, 0.1) 0 0 50px 0;
    margin-top: 48px;
    display: flex;
    align-items: center;
    flex-direction: column;
    .avatar-container {
      width: 176px;
      height: 176px;
      margin: 64px 0px 56px;
      border: 8px solid #fff;
      border-radius: 50%;
      background-size: 100% 100%;
      //   margin-bottom: 40px;
      position: relative;
      box-shadow: 0 10px 16px 0px rgba(0, 124, 199, 0.1);
      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
      .avatar-camera {
        width: 48px;
        height: 48px;
        position: absolute;
        bottom: -12px;
        right: -10px;
        border: 8px solid #fff;
        border-radius: 50%;
      }
    }
    .filed-box {
      width: calc(100% - 160px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .field-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
  .submit {
    width: 576px;
    height: 88px;
    border-radius: 43.2px;
    background: linear-gradient(
      to bottom,
      rgba(45, 213, 255),
      rgba(0, 204, 255)
    );
    box-shadow: 0 3px 5px 0 rgba(0, 56, 79, .1), 0px 5px 5px 0px #96e6ff inset, 0px -5px 5px 0px #00bdff inset;
    text-align: center;
    line-height: 88px;
    font-size: 38px;
    color: #ffffff;
    margin-top: 32px;
  }
  .field-container {
    box-sizing: border-box;
    width: 728px;
    height: 88px;
    border-radius: 48px;
    background-color: #f6f8fb;
    line-height: 88px;
    padding: 24px 40px 24px 48.68px;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    .field-label {
      font-size: 32px;
      line-height: 38px;
      color: #333333;
      width: 72px;
    }
    .divider {
      display: inline-block;
      height: 48px;
      width: 1.6px;
      background-color: rgba(191, 193, 197, 0.25);
      margin: 0 23.6px;
    }
    input {
      border: none!important;
      background-color: #f6f8fb;
      font-size: 32px;
      line-height: 38px;
      outline: none!important;
      width: 478px;
    }
    .pull-down {
      width: 48px;
      height: 48px;
    }
  }
  .bottom-field-container {
    width: 100%;
  }
}
.addChooseButton {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 32px;
  padding: 45px 56px 43px;
  cursor: pointer;
  z-index: 1;
  position: relative;
  background-color: #fff;
  .cancelBut {
    color: #666666;
  }
  .confirmBut {
    color: #00baff;
  }
}

::v-deep .picker-toolbar{
  height: 136px;
  padding: 0 56px;
  .mint-datetime-action {
    line-height: 48px;
    padding-top: 45px;
  }
}
.mint-popup {
  width: 100%;
  height: 416px;
  z-index: 10;
  background: #fff;
  border-radius: 32px 32px 0 0;
  overflow: hidden;
  ::v-deep .picker-center-highlight {
    background-color: #EEEEF0;
    border-radius: 14.04px;
    margin: 0 32px;
    width: calc(100vw - 64px);
    border: none;
    z-index: -1;
  }
  ::v-deep .picker-item {
    font-size: 38px;
    font-family: jcyt500w;
  }
  ::v-deep .picker-center-highlight:before,
  ::v-deep .picker-center-highlight:after {
    height: 0px;
  }
  ::v-deep .mint-datetime-confirm {
    color: #00baff;
    font-size: 32px;
    font-family: jcyt500w;
  }
  ::v-deep .mint-datetime-cancel {
    color: #666666;
    font-size: 32px;
    font-family: jcyt500w;
  }
}
.sex-popup {
  ::v-deep .picker-center-highlight {
    margin: 0 calc((100vw - 640px) / 2);
    width: 640px;
  }
}
</style>
