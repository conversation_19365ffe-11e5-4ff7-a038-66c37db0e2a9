<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%'
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt500">注销账号</div>
      <div class="center-box">
        <div class="center-container">
          <div class="field-container jcyt500">
            <h1>注销账号条件</h1>
            <p style="margin-bottom: 0">
              您提交注销申请后，聂卫平围棋网校团队将对下列信息进行验证，以保证您的账号、财产安全：
            </p>
            <span>请先确保账户内没有剩余课程</span>
            <h1>注销账号需知</h1>
            <p>点击下方“确认注销”按钮，即表示您已阅读且同意《注销账号须知》</p>
            <p>
              1.注销账号是不可恢复的操作，请谨慎操作，注销账号后，会永久删除账号下所有个人资料和历史信息，且无法找回。包括但不限于昵称头像、课程信息、交易记录、学习记录、比赛记录、排位赛晋级记录以及解除对外授权的绑定关系等
            </p>
            <p>
              2.注销账号并不代表账号注销前的账号行为和相关责任得到豁免或减轻。
            </p>
            <p>
              3.操作前，请务必确认聂卫平围棋账号内所有的服务均妥善处理，如因注销账号造成的损失，由您自行承担。
            </p>
          </div>
          <div class="submit jcyt600" @click="confirm()">确认注销</div>
        </div>
      </div>
    </div>

    <mt-dialog
      :visible.sync="cancelAccountVisible"
      @confirm="accountConfirm()"
      title="注销账号"
      confirmText="确认"
    >
      <template>
        <div class="mobile-container">
          <input
            v-model="mobile"
            type="number"
            placeholder="请输入手机号"
            readonly
            class="jcyt500"
          />
        </div>
        <div class="code-container">
          <div class="mobile-container code-box">
            <input
              type="text"
              placeholder="请输入验证码"
              maxlength="6"
              v-model="code"
              @change="passCanSubmit($event)"
              class="jcyt500"
            />
          </div>
          <button
            @click="getUpdateSms"
            class="mobile-container jcyt500"
            :style="{ color: isEnabledGetSms ? '#00BDFF' : '#BFC1C5' }"
          >
            {{ codeCountdownStr }}
          </button>
        </div>
      </template></mt-dialog
    >

    <changeAccountDialog
      :enable_dialog="accountVisible"
      @closeAccount="closeAccount"
      :mobile="mobile"
      :code="smsCode"
    ></changeAccountDialog>
  </div>
</template>

<script>
import mineApi from "@/api/mine";
import loginApi from "@/api/login";
import { Toast } from "mint-ui";
import dialog from "./dialog/publicDialog";
import textarea from "@/component/counter-textarea";
import { sha256 } from "js-sha256";
import changeAccountDialog from "./dialog/changeAccountDialog";
export default {
  name: "mineIndex",
  components: {
    "mt-dialog": dialog,
    "ty-textarea": textarea,
    changeAccountDialog
  },
  data() {
    return {
      info: {},
      isEnabledGetSms: true,
      codeCountdownStr: "获取验证码",
      mobile: "",
      smsCode: "",
      expireTime: 60,
      isEnabledSubmit: false, //登录按钮状态
      code: "",
      cancelAccountVisible: false,
      accountVisible: false
    };
  },
  created() {
    this.initInfo();
  },
  computed: {},
  methods: {
    goBack() {
      this.$router.push("/systemSetting");
    },
    initInfo() {
      mineApi.GetInfo().then((res) => {
        this.info = res.data;
        this.mobile = this.info.mobile;
      });
    },
    confirm() {
      this.cancelAccountVisible = true;
    },

    getUpdateSms() {
      if (!this.isEnabledGetSms) {
        return null;
      } else {
        loginApi
          .SendSMS({
            username: this.mobile
          })
          .then((res) => {
            if (res.status == 200) {
              this.smsCode = res.data.sms_code;
              this.expireTime = res.data.expire_time;
              this.reGetCountdown();
              this.isEnabledGetSms = false;
            } else if (res.error_code) {
              Toast(res.error_code);
            }
          });
      }
    },
    reGetCountdown() {
      if (this.expireTime >= 1) {
        this.expireTime--;
        this.codeCountdownStr = `${this.expireTime}s后重发`;
        setTimeout(this.reGetCountdown, 1000);
      } else {
        this.expireTime = 0;
        this.codeCountdownStr = "获取验证码";
        this.isEnabledGetSms = true;
      }
    },

    passCanSubmit() {
      if (sha256(`${this.code}_eStarGo2019`) == this.smsCode) {
        this.isEnabledSubmit = true;
      } else {
        this.isEnabledSubmit = false;
      }
    },
    accountConfirm() {
      console.log(this.isEnabledSubmit);
      if (!this.isEnabledSubmit) {
        return null;
      } else {
        this.cancelAccountVisible = false;
        this.accountVisible = true;
      }
    },
    closeAccount() {
      this.accountVisible = false;
    }
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    margin-top: 103px;
    margin-bottom: 41px;
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }

  .center-box {
    width: 100%;
    flex: 1;
    overflow: auto;

    .center-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .field-container {
      width: 1640px;
      min-height: 704px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 32px;
      margin-bottom: 32px;
      padding: 65px 79px 0;
      box-shadow: rgba(124, 143, 166, .1) 0 0 40px 0;
      h1 {
        font-size: 38px;
        color: #333333;
        letter-spacing: 0;
        line-height: 40px;
        margin-bottom: 35px;
      }
      p {
        font-size: 26px;
        color: #666666;
        letter-spacing: 0;
        text-align: justify;
        line-height: 38px;
        margin-bottom: 43px;
      }
      span {
        font-size: 26px;
        color: #ff6461;
        letter-spacing: 0;
        text-align: justify;
        line-height: 38px;
        margin-bottom: 43px;
        display: inline-block;
      }
    }
    .submit {
      width: 576px;
      height: 88px;
      border-radius: 43.2px;
      background: linear-gradient(
        to bottom,
        rgba(45, 213, 255),
        rgba(0, 204, 255)
      );
      box-shadow: rgba(0, 56, 79, .1) 0 3px 5px 0, 0px 5px 5px 0px #96e6ff inset, 0px -5px 5px 0px #00bdff inset;
      text-align: center;
      line-height: 88px;
      font-size: 38px;
      color: #ffffff;
      opacity: 0.8;
      margin-bottom: 40px;
    }
  }
}

.mobile-container {
  width: 100%;
  border-radius: 54px;
  height: 104px;
  box-sizing: border-box;
  padding: 0 48px;
  background-color: #f6f8fb;
  margin-bottom: 32px;

  input {
    border: none;
    font-size: 36px;
    line-height: 48px;
    outline: none;
    width: 100%;
    background-color: #f6f8fb;
    height: 104px;
  }
}
.mobile-icon {
  padding-left: 24px;
  width: 56px;
  height: 56px;
}
img {
  width: 56px;
  height: 56px;
}
.code-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .code-box {
    width: 552px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .clean-code-icon {
      width: 56px;
      height: 56px;
    }
  }
  button {
    width: 288px;
    border: none;
    font-size: 36px;
    color: #00bdff;
    box-sizing: border-box;
    line-height: 104px;
    margin-left: 24px;
  }
}
.footer-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    width: 312px;
    height: 112px;
    line-height: 112px;
    text-align: center;
    font-size: 40px;
    border-radius: 60px;
    font-weight: 500;
  }
  .footer-confirm-button {
    border: 6px solid #00bdff;
    color: #00bdff;
  }
  .footer-cancel-button {
    background: #08ccfd;
    color: #fff;
  }
}
::v-deep .dialog .dialog-container {
  width: 1080px!important;
  min-height: 648px;
  border-radius: 80px;
  box-sizing: border-box;
  padding: 72px 104px 80px;
  .dialog-title {
    margin-bottom: 55px;
    font-family: jcyt600w;
  }
  .confirm-button {
    width: 864px;
    height: 104px;
    line-height: 104px;
    font-family: jcyt500w;
    font-size: 38px;
  }
  input::-webkit-input-placeholder {
    color:#BFC1C5;
  }
}
</style>
