<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/questionBank/题库列表背景.png')})`,
      'background-size': '100% 100%'
    }"
  >
    <div class="main-box">
      <div class="header-box">
        <div
          class="back"
          @click="goBack"
          :style="{
            'background-image': `url(${require('@/assets/index/back.png')})`,
            'background-size': '100% 100%'
          }"
        ></div>
        <div class="title jcyt600">我的棋谱</div>
        <div class="select" :tabindex="1000" @blur="selecting = false">
          <div
            class="select-w flex-row"
            @click="selecting = !selecting"
            :class="['show-box', selecting ? 'selecting' : '']"
          >
            <!-- <div>{{ label }}</div>
            <div
              class="arrow-down"
              :style="{
                'background-image': `url(${require('@/assets/questionBank/Fill.png')})`,
                'background-size': '100% 100%'
              }"
            ></div> -->
              <div class="select-left">
                <div class="select_icon" :style="{
                  'background-image': `url(${require('@/assets/questionBank/Fill.png')})`,
                  'background-size': 'cover'
                }" ></div>
              </div>
              <span class="jcyt600 label">{{ label }}</span>
          </div>
          <ul v-show="selecting" class="dropdown-box">
            <li
              class="dropdown-item jcyt500"
              :class="{
                selectItem: selectNum == index
              }"
              v-for="(item, index) in matchTypeList"
              v-bind:key="index"
              @click="searchValueHandle(item, index)"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
      </div>
      <div class="center-box">
        <div class="center-container">
          <div class="chessboard-list-container">
            <div class="no-Message-container" v-if="sgfList.length == 0">
              <img class="no-Message" src="@/assets/nothing/暂无课程.png" />
              <p class="no-message-content jcyt500">暂无内容</p>
            </div>
            <div v-else class="chessboard-list-box">
              <div
                v-for="(item, index) in sgfList"
                v-bind:key="index"
                class="chessboard-container"
                @click="goDetail(item.game_id)"
              >
                <div class="left-box">
                  <p class="game-type jcyt500">
                    {{
                      getBussinessTypeLabel(item.business_type) +
                      "·" +
                      (item.board_size || 19) +
                      "路" +
                      (item.type === 'territory' ?  "围地" : "吃子")
                    }}
                  </p>
                  <p class="game-time jcyt400">
                    {{
                      getTime(item.started_at)
                    }}
                  </p>
                </div>
                <div class="match-result-container">
                  <div class="user-info">
                    <div class="user-avatar">
                      <img :src="item.user_avatar" />
                      <div
                        class="user-color current-user-color"
                        :style="{
                          'background-image':
                            'url(' +
                            (item.user_side == 1 ? require('../../assets/mine/black.png') :
                            require('../../assets/mine/white.png')) +
                            ')',
                          'background-repeat': 'no-repeat',
                          'background-size': 'cover'
                        }"
                      ></div>
                    </div>
                    <div class="user-name-container">
                      <div class="user-name jcyt500">
                        {{ shortToLongName(item.user_nick_name) }}
                      </div>
                    </div>
                  </div>
                  <div class="result-container" :class="item.business_type == '智能棋盘'? 'new-vs-wrap' : item.user_win == 4 ? 'waiver' : (item.user_win == 1 || item.user_win == 2 || item.user_win == 3) ? 'normal' : 'new-vs-wrap'">
                    <img v-if="item.business_type == '智能棋盘' || (item.user_win != 1 && item.user_win != 2 && item.user_win != 3 && item.user_win != 4)"
                      :src="require('../../assets/mine/new-vs.png')"
                      alt=""
                    />
                    <span v-else class="jcyt500">{{getLabel(item.user_win, item.win_result) }}</span>
                  </div>
                  <div class="user-info">
                    <div class="other-user-name-container">
                      <div class="user-name jcyt500">
                        {{ shortToLongName(item.opponent_user_nick_name) }}
                      </div>
                    </div>
                    <div class="user-avatar">
                      <img :src="item.opponent_user_avatar" />
                      <div
                        class="user-color other-user-color"
                        :style="{
                          'background-image':
                            'url(' +
                            (item.opponent_user_side== 1 ? require('../../assets/mine/black.png') :
                            require('../../assets/mine/white.png')) +
                            ')',
                          'background-repeat': 'no-repeat',
                          'background-size': 'cover'
                        }"
                      ></div>
                    </div>
                  </div>
                </div>
                <!-- <div
                  class="navigate-button"
                  @click="goDetail(item.game_id)"
                  :style="{
                    'background-image': `url(${require('@/assets/mine/我的棋谱按钮.png')})`,
                    'background-size': '100% 100%'
                  }"
                >
                  去复盘
                </div> -->
              </div>
            </div>
          </div>
          <div class="paging-container">
            <div class="page-button-container">
              <div
                class="page-button jcyt500"
                @click="switchPaging(1)"
                :style="{
                  'background-image': `url(${
                    page !== 1
                      ? require('@/assets/questionBank/paging_btn.png')
                      : require('@/assets/questionBank/paging_btn_dis.png')
                  })`,
                  'background-size': '100% 100%'
                }"
                :class="{
                  disabledButton: page == 1
                }"
              >
                首页
              </div>
              <div
                class="page-button jcyt500"
                @click="goToLast"
                :style="{
                  'background-image': `url(${
                    page !== 1
                      ? require('@/assets/questionBank/paging_btn.png')
                      : require('@/assets/questionBank/paging_btn_dis.png')
                  })`,
                  'background-size': '100% 100%'
                }"
                :class="{
                  disabledButton: page == 1
                }"
              >
                上一页
              </div>
            </div>
            <div class="count-container jcyt500">
              共<span>{{ page }}</span
              >/<span>{{ totalPage }}</span
              >页
            </div>
            <div class="page-button-container">
              <div
                class="page-button jcyt500"
                :style="{
                  'background-image': `url(${
                    page !== totalPage
                      ? require('@/assets/questionBank/paging_btn.png')
                      : require('@/assets/questionBank/paging_btn_dis.png')
                  })`,
                  'background-size': '100% 100%'
                }"
                :class="{
                  disabledButton: page == totalPage
                }"
                @click="nextPage"
              >
                下一页
              </div>
              <div
                class="page-button jcyt500"
                :style="{
                  'background-image': `url(${
                    page !== totalPage
                      ? require('@/assets/questionBank/paging_btn.png')
                      : require('@/assets/questionBank/paging_btn_dis.png')
                  })`,
                  'background-size': '100% 100%'
                }"
                :class="{
                  disabledButton: page == totalPage
                }"
                @click="switchPaging(totalPage)"
              >
                尾页
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mineApi from "@/api/mine";
import timeFormat from "@/public/timeFormat";
import { format_win_result } from "@/public/result";


export default {
  name: "mineIndex",
  components: {},
  data() {
    return {
      informDetail: "",
      apiDone: false,
      selecting: false,
      label: "全部对弈",
      selectNum: 0,
      page: 1,
      pageSize:10,
      dataId: "",
      sgfList: [],
      totalCount: 0,
      totalPage: 0,
      matchTypeList: [
        { name: "全部赛事", source: "all" },
        { name: "聂道赛事", source: "tournament" },
        { name: "AI对弈", source: "ai-tournament" },
        { name: "课后对弈", source: "ai-stage" },
        { name: "评测赛事", source: "evaluation" },
        { name: "好友约战", source: "room_solo" },
        { name: "排位赛", source: "season-rank" },
        { name: "定级赛", source: "rank_level" },
        { name: "智能棋盘", source: "smart_board" },
        { name: "单元测", source: "unit"}
      ],
    };
  },
  created() {
    // if (this.$storage.$getStroage("mySgfPage")) {

    this.init(this.$storage.$getStroage("mySgfPage") ?? 1, "");
    // }
  },
  computed: {},
  methods: {
    getLabel(win,winResult){
      return win == 3  ? "和棋" :win == 4 ? "弃权" : format_win_result(winResult);
    },
    getBussinessTypeLabel(source){
      var matchTypeList = [...this.matchTypeList, {name: "排位赛", source: "auto_pair"}];
      let item = matchTypeList.find(item => item.source == source);
      let name = typeof item == "undefined" ? "未知" : item.name;
      return name;
    },
    init(page, source) {
      let data = {
          page: this.page,
          page_size: this.pageSize,
      };
      if(source != 'all') {
        data = {...data, "business_type": source}
      }
      this.$store.commit("setApiLoading", true);
      mineApi.GetSgfList(data).then((res) => {
        this.apiDone = true;
        this.sgfList = res.data.results || [];
        this.totalCount = res.data.count;
        this.totalPage = Math.ceil(this.totalCount / 10);
        this.$store.commit("setApiLoading", false);
      });
    },
    getTime(time) {
      return time == 0 ? '' : timeFormat.GetCustTime(time * 1000, "YYYY-MM-DD HH:mm");
    },
    goBack() {
      this.$router.push("/mine");
    },
    shortToLongName(name) {
      if (name.length > 6) {
        return name.substring(0, 6) + "...";
      } else {
        return name;
      }
    },
    // goGame(item) {
    //   if (item.game_type == "聂道赛事") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "chessboard",
    //         from: "playing"
    //       }
    //     });
    //   } else if (item.game_type == "AI对弈" || item.game_type == "课后对弈") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "chessboard",
    //         from: "Ai"
    //       }
    //     });
    //   } else if (item.game_type == "排位赛") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "chessboard",
    //         from: "qualifying"
    //       }
    //     });
    //   } else if (item.game_type == "定级赛") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "chessboard",
    //         from: "Ai"
    //       }
    //     });
    //   } else if (item.game_type == "智能棋盘") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "chessboard",
    //         from: "smartBoard"
    //       }
    //     });
    //   } else if (item.game_type == "好友约战") {
    //     this.$router.push({
    //       path: "/AIgame",
    //       query: {
    //         game_id: item.game_id,
    //         from_url: "chessboard",
    //         from: "solo"
    //       }
    //     });
    //   }
    // },
    goDetail(id){
      __bl.sum("观战");
      let arr = [
        { name: "聂道赛事", source: "tournament" },
        { name: "AI对弈", source: "ai-tournament"},
        { name: "课后对弈", source: "ai-stage" },
        { name: "评测赛事", source: "evaluation"},
        { name: "好友约战", source: "room_solo" },
        { name: "排位赛", source: "season-rank" },
        { name: "定级赛", source: "rank_level" },
        { name: "智能棋盘", source: "smart_board"},
        { name: "单元测", source: "unit" },
        { name: "排位赛", source: "auto_pair"},
      ]
      console.log(id);
      this.$router.push({
        path: "/watchingGame",
        query: {
          game_id: id,
          from_url: "/chessboard"
        }
      })
    },
    searchValueHandle(item, index) {
      this.label = item.name;
      this.dataId = item.source;
      this.selectNum = index;
      this.selecting = false;
      this.page = 1;
      this.init(this.page, this.dataId);
    },
    switchPaging(val) {
      this.page = val;
      this.$storage.$setStroage("mySgfPage", val);
      this.init(this.page, this.dataId);
    },
    goToLast() {
      if (this.page > 1) {
        this.page--;
        this.switchPaging(this.page);
      }
    },
    nextPage() {
      if (this.page < this.totalPage) {
        this.page++;
        this.switchPaging(this.page);
      }
    },
  }
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    position: absolute;
    z-index: 100;
    top: 24px;
    left: 40px;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    font-size: 52px;
    line-height: 64px;
    color: #fff;
    width: 100%;
    text-align: center;
    padding-top: 80px;
    padding-bottom: 48px;
  }

  .center-box {
    width: 100%;
    flex: 1;
    display: flex;
    justify-content: center;
    .center-container {
      width: 1824px;
      height: 832px;
      display: flex;
      box-sizing: border-box;
      flex-direction: column;
      align-items: center;
      border-radius: 40px;
      background: #ffffff;
      box-shadow: 0 10px 20px 0px rgba(0, 0, 0, 0.1), 0 -8px 8px #E4F3FF inset;
      padding: 24px;
      overflow: hidden;
      position: relative;
      .no-Message-container {
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
      }
      .no-Message {
        width: auto;
        height: 442px;
        margin-top: 60px;
        margin-bottom: 31px;
        margin-left: 70px;
      }
      .no-message-content {
        font-size: 36px;
        color: #3A3B3B;
      }
      .chessboard-list-container {
        width: 100%;
        height: 700px;
        overflow: auto;
        .chessboard-list-box {
          width: 100%;
          height: auto;
          .chessboard-container {
            width: 100%;
            height: 168px;
            border-radius: 84px;
            background-color: #e3f5fd;
            margin-bottom: 24px;
            box-sizing: border-box;
            padding: 20px 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .left-box {
              width: 400px;
            }
            .game-type {
              font-size: 40px;
              line-height: 48px;
              color: #333;
            }
            .game-time {
              font-size: 28px;
              line-height: 32px;
              color: #6e95b5;
            }
            .match-result-container {
              width: 1184px;
              height: 100%;
              border-radius: 64px;
              background-color: #fff;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 16px 48px;
              box-sizing: border-box;
              .user-info {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                .user-avatar {
                  width: 96px;
                  height: 96px;
                  border-radius: 50%;
                  box-sizing: border-box;
                  position: relative;
                  img {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                  }
                  .user-color {
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    // box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.08);
                    position: absolute;
                    bottom: -5px;
                  }
                  .current-user-color {
                    right: -5px;
                  }
                  .other-user-color {
                    left: -5px;
                  }
                }
                .user-name-container {
                  width: 270px;
                  margin-left: 24px;
                  text-align: left;
                }
                .other-user-name-container {
                  width: 270px;
                  margin-right: 24px;
                  text-align: end;
                }
                .user-name {
                  font-size: 40px;
                  line-height: 48px;
                  color: #333;
                }
              }
              .result-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                img {
                  width: 64px;
                  height: 36px;
                }
              }
            }
            // .navigate-button {
            //   width: 232px;
            //   height: 88px;
            //   border-radius: 44px;
            //   line-height: 88px;
            //   font-size: 40px;
            //   color: #fff;
            //   text-align: center;
            // }
          }
        }
      }
      .chessboard-list-container::-webkit-scrollbar {
        width: 0px;
      }
      .paging-container {
        width: 100%;
        height: 120px;
        background-color: #ffe8c8;
        position: absolute;
        left: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 16px 80px 24px 80px;
        .page-button-container {
          width: 480px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .count-container {
          font-size: 32px;
          color: #b65600;
        }
        .page-button {
          width: 224px;
          height: 80px;
          border-radius: 40px;
          line-height: 76px;
          text-align: center;
          font-size: 32px;
          color: #b65600;
        }
        .disabledButton {
          color: rgba(182, 86, 0, 0.5);
        }
      }
    }
  }
  // .select-box {
  //   position: relative;
  // }
  // .select-box .show-box {
  //   width: 286px;
  //   height: 88px;
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  //   border-radius: 44px;
  //   background: #ffffff;
  //   padding: 20px 40px;
  //   box-sizing: border-box;
  //   cursor: pointer;
  //   font-size: 40px;
  //   line-height: 48px;
  //   color: #333;
  //   font-weight: 400;
  // }
  // .select-box .show-box.selecting {
  //   border: 1px solid #00d9ffff;
  // }
  // .select-box .show-box .arrow-down {
  //   width: 30px;
  //   height: 32px;
  // }
  .select {
    position: absolute;
    height: 80px;
    width: 280px;
    margin-left: 40px;
    right: 48px;
    top: 56px;
    background-image: linear-gradient(to bottom, #FFFFFF, #DAF4FF);
    // background: #ffffff;
    border-radius: 44px;
    box-shadow: #8BB6E1 0 -3px 3px 0 inset, 0 4px 8px 0 rgba(0, 33, 135, .5);
    box-sizing: border-box;
    text-align: center;
    font-size: 36px;
    cursor: pointer;
    display: flex;
    .select-w {
      width: 100%;
    }
    .label {
      margin-left: 22px;
      line-height: 80px;
      color: #294584;
      padding-right: 30px;
      width: 144px;
      text-align: center;
    }
    .select_icon {
      width: 49px;
      height: 52px;
      margin-top: 18px;
      margin-left: 28px;
    }
    .select-left {
      width: 84px;
      height: 80px;
      border-radius: 44px 0 0 44px;
      background: #3168E7;
      position: relative;
      box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      &::after {
        content:"";
        width: 0; height: 0;
        border-color: transparent #3168E7; /*上下颜色 左右颜色*/
        border-width: 0 0 80px 20px;
        border-style: solid;
        position: absolute;
        top: 0;
        left: 84px;
        box-shadow: rgba(226, 247, 255, .2) 3px 3px 3px inset, rgba(38, 91, 214, .35) 3px -3px 3px 0 inset;
      }
    }

  }
  .dropdown-box {
    list-style: none;
    min-width: 320px;
    padding: 32px 40px;
    background: #ffffff;
    box-shadow: rgba(18, 103, 180, .5) 0 6px 29px 0, rgba(154, 221, 255, .75) 0 -5px 8px 0 inset;
    border-radius: 40px;
    margin-top: 16px;
    position: absolute;
    top: 88px;
    right: 0;
    z-index: 19;
    box-sizing: border-box;
    overflow: hidden;
  }
  .dropdown-box .dropdown-item {
    text-align: center;
    height: 80px;
    line-height: 80px;
    font-size: 32px;
    cursor: pointer;
    color: #666666;
  }
  .dropdown-box .dropdown-item:hover {
    background: rgba(0, 150, 255, 0.1);
    border-radius: 41.9px;
    color: #0096FF;
    letter-spacing: 0;
    text-align: center;
  }
  .selectItem {
    background: rgba(0, 150, 255, 0.1);
    border-radius: 41.9px;
    color: #0096FF!important;
    letter-spacing: 0;
    text-align: center;
  }
}

.fading-circle {
  color: rgba(100, 49, 191, 255);
}
.new-vs-wrap {
  background: linear-gradient(to bottom,#FFFFFE,#FFFBEC);
  height: 88px;
  width: 286px;
  border-radius: 44px;
  border: 4px solid #FAE652;
  box-sizing: border-box;
}

.waiver {
  height: 88px;
  width: 286px;
  background: #EBF0F5;
  border-radius: 44px;
  span {
    color: #536786;
    font-size: 40px;
    text-align: center;
  }
}

.normal {
  height: 88px;
  width: 286px;
  background: #FCF3E4;
  border-radius: 44px;
  span {
    color: #A85D1B;
    font-size: 40px;
    text-align: center;
  }
}
</style>
