<template>
  <div
    class="container"
    :style="{
      'background-image': `url(${require('@/assets/mine/information_bg.png')})`,
      'background-size': '100% 100%',
    }"
  >
    <div
      class="back"
      @click="goBack"
      :style="{
        'background-image': `url(${require('@/assets/index/back.png')})`,
        'background-size': '100% 100%',
      }"
    ></div>
    <div class="main-box">
      <div class="title jcyt500">我的订单</div>
      <div class="center-box">
        <div class="center-container">
          <div class="no-Message-container" v-if="orderList.length == 0">
            <img class="no-Message" src="@/assets/nothing/暂无课程.png" />
            <p class="no-message-content jcyt500">暂无订单</p>
          </div>
          <div v-else>
            <div
              v-for="(item, index) in orderList"
              v-bind:key="index"
              class="message-container"
              @click="goDetail(item.id.toString())"
            >
              <div class="order-number-container">
                <div class="order-number jcyt400">
                  订单号：<span>{{ item.id }}</span>
                </div>
                <div
                  class="is-pay jcyt400"
                  :style="{
                    color:
                      item.order_status == 'is_pay' ||
                      item.order_status == 'is_free'
                        ? '#00BDFF'
                        : '#C3C7CD',
                  }"
                >
                  {{  item.order_status == 'is_pay'
                      ? '已支付'
                      : item.order_status == 'is_close'
                          ? '已关闭'
                          : item.order_status == 'is_free'
                              ? '已领取'
                              : item.order_status == 'is_refund'
                                  ? '已退款'
                                  : item.order_status == 'is_appointment'
                                      ? '已预约'
                                      : item.order_status ==
                                              'close_appointment'
                                          ? '已取消'
                                          : item.order_status ==
                                                  'confirm_appointment'
                                              ? '已确认'
                                              : ''}}
                </div>
              </div>
              <div class="course-container jcyt500">{{ item?.merchandise?.title }}</div>
              <div class="order-date-container">
                <div class="order-date jcyt400">
                  {{
                    item?.created_at.substring(0, 10) +
                    " " +
                    item?.created_at.substring(11, 16)
                  }}
                </div>
                <div class="order-price jcyt500">
                  {{ "¥" + Math.trunc(item.price / 100).toString() }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
import mineApi from "@/api/mine";
// import informApi from "@/api/inform";
import { Indicator } from "mint-ui";
export default {
  name: "mineIndex",
  components: {},
  data() {
    return {
      orderList: [],
      apiDone: false,
      totalCount: 0,
    };
  },
  created() {
    this.initInfo();
    // Indicator.open({
    //   text: "加载中...",
    //   //文字
    //   spinnerType: "fading-circle",
    //   //样式
    // });
  },
  destroyed() {
    // Indicator.close();
  },
  computed: {},
  methods: {
    initInfo() {
      this.$store.commit("setApiLoading", true);
      mineApi.GetOrderList({ page: 1, limit: 20 }).then((res) => {
        this.apiDone = true;
        Indicator.close();
        this.orderList = res.data.order_list;
        this.$store.commit("setApiLoading", false);
        // this.totalCount = res.data.total_number;
        //   if (this.totalCount % 10 == 0) {
        //     this.totalPage = this.totalCount ~/ 10;
        //   } else {
        //     this.totalPage = this.totalCount ~/ 10 + 1;
        //   }
        // this.totalPage = Math.ceil(this.totalCount / 10);
      });
    },
    goDetail(id) {
      console.log(id, "order");
      this.$router.push(`/orderDetail?order_id=${id}`);
    },
    goBack() {
      this.$router.push("/mine");
    },
  },
};
</script>

<style scoped lang="less">
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  .back {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    left: 40px;
    border-radius: 50%;
    z-index: 9;
    // overflow: auto;
  }
}
.main-box {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //   margin-top: 376px;
  // margin: 0 176px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    margin-top: 103px;
    margin-bottom: 41px;
    font-size: 52px;
    line-height: 64px;
    color: #333333;
  }

  .center-box {
    width: 100%;
    flex: 1;
    overflow: auto;

    .center-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .message-container {
        width: 1824px;
        height: 368px;
        background-color: #ffffff;
        border-radius: 40px;
        box-shadow: 0 0px 50px 0px rgba(124, 143, 166, 0.1);
        margin-bottom: 40px;
        padding: 60px 40px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;
        .order-number-container {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 36px;
          line-height: 48px;
          .order-number {
            color: #999999;
          }
        }
        .course-container {
          font-size: 48px;
          line-height: 56px;
          color: #333;
          width: 100%;
          display: flex;
          align-items: flex-end;
          margin-top: 32px;
        }
        .order-date-container {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          width: 100%;
          margin-top: 54px;
          .order-date {
            font-size: 36px;
            color: #999999;
            line-height: 48px;
          }
          .order-price {
            font-size: 56px;
            color: #333;
            line-height: 64px;
          }
        }
      }
    }
    .no-Message-container {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
    }
    .no-Message {
      width: auto;
      height: 442px;
      margin-top: 60px;
      margin-bottom: 31px;
      margin-left: 70px;
    }
    .no-message-content {
      font-size: 36px;
      color: #3A3B3B;
    }
  }
  .center-box::-webkit-scrollbar {
    width: 0px;
  }
}
.fading-circle {
  color: rgba(100, 49, 191, 255);
}
</style>
