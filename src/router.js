import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);
const router = new Router({
  mode: "hash",
  routes: [
    {
      path: "/",
      name: "index",
      component: () => import("@/pages/index/index"),
      meta: { keepAlive: true }
    },
    {
      path: "/AIgame",
      name: "AIgame",
      component: () => import("@/pages/course/AIgame"),
      meta: { keepAlive: false }
    },
    {
      path: "/lessonInfo",
      name: "lessonInfo",
      component: () => import("@/pages/course/lessonInfo"),
      meta: { keepAlive: false }
    },
    {
      path: "/lessonList",
      name: "lessonList",
      component: () => import("@/pages/course/lessonList"),
      meta: { keepAlive: false }
    },
    {
      path: "/card",
      name: "card",
      component: () => import("@/pages/course/card"),
      meta: { keepAlive: false }
    },
    {
      path: "/homeworkFinish",
      name: "homeworkFinish",
      component: () => import("@/pages/course/homeworkFinish"),
      meta: { keepAlive: false }
    },
    {
      path: "/exerciseDetail",
      name: "exerciseDetail",
      component: () => import("@/pages/course/exerciseDetail"),
      meta: { keepAlive: false }
    },
    {
      path: "/video",
      name: "video",
      component: () => import("@/pages/course/video/video"),
      meta: { keepAlive: false }
    },
    {
      path: "/smsLogin",
      name: "smsLogin",
      component: () => import("@/pages/login/smsLogin"),
      meta: { keepAlive: false }
    },
    {
      path: "/mine",
      name: "mine",
      component: () => import("@/pages/mine/mineIndex"),
      meta: { keepAlive: false }
    },
    {
      path: "/information",
      name: "information",
      component: () => import("@/pages/mine/information"),
      meta: { keepAlive: false }
    },
    {
      path: "/systemSetting",
      name: "systemSetting",
      component: () => import("@/pages/mine/systemSettings"),
      meta: { keepAlive: false }
    },
    {
      path: "/message",
      name: "message",
      component: () => import("@/pages/mine/message"),
      meta: { keepAlive: false }
    },
    {
      path: "/informDetail",
      name: "informDetail",
      component: () => import("@/pages/mine/informDetail"),
      meta: { keepAlive: false }
    },
    {
      path: "/chessboard",
      name: "chessboard",
      component: () => import("@/pages/mine/chessboard"),
      meta: { keepAlive: false }
    },
    {
      path: "/mineOrder",
      name: "mineOrder",
      component: () => import("@/pages/mine/mineOrder"),
      meta: { keepAlive: false }
    },
    {
      path: "/questionBank",
      name: "questionBank",
      component: () => import("@/pages/questionBank/index"),
      meta: { keepAlive: false }
    },
    {
      path: "/questionList",
      name: "questionList",
      component: () => import("@/pages/questionBank/questionList"),
      meta: { keepAlive: false }
    },
    {
      path: "/myCollect",
      name: "myCollect",
      component: () => import("@/pages/questionBank/myCollect"),
      meta: { keepAlive: false }
    },
    {
      path: "/unitTest",
      name: "unitTest",
      component: () => import("@/pages/unitTest/unitTest"),
      meta: { keepAlive: false }
    },
    {
      path: "/unitTestReport",
      name: "unitTestReport",
      component: () => import("@/pages/unitTest/unitTestReport"),
      meta: { keepAlive: false }
    },
    {
      path: "/aiSelect",
      name: "aiSelect",
      component: () => import("@/pages/aiSelect/aiSelect"),
      meta: { keepAlive: false }
    },
    {
      path: "/unitTestPlay",
      name: "unitTestPlay",
      component: () => import("@/pages/unitTest/unitTestPlay"),
      meta: { keepAlive: false }
    },
    {
      path: "/unitTest/game",
      name: "unitTestGame",
      component: () => import("@/pages/unitTest/game"),
      meta: { keepAlive: false }
    },
    {
      path: "/privateAgreement",
      name: "privateAgreement",
      component: () => import("@/pages/login/privateAgreement"),
      meta: { keepAlive: false }
    },
    {
      path: "/questionPlay",
      name: "questionPlay",
      component: () => import("@/pages/questionBank/questionPlay"),
      meta: { keepAlive: false }
    },
    {
      path: "/userAgreement",
      name: "userAgreement",
      component: () => import("@/pages/login/userAgreement"),
      meta: { keepAlive: false }
    },
    {
      path: "/orderDetail",
      name: "orderDetail",
      component: () => import("@/pages/merchandise/orderDetail"),
      meta: { keepAlive: false }
    },
    {
      path: "/shoppingAddress",
      name: "shoppingAddress",
      component: () => import("@/pages/merchandise/shoppingAddress"),
      meta: { keepAlive: false }
    },
    {
      path: "/watchingHall",
      name: "watchingHall",
      component: () => import("@/pages/match/watchingHall"),
      meta: { keepAlive: false }
    },
    {
      path: "/seasonIndex",
      name: "seasonIndex",
      component: () => import("@/pages/season/index"),
      meta: { keepAlive: false }
    },
    {
      path: "/seasonRule",
      name: "seasonRule",
      component: () => import("@/pages/season/rule"),
      meta: { keepAlive: false }
    },
    {
      path: "/studyReportDetail",
      name: "studyReportDetail",
      component: () => import("@/pages/studyReport/studyReportDetail"),
      meta: { keepAlive: false }
    },
    {
      path: "/studyList",
      name: "studyList",
      component: () => import("@/pages/studyList/studyList"),
      meta: { keepAlive: false }
    },
    {
      path: "/matching",
      name: "matching",
      component: () => import("@/pages/matching/matching"),
      meta: { keepAlive: false }
    },
    {
      path: "/nwpMatch",
      name: "nwpMatch",
      component: () => import("@/pages/nwpMatch/nwpMatch"),
      meta: { keepAlive: false }
    },
    {
      path: "/season/game",
      name: "seasonGame",
      component: () => import("@/pages/matching/game"),
      meta: { keepAlive: false }
    },
    {
      path: "/tournament/game",
      name: "seasonGame",
      component: () => import("@/pages/nwpMatch/game"),
      meta: { keepAlive: false }
    },
    {
      path: "/nwpMatchList",
      name: "nwpMatchList",
      component: () => import("@/pages/nwpMatch/nwpMatchList"),
      meta: { keepAlive: false }
    },
    {
      path: "/howToLearn",
      name: "howToLearn",
      component: () => import("@/pages/index/information/howToLearn"),
      meta: { keepAlive: false }
    },
    {
      path: "/knowNie",
      name: "knowNie",
      component: () => import("@/pages/index/information/knowNie"),
      meta: { keepAlive: false }
    },
    {
      path: "/merchandiseDetail",
      name: "merchandiseDetail",
      component: () => import("@/pages/index/merchandiseDetail"),
      meta: { keepAlive: false }
    },
    {
      path: "/rule",
      name: "rule",
      component: () => import("@/pages/soloFriends/rule"),
      meta: { keepAlive: false }
    },
    {
      path: "/rival",
      name: "rival",
      component: () => import("@/pages/soloFriends/rival"),
      meta: { keepAlive: false }
    },
    {
      path: "/join",
      name: "join",
      component: () => import("@/pages/soloFriends/join"),
      meta: { keepAlive: false }
    },
    {
      path: "/rankingList",
      name: "rankingList",
      component: () => import("@/pages/season/rankingList/rankingList"),
      meta: { keepAlive: false }
    },
    {
      path: "/livingCourseTimetable",
      name: "livingCourseTimetable",
      component: () => import("@/pages/mine/livingCourseTimetable"),
      meta: { keepAlive: false }
    },
    {
      path: "/livingCourseReplay",
      name: "livingCourseReplay",
      component: () => import("@/pages/mine/livingCourseReplay"),
      meta: { keepAlive: false }
    },
    {
      path: "/cancelAccount",
      name: "cancelAccount",
      component: () => import("@/pages/mine/cancelAccount"),
      meta: { keepAlive: false }
    },
    {
      path: "/delNumberDown",
      name: "delNumberDown",
      component: () => import("@/pages/mine/delNumberDown"),
      meta: { keepAlive: false }
    },
    {
      path: "/evaluationIndex",
      name: "evaluationIndex",
      component: () => import("@/pages/evaluation/index"),
      meta: { keepAlive: false }
    },
    {
      path: "/evaluationQuestionPlay",
      name: "evaluationQuestionPlay",
      component: () => import("@/pages/evaluation/evaluationQuestionPlay"),
      meta: { keepAlive: false }
    },
    {
      path: "/evaluationStart",
      name: "evaluationStart",
      component: () => import("@/pages/evaluation/start"),
      meta: { keepAlive: false }
    },
    {
      path: "/evaluationReport",
      name: "evaluationReport",
      component: () => import("@/pages/evaluation/report"),
      meta: { keepAlive: false }
    },
    {
      path: "/evaluationGame",
      name: "evaluationGame",
      component: () => import("@/pages/evaluation/game"),
      meta: { keepAlive: false }
    },
    {
      path: "/soloGames",
      name: "soloGames",
      component: () => import("@/pages/soloFriends/game"),
      meta: { keepAlive: false }
    },
    {
      path: "/watchingGame",
      name: "watchingGame",
      component: () => import("@/pages/match/game"),
      meta: { keepAlive: false }
    },
    {
      path: "/netCheck",
      name: "netCheck",
      component: () => import("@/pages/mine/netCheck"),
      meta: { keepAlive: false }
    },
    // {
    //   path: "/courseIndex",
    //   name: "courseIndex",
    //   component: () => import("@/pages/index/courseIndex")
    // },
    // {
    //   path: "/studyRoom",
    //   name: "studyRoom",
    //   component: () => import("@/pages/index/studyRoomIndex")
    // },
    // {
    //   path: "/learnHall",
    //   name: "learnHall",
    //   component: () => import("@/pages/index/learnHallIndex")
    // },
    // {
    //   path: "/pk",
    //   name: "pk",
    //   component: () => import("@/pages/index/pkIndex")
    // },
    {
      path: "/living",
      name: "living",
      component: () => import("@/pages/living/index")
    },
    {
      path: "/playbackPage",
      name: "playbackPage",
      component: () => import("@/pages/playbackPage/index")
    },
    {
      path: "/inspection",
      name: "inspection",
      component: () => import("@/pages/living/inspection")
    },
    {
      path: "/capturescreen",
      name: "capturescreen",
      component: () => import("@/pages/living/capturescreen")
    },
    {
      path: "/classReports",
      name: "classReports",
      component: () => import("@/pages/playbackPage/classReports")
    },
    {
      path:"/appraise",
      name:"appraise",
      component: () => import("@/pages/playbackPage/appraise")
    }
  ]
});

export default router;
