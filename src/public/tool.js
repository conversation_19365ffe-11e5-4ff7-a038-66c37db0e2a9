import "./wgo/wgo.min";
import "./wgo/wgo.player.min";
function sleep(time) {
  return new Promise((resolve) => setTimeout(resolve, time));
}
function convert_sgf_to_xy(sgf) {
  let xyInt = [
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
  ];
  let turn = sgf.split(";")[1].split("[")[0] === "W" ? -1 : 1;
  let xyStr = sgf.split(";")[1].split("[")[1].split("]")[0];
  let x = xyInt.indexOf(xyStr[0]);
  let y = xyInt.indexOf(xyStr[1]);
  return { turn: turn, x: x, y: y };
}
function convert_xy_to_sgf(x, y, turn) {
  let cow = "abcdef<PERSON><PERSON>jklmnopqrst";
  if (turn === 1) {
    return `;B[${cow[x]}${cow[y]}]`;
  } else {
    return `;W[${cow[x]}${cow[y]}]`;
  }
}
function move_to_last(player) {
  // 将前端棋盘移动到最后一步
  let p = WGo.clone(player.kifuReader.path);
  p.m += 1000;
  player.goTo(p);
}
function move_to_first(player) {
  let p = WGo.clone(player.kifuReader.path);
  p.m -= 1000;
  player.goTo(p);
}
function to_obtain_coordinate(event, player) {
  // 捕捉落子的x y 坐标
  let board = player.board;
  let x, y;
  x = event.offsetX * board.pixelRatio;
  y = event.offsetY * board.pixelRatio;
  x -= board.left;
  x /= board.fieldWidth;
  x = Math.round(x);
  y -= board.top;
  y /= board.fieldHeight;
  y = Math.round(y);
  return {
    x: x >= board.size ? -1 : x,
    y: y >= board.size ? -1 : y,
  };
}
function mouse_move(event, player, last_mark) {
  // 鼠标在棋盘范围内移动
  if (last_mark !== "") {
    player.board.removeObject(last_mark);
  }
  // 只有当前是人类落子时 才添加半透明棋子
  let coordinates = to_obtain_coordinate(event, player);
  let x = coordinates.x;
  let y = coordinates.y;
  if (player.kifuReader.game.isValid(x, y)) {
    last_mark = {
      type: "outline",
      x: x,
      y: y,
      c: player.kifuReader.game.turn,
    };
    player.board.addObject(last_mark);
    return last_mark;
  } else {
    return "";
  }
}
function ownerShip(ownership, player) {
  for (let i = 0; i < ownership.length; i++) {
    let c;
    if (ownership[i].size >= 0.5 && ownership[i].size <= 1) {
      c = "black";
    } else if (ownership[i].size <= -0.5 && ownership[i].size >= -1) {
      c = "white";
    }
    let sq = {
      x: ownership[i].x,
      y: ownership[i].y,
      type: "SQ",
      c: c,
      size: Math.abs(ownership[i].size),
    };
    for (let index in player.board.obj_arr[ownership[i].x][ownership[i].y]) {
      if (
        player.board.obj_arr[ownership[i].x][ownership[i].y][index].c === 1 &&
        c === "white"
      ) {
        player.board.addObject(sq);
        continue;
      }
      if (
        player.board.obj_arr[ownership[i].x][ownership[i].y][index].c === -1 &&
        c === "black"
      ) {
        player.board.addObject(sq);
      }
    }
  }
}

function removeOwnerShip(ownership, player) {
  for (let i = 0; i < ownership.length; i++) {
    let c;
    if (ownership[i].size >= 0.5 && ownership[i].size <= 1) {
      c = "black";
    } else if (ownership[i].size <= -0.5 && ownership[i].size >= -1) {
      c = "white";
    }
    let sq = {
      x: ownership[i].x,
      y: ownership[i].y,
      type: "SQ",
      c: c,
      size: Math.abs(ownership[i].size),
    };
    for (let index in player.board.obj_arr[ownership[i].x][ownership[i].y]) {
      if (
          player.board.obj_arr[ownership[i].x][ownership[i].y][index].c === 1 &&
          c === "white"
      ) {
        player.board.removeObject(sq);
        continue;
      }
      if (
          player.board.obj_arr[ownership[i].x][ownership[i].y][index].c === -1 &&
          c === "black"
      ) {
        player.board.removeObject(sq);
      }
    }
  }
}
function mouse_out(player, last_mark) {
  // 鼠标移出棋盘
  if (last_mark !== "") {
    player.board.removeObject(last_mark);
  }
}
function modify_new_image_data(new_image_data, old_image_data) {
  for (let i = 0; i < old_image_data.data.length; i += 4) {
    if (
      old_image_data.data[i] !== 0 &&
      old_image_data.data[i + 1] !== 0 &&
      old_image_data.data[i + 2] !== 0 &&
      old_image_data.data[i + 3] !== 0
    ) {
      new_image_data.data[i] = old_image_data.data[i];
      new_image_data.data[i + 1] = old_image_data.data[i + 1];
      new_image_data.data[i + 2] = old_image_data.data[i + 2];
      new_image_data.data[i + 3] = old_image_data.data[i + 3];
    }
  }
}
function genPic() {
  let wgo_canvas = document.getElementsByClassName("wgo-board");
  let old_canvas = wgo_canvas[0].children;
  let canvas = document.createElement("canvas");
  let ctx = canvas.getContext("2d");
  canvas.width = wgo_canvas[0].clientWidth;
  canvas.height = wgo_canvas[0].clientHeight;
  let newImagesData = ctx.createImageData(canvas.width, canvas.height);
  modify_new_image_data(
    newImagesData,
    old_canvas[0]
      .getContext("2d")
      .getImageData(0, 0, canvas.width, canvas.height)
  );
  modify_new_image_data(
    newImagesData,
    old_canvas[3]
      .getContext("2d")
      .getImageData(0, 0, canvas.width, canvas.height)
  );
  modify_new_image_data(
    newImagesData,
    old_canvas[4]
      .getContext("2d")
      .getImageData(0, 0, canvas.width, canvas.height)
  );
  modify_new_image_data(
    newImagesData,
    old_canvas[5]
      .getContext("2d")
      .getImageData(0, 0, canvas.width, canvas.height)
  );
  modify_new_image_data(
    newImagesData,
    old_canvas[6]
      .getContext("2d")
      .getImageData(0, 0, canvas.width, canvas.height)
  );
  ctx.putImageData(newImagesData, 0, 0);
  return canvas.toDataURL("image/png");
}

function random_color() {
  return "rgba(0,0,0,0.8)";
}

function get_abc(index) {
  let cow = "ABCDEFGHIJKLMNOPQRST";
  return cow[index];
}
export default {
  Sleep(time) {
    return sleep(time);
  },
  ConvertSgfToXY(sgf) {
    return convert_sgf_to_xy(sgf);
  },
  ConvertXYtoSgf(x, y, turn) {
    return convert_xy_to_sgf(x, y, turn);
  },
  MoveToLast(player) {
    return move_to_last(player);
  },
  MoveToFirst(player) {
    return move_to_first(player);
  },
  ToObtainCoordinate(event, player) {
    return to_obtain_coordinate(event, player);
  },
  MouseMove(event, player, last_mark) {
    return mouse_move(event, player, last_mark);
  },
  MouseOut(player, last_mark) {
    return mouse_out(player, last_mark);
  },
  GenPic() {
    return genPic();
  },
  GetAbc(index) {
    return get_abc(index);
  },
  RandomColor() {
    return random_color();
  },
  OwnerShip(ownership, player) {
    return ownerShip(ownership, player);
  },
  RemoveOwnerShip(ownership,player) {
    return removeOwnerShip(ownership,player);
  }
};
