import { Rive, Layout, Fit, Alignment ,RuntimeLoader} from '@rive-app/canvas';
// import riveWASMResource from '@rive-app/canvas/rive.wasm';

RuntimeLoader.setWasmUrl("https://higo-cdn.oss-cn-beijing.aliyuncs.com/rive.wasm");
function setWH(id){
  let c = document.getElementById(id);
  let scaleValue = 3;
  c.width = c.style.width * scaleValue;
  c.height = c.style.height * scaleValue;
}

function createRive(id, src){
  return new Rive({
    canvas: document.getElementById(id),
    src: `/rive/${src}`,
    autoplay: true,
    layout: new Layout({
        fit: Fit.Fill,
        alignment: Alignment.TopCenter,
    }),
  })
}

export {
  setWH,
  createRive,
}