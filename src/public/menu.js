// ElectronMenu.js
import { app, Menu } from 'electron'
// 设置菜单栏, win表示当前窗口实例
export function createMenu (win) {
  // darwin表示macOS，这里我们选择对macOS系统的创建应用内菜单
  if (process.platform === 'win32') {
    const template = [{label: '视图', submenu: [
      { label: '刷新', accelerator: 'F5', role: 'reload' },
      { label: '退出', accelerator: 'Ctrl+Q', click: () => {win.destroy();} }]}];
    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }
}
