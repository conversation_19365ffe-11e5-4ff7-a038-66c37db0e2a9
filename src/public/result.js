export function format_win_result(win_result) {
  // B+R 黑胜白认输 B+T 黑胜白超时 W+R 白胜黑认输 W+T 白胜黑超时 B+C1 黑胜吃1子 W+C1 白胜吃一子 B+L 黑胜白未参加 W+L  白胜黑未参加 B+A 黑胜仲裁 W+A 白胜仲裁 B+2.5 黑胜2.5目 W+2.5 白胜2.5目
  let re = /\+([0-9]+\.[0-9]+|[0-9]+)/;
  let result = "";
  // check win_result has B or W B is黑胜 W is 白胜
  if (win_result.indexOf("B") !== -1) {
    result = "黑胜";
  } else if (win_result.indexOf("W") !== -1) {
    result = "白胜";
  }
  // check win_result has R or T R is 认输 T is 超时
  if (win_result.indexOf("R") !== -1) {
    result += "认输";
  } else if (win_result.indexOf("T") !== -1) {
    result += "超时";
  } else if (win_result.indexOf("C") !== -1) {
    result += "吃" + win_result.split("C")[1] + "子";
  } else if (win_result.indexOf("L") !== -1) {
    result += "未参加";
  } else if (win_result.indexOf("Abstain") != -1) {
    result = "弃权";
  } else if (win_result.indexOf("+A") !== -1) {
    result += "仲裁";
  } else if (re.test(win_result)) {
    result += win_result.split("+")[1] + "目";
  } else if (win_result.indexOf("Draw") != -1) {
    result = "和";
  } else if (win_result.indexOf("O") != -1) {
    result += "";
  } else {
    result += win_result.split("+")[1];
  }
  return result;
}
