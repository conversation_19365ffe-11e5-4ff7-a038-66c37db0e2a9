//calcWinAreaScore 计算结果
function calcWinAreaScore(isBlack, areaScore, boardSize) {
    areaScore = Math.abs(areaScore);
    let result = boardSize == 19
        ? 180.5 + areaScore / 2
        : boardSize == 9
            ? 40.5 + areaScore / 2
            : boardSize == 13
                ? 84.5 + areaScore / 2
                : 0.0;
    if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
    } else {
        // 白方向下取整
        return Math.trunc(result);
    }
}

//calcLoseAreaScore 计算结果
function calcLoseAreaScore(isBlack, areaScore, boardSize) {
    areaScore = Math.abs(areaScore);
    let result = boardSize == 19
        ? 361 - (180.5 + areaScore / 2)
        : boardSize == 9
            ? 81 - (40.5 + areaScore / 2)
            : boardSize == 13
                ? 169 - (84.5 + areaScore / 2)
                : 0.0;
    if (isBlack) {
        // 黑方向上取整
        return Math.ceil(result);
    } else {
        // 白方向下取整
        return Math.trunc(result);
    }
}
export default {
    CalcWinAreaScore(isBlack, areaScore, boardSize) {
        return calcWinAreaScore(isBlack, areaScore, boardSize);
    },
    CalcLoseAreaScore(isBlack, areaScore, boardSize) {
        return calcLoseAreaScore(isBlack, areaScore, boardSize);
    },
};