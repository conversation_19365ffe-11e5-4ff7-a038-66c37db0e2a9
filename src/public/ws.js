import store from "../store/index";
import {Toast} from "mint-ui";
import WebsocketHeartbeatJs from 'websocket-heartbeat-js';

class myWebsocket extends WebsocketHeartbeatJs {
    lastPingTime = 0;
    pingDelay = 0;

    ondelay(delay) {
    };

    heartReset() {
        if (this.lastPingTime !== 0) {
            this.pingDelay = Date.now() - this.lastPingTime;
            this.ondelay(this.pingDelay)
        }
        super.heartReset();

    }

    heartStart() {
        if (this.forbidReconnect) return;//不再重连就不再执行心跳
        this.pingTimeoutId = setTimeout(() => {
            //这里发送一个心跳，后端收到后，返回一个心跳消息，
            //onmessage拿到返回的心跳就说明连接正常
            this.lastPingTime = Date.now();
            this.ws.send("ping");
            //如果超过一定时间还没重置，说明后端主动断开了
            this.pongTimeoutId = setTimeout(() => {
                //如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
                this.ws.close();
            }, this.opts.pongTimeout);
        }, this.opts.pingTimeout);
    }

    heartCheck(event) {
        if (event === "pong") {
            super.heartCheck();
        }
    }

    initEventHandle() {
        this.ws.onclose = (e) => {
            this.onclose(e);
            this.lastPingTime = 0;
            this.reconnect();
        };
        this.ws.onerror = (e) => {
            this.onerror(e);
            this.lastPingTime = 0;
            this.reconnect();
        };
        this.ws.onopen = (e) => {
            this.lastPingTime = Date.now();
            this.repeat = 0;
            this.onopen(e);
            //心跳检测重置
            this.heartCheck("pong");
        };
        this.ws.onmessage = (event) => {
            this.onmessage(event);
            //如果获取到消息，心跳检测重置
            //拿到任何消息都说明当前连接是正常的
            this.heartCheck(event.data);
        };
    }
}

const Socket = {
    toastObj: null,
    lastPingTime: 0,
    init: function (wsUrl) {
        this.conn = new myWebsocket({
            url: wsUrl,
            pingTimeout: 5000,
            pongTimeout: 10000,
        });
        this.conn.onopen = () => {
            if (this.toastObj) {
                this.toastObj.close();
                this.toastObj = null;
            }
            store.dispatch("wsReadyStats", 1);
        }
        this.conn.ondelay = (delay) => {
            console.log(delay)
            store.dispatch("network",delay);
        }
        this.conn.onreconnect = () => {
            if (this.toastObj == null) {
                this.toastObj = Toast({
                    message: "断线重连中...",
                    duration: -1
                });
            }
            store.dispatch("network",999);
            store.dispatch("wsReadyStats", 0);
        }
        this.conn.onerror = () => {
            store.dispatch("wsReadyStats", 0);
        }
        this.conn.onmessage = (evt) => {
            this.message(evt);
        }
        this.conn.onclose = () => {
            store.dispatch("wsReadyStats", 0);
        }
        return this.conn;
    },
    message(evt) {
        if (evt.data !== "Ping" && evt.data !== "Pong" && evt.data !== "pong" && evt.data !== "ping") {
            store.dispatch("receive", evt.data);
        }
    },
    send(message) {
        message = JSON.stringify(message);
        this.conn.send(message);
    },
    close() {
        if (this.conn) {
            this.conn.close();
        }
    },
};

export default {
    Socket,
};
