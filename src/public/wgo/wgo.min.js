/* eslint-disable */
/*
 Copyright (c) 2013 Jan Prokop

 Permission is hereby granted, free of charge, to any person obtaining a copy of this
 software and associated documentation files (the "Software"), to deal in the Software
 without restriction, including without limitation the rights to use, copy, modify, merge,
 publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons
 to whom the Software is furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in all copies or
 substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
 BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
 DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/
(function (p, t) {
  var m = document.getElementsByTagName("script"),
    g = {
      version: "2.3.1",
      B: 1,
      W: -1,
      ERROR_REPORT: !0,
      DIR:
        m[m.length - 1].src.split("?")[0].split("/").slice(0, -1).join("/") +
        "/",
      lang: "en",
      i18n: { en: {} }
    };
  g.opera =
    -1 != navigator.userAgent.search(/(opera)(?:.*version)?[ \/]([\w.]+)/i);
  g.webkit = -1 != navigator.userAgent.search(/(webkit)[ \/]([\w.]+)/i);
  g.msie = -1 != navigator.userAgent.search(/(msie) ([\w.]+)/i);
  g.mozilla =
    -1 != navigator.userAgent.search(/(mozilla)(?:.*? rv:([\w.]+))?/i) &&
    !g.webkit &&
    !g.msie;
  g.t = function (a) {
    var b = g.i18n[g.lang][a] || g.i18n.en[a];
    if (b) {
      for (var c = 1; c < arguments.length; c++)
        b = b.replace("$", arguments[c]);
      return b;
    }
    return a;
  };
  g.extendClass = function (a, b) {
    b.prototype = Object.create(a.prototype);
    b.prototype.constructor = b;
    b.prototype.super = a;
    return b;
  };
  g.abstractMethod = function () {
    throw Error("unimplemented abstract method");
  };
  g.clone = function (a) {
    if (a && "object" == typeof a) {
      var b = a.constructor == Array ? [] : {},
        c;
      for (c in a) b[c] = a[c] == a ? a : g.clone(a[c]);
      return b;
    }
    return a;
  };
  g.filterHTML = function (a) {
    return a && "string" == typeof a
      ? a.replace(/</g, "&lt;").replace(/>/g, "&gt;")
      : a;
  };
  var h = function (a, b) {
    b = b || {};
    for (var c in b) this[c] = b[c];
    for (c in g.Board.default) this[c] === t && (this[c] = g.Board.default[c]);
    for (c in h.themes.default)
      this.theme[c] === t && (this.theme[c] = h.themes.default[c]);
    this.tx = this.section.left;
    this.ty = this.section.top;
    this.bx = this.size - 1 - this.section.right;
    this.by = this.size - 1 - this.section.bottom;
    var circleGraphic = new Image();
    // Redraw the whole board after the image has been loaded.
    // This prevents 'missing stones' and similar graphical errors
    // especially on slower internet connections.
    circleGraphic.onload = this.redraw;
    circleGraphic.src = this.img;
    this.circle = circleGraphic;
    this.init();
    a.appendChild(this.element);
    this.pixelRatio = 3;
    this.width && this.height
      ? this.setDimensions(this.width, this.height)
      : this.width
      ? this.setWidth(this.width)
      : this.height && this.setHeight(this.height);
  };
  h.themes = {};
  h.themes.old = {
    shadowColor: "rgba(0,0,0,0.5)",
    shadowTransparentColor: "rgba(0,0,0,0)",
    shadowBlur: 0,
    shadowSize: function (a) {
      return a.shadowSize;
    },
    markupBlackColor: "rgba(255,255,255,0.8)",
    markupWhiteColor: "rgba(0,0,0,0.8)",
    markupNoneColor: "rgba(0,0,0,0.8)",
    markupLinesWidth: function (a) {
      return a.autoLineWidth ? a.stoneRadius / 7 : a.lineWidth;
    },
    gridLinesWidth: 1,
    gridLinesColor: function (a) {
      return "rgba(0,0,0," + Math.min(1, a.stoneRadius / 15) + ")";
    },
    starColor: "#000",
    starSize: function (a) {
      return a.starSize * (a.width / 300 + 1);
    },
    stoneSize: function (a) {
      return (a.stoneSize * Math.min(a.fieldWidth, a.fieldHeight)) / 2;
    },
    coordinatesColor: "rgba(0,0,0,0.7)",
    font: function (a) {
      return a.font;
    },
    linesShift: 0.5
  };
  h.themes.default = {
    shadowColor: "rgba(0,0,0,0.5)",
    shadowTransparentColor: "rgba(0,0,0,0)",
    shadowBlur: function (a) {
      return 0.1 * a.stoneRadius;
    },
    shadowSize: 1,
    markupBlackColor: "rgba(255,255,255,0.9)",
    markupWhiteColor: "rgba(0,0,0,0.7)",
    markupNoneColor: "rgba(0,0,0,0.7)",
    markupLinesWidth: function (a) {
      return a.stoneRadius / 8;
    },
    crossLinesWidth: function (a) {
      return a.stoneRadius / 6;
    },
    gridLinesWidth: function (a) {
      return a.stoneRadius / 15;
    },
    gridLinesColor: "#654525",
    starColor: "#531",
    starSize: function (a) {
      return a.stoneRadius / 8 + 1;
    },
    stoneSize: function (a) {
      return Math.min(a.fieldWidth, a.fieldHeight) / 2;
    },
    coordinatesColor: "#531",
    variationColor: "rgba(0,32,128,0.8)",
    font: "calibri",
    linesShift: 0.25
  };
  var k = function (a, b) {
    return "function" == typeof b.theme[a] ? b.theme[a](b) : b.theme[a];
  };
  m = {
    draw: function (a, b) {
      var c = b.getX(a.x);
      a = b.getY(a.y);
      var e = b.stoneRadius;
      this.beginPath();
      var d = k("shadowBlur", b);
      e = Math.max(0, e - 0.5);
      var f = this.createRadialGradient(
        c - b.ls,
        a - b.ls,
        e - 1 - d,
        c - b.ls,
        a - b.ls,
        e + d
      );
      f.addColorStop(0, k("shadowColor", b));
      f.addColorStop(1, k("shadowTransparentColor", b));
      this.fillStyle = f;
      this.arc(c - b.ls, a - b.ls, e + d, 0, 2 * Math.PI, !0);
      this.fill();
    },
    clear: function (a, b) {
      var c = b.getX(a.x);
      a = b.getY(a.y);
      var e = b.stoneRadius;
      this.clearRect(c - 1.1 * e - b.ls, a - 1.1 * e - b.ls, 2.2 * e, 2.2 * e);
    }
  };
  var q = function (a, b, c) {
      return a.obj_arr[b][c][0].c == g.B
        ? k("markupBlackColor", a)
        : a.obj_arr[b][c][0].c == g.W
        ? k("markupWhiteColor", a)
        : k("markupNoneColor", a);
    },
    D = function (a, b, c) {
      return (
        (a.obj_arr[b][c][0] && a.obj_arr[b][c][0].c == g.W) ||
        a.obj_arr[b][c][0].c == g.B
      );
    },
    w,
    y = function (a) {
      for (var b = a.angle, c = a.angle, e = 0; e < a.lines.length; e++) {
        b += a.lines[e];
        c -= a.lines[e];
        var d = a.ctx,
          f = a.x,
          l = a.y;
        var g = a.radius;
        var h = b;
        var k = c;
        var m = a.factor,
          n = a.thickness;
        d.strokeStyle = "rgba(64,64,64,0.2)";
        d.lineWidth = (g / 30) * n;
        d.beginPath();
        g -= Math.max(1, d.lineWidth);
        n = f + g * Math.cos(h * Math.PI);
        h = l + g * Math.sin(h * Math.PI);
        f += g * Math.cos(k * Math.PI);
        l += g * Math.sin(k * Math.PI);
        k =
          f > n
            ? Math.atan((l - h) / (f - n))
            : f == n
            ? Math.PI / 2
            : Math.atan((l - h) / (f - n)) - Math.PI;
        m *= g;
        g = Math.sin(k) * m;
        var p = Math.cos(k) * m;
        m = n + g;
        k = h - p;
        g = f + g;
        p = l - p;
        d.moveTo(n, h);
        d.bezierCurveTo(m, k, g, p, f, l);
        d.stroke();
      }
    };
  h.drawHandlers = {
    NORMAL: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          a.c == g.W
            ? ((a = this.createRadialGradient(
                c - (2 * d) / 5,
                e - (2 * d) / 5,
                d / 3,
                c - d / 5,
                e - d / 5,
                (5 * d) / 5
              )),
              a.addColorStop(0, "#fff"),
              a.addColorStop(1, "#aaa"))
            : ((a = this.createRadialGradient(
                c - (2 * d) / 5,
                e - (2 * d) / 5,
                1,
                c - d / 5,
                e - d / 5,
                (4 * d) / 5
              )),
              a.addColorStop(0, "#666"),
              a.addColorStop(1, "#000"));
          this.beginPath();
          this.fillStyle = a;
          this.arc(
            c - b.ls,
            e - b.ls,
            Math.max(0, d - 0.5),
            0,
            2 * Math.PI,
            !0
          );
          this.fill();
        }
      },
      shadow: m
    },
    gif: {
      stone: {
        draw: function (a, b) {
          this.globalAlpha = 0.5;
          var c = b.getX(a.x),
              e = b.getY(a.y),
              d = b.stoneRadius,
              f = b.whiteStoneGraphic.length,
              l = b.blackStoneGraphic.length;
          "undefined" === typeof this.randIndex &&
          (this.randIndex = Math.ceil(1e5 * Math.random()));
          var E = function () {
                b.redraw();
              },
              k = function (a) {
                return "string" === typeof a ||
                !a.complete ||
                ("undefined" != typeof a.naturalWidth && 0 == a.naturalWidth)
                    ? !1
                    : !0;
              };
          a.c == g.W
              ? ((f = this.randIndex % f),
              "string" === typeof b.whiteStoneGraphic[f] &&
              ((l = new Image()),
                  (l.onload = E),
                  (l.src = b.whiteStoneGraphic[f]),
                  (b.whiteStoneGraphic[f] = l)),
                  k(b.whiteStoneGraphic[f])
                      ? this.drawImage(
                          b.whiteStoneGraphic[f],
                          c - d * 0.9,
                          e - d * 0.9,
                          2 * d * 0.9,
                          2 * d * 0.9
                      )
                      : h.drawHandlers.SHELL.stone.draw.call(this, a, b))
              : ((f = this.randIndex % l),
              "string" === typeof b.blackStoneGraphic[f] &&
              ((l = new Image()),
                  (l.onload = E),
                  (l.src = b.blackStoneGraphic[f]),
                  (b.blackStoneGraphic[f] = l)),
                  k(b.blackStoneGraphic[f])
                      ? this.drawImage(
                          b.blackStoneGraphic[f],
                          c - d * 0.9,
                          e - d * 0.9,
                          2 * d * 0.9,
                          2 * d * 0.9
                      )
                      : h.drawHandlers.SHELL.stone.draw.call(this, a, b));
          if (k(b.circle)) {
            this.drawImage(b.circle, c - d * a.scale, e - d * a.scale, 2 * d * a.scale, 2 * d * a.scale);
          }
          this.globalAlpha = 1;
        }
      }
    },
    PAINTED: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          if (a.c == g.W) {
            var f = this.createRadialGradient(
              c - (2 * d) / 5,
              e - (2 * d) / 5,
              2,
              c - d / 5,
              e - d / 5,
              (4 * d) / 5
            );
            f.addColorStop(0, "#fff");
            f.addColorStop(1, "#ddd");
          } else
            (f = this.createRadialGradient(
              c - (2 * d) / 5,
              e - (2 * d) / 5,
              1,
              c - d / 5,
              e - d / 5,
              (4 * d) / 5
            )),
              f.addColorStop(0, "#111"),
              f.addColorStop(1, "#000");
          this.beginPath();
          this.fillStyle = f;
          this.arc(
            c - b.ls,
            e - b.ls,
            Math.max(0, d - 0.5),
            0,
            2 * Math.PI,
            !0
          );
          this.fill();
          this.beginPath();
          this.lineWidth = d / 6;
          a.c == g.W
            ? ((this.strokeStyle = "#999"),
              this.arc(c + d / 8, e + d / 8, d / 2, 0, Math.PI / 2, !1))
            : ((this.strokeStyle = "#ccc"),
              this.arc(c - d / 8, e - d / 8, d / 2, Math.PI, 1.5 * Math.PI));
          this.stroke();
        }
      },
      shadow: m
    },
    ESTARGO: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius,
            f = b.whiteStoneGraphic.length,
            l = b.blackStoneGraphic.length;
          "undefined" === typeof this.randIndex &&
            (this.randIndex = Math.ceil(1e5 * Math.random()));
          var E = function () {
              b.redraw();
            },
            k = function (a) {
              return "string" === typeof a ||
                !a.complete ||
                ("undefined" != typeof a.naturalWidth && 0 == a.naturalWidth)
                ? !1
                : !0;
            };
          a.c == g.W
            ? ((f = this.randIndex % f),
              "string" === typeof b.whiteStoneGraphic[f] &&
                ((l = new Image()),
                (l.onload = E),
                (l.src = b.whiteStoneGraphic[f]),
                (b.whiteStoneGraphic[f] = l)),
              k(b.whiteStoneGraphic[f])
                ? this.drawImage(
                    b.whiteStoneGraphic[f],
                    c - d * 0.9,
                    e - d * 0.9,
                    2 * d * 0.9,
                    2 * d * 0.9
                  )
                : h.drawHandlers.SHELL.stone.draw.call(this, a, b))
            : ((f = this.randIndex % l),
              "string" === typeof b.blackStoneGraphic[f] &&
                ((l = new Image()),
                (l.onload = E),
                (l.src = b.blackStoneGraphic[f]),
                (b.blackStoneGraphic[f] = l)),
              k(b.blackStoneGraphic[f])
                ? this.drawImage(
                    b.blackStoneGraphic[f],
                    c - d * 0.9,
                    e - d * 0.9,
                    2 * d * 0.9,
                    2 * d * 0.9
                  )
                : h.drawHandlers.SHELL.stone.draw.call(this, a, b));
        }
      },
      shadow: {
        draw: function (a, b) {
          var c = b.getX(a.x);
          a = b.getY(a.y);
          var e = b.stoneRadius;
          this.beginPath();
          e = Math.max(0, 0.85 * (e - 0.5));
          var d = this.createRadialGradient(
            c - 4,
            a,
            e - 1 - 4,
            c - 4,
            a,
            e + 4
          );
          d.addColorStop(0, k("shadowColor", b));
          d.addColorStop(1, k("shadowTransparentColor", b));
          this.fillStyle = d;
          this.arc(c - 1, a - -5, e + 5, 0, 2 * Math.PI, !0);
          this.fill();
        },
        clear: function (a, b) {
          var c = b.getX(a.x);
          a = b.getY(a.y);
          b = b.stoneRadius;
          this.clearRect(c - 1.1 * b - 1, a - 1.1 * b - -5, 2.2 * b, 2.2 * b);
        }
      }
    },
    GLOW: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          a.c == g.W
            ? ((a = this.createRadialGradient(
                c - (2 * d) / 5,
                e - (2 * d) / 5,
                d / 3,
                c - d / 5,
                e - d / 5,
                (8 * d) / 5
              )),
              a.addColorStop(0, "#fff"),
              a.addColorStop(1, "#666"))
            : ((a = this.createRadialGradient(
                c - (2 * d) / 5,
                e - (2 * d) / 5,
                1,
                c - d / 5,
                e - d / 5,
                (3 * d) / 5
              )),
              a.addColorStop(0, "#555"),
              a.addColorStop(1, "#000"));
          this.beginPath();
          this.fillStyle = a;
          this.arc(
            c - b.ls,
            e - b.ls,
            Math.max(0, d - 0.5),
            0,
            2 * Math.PI,
            !0
          );
          this.fill();
        }
      },
      shadow: m
    },
    SHELL: {
      stone: {
        draw: function (a, b) {
          var c = b.stoneRadius;
          w = w || Math.ceil(9999999 * Math.random());
          var e = b.getX(a.x);
          var d = b.getY(a.y);
          var f = a.c == g.W ? "#aaa" : "#000";
          this.beginPath();
          this.fillStyle = f;
          this.arc(
            e - b.ls,
            d - b.ls,
            Math.max(0, c - 0.5),
            0,
            2 * Math.PI,
            !0
          );
          this.fill();
          a.c == g.W
            ? ((f = (w % (3 + a.x * b.size + a.y)) % 3),
              (a = b.size * b.size + a.x * b.size + a.y),
              (a = (2 / a) * (w % a)),
              0 == f
                ? y({
                    ctx: this,
                    x: e,
                    y: d,
                    radius: c,
                    angle: a,
                    lines: [0.1, 0.12, 0.11, 0.1, 0.09, 0.09, 0.09, 0.09],
                    factor: 0.25,
                    thickness: 1.75
                  })
                : 1 == f
                ? y({
                    ctx: this,
                    x: e,
                    y: d,
                    radius: c,
                    angle: a,
                    lines: [
                      0.1, 0.09, 0.08, 0.07, 0.06, 0.06, 0.06, 0.06, 0.06, 0.06,
                      0.06
                    ],
                    factor: 0.2,
                    thickness: 1.5
                  })
                : y({
                    ctx: this,
                    x: e,
                    y: d,
                    radius: c,
                    angle: a,
                    lines: [0.12, 0.14, 0.13, 0.12, 0.12, 0.12],
                    factor: 0.3,
                    thickness: 2
                  }),
              (f = this.createRadialGradient(
                e - (2 * c) / 5,
                d - (2 * c) / 5,
                c / 3,
                e - c / 5,
                d - c / 5,
                (5 * c) / 5
              )),
              f.addColorStop(0, "rgba(255,255,255,0.9)"),
              f.addColorStop(1, "rgba(255,255,255,0)"))
            : ((f = this.createRadialGradient(
                e + 0.4 * c,
                d + 0.4 * c,
                0,
                e + 0.5 * c,
                d + 0.5 * c,
                c
              )),
              f.addColorStop(0, "rgba(32,32,32,1)"),
              f.addColorStop(1, "rgba(0,0,0,0)"),
              this.beginPath(),
              (this.fillStyle = f),
              this.arc(
                e - b.ls,
                d - b.ls,
                Math.max(0, c - 0.5),
                0,
                2 * Math.PI,
                !0
              ),
              this.fill(),
              (f = this.createRadialGradient(
                e - 0.4 * c,
                d - 0.4 * c,
                1,
                e - 0.5 * c,
                d - 0.5 * c,
                1.5 * c
              )),
              f.addColorStop(0, "rgba(64,64,64,1)"),
              f.addColorStop(1, "rgba(0,0,0,0)"));
          this.beginPath();
          this.fillStyle = f;
          this.arc(
            e - b.ls,
            d - b.ls,
            Math.max(0, c - 0.5),
            0,
            2 * Math.PI,
            !0
          );
          this.fill();
        }
      },
      shadow: m
    },
    MONO: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          b = k("markupLinesWidth", b) || 1;
          this.fillStyle = a.c == g.W ? "white" : "black";
          this.beginPath();
          this.arc(c, e, Math.max(0, d - b), 0, 2 * Math.PI, !0);
          this.fill();
          this.lineWidth = b;
          this.strokeStyle = "black";
          this.stroke();
        }
      }
    },
    CR: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          this.strokeStyle = a.c || q(b, a.x, a.y);
          this.lineWidth = a.lineWidth || k("markupLinesWidth", b) || 1;
          this.beginPath();
          this.arc(c - b.ls, e - b.ls, d / 2, 0, 2 * Math.PI, !0);
          this.stroke();
        }
      }
    },
    LB: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius,
            f = a.font || k("font", b) || "";
          this.fillStyle = a.c || q(b, a.x, a.y);
          this.font =
            1 == a.text.length
              ? Math.round(1.5 * d) + "px " + f
              : 2 == a.text.length
              ? Math.round(1.2 * d) + "px " + f
              : Math.round(d) + "px " + f;
          this.beginPath();
          this.textBaseline = "middle";
          this.textAlign = "center";
          this.fillText(a.text, c, e, 2 * d);
        }
      },
      grid: {
        draw: function (a, b) {
          if (!D(b, a.x, a.y) && !a._nodraw) {
            var c = b.getX(a.x);
            a = b.getY(a.y);
            b = b.stoneRadius;
            this.clearRect(c - b, a - b, 2 * b, 2 * b);
          }
        },
        clear: function (a, b) {
          if (!D(b, a.x, a.y)) {
            a._nodraw = !0;
            b.grid.clear();
            b.grid.draw(b);
            for (var c = 0; c < b.size; c++)
              for (var e = 0; e < b.size; e++)
                for (var d = 0; d < b.obj_arr[c][e].length; d++) {
                  var f = b.obj_arr[c][e][d];
                  var l = f.type
                    ? "string" == typeof f.type
                      ? h.drawHandlers[f.type]
                      : f.type
                    : b.stoneHandler;
                  l.grid && l.grid.draw.call(b.grid.getContext(f), f, b);
                }
            for (c = 0; c < b.obj_list.length; c++)
              (f = b.obj_list[c]),
                (l = f.handler),
                l.grid &&
                  l.grid.draw.call(b.grid.getContext(f.args), f.args, b);
            delete a._nodraw;
          }
        }
      }
    },
    SQ: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = Math.round(b.stoneRadius);
          this.strokeStyle = a.c || q(b, a.x, a.y);
          this.fillStyle = a.c || q(b, a.x, a.y);
          this.lineWidth = a.lineWidth || k("markupLinesWidth", b) || 1;
          this.beginPath();
          this.fillRect(
            Math.round(c - d / 2) - b.ls,
            Math.round(e - d / 2) - b.ls,
            d,
            d 
          );
        }
      }
    },
    TR: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          this.strokeStyle = a.c || q(b, a.x, a.y);
          this.lineWidth = a.lineWidth || k("markupLinesWidth", b) || 1;
          this.beginPath();
          this.moveTo(c - b.ls, e - b.ls - Math.round(d / 2));
          this.lineTo(
            Math.round(c - d / 2) - b.ls,
            Math.round(e + d / 3) + b.ls
          );
          this.lineTo(
            Math.round(c + d / 2) + b.ls,
            Math.round(e + d / 3) + b.ls
          );
          this.closePath();
          this.stroke();
        }
      }
    },
    MA: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          this.strokeStyle = a.c || q(b, a.x, a.y);
          this.lineCap = "round";
          this.lineWidth =
            2 * (a.lineWidth || k("markupLinesWidth", b) || 1) - 1;
          this.beginPath();
          this.moveTo(Math.round(c - d / 2), Math.round(e - d / 2));
          this.lineTo(Math.round(c + d / 2), Math.round(e + d / 2));
          this.moveTo(Math.round(c + d / 2) - 1, Math.round(e - d / 2));
          this.lineTo(Math.round(c - d / 2) - 1, Math.round(e + d / 2));
          this.stroke();
          this.lineCap = "butt";
        }
      }
    },
    SL: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          this.fillStyle = a.c || q(b, a.x, a.y);
          this.beginPath();
          this.rect(c - d / 2, e - d / 2, d, d);
          this.fill();
        }
      }
    },
    SM: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y),
            d = b.stoneRadius;
          this.strokeStyle = a.c || q(b, a.x, a.y);
          this.lineWidth = 2 * (a.lineWidth || k("markupLinesWidth", b) || 1);
          this.beginPath();
          this.arc(c - d / 3, e - d / 3, d / 6, 0, 2 * Math.PI, !0);
          this.stroke();
          this.beginPath();
          this.arc(c + d / 3, e - d / 3, d / 6, 0, 2 * Math.PI, !0);
          this.stroke();
          this.beginPath();
          this.moveTo(c - d / 1.5, e);
          this.bezierCurveTo(
            c - d / 1.5,
            e + d / 2,
            c + d / 1.5,
            e + d / 2,
            c + d / 1.5,
            e
          );
          this.stroke();
        }
      }
    },
    outline: {
      stone: {
        draw: function (a, b) {
          this.globalAlpha = a.alpha ? a.alpha : 0.3;
          a.stoneStyle
            ? h.drawHandlers[a.stoneStyle].stone.draw.call(this, a, b)
            : b.stoneHandler.stone.draw.call(this, a, b);
          this.globalAlpha = 1;
        }
      }
    },
    cross: {
      stone: {
        draw: function (a, b) {
          var c = b.getX(a.x),
            e = b.getY(a.y);
          this.strokeStyle = a.c || q(b, a.x, a.y);
          this.lineWidth = a.lineWidth || k("crossLinesWidth", b) || 1;
          this.globalAlpha = 1;
          this.beginPath();
          for(let i =0 ;i< 4; i++) {
            //i=0上 i=1下 i=2左 i=3右
            let b_x = c - b.ls * 0.5;
            let b_y = e - b.ls * 0.5;
            let start_l =  12 * b.stoneRadius * 0.03;
            let end_l = 6 * b.stoneRadius * 0.03;
            let x1 = (i == 0 || i == 1) ? b_x : i == 2 ? b_x - start_l : b_x + start_l;
            let x2 = (i == 0 || i == 1) ? b_x : i == 2 ? b_x - end_l : b_x + end_l;
            let y1 = i == 0 ? b_y - start_l : i == 1 ? b_y + start_l : b_y;
            let y2 = i == 0 ? b_y - end_l : i == 1 ? b_y + end_l : b_y;
            this.lineCap = "round";
            this.moveTo(x1, y1);
            this.lineTo(x2, y2);
            this.stroke();
            if(i == 3) {
              //第3条线不清晰
              this.moveTo(x1, y1);
              this.lineTo(x2, y2);
              this.stroke();
            }
          }
        }
      }
    },
    mini: {
      stone: {
        draw: function (a, b) {
          b.stoneRadius /= 2;
          a.stoneStyle
            ? h.drawHandlers[a.stoneStyle].stone.draw.call(this, a, b)
            : b.stoneHandler.stone.draw.call(this, a, b);
          b.stoneRadius *= 2;
        }
      }
    }
  };
  h.coordinates = {
    grid: {
      draw: function (a, b) {
        this.fillStyle = k("coordinatesColor", b);
        this.textBaseline = "middle";
        this.textAlign = "center";
        this.font = b.stoneRadius + "px " + (b.font || "");
        var c = b.getX(-0.75);
        var e = b.getX(b.size - 0.25);
        var d = b.getY(-0.75);
        var f = b.getY(b.size - 0.25);
        for (var l = 0; l < b.size; l++) {
          a = l + 65;
          73 <= a && a++;
          var g = b.getY(l);
          this.fillText(b.size - l, c, g);
          this.fillText(b.size - l, e, g);
          g = b.getX(l);
          this.fillText(String.fromCharCode(a), g, d);
          this.fillText(String.fromCharCode(a), g, f);
        }
        this.fillStyle = "black";
      }
    }
  };
  h.CanvasLayer = function () {
    this.element = document.createElement("canvas");
    this.context = this.element.getContext("2d");
    this.pixelRatio = 3;
    1 < this.pixelRatio && this.context.scale(this.pixelRatio, this.pixelRatio);
  };
  h.CanvasLayer.prototype = {
    constructor: h.CanvasLayer,
    setDimensions: function (a, b) {
      this.element.width = a;
      this.element.style.width = a / this.pixelRatio + "px";
      this.element.height = b;
      this.element.style.height = b / this.pixelRatio + "px";
    },
    appendTo: function (a, b) {
      this.element.style.position = "absolute";
      this.element.style.zIndex = b;
      a.appendChild(this.element);
    },
    removeFrom: function (a) {
      a.removeChild(this.element);
    },
    getContext: function () {
      return this.context;
    },
    draw: function (a) {},
    clear: function () {
      this.context.clearRect(0, 0, this.element.width, this.element.height);
    }
  };
  h.GridLayer = g.extendClass(h.CanvasLayer, function () {
    this.super.call(this);
  });
  h.GridLayer.prototype.draw = function (a) {
    this.context.beginPath();
    this.context.lineWidth = 2.8;
    this.context.strokeStyle = k("gridLinesColor", a);
    var b = Math.round(a.left),
      c = Math.round(a.top),
      e = Math.round(a.fieldWidth * (a.size - 1)),
      d = Math.round(a.fieldHeight * (a.size - 1));
    this.context.strokeRect(b - a.ls, c - a.ls, e, d);
    for (var f = 1; f < a.size - 1; f++) {
      if (f == 1) {
        this.context.lineWidth = 1.6;
      }
      var g = Math.round(a.getX(f)) - a.ls;
      this.context.moveTo(g, c);
      this.context.lineTo(g, c + d);
      g = Math.round(a.getY(f)) - a.ls;
      this.context.moveTo(b, g);
      this.context.lineTo(b + e, g);
    }
    this.context.stroke();
    this.context.fillStyle = k("starColor", a);
    if (a.starPoints[a.size])
      for (var h in a.starPoints[a.size])
        this.context.beginPath(),
          this.context.arc(
            a.getX(a.starPoints[a.size][h].x) - a.ls,
            a.getY(a.starPoints[a.size][h].y) - a.ls,
            k("starSize", a),
            0,
            2 * Math.PI,
            !0
          ),
          this.context.fill();
  };
  h.MultipleCanvasLayer = g.extendClass(h.CanvasLayer, function () {
    this.init(4);
  });
  h.MultipleCanvasLayer.prototype.init = function (a) {
    this.layers = a;
    this.elements = [];
    this.contexts = [];
    this.pixelRatio = 3;
    for (var b = 0; b < a; b++) {
      var c = document.createElement("canvas");
      var e = c.getContext("2d");
      1 < this.pixelRatio && e.scale(this.pixelRatio, this.pixelRatio);
      this.elements.push(c);
      this.contexts.push(e);
    }
  };
  h.MultipleCanvasLayer.prototype.appendTo = function (a, b) {
    for (var c = 0; c < this.layers; c++)
      (this.elements[c].style.position = "absolute"),
        (this.elements[c].style.zIndex = b),
        a.appendChild(this.elements[c]);
  };
  h.MultipleCanvasLayer.prototype.removeFrom = function (a) {
    for (var b = 0; b < this.layers; b++) a.removeChild(this.elements[b]);
  };
  h.MultipleCanvasLayer.prototype.getContext = function (a) {
    return a.x % 2
      ? a.y % 2
        ? this.contexts[0]
        : this.contexts[1]
      : a.y % 2
      ? this.contexts[2]
      : this.contexts[3];
  };
  h.MultipleCanvasLayer.prototype.clear = function (a, b) {
    for (a = 0; a < this.layers; a++)
      this.contexts[a].clearRect(
        0,
        0,
        this.elements[a].width,
        this.elements[a].height
      );
  };
  h.MultipleCanvasLayer.prototype.setDimensions = function (a, b) {
    for (var c = 0; c < this.layers; c++)
      (this.elements[c].width = a),
        (this.elements[c].style.width = a / this.pixelRatio + "px"),
        (this.elements[c].height = b),
        (this.elements[c].style.height = b / this.pixelRatio + "px");
  };
  h.ShadowLayer = g.extendClass(h.MultipleCanvasLayer, function (a, b, c) {
    this.init(2);
    this.shadowSize = b === t ? 1 : b;
    this.board = a;
  });
  h.ShadowLayer.prototype.getContext = function (a) {
    return (a.x % 2 && a.y % 2) || !(a.x % 2 || a.y % 2)
      ? this.contexts[0]
      : this.contexts[1];
  };
  h.ShadowLayer.prototype.setDimensions = function (a, b) {
    this.super.prototype.setDimensions.call(this, a, b);
    for (a = 0; a < this.layers; a++)
      this.contexts[a].setTransform(
        1,
        0,
        0,
        1,
        Math.round((this.shadowSize * this.board.stoneRadius) / 7),
        Math.round((this.shadowSize * this.board.stoneRadius) / 7)
      );
  };
  var z = function () {
      return (
        (3 * this.width) / (4 * (this.bx + 1 - this.tx) + 2) -
        this.fieldWidth * this.tx
      );
    },
    A = function () {
      return (
        (3 * this.height) / (4 * (this.by + 1 - this.ty) + 2) -
        this.fieldHeight * this.ty
      );
    },
    B = function (a, b) {
      for (var c, e = 0; e < this.obj_arr[a][b].length; e++) {
        var d = this.obj_arr[a][b][e];
        c = d.type
          ? "string" == typeof d.type
            ? h.drawHandlers[d.type]
            : d.type
          : this.stoneHandler;
        for (var f in c)
          if (c[f].clear) c[f].clear.call(this[f].getContext(d), d, this);
          else {
            var g = this[f].getContext(d),
              k = d,
              m = this.getX(k.x);
            k = this.getY(k.y);
            var n = this.stoneRadius;
            g.clearRect(m - 2 * n - this.ls, k - 2 * n - this.ls, 4 * n, 4 * n);
          }
      }
    },
    x = function (a, b) {
      for (var c, e = 0; e < this.obj_arr[a][b].length; e++) {
        var d = this.obj_arr[a][b][e];
        c = d.type
          ? "string" == typeof d.type
            ? h.drawHandlers[d.type]
            : d.type
          : this.stoneHandler;
        for (var f in c) c[f].draw.call(this[f].getContext(d), d, this);
      }
    },
    C = function () {
      this.element.style.width = this.width / this.pixelRatio + "px";
      this.element.style.height = this.height / this.pixelRatio + "px";
      this.stoneRadius = k("stoneSize", this);
      this.ls = k("linesShift", this);
      for (var a = 0; a < this.layers.length; a++)
        this.layers[a].setDimensions(this.width, this.height);
    };
  h.prototype = {
    constructor: h,
    init: function () {
      this.obj_arr = [];
      for (var a = 0; a < this.size; a++) {
        this.obj_arr[a] = [];
        for (var b = 0; b < this.size; b++) this.obj_arr[a][b] = [];
      }
      this.obj_list = [];
      this.layers = [];
      this.listeners = [];
      this.element = document.createElement("div");
      this.element.className = "wgo-board";
      this.element.style.position = "relative";
      this.background &&
        ("#" == this.background[0]
          ? (this.element.style.backgroundColor = this.background)
          : ((this.element.style.backgroundImage =
              "url('" + this.background + "')"),
            this.stoneHandler == h.drawHandlers.REALISTIC &&
              (this.element.style.backgroundSize = "100%")));
      this.grid = new h.GridLayer();
      this.shadow = new h.ShadowLayer(this, k("shadowSize", this));
      this.stone = new h.MultipleCanvasLayer();
      this.addLayer(this.grid, 100);
      this.addLayer(this.shadow, 200);
      this.addLayer(this.stone, 300);
    },
    setWidth: function (a) {
      this.width = a;
      this.width *= this.pixelRatio;
      this.fieldHeight = this.fieldWidth =
        (4 * this.width) / (4 * (this.bx + 1 - this.tx) + 2);
      this.left = z.call(this);
      this.height = (this.by - this.ty + 1.5) * this.fieldHeight;
      this.top = A.call(this);
      C.call(this);
      this.redraw();
    },
    setHeight: function (a) {
      this.height = a;
      this.height *= this.pixelRatio;
      this.fieldWidth = this.fieldHeight =
        (4 * this.height) / (4 * (this.by + 1 - this.ty) + 2);
      this.top = A.call(this);
      this.width = (this.bx - this.tx + 1.5) * this.fieldWidth;
      this.left = z.call(this);
      C.call(this);
      this.redraw();
    },
    setDimensions: function (a, b) {
      this.width = a || parseInt(this.element.style.width, 10);
      this.width *= this.pixelRatio;
      this.height = b || parseInt(this.element.style.height, 10);
      this.height *= this.pixelRatio;
      this.fieldWidth = (4 * this.width) / (4 * (this.bx + 1 - this.tx) + 2);
      this.fieldHeight = (4 * this.height) / (4 * (this.by + 1 - this.ty) + 2);
      this.left = z.call(this);
      this.top = A.call(this);
      C.call(this);
      this.redraw();
    },
    getSection: function () {
      return this.section;
    },
    setSection: function (a, b, c, e) {
      this.section =
        "object" == typeof a ? a : { top: a, right: b, bottom: c, left: e };
      this.tx = this.section.left;
      this.ty = this.section.top;
      this.bx = this.size - 1 - this.section.right;
      this.by = this.size - 1 - this.section.bottom;
      this.setDimensions();
    },
    setSize: function (a) {
      a = a || 19;
      if (a != this.size) {
        this.size = a;
        this.obj_arr = [];
        for (a = 0; a < this.size; a++) {
          this.obj_arr[a] = [];
          for (var b = 0; b < this.size; b++) this.obj_arr[a][b] = [];
        }
        this.bx = this.size - 1 - this.section.right;
        this.by = this.size - 1 - this.section.bottom;
        this.setDimensions();
      }
    },
    redraw: function () {
      try {
        for (var a = 0; a < this.layers.length; a++)
          this.layers[a].clear(this), this.layers[a].draw(this);
        for (a = 0; a < this.size; a++)
          for (var b = 0; b < this.size; b++) x.call(this, a, b);
        for (a = 0; a < this.obj_list.length; a++) {
          var c = this.obj_list[a],
            e = c.handler,
            d;
          for (d in e) e[d].draw.call(this[d].getContext(c.args), c.args, this);
        }
      } catch (f) {
        console.log("WGo board failed to render. Error: " + f.message);
      }
    },
    getX: function (a) {
      return this.left + a * this.fieldWidth;
    },
    getY: function (a) {
      return this.top + a * this.fieldHeight;
    },
    addLayer: function (a, b) {
      a.appendTo(this.element, b);
      a.setDimensions(this.width, this.height);
      this.layers.push(a);
    },
    removeLayer: function (a) {
      var b = this.layers.indexOf(a);
      0 <= b && (this.layers.splice(b, 1), a.removeFrom(this.element));
    },
    update: function (a) {
      var b;
      if (a.remove && "all" == a.remove) this.removeAllObjects();
      else if (a.remove) {
        for (b = 0; b < a.remove.length; b++) {
          this.removeObject(a.remove[b]);
          for (
            let z = 0;
            z < this.obj_arr[a.remove[b].x][a.remove[b].y].length;
            z++
          ) {
            if (
              this.obj_arr[a.remove[b].x][a.remove[b].y][z].type === "LB" &&
              typeof this.obj_arr[a.remove[b].x][a.remove[b].y][z].text ===
                "number"
            ) {
              this.removeObject(this.obj_arr[a.remove[b].x][a.remove[b].y][z]);
            }
          }
        }
      }
      if (a.add) for (b = 0; b < a.add.length; b++) this.addObject(a.add[b]);
    },
    addObject: function (a) {
      if (a?.constructor == Array)
        for (var b = 0; b < a.length; b++) this.addObject(a[b]);
      else
        try {
          B.call(this, a.x, a.y);
          b = this.obj_arr[a.x][a.y];

          var has_c = false;
          for (var c = 0; c < b.length; c++) {
            if (b[c].type == a.type) {
              b[c] = a;
              x.call(this, a.x, a.y);
              return;
            }
            if (b[c]["c"]) {
              has_c = true;
            }
          }
          a.type ? b.push(a) : b.unshift(a);

          x.call(this, a.x, a.y);
          if (!has_c) {
            for (var z = 0; z < b.length; z++) {
              if (b[z].type === "LB" && typeof b[z].text === "number") {
                this.removeObject(b[z]);
              }
            }
          }
        } catch (e) {
          console.log("WGo board failed to render. Error: " + e.message);
        }
    },
    removeObject: function (a) {
      if (a?.constructor == Array)
        for (var b = 0; b < a.length; b++) this.removeObject(a[b]);
      else
        try {
          for (var c = 0; c < this.obj_arr[a.x][a.y].length; c++)
            if (this.obj_arr[a.x][a.y][c].type == a.type) {
              b = c;
              break;
            }
          b !== t &&
            (B.call(this, a.x, a.y),
            this.obj_arr[a.x][a.y].splice(b, 1),
            x.call(this, a.x, a.y));
        } catch (e) {
          console.log("WGo board failed to render. Error: " + e.message);
        }
    },
    removeObjectsAt: function (a, b) {
      this.obj_arr[a][b].length &&
        (B.call(this, a, b), (this.obj_arr[a][b] = []));
    },
    removeAllObjects: function () {
      this.obj_arr = [];
      for (var a = 0; a < this.size; a++) {
        this.obj_arr[a] = [];
        for (var b = 0; b < this.size; b++) this.obj_arr[a][b] = [];
      }
      this.redraw();
    },
    addCustomObject: function (a, b) {
      this.obj_list.push({ handler: a, args: b });
      this.redraw();
    },
    removeCustomObject: function (a, b) {
      for (var c = 0; c < this.obj_list.length; c++) {
        var e = this.obj_list[c];
        if (e.handler == a && e.args == b)
          return this.obj_list.splice(c, 1), this.redraw(), !0;
      }
      return !1;
    },
    addEventListener: function (a, b) {
      var c = this,
        e = {
          type: a,
          callback: b,
          handleEvent: function (a) {
            var d = a.offsetX * c.pixelRatio;
            d -= c.left;
            d /= c.fieldWidth;
            d = Math.round(d);
            var e = a.offsetY * c.pixelRatio;
            e -= c.top;
            e /= c.fieldHeight;
            e = Math.round(e);
            b(d >= c.size ? -1 : d, e >= c.size ? -1 : e, a);
          }
        };
      this.element.addEventListener(a, e, !0);
      this.listeners.push(e);
    },
    removeEventListener: function (a, b) {
      for (var c = 0; c < this.listeners.length; c++) {
        var e = this.listeners[c];
        if (e.type == a && e.callback == b)
          return (
            this.element.removeEventListener(e.type, e, !0),
            this.listeners.splice(c, 1),
            !0
          );
      }
      return !1;
    },
    getState: function () {
      return { objects: g.clone(this.obj_arr), custom: g.clone(this.obj_list) };
    },
    restoreState: function (a) {
      this.obj_arr = a.objects || this.obj_arr;
      this.obj_list = a.custom || this.obj_list;
      this.redraw();
    }
  };
  h.default = {
    size: 19,
    width: 0,
    height: 0,
    font: "Calibri",
    lineWidth: 1,
    autoLineWidth: !1,
    starPoints: {
      5: [{ x: 2, y: 2 }],
      7: [{ x: 3, y: 3 }],
      8: [
        { x: 2, y: 2 },
        { x: 5, y: 2 },
        { x: 2, y: 5 },
        { x: 5, y: 5 }
      ],
      9: [
        { x: 2, y: 2 },
        { x: 6, y: 2 },
        { x: 2, y: 6 },
        { x: 6, y: 6 }
      ],
      10: [
        { x: 2, y: 2 },
        { x: 7, y: 2 },
        { x: 2, y: 7 },
        { x: 7, y: 7 }
      ],
      11: [
        { x: 2, y: 2 },
        { x: 8, y: 2 },
        { x: 5, y: 5 },
        { x: 2, y: 8 },
        { x: 8, y: 8 }
      ],
      12: [
        { x: 3, y: 3 },
        { x: 8, y: 3 },
        { x: 3, y: 8 },
        { x: 8, y: 8 }
      ],
      13: [
        { x: 3, y: 3 },
        { x: 9, y: 3 },
        { x: 6, y: 6 },
        { x: 3, y: 9 },
        { x: 9, y: 9 }
      ],
      14: [
        { x: 3, y: 3 },
        { x: 10, y: 3 },
        { x: 3, y: 10 },
        { x: 10, y: 10 }
      ],
      15: [
        { x: 3, y: 3 },
        { x: 11, y: 3 },
        { x: 7, y: 7 },
        { x: 3, y: 11 },
        { x: 11, y: 11 }
      ],
      16: [
        { x: 3, y: 3 },
        { x: 12, y: 3 },
        { x: 3, y: 12 },
        { x: 12, y: 12 }
      ],
      17: [
        { x: 3, y: 3 },
        { x: 8, y: 3 },
        { x: 13, y: 3 },
        { x: 3, y: 8 },
        { x: 8, y: 8 },
        { x: 13, y: 8 },
        { x: 3, y: 13 },
        { x: 8, y: 13 },
        { x: 13, y: 13 }
      ],
      18: [
        { x: 3, y: 3 },
        { x: 14, y: 3 },
        { x: 3, y: 14 },
        { x: 14, y: 14 }
      ],
      19: [
        { x: 3, y: 3 },
        { x: 9, y: 3 },
        { x: 15, y: 3 },
        { x: 3, y: 9 },
        { x: 9, y: 9 },
        { x: 15, y: 9 },
        { x: 3, y: 15 },
        { x: 9, y: 15 },
        { x: 15, y: 15 }
      ],
      20: [
        { x: 3, y: 3 },
        { x: 16, y: 3 },
        { x: 3, y: 16 },
        { x: 16, y: 16 }
      ],
      21: [
        { x: 3, y: 3 },
        { x: 10, y: 3 },
        { x: 17, y: 3 },
        { x: 3, y: 10 },
        { x: 10, y: 10 },
        { x: 17, y: 10 },
        { x: 3, y: 17 },
        { x: 10, y: 17 },
        { x: 17, y: 17 }
      ]
    },
    stoneHandler: h.drawHandlers.ESTARGO,
    starSize: 1,
    shadowSize: 1,
    stoneSize: 1,
    section: { top: 0, right: 0, bottom: 0, left: 0 },
    blackStoneGraphic: [
      // "https://cdn.elf-go.com/image/default/9B3EFC0A75894672A7A84DB9C9B12220-6-2.png"
      require("@/assets/board/black.png")
    ],
    whiteStoneGraphic: [
      // "https://cdn.elf-go.com/image/default/F11AA23A00DC466DBF3F79044D83D301-6-2.png"
      require("@/assets/board/white.png")
    ],
    img: require("@/assets/board/circle.png"),
    theme: {}
  };
  g.Board = h;
  var r = function (a) {
    this.size = a || 19;
    this.schema = [];
    for (a = 0; a < this.size * this.size; a++) this.schema[a] = 0;
  };
  r.prototype = {
    constructor: g.Position,
    get: function (a, b) {
      return 0 > a || 0 > b || a >= this.size || b >= this.size
        ? t
        : this.schema[a * this.size + b];
    },
    set: function (a, b, c) {
      this.schema[a * this.size + b] = c;
      return this;
    },
    clear: function () {
      for (var a = 0; a < this.size * this.size; a++) this.schema[a] = 0;
      return this;
    },
    clone: function () {
      var a = new r(this.size);
      a.schema = this.schema.slice(0);
      return a;
    },
    compare: function (a) {
      for (var b = [], c = [], e = 0; e < this.size * this.size; e++)
        this.schema[e] && !a.schema[e]
          ? c.push({ x: Math.floor(e / this.size), y: e % this.size })
          : this.schema[e] != a.schema[e] &&
            b.push({
              x: Math.floor(e / this.size),
              y: e % this.size,
              c: a.schema[e]
            });
      return { add: b, remove: c };
    }
  };
  g.Position = r;
  m = function (a, b, c, e) {
    this.size = a || 19;
    this.repeating = b === t ? "KO" : b;
    this.allow_rewrite = c || !1;
    this.allow_suicide = e || !1;
    this.stack = [];
    this.stack[0] = new r(this.size);
    this.stack[0].capCount = { black: 0, white: 0 };
    this.turn = g.B;
    Object.defineProperty(this, "position", {
      get: function () {
        return this.stack[this.stack.length - 1];
      },
      set: function (a) {
        this.stack[this.stack.length - 1] = a;
      }
    });
  };
  var u = function (a, b, c, e, d) {
      0 <= c &&
        c < a.size &&
        0 <= e &&
        e < a.size &&
        a.get(c, e) == d &&
        (a.set(c, e, 0),
        b.push({ x: c, y: e }),
        u(a, b, c, e - 1, d),
        u(a, b, c, e + 1, d),
        u(a, b, c - 1, e, d),
        u(a, b, c + 1, e, d));
    },
    v = function (a, b, c, e, d) {
      if (0 > c || c >= a.size || 0 > e || e >= a.size) return !0;
      if (0 == a.get(c, e)) return !1;
      if (1 == b.get(c, e) || a.get(c, e) == -d) return !0;
      b.set(c, e, !0);
      return (
        v(a, b, c, e - 1, d) &&
        v(a, b, c, e + 1, d) &&
        v(a, b, c - 1, e, d) &&
        v(a, b, c + 1, e, d)
      );
    },
    n = function (a, b, c, e) {
      var d = [];
      if (0 <= b && b < a.size && 0 <= c && c < a.size && a.get(b, c) == e) {
        var f = new r(a.size);
        v(a, f, b, c, e) && u(a, d, b, c, e);
      }
      return d;
    };
  m.prototype = {
    constructor: m,
    getPosition: function () {
      return this.stack[this.stack.length - 1];
    },
    play: function (a, b, c, e) {
      if (!this.isOnBoard(a, b)) return 1;
      if (!this.allow_rewrite && 0 != this.position.get(a, b)) return 2;
      c || (c = this.turn);
      var d = this.position.clone();
      d.set(a, b, c);
      var f = c,
        h = n(d, a - 1, b, -c).concat(
          n(d, a + 1, b, -c),
          n(d, a, b - 1, -c),
          n(d, a, b + 1, -c)
        );
      if (!h.length) {
        var k = new r(this.size);
        if (v(d, k, a, b, c))
          if (this.allow_suicide) (f = -c), u(d, h, a, b, c);
          else return 3;
      }
      if ((k = this.repeating)) {
        a: {
          if ("KO" == this.repeating && 0 <= this.stack.length - 2)
            var m = this.stack.length - 2;
          else if ("ALL" == this.repeating) m = 0;
          else {
            a = !0;
            break a;
          }
          for (var p = this.stack.length - 2; p >= m; p--)
            if (this.stack[p].get(a, b) == d.get(a, b)) {
              k = !0;
              for (var q = 0; q < this.size * this.size; q++)
                if (this.stack[p].schema[q] != d.schema[q]) {
                  k = !1;
                  break;
                }
              if (k) {
                a = !1;
                break a;
              }
            }
          a = !0;
        }
        k = !a;
      }
      if (k) return 4;
      if (e) return !1;
      d.color = c;
      d.capCount = {
        black: this.position.capCount.black,
        white: this.position.capCount.white
      };
      f == g.B
        ? (d.capCount.black += h.length)
        : (d.capCount.white += h.length);
      this.pushPosition(d);
      this.turn = -c;
      return h;
    },
    pass: function (a) {
      this.pushPosition();
      a
        ? ((this.position.color = a), (this.turn = -a))
        : ((this.position.color = this.turn), (this.turn = -this.turn));
    },
    isValid: function (a, b, c) {
      return "number" != typeof this.play(a, b, c, !0);
    },
    isOnBoard: function (a, b) {
      return 0 <= a && 0 <= b && a < this.size && b < this.size;
    },
    addStone: function (a, b, c) {
      return this.isOnBoard(a, b) && 0 == this.position.get(a, b)
        ? (this.position.set(a, b, c || 0), !0)
        : !1;
    },
    removeStone: function (a, b) {
      return this.isOnBoard(a, b) && 0 != this.position.get(a, b)
        ? (this.position.set(a, b, 0), !0)
        : !1;
    },
    setStone: function (a, b, c) {
      return this.isOnBoard(a, b) ? (this.position.set(a, b, c || 0), !0) : !1;
    },
    getStone: function (a, b) {
      return this.isOnBoard(a, b) ? this.position.get(a, b) : 0;
    },
    pushPosition: function (a) {
      a ||
        ((a = this.position.clone()),
        (a.capCount = {
          black: this.position.capCount.black,
          white: this.position.capCount.white
        }),
        (a.color = this.position.color));
      this.stack.push(a);
      a.color && (this.turn = -a.color);
      return this;
    },
    popPosition: function () {
      var a = null;
      0 < this.stack.length &&
        ((a = this.stack.pop()),
        (this.turn =
          0 == this.stack.length
            ? g.B
            : this.position.color
            ? -this.position.color
            : -this.turn));
      return a;
    },
    firstPosition: function () {
      this.stack = [];
      this.stack[0] = new r(this.size);
      this.stack[0].capCount = { black: 0, white: 0 };
      this.turn = g.B;
      return this;
    },
    getCaptureCount: function (a) {
      return a == g.B
        ? this.position.capCount.black
        : this.position.capCount.white;
    },
    validatePosition: function () {
      for (
        var a, b, c = 0, e = 0, d = [], f = this.position.clone(), h = 0;
        h < this.size;
        h++
      )
        for (var k = 0; k < this.size; k++)
          if ((a = this.position.get(h, k)))
            (b = d.length),
              (d = d.concat(
                n(f, h - 1, k, -a),
                n(f, h + 1, k, -a),
                n(f, h, k - 1, -a),
                n(f, h, k + 1, -a)
              )),
              a == g.B ? (e += d - b) : (c += d - b);
      this.position.capCount.black += e;
      this.position.capCount.white += c;
      this.position.schema = f.schema;
      return d;
    }
  };
  g.Game = m;
  p.WGo = g;
})(window);
