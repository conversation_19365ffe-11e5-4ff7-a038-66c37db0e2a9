/* eslint-disable */
(function (e, k) {
  var g = function (n) {
      var a = new u(JSON.parse(JSON.stringify(n.getProperties()))),
        b;
      for (b in n.children) a.appendChild(g(n.children[b]));
      return a;
    },
    h = function (n, a) {
      var b;
      if (a[n] !== k) return a[n];
      for (var c in a.children) if ((b = h(n, a.children[c]))) return b;
      return !1;
    },
    l = function (a, b) {
      a.push(JSON.parse(JSON.stringify(b.getProperties())));
      if (1 < b.children.length) {
        for (var n = [], c = 0; c < b.children.length; c++) {
          var d = [];
          l(d, b.children[c]);
          n.push(d);
        }
        a.push(n);
      } else b.children.length && l(a, b.children[0]);
    },
    f = function (a, b) {
      for (var n, c = 1; c < a.length; c++)
        if (a[c].constructor == Array)
          for (var d = 0; d < a[c].length; d++)
            (n = new u(a[c][d][0])), b.appendChild(n), f(a[c][d], n);
        else (n = new u(a[c])), b.insertAfter(n), (b = n);
    },
    d = function (a) {
      return "string" == typeof a
        ? a.replace(/\\/g, "\\\\").replace(/]/g, "\\]")
        : a;
    },
    b = function (a, b) {
      return String.fromCharCode(97 + a) + String.fromCharCode(97 + b);
    },
    c = function (a, b, c) {
      if (b.length) {
        c.sgf += a;
        for (var n in b) c.sgf += "[" + b[n] + "]";
      }
    },
    q = function (a, m) {
      if (a.move) {
        var n = "";
        a.move.pass || (n = b(a.move.x, a.move.y));
        m.sgf =
          a.move.c == e.B ? m.sgf + ("B[" + n + "]") : m.sgf + ("W[" + n + "]");
      }
      if (a.setup) {
        n = [];
        var f = [],
          A = [];
        for (g in a.setup)
          a.setup[g].c == e.B
            ? n.push(b(a.setup[g].x, a.setup[g].y))
            : a.setup[g].c == e.W
            ? f.push(b(a.setup[g].x, a.setup[g].y))
            : A.push(b(a.setup[g].x, a.setup[g].y));
        c("AB", n, m);
        c("AW", f, m);
        c("AE", A, m);
      }
      if (a.markup) {
        n = {};
        for (g in a.markup)
          (n[a.markup[g].type] = n[a.markup[g].type] || []),
            "LB" == a.markup[g].type
              ? n.LB.push(
                  b(a.markup[g].x, a.markup[g].y) + ":" + d(a.markup[g].text)
                )
              : n[a.markup[g].type].push(b(a.markup[g].x, a.markup[g].y));
        for (var p in n) c(p, n[p], m);
      }
      var g = a.getProperties();
      for (p in g)
        "object" != typeof g[p] &&
          (m.sgf =
            "turn" == p
              ? m.sgf + ("PL[" + (g[p] == e.B ? "B" : "W") + "]")
              : "comment" == p
              ? m.sgf + ("C[" + d(g[p]) + "]")
              : m.sgf + (p + "[" + d(g[p]) + "]"));
      if (1 == a.children.length) (m.sgf += "\n;"), q(a.children[0], m);
      else if (1 < a.children.length)
        for (p in a.children)
          (g = a.children[p]),
            (n = m),
            (n.sgf += "(\n;"),
            q(g, n),
            (n.sgf += "\n)");
    },
    a = function () {
      this.size = 19;
      this.info = {};
      this.root = new u();
      this.propertyCount = this.nodeCount = 0;
    };
  a.prototype = {
    constructor: a,
    clone: function () {
      var b = new a();
      b.size = this.size;
      b.info = JSON.parse(JSON.stringify(this.info));
      b.root = g(this.root);
      b.nodeCount = this.nodeCount;
      b.propertyCount = this.propertyCount;
      return b;
    },
    hasComments: function () {
      return !!h("comment", this.root);
    }
  };
  a.fromSgf = function (a) {
    return e.SGF.parse(a);
  };
  a.fromJGO = function (b) {
    b = "string" == typeof b ? JSON.parse(b) : b;
    var c = new a();
    c.info = JSON.parse(JSON.stringify(b.info));
    c.size = b.size;
    c.nodeCount = b.nodeCount;
    c.propertyCount = b.propertyCount;
    c.root = new u(b.game[0]);
    f(b.game, c.root);
    return c;
  };
  a.prototype.toSgf = function () {
    var a = { sgf: "(\n;" },
      b = {},
      c;
    for (c in this.info)
      "black" == c
        ? (this.info.black.name && (b.PB = d(this.info.black.name)),
          this.info.black.rank && (b.BR = d(this.info.black.rank)),
          this.info.black.team && (b.BT = d(this.info.black.team)))
        : "white" == c
        ? (this.info.white.name && (b.PW = d(this.info.white.name)),
          this.info.white.rank && (b.WR = d(this.info.white.rank)),
          this.info.white.team && (b.WT = d(this.info.white.team)))
        : (b[c] = d(this.info[c]));
    this.size && (b.SZ = this.size);
    b.AP || (b.AP = "WGo.js:2");
    b.FF || (b.FF = "4");
    b.GM || (b.GM = "1");
    b.CA || (b.CA = "UTF-8");
    for (c in b) b[c] && (a.sgf += c + "[" + b[c] + "]");
    q(this.root, a);
    a.sgf += ")";
    return a.sgf;
  };
  a.prototype.toJGO = function (a) {
    var b = {};
    b.size = this.size;
    b.info = JSON.parse(JSON.stringify(this.info));
    b.nodeCount = this.nodeCount;
    b.propertyCount = this.propertyCount;
    b.game = [];
    l(b.game, this.root);
    return a ? JSON.stringify(b) : b;
  };
  var p = function (a) {
    if (a.name) {
      var b = e.filterHTML(a.name);
      a.rank && (b += " (" + e.filterHTML(a.rank) + ")");
      a.team && (b += ", " + e.filterHTML(a.team));
    } else
      a.team && (b = e.filterHTML(a.team)),
        a.rank && (b += " (" + e.filterHTML(a.rank) + ")");
    return b;
  };
  a.infoFormatters = {
    black: p,
    white: p,
    TM: function (a) {
      if (0 == a) return e.t("none");
      var b,
        c = Math.floor(a / 60);
      1 == c
        ? (b = "1 " + e.t("minute"))
        : 1 < c && (b = c + " " + e.t("minutes"));
      c = a % 60;
      1 == c
        ? (b += " 1 " + e.t("second"))
        : 1 < c && (b += " " + c + " " + e.t("seconds"));
      return b;
    },
    RE: function (a) {
      return (
        '<a href="javascript: void(0)" onclick="this.parentNode.innerHTML = \'' +
        e.filterHTML(a) +
        '\'" title="' +
        e.t("res-show-tip") +
        '">' +
        e.t("show") +
        "</a>"
      );
    }
  };
  a.infoList =
    "black white AN CP DT EV GN GC HA ON OT RE RO RU SO TM US PC KM".split(" ");
  e.Kifu = a;
  var m = function (a, b, c) {
      for (var m = 0; m < a.length; m++)
        if (a[m].x == b.x && a[m].y == b.y) {
          a[m][c] = b[c];
          return;
        }
      a.push(b);
    },
    v = function (a, b) {
      if (a)
        for (var c = 0; c < a.length; c++)
          if (a[c].x == b.x && a[c].y == b.y) {
            a.splice(c, 1);
            break;
          }
    },
    u = function (a, b) {
      this.step = 0;
      this.has_setup = false;
      this.parent = b || null;
      this.children = [];
      if (a) for (var c in a) this[c] = a[c];
    };
  u.prototype = {
    constructor: u,
    getChild: function (a) {
      a = a || 0;
      return this.children[a] ? this.children[a] : null;
    },
    addSetup: function (a) {
      this.setup = this.setup || [];
      m(this.setup, a, "c");
      return this;
    },
    removeSetup: function (a) {
      v(this.setup, a);
      return this;
    },
    addMarkup: function (a) {
      this.markup = this.markup || [];
      m(this.markup, a, "type");
      return this;
    },
    removeMarkup: function (a) {
      v(this.markup, a);
      return this;
    },
    remove: function () {
      var a = this.parent;
      if (!a) throw new Exception("Root node cannot be removed");
      for (var b in a.children)
        if (a.children[b] == this) {
          a.children.splice(b, 1);
          break;
        }
      a.children = a.children.concat(this.children);
      this.parent = null;
      return a;
    },
    insertAfter: function (a) {
      for (var b in this.children) this.children[b].parent = a;
      a.children = a.children.concat(this.children);
      a.parent = this;
      this.children = [a];
      return a;
    },
    appendChild: function (a) {
      a.parent = this;
      this.children.push(a);
      return a;
    },
    getProperties: function () {
      var a = {},
        b;
      for (b in this)
        this.hasOwnProperty(b) &&
          "children" != b &&
          "parent" != b &&
          "_" != b[0] &&
          (a[b] = this[b]);
      return a;
    }
  };
  e.KNode = u;
  var r = function (a, b) {
    for (var c = a.size, m = [], d = [], f = 0; f < c * c; f++)
      a.schema[f] && !b.schema[f]
        ? d.push({ x: Math.floor(f / c), y: f % c })
        : a.schema[f] != b.schema[f] &&
          m.push({ x: Math.floor(f / c), y: f % c, c: b.schema[f] });
    return { add: m, remove: d };
  };
  p = function (a, b, c) {
    this.kifu = a;
    this.node = this.kifu.root;
    this.allow_illegal = c || !1;
    this.game = new e.Game(
      this.kifu.size,
      this.allow_illegal ? "NONE" : "KO",
      this.allow_illegal,
      this.allow_illegal
    );
    this.path = { m: 0 };
    this.kifu.info.HA && 1 < this.kifu.info.HA && (this.game.turn = e.W);
    this.change = y(this.game, this.node, !0);
    this.rememberPath = b ? !0 : !1;
  };
  var w = function (a, b) {
      var c = [],
        m;
      for (m in a) {
        var d = !0;
        for (var f in b)
          if (a[m].x == b[f].x && a[m].y == b[f].y) {
            d = !1;
            break;
          }
        d && c.push(a[m]);
      }
      return c;
    },
    y = function (a, b, c) {
      b.parent && (b.parent._last_selected = b.parent.children.indexOf(b));
      if (b.move != k) {
        if (b.move.pass) return a.pass(b.move.c), { add: [], remove: [] };
        a = a.play(b.move.x, b.move.y, b.move.c);
        if ("number" == typeof a) throw new x(a, b);
        for (var m in a)
          if (a[m].x == b.move.x && a[m].y == b.move.y)
            return { add: [], remove: a };
        return { add: [b.move], remove: a };
      }
      c || a.pushPosition();
      c = [];
      var d = [];
      if (b.setup != k)
        for (m in b.setup)
          b.setup[m].c
            ? (a.setStone(b.setup[m].x, b.setup[m].y, b.setup[m].c),
              c.push(b.setup[m]))
            : (a.removeStone(b.setup[m].x, b.setup[m].y), d.push(b.setup[m]));
      b.turn && (a.turn = b.turn);
      return { add: c, remove: d };
    },
    z = function (a) {
      a === k && this.rememberPath && (a = this.node._last_selected);
      a = a || 0;
      var b = this.node.children[a];
      if (!b) return !1;
      if(typeof b.setup == "undefined") {
        b.step = this.path.m + 1;
      }
      var c = y(this.game, b);
      if(typeof b.setup == "undefined") {
        this.path.m++;
      }
      1 < this.node.children.length && (this.path[this.path.m] = a);
      this.node = b;
      return c;
    },
    B = function () {
      if (!this.node.parent) return !1;
      this.node = this.node.parent;
      this.game.popPosition();
      this.node.turn && (this.game.turn = this.node.turn);
      this.path[this.path.m] !== k && delete this.path[this.path.m];
      this.path.m--;
      return !0;
    },
    C = function () {
      this.game.firstPosition();
      this.node = this.kifu.root;
      this.path = { m: 0 };
      this.kifu.info.HA && 1 < this.kifu.info.HA && (this.game.turn = e.W);
      this.change = y(this.game, this.node, !0);
    };
  p.prototype = {
    constructor: p,
    next: function (a) {
      this.change = z.call(this, a);
      return this;
    },
    last: function () {
      var a;
      for (this.change = { add: [], remove: [] }; (a = z.call(this)); ) {
        var b = this.change;
        b.add = w(b.add, a.remove).concat(a.add);
        b.remove = w(b.remove, a.add).concat(a.remove);
      }
      return this;
    },
    previous: function () {
      var a = this.game.getPosition();
      B.call(this);
      this.change = r(a, this.game.getPosition());
      return this;
    },
    first: function () {
      var a = this.game.getPosition();
      C.call(this);
      this.change = r(a, this.game.getPosition());
      return this;
    },
    goTo: function (a) {
      if (a === k) return this;
      var b = this.game.getPosition();
      C.call(this);
      for (var c = 0; c < a.m && z.call(this, a[c + 1]); c++);
      this.change = r(b, this.game.getPosition());
      return this;
    },
    previousFork: function () {
      for (
        var a = this.game.getPosition();
        B.call(this) && 1 == this.node.children.length;

      );
      this.change = r(a, this.game.getPosition());
      return this;
    },
    getPosition: function () {
      return this.game.getPosition();
    },
    allowIllegalMoves: function (a) {
      a
        ? ((this.game.allow_rewrite = !0),
          (this.game.allow_suicide = !0),
          (this.repeating = "NONE"))
        : ((this.game.allow_rewrite = !1),
          (this.game.allow_suicide = !1),
          (this.repeating = "KO"));
    }
  };
  e.KifuReader = p;
  var x = function (a, b) {
    this.name = "InvalidMoveError";
    this.message = "Invalid move in kifu detected. ";
    if (b.move && b.move.c !== k && b.move.x !== k && b.move.y !== k) {
      var c = b.move.x;
      7 < b.move.x && c++;
      String.fromCharCode(c + 65);
      this.message +=
        "Trying to play " +
        (b.move.c == e.WHITE ? "white" : "black") +
        " move on " +
        String.fromCharCode(b.move.x + 65) +
        (19 - b.move.y);
    } else this.message += "Move object doesn't contain arbitrary attributes.";
    if (a)
      switch (a) {
        case 1:
          this.message += ", but these coordinates are not on board.";
          break;
        case 2:
          this.message += ", but there already is a stone.";
          break;
        case 3:
          this.message += ", but this move is a suicide.";
          break;
        case 4:
          this.message += ", but this position already occured.";
      }
    else this.message += ".";
  };
  x.prototype = Error();
  x.prototype.constructor = x;
  e.InvalidMoveError = x;
  e.i18n.en.show = "show";
  e.i18n.en["res-show-tip"] = "Click to show result.";
})(WGo);
(function (e, k) {
  e.SGF = {};
  k = function (b, c, d, a, f, m) {
    c = m == c ? "black" : "white";
    d.info[c] = d.info[c] || {};
    d.info[c][b] = f[0];
  };
  var g = (e.SGF.properties = {});
  g.B = g.W = function (b, c, d, a) {
    c.move =
      !d[0] || (19 >= b.size && "tt" == d[0])
        ? { pass: !0, c: "B" == a ? e.B : e.W }
        : {
            x: d[0].charCodeAt(0) - 97,
            y: d[0].charCodeAt(1) - 97,
            c: "B" == a ? e.B : e.W
          };
  };
  g.AB = g.AW = function (b, c, d, a) {
    for (var f in d)
      c.addSetup({
        x: d[f].charCodeAt(0) - 97,
        y: d[f].charCodeAt(1) - 97,
        c: "AB" == a ? e.B : e.W
      });
  };
  g.AE = function (b, c, d) {
    for (var a in d)
      c.addSetup({ x: d[a].charCodeAt(0) - 97, y: d[a].charCodeAt(1) - 97 });
  };
  g.PL = function (b, c, d) {
    c.turn = "b" == d[0] || "B" == d[0] ? e.B : e.W;
  };
  g.C = function (b, c, d) {
    c.comment = d.join();
  };
  g.LB = function (b, c, d) {
    for (var a in d)
      c.addMarkup({
        x: d[a].charCodeAt(0) - 97,
        y: d[a].charCodeAt(1) - 97,
        type: "LB",
        text: d[a].substr(3)
      });
  };
  g.CR =
    g.SQ =
    g.TR =
    g.SL =
    g.MA =
      function (b, c, d, a) {
        for (var f in d)
          c.addMarkup({
            x: d[f].charCodeAt(0) - 97,
            y: d[f].charCodeAt(1) - 97,
            type: a
          });
      };
  g.SZ = function (b, c, d) {
    b.size = parseInt(d[0]);
  };
  g.BR = g.WR = k.bind(this, "rank", "BR");
  g.PB = g.PW = k.bind(this, "name", "PB");
  g.BT = g.WT = k.bind(this, "team", "BT");
  g.TM = function (b, c, d, a) {
    b.info[a] = d[0];
    c.BL = d[0];
    c.WL = d[0];
  };
  var h = /\(|\)|(;(\s*[A-Z]+(\s*((\[\])|(\[(.|\s)*?([^\\]\]))))+)*)/g,
    l = /[A-Z]+(\s*((\[\])|(\[(.|\s)*?([^\\]\]))))+/g,
    f = /[A-Z]+/,
    d = /(\[\])|(\[(.|\s)*?([^\\]\]))/g;
  e.SGF.parse = function (b) {
    var c = [],
      g = new e.Kifu(),
      a = null;
    b = b.match(h);
    for (var p in b)
      if ("(" == b[p]) c.push(a);
      else if (")" == b[p]) a = c.pop();
      else {
        a && g.nodeCount++;
        a = a ? a.appendChild(new e.KNode()) : g.root;
        var m = b[p].match(l) || [];
        g.propertyCount += m.length;
        for (var v in m) {
          var k = f.exec(m[v])[0];
          var r = m[v].match(d);
          for (var w in r)
            r[w] = r[w].substring(1, r[w].length - 1).replace(/\\(?!\\)/g, "");
          if (e.SGF.properties[k]) e.SGF.properties[k](g, a, r, k);
          else
            1 >= r.length && (r = r[0]),
              a.parent ? (a[k] = r) : (g.info[k] = r);
        }
      }
    return g;
  };
})(WGo);
(function (e) {
  var k = function (a, b) {
    this.name = "FileError";
    this.message =
      1 == b
        ? "File '" + a + "' is empty."
        : 2 == b
        ? "Network error. It is not possible to read '" + a + "'."
        : "File '" + a + "' hasn't been found on server.";
  };
  k.prototype = Error();
  k.prototype.constructor = k;
  e.FileError = k;
  var g = (e.loadFromUrl = function (a, b) {
      var c = new XMLHttpRequest();
      c.onreadystatechange = function () {
        if (4 == c.readyState)
          if (200 == c.status) {
            if (0 == c.responseText.length) throw new k(a, 1);
            b(c.responseText);
          } else throw new k(a);
      };
      try {
        c.open("GET", a, !0), c.send();
      } catch (r) {
        throw new k(a, 2);
      }
    }),
    h = function (a) {
      a.change && this.board.update(a.change);
      this.temp_marks && this.board.removeObject(this.temp_marks);
      this.numb_marks && this.board.removeObject(this.numb_marks);
      this.numb_marks = [];
      var b = [];
      this.notification();
      a.node.move &&
        this.config.markLastMove &&
        (a.node.move.pass
          ? this.notification(e.t((a.node.move.c == e.B ? "b" : "w") + "pass"))
          : b.push({ type: "cross", x: a.node.move.x, y: a.node.move.y }));
      if (1 < a.node.children.length && this.config.displayVariations)
        for (var c = 0; c < a.node.children.length; c++)
          a.node.children[c].move &&
            !a.node.children[c].move.pass &&
            b.push({
              type: "LB",
              text: String.fromCharCode(65 + c),
              x: a.node.children[c].move.x,
              y: a.node.children[c].move.y,
              c: this.board.theme.variationColor || "rgba(0,32,128,0.8)"
            });
      if (a.node.markup) {
        for (c in a.node.markup)
          for (var d = 0; d < b.length; d++)
            a.node.markup[c].x == b[d].x &&
              a.node.markup[c].y == b[d].y &&
              (b.splice(d, 1), d--);
        b = b.concat(a.node.markup);
      }
      if(a.node.parent) {
        n(a.node, this.numb_marks)
      }
      this.temp_marks = b;
      this.board.addObject(b);
      if(this.config.showNumMove) {
        this.board.addObject(this.numb_marks);
      }
    },
    n = function(a, b) {
      if(a.step !==0 && a.move) {
        b.push({
          type: "LB",
          text: a.step,
          x: a.move.x,
          y: a.move.y
        })
      }
      if(a.parent) {
        n(a.parent, b)
      }
    },
    l = function (a) {
      this.board.setSize(a.kifu.size);
      this.board.removeAllObjects();
      this.config.enableWheel && this.setWheel(!0);
    },
    f = function (a, b) {
      return a == b.element || a == b.element
        ? !1
        : a._wgo_scrollable ||
          (a.scrollHeight > a.offsetHeight &&
            "auto" == window.getComputedStyle(a).overflow)
        ? !0
        : f(a.parentNode, b);
    },
    d = function (a) {
      var b = a.wheelDelta || -1 * a.detail;
      return f(a.target, this)
        ? !0
        : 0 > b
        ? (this.next(),
          this.config.lockScroll && a.preventDefault && a.preventDefault(),
          !this.config.lockScroll)
        : 0 < b
        ? (this.previous(),
          this.config.lockScroll && a.preventDefault && a.preventDefault(),
          !this.config.lockScroll)
        : !0;
    },
    b = function (a) {
      if (document.querySelector("input:focus, textarea:focus")) return !0;
      switch (a.keyCode) {
        case 39:
          this.next();
          break;
        case 37:
          this.previous();
          break;
        default:
          return !0;
      }
      this.config.lockScroll && a.preventDefault && a.preventDefault();
      return !this.config.lockScroll;
    },
    c = function (a, b) {
      if (!this.kifuReader || !this.kifuReader.node) return !1;
      for (var c in this.kifuReader.node.children)
        if (
          this.kifuReader.node.children[c].move &&
          this.kifuReader.node.children[c].move.x == a &&
          this.kifuReader.node.children[c].move.y == b
        ) {
          this.next(c);
          break;
        }
    },
    q = function (a) {
      this.config = a;
      for (var b in q.default)
        void 0 === this.config[b] &&
          void 0 !== q.default[b] &&
          (this.config[b] = q.default[b]);
      this.element = document.createElement("div");
      this.board = new e.Board(this.element, this.config.board);
      this.init();
      this.initGame();
    };
  q.prototype = {
    constructor: q,
    init: function () {
      this.kifu = null;
      this.listeners = {
        kifuLoaded: [l.bind(this)],
        update: [h.bind(this)],
        frozen: [],
        unfrozen: []
      };
      this.config.kifuLoaded &&
        this.addEventListener("kifuLoaded", this.config.kifuLoaded);
      this.config.update && this.addEventListener("update", this.config.update);
      this.config.frozen && this.addEventListener("frozen", this.config.frozen);
      this.config.unfrozen &&
        this.addEventListener("unfrozen", this.config.unfrozen);
      this.board.addEventListener("click", c.bind(this));
      this.element.addEventListener("click", this.focus.bind(this));
      this.focus();
    },
    initGame: function () {
      this.config.sgf
        ? this.loadSgf(this.config.sgf, this.config.move)
        : this.config.json
        ? this.loadJSON(this.config.json, this.config.move)
        : this.config.sgfFile &&
          this.loadSgfFromFile(this.config.sgfFile, this.config.move);
    },
    update: function (a) {
      this.kifuReader &&
        this.kifuReader.change &&
        ((a = {
          type: "update",
          op: a,
          target: this,
          node: this.kifuReader.node,
          position: this.kifuReader.getPosition(),
          path: this.kifuReader.path,
          change: this.kifuReader.change
        }),
        this.dispatchEvent(a));
    },
    forceUpdate: function (a) {
        (a = {
          type: "update",
          op: a,
          target: this,
          node: this.kifuReader.node,
          position: this.kifuReader.getPosition(),
          path: this.kifuReader.path,
          change: this.kifuReader.change
        },
        this.dispatchEvent(a));
    },
    loadKifu: function (a, b) {
      this.kifu = a;
      this.kifuReader = new e.KifuReader(
        this.kifu,
        this.config.rememberPath,
        this.config.allowIllegalMoves
      );
      this.dispatchEvent({ type: "kifuLoaded", target: this, kifu: this.kifu });
      this.update("init");
      b && this.goTo(b);
    },
    hideLastMove(){
      this.config.markLastMove = false;
      this.forceUpdate({});
    },
    setShowNumMove: function(showNumMove, a) {
      this.config.showNumMove = showNumMove;
      this.config.markLastMove = !showNumMove;
      this.forceUpdate(a);
    },
    loadSgf: function (a, b) {
      try {
        this.loadKifu(e.Kifu.fromSgf(a), b);
      } catch (u) {
        this.error(u);
      }
    },
    loadJSON: function (a, b) {
      try {
        this.loadKifu(e.Kifu.fromJGO(a), b);
      } catch (u) {
        this.error(u);
      }
    },
    loadSgfFromFile: function (a, b) {
      var c = this;
      try {
        g(a, function (a) {
          c.loadSgf(a, b);
        });
      } catch (r) {
        this.error(r);
      }
    },
    addEventListener: function (a, b) {
      this.listeners[a] = this.listeners[a] || [];
      this.listeners[a].push(b);
    },
    removeEventListener: function (a, b) {
      this.listeners[a] &&
        ((b = this.listeners[a].indexOf(b)),
        -1 != b && this.listeners[a].splice(b, 1));
    },
    dispatchEvent: function (a) {
      if (this.listeners[a.type])
        for (var b in this.listeners[a.type]) this.listeners[a.type][b](a);
    },
    notification: function (a) {
      console && a && console.log(a);
    },
    help: function (a) {
      console && console.log(a);
    },
    error: function (a) {
      if (!e.ERROR_REPORT) throw a;
      console && console.log(a);
    },
    next: function (a) {
      if (!this.frozen && this.kifu)
        try {
          this.kifuReader.next(a), this.update();
        } catch (v) {
          this.error(v);
        }
    },
    previous: function () {
      if (!this.frozen && this.kifu)
        try {
          this.kifuReader.previous(), this.update();
        } catch (m) {
          this.error(m);
        }
    },
    last: function () {
      if (!this.frozen && this.kifu)
        try {
          this.kifuReader.last(), this.update();
        } catch (m) {
          this.error(m);
        }
    },
    first: function () {
      if (!this.frozen && this.kifu)
        try {
          this.kifuReader.first(), this.update();
        } catch (m) {
          this.error(m);
        }
    },
    goTo: function (a) {
      if (!this.frozen && this.kifu) {
        "function" == typeof a && (a = a.call(this));
        if ("number" == typeof a) {
          var b = e.clone(this.kifuReader.path);
          b.m = a || 0;
        } else b = a;
        try {
          this.kifuReader.goTo(b), this.update();
        } catch (u) {
          this.error(u);
        }
      }
    },
    getGameInfo: function () {
      if (!this.kifu) return null;
      var a = {},
        b;
      for (b in this.kifu.info)
        -1 != e.Kifu.infoList.indexOf(b) &&
          (e.Kifu.infoFormatters[b]
            ? (a[e.t(b)] = e.Kifu.infoFormatters[b](this.kifu.info[b]))
            : (a[e.t(b)] = e.filterHTML(this.kifu.info[b])));
      return a;
    },
    setFrozen: function (a) {
      this.frozen = a;
      this.dispatchEvent({
        type: this.frozen ? "frozen" : "unfrozen",
        target: this
      });
    },
    appendTo: function (a) {
      a.appendChild(this.element);
    },
    focus: function () {
      this.config.enableKeys && this.setKeys(!0);
    },
    setKeys: function (a) {
      document.onkeydown = a ? b.bind(this) : null;
    },
    setWheel: function (a) {
      !this._wheel_listener && a
        ? ((this._wheel_listener = d.bind(this)),
          (a =
            void 0 !== document.onmousewheel ? "mousewheel" : "DOMMouseScroll"),
          this.element.addEventListener(a, this._wheel_listener))
        : this._wheel_listener &&
          !a &&
          ((a =
            void 0 !== document.onmousewheel ? "mousewheel" : "DOMMouseScroll"),
          this.element.removeEventListener(a, this._wheel_listener),
          delete this._wheel_listener);
    },
    setCoordinates: function (a) {
      !this.coordinates && a
        ? (this.board.setSection(-0.5, -0.5, -0.5, -0.5),
          this.board.addCustomObject(e.Board.coordinates))
        : this.coordinates &&
          !a &&
          (this.board.setSection(0, 0, 0, 0),
          this.board.removeCustomObject(e.Board.coordinates));
      this.coordinates = a;
    }
  };
  q.default = {
    sgf: void 0,
    json: void 0,
    sgfFile: void 0,
    move: void 0,
    board: {},
    enableWheel: !0,
    lockScroll: !0,
    enableKeys: !0,
    rememberPath: !0,
    kifuLoaded: void 0,
    update: void 0,
    frozen: void 0,
    unfrozen: void 0,
    allowIllegalMoves: !1,
    markLastMove: !0,
    displayVariations: !0,
    showNumMove: !1
  };
  e.Player = q;
  var a = {
      "about-text":
        "<h1>WGo.js Player 2.0</h1><p>WGo.js Player is extension of WGo.js, HTML5 library for purposes of game of go. It allows to replay go game records and it has many features like score counting. It is also designed to be easily extendable.</p><p>WGo.js is open source licensed under <a href='http://en.wikipedia.org/wiki/MIT_License' target='_blank'>MIT license</a>. You can use and modify any code from this project.</p><p>You can find more information at <a href='http://wgo.waltheri.net/player' target='_blank'>wgo.waltheri.net/player</a></p><p>Copyright &copy; 2013 Jan Prokop</p>",
      black: "Black",
      white: "White",
      DT: "Date",
      KM: "Komi",
      HA: "Handicap",
      AN: "Annotations",
      CP: "Copyright",
      GC: "Game comments",
      GN: "Game name",
      ON: "Fuseki",
      OT: "Overtime",
      TM: "Basic time",
      RE: "Result",
      RO: "Round",
      RU: "Rules",
      US: "Recorder",
      PC: "Place",
      EV: "Event",
      SO: "Source",
      none: "none",
      bpass: "Black passed.",
      wpass: "White passed."
    },
    p;
  for (p in a) e.i18n.en[p] = a[p];
})(WGo);
(function (e) {
  var k = 0,
    g = function (f, d, b) {
      var c = {};
      c.element = document.createElement("div");
      c.element.className = "wgo-player-" + f;
      c.wrapper = document.createElement("div");
      c.wrapper.className = "wgo-player-" + f + "-wrapper";
      c.element.appendChild(c.wrapper);
      d.appendChild(c.element);
      b || (c.element.style.display = "none");
      return c;
    },
    h = function (f) {
      var d;
      if (
        (d = this.currentLayout.layout
          ? this.currentLayout.layout[f]
          : this.currentLayout[f])
      ) {
        this.regions[f].element.style.display = "block";
        d.constructor != Array && (d = [d]);
        for (var b in d)
          this.components[d[b]] ||
            (this.components[d[b]] = new l.component[d[b]](this)),
            this.components[d[b]].appendTo(this.regions[f].wrapper),
            (this.components[d[b]]._detachFromPlayer = !1);
      } else this.regions[f].element.style.display = "none";
    },
    l = e.extendClass(e.Player, function (f, d) {
      this.config = d;
      for (var b in l.default)
        void 0 === this.config[b] &&
          void 0 !== l.default[b] &&
          (this.config[b] = l.default[b]);
      for (b in e.Player.default)
        void 0 === this.config[b] &&
          void 0 !== e.Player.default[b] &&
          (this.config[b] = e.Player.default[b]);
      this.element = f;
      this.element.innerHTML = "";
      this.classes =
        (this.element.className ? this.element.className + " " : "") +
        "wgo-player-main";
      this.element.className = this.classes;
      this.element.id || (this.element.id = "wgo_" + k++);
      this.dom = {};
      this.dom.center = document.createElement("div");
      this.dom.center.className = "wgo-player-center";
      this.dom.board = document.createElement("div");
      this.dom.board.className = "wgo-player-board";
      this.regions = {};
      this.regions.left = g("left", this.element);
      this.element.appendChild(this.dom.center);
      this.regions.right = g("right", this.element);
      this.regions.top = g("top", this.dom.center);
      this.dom.center.appendChild(this.dom.board);
      this.regions.bottom = g("bottom", this.dom.center);
      this.board = new e.Board(this.dom.board, this.config.board);
      this.init();
      this.components = {};
      window.addEventListener(
        "resize",
        function () {
          this.noresize || this.updateDimensions();
        }.bind(this)
      );
      this.updateDimensions();
      this.initGame();
    });
  l.prototype.appendTo = function (f) {
    f.appendChild(this.element);
    this.updateDimensions();
  };
  l.prototype.updateDimensions = function () {
    for (
      var f = window.getComputedStyle(this.element), d = [];
      this.element.firstChild;

    )
      d.push(this.element.firstChild),
        this.element.removeChild(this.element.firstChild);
    var b = parseInt(f.width),
      c = parseInt(f.height),
      e = parseInt(f.maxHeight) || 0;
    for (f = 0; f < d.length; f++) this.element.appendChild(d[f]);
    if (b != this.width || c != this.height || e != this.maxHeight) {
      this.width = b;
      this.height = c;
      this.maxHeight = e;
      a: if (((d = this.config.layout), d.constructor == Array)) {
        b = this.height || this.maxHeight;
        for (c = 0; c < d.length; c++)
          if (
            !d[c].conditions ||
            !(
              (d[c].conditions.minWidth &&
                !(d[c].conditions.minWidth <= this.width)) ||
              (d[c].conditions.minHeight &&
                b &&
                !(d[c].conditions.minHeight <= b)) ||
              (d[c].conditions.maxWidth &&
                !(d[c].conditions.maxWidth >= this.width)) ||
              (d[c].conditions.maxHeight &&
                b &&
                !(d[c].conditions.maxHeight >= b)) ||
              (d[c].conditions.custom && !d[c].conditions.custom.call(this))
            )
          ) {
            d = d[c];
            break a;
          }
        d = void 0;
      }
      if ((this.currentLayout = d) && this.lastLayout != this.currentLayout) {
        this.element.className = this.currentLayout.className
          ? this.classes + " " + this.currentLayout.className
          : this.classes;
        for (var a in this.components)
          this.components[a]._detachFromPlayer = !0;
        h.call(this, "left");
        h.call(this, "right");
        h.call(this, "top");
        h.call(this, "bottom");
        for (a in this.components)
          this.components[a]._detachFromPlayer &&
            this.components[a].element.parentNode &&
            this.components[a].element.parentNode.removeChild(
              this.components[a].element
            );
        this.lastLayout = this.currentLayout;
      }
      d = this.dom.board.clientWidth;
      (a = this.height || this.maxHeight) &&
        (a -=
          this.regions.top.element.offsetHeight +
          this.regions.bottom.element.offsetHeight);
      a && a < d
        ? a != this.board.height && this.board.setHeight(a)
        : d != this.board.width && this.board.setWidth(d);
      d = a - d;
      0 < d
        ? ((this.dom.board.style.height = a + "px"),
          (this.dom.board.style.paddingTop = d / 2 + "px"))
        : ((this.dom.board.style.height = "auto"),
          (this.dom.board.style.paddingTop = "0"));
      this.regions.left.element.style.height =
        this.dom.center.offsetHeight + "px";
      this.regions.right.element.style.height =
        this.dom.center.offsetHeight + "px";
      for (f in this.components)
        this.components[f].updateDimensions &&
          this.components[f].updateDimensions();
    }
  };
  l.prototype.showMessage = function (f, d, b) {
    this.info_overlay = document.createElement("div");
    this.info_overlay.style.width = this.element.offsetWidth + "px";
    this.info_overlay.style.height = this.element.offsetHeight + "px";
    this.info_overlay.className = "wgo-info-overlay";
    this.element.appendChild(this.info_overlay);
    var c = document.createElement("div");
    c.className = "wgo-info-message";
    c.innerHTML = f;
    f = document.createElement("div");
    f.className = "wgo-info-close";
    b || (f.innerHTML = e.t("BP:closemsg"));
    c.appendChild(f);
    this.info_overlay.appendChild(c);
    d
      ? this.info_overlay.addEventListener("click", function (b) {
          d(b);
        })
      : b ||
        this.info_overlay.addEventListener(
          "click",
          function (b) {
            this.hideMessage();
          }.bind(this)
        );
    this.setFrozen(!0);
  };
  l.prototype.hideMessage = function () {
    this.element.removeChild(this.info_overlay);
    this.setFrozen(!1);
  };
  l.prototype.error = function (f) {
    if (!e.ERROR_REPORT) throw f;
    switch (f.name) {
      case "InvalidMoveError":
        this.showMessage(
          "<h1>" +
            f.name +
            "</h1><p>" +
            f.message +
            '</p><p>If this message isn\'t correct, please report it by clicking <a href="#">here</a>, otherwise contact maintainer of this site.</p>'
        );
        break;
      case "FileError":
        this.showMessage(
          "<h1>" +
            f.name +
            "</h1><p>" +
            f.message +
            "</p><p>Please contact maintainer of this site. Note: it is possible to read files only from this host.</p>"
        );
        break;
      default:
        this.showMessage(
          "<h1>" +
            f.name +
            "</h1><p>" +
            f.message +
            "</p><pre>" +
            f.stacktrace +
            '</pre><p>Please contact maintainer of this site. You can also report it <a href="#">here</a>.</p>'
        );
    }
  };
  l.component = {};
  l.layouts = {
    one_column: { top: [], bottom: [] },
    no_comment: { top: [], bottom: [] },
    right_top: { top: [], right: [] },
    right: { right: [] },
    minimal: { bottom: [] }
  };
  l.dynamicLayout = [
    {
      conditions: { minWidth: 650 },
      layout: l.layouts.right_top,
      className: "wgo-twocols wgo-large"
    },
    {
      conditions: { minWidth: 550, minHeight: 600 },
      layout: l.layouts.one_column,
      className: "wgo-medium"
    },
    {
      conditions: { minWidth: 350 },
      layout: l.layouts.no_comment,
      className: "wgo-small"
    },
    { layout: l.layouts.no_comment, className: "wgo-xsmall" }
  ];
  l.default = { layout: l.dynamicLayout };
  e.i18n.en["BP:closemsg"] = "click anywhere to close this window";
  l.attributes = {
    "data-wgo": function (f) {
      f && ("(" == f[0] ? (this.sgf = f) : (this.sgfFile = f));
    },
    "data-wgo-board": function (f) {
      this.board = eval("({" + f + "})");
    },
    "data-wgo-onkifuload": function (f) {
      this.kifuLoaded = new Function(f);
    },
    "data-wgo-onupdate": function (f) {
      this.update = new Function(f);
    },
    "data-wgo-onfrozen": function (f) {
      this.frozen = new Function(f);
    },
    "data-wgo-onunfrozen": function (f) {
      this.unfrozen = new Function(f);
    },
    "data-wgo-layout": function (f) {
      this.layout = eval("({" + f + "})");
    },
    "data-wgo-enablewheel": function (f) {
      "false" == f.toLowerCase() && (this.enableWheel = !1);
    },
    "data-wgo-lockscroll": function (f) {
      "false" == f.toLowerCase() && (this.lockScroll = !1);
    },
    "data-wgo-enablekeys": function (f) {
      "false" == f.toLowerCase() && (this.enableKeys = !1);
    },
    "data-wgo-rememberpath": function (f) {
      "false" == f.toLowerCase() && (this.rememberPath = !1);
    },
    "data-wgo-allowillegal": function (f) {
      "false" != f.toLowerCase() && (this.allowIllegalMoves = !0);
    },
    "data-wgo-move": function (f) {
      var d = parseInt(f);
      isNaN(d) ? (this.move = eval("({" + f + "})")) : (this.move = d);
    },
    "data-wgo-marklastmove": function (f) {
      "false" == f.toLowerCase() && (this.markLastMove = !1);
    },
    "data-wgo-diagram": function (f) {
      f &&
        ("(" == f[0] ? (this.sgf = f) : (this.sgfFile = f),
        (this.enableWheel = this.enableKeys = this.markLastMove = !1),
        (this.layout = { top: [], right: [], left: [], bottom: [] }));
    }
  };
  e.BasicPlayer = l;
  window.addEventListener("load", function () {
    for (
      var f = document.querySelectorAll("[data-wgo],[data-wgo-diagram]"), d = 0;
      d < f.length;
      d++
    ) {
      var b = f[d];
      var c = {};
      for (var e = 0; e < b.attributes.length; e++) {
        var a = b.attributes[e];
        l.attributes[a.name] && l.attributes[a.name].call(c, a.value, a.name);
      }
      c = new l(b, c);
      b._wgo_player = c;
    }
  });
})(WGo);
(function (e, k) {
  k = function () {
    this.element = document.createElement("div");
  };
  k.prototype = {
    constructor: k,
    appendTo: function (e) {
      e.appendChild(this.element);
    },
    getWidth: function () {
      var e = window.getComputedStyle(this.element);
      return parseInt(e.width);
    },
    getHeight: function () {
      var e = window.getComputedStyle(this.element);
      return parseInt(e.height);
    },
    updateDimensions: function () {}
  };
  e.BasicPlayer.component.Component = k;
})(WGo);
(function () {
  var e = function (b) {
      this[b] = {};
      var c = this[b];
      c.box = document.createElement("div");
      c.box.className = "wgo-box-wrapper wgo-player-wrapper wgo-" + b;
      c.name = document.createElement("div");
      c.name.className = "wgo-box-title";
      c.name.innerHTML = b;
      c.box.appendChild(c.name);
      b = document.createElement("div");
      b.className = "wgo-player-info";
      c.box.appendChild(b);
      c.info = {};
      c.info.rank = k("rank");
      c.info.rank.val.innerHTML = "-";
      c.info.caps = k("caps");
      c.info.caps.val.innerHTML = "0";
      c.info.time = k("time");
      c.info.time.val.innerHTML = "--:--";
      b.appendChild(c.info.rank.wrapper);
      b.appendChild(c.info.caps.wrapper);
      b.appendChild(c.info.time.wrapper);
    },
    k = function (b) {
      var c = {};
      c.wrapper = document.createElement("div");
      c.wrapper.className = "wgo-player-info-box-wrapper";
      c.box = document.createElement("div");
      c.box.className = "wgo-player-info-box";
      c.wrapper.appendChild(c.box);
      c.title = document.createElement("div");
      c.title.className = "wgo-player-info-title";
      c.title.innerHTML = WGo.t(b);
      c.box.appendChild(c.title);
      c.val = document.createElement("div");
      c.val.className = "wgo-player-info-value";
      c.box.appendChild(c.val);
      return c;
    },
    g = function (b) {
      b = b.kifu.info || {};
      b.black
        ? ((this.black.name.innerHTML =
            WGo.filterHTML(b.black.name) || WGo.t("black")),
          (this.black.info.rank.val.innerHTML =
            WGo.filterHTML(b.black.rank) || "-"))
        : ((this.black.name.innerHTML = WGo.t("black")),
          (this.black.info.rank.val.innerHTML = "-"));
      b.white
        ? ((this.white.name.innerHTML =
            WGo.filterHTML(b.white.name) || WGo.t("white")),
          (this.white.info.rank.val.innerHTML =
            WGo.filterHTML(b.white.rank) || "-"))
        : ((this.white.name.innerHTML = WGo.t("white")),
          (this.white.info.rank.val.innerHTML = "-"));
      this.black.info.caps.val.innerHTML = "0";
      this.white.info.caps.val.innerHTML = "0";
      b.TM
        ? (this.setPlayerTime("black", b.TM), this.setPlayerTime("white", b.TM))
        : ((this.black.info.time.val.innerHTML = "--:--"),
          (this.white.info.time.val.innerHTML = "--:--"));
      this.updateDimensions();
    },
    h = function (b) {
      if (b.style.fontSize) {
        var c = parseInt(b.style.fontSize);
        b.style.fontSize = "";
        var d = window.getComputedStyle(b);
        d = parseInt(d.fontSize);
        b.style.fontSize = c + "px";
      } else (d = window.getComputedStyle(b)), (d = c = parseInt(d.fontSize));
      if (!(c == d && b.scrollHeight <= b.offsetHeight))
        if (b.scrollHeight > b.offsetHeight)
          for (c -= 2; b.scrollHeight > b.offsetHeight && 1 < c; )
            (b.style.fontSize = c + "px"), (c -= 2);
        else if (c < d) {
          for (c += 2; b.scrollHeight <= b.offsetHeight && c <= d; )
            (b.style.fontSize = c + "px"), (c += 2);
          b.scrollHeight > b.offsetHeight && (b.style.fontSize = c - 4 + "px");
        }
    },
    l = function (b) {
      b.node.BL && this.setPlayerTime("black", b.node.BL);
      b.node.WL && this.setPlayerTime("white", b.node.WL);
      void 0 !== b.position.capCount.black &&
        (this.black.info.caps.val.innerHTML = b.position.capCount.black);
      void 0 !== b.position.capCount.white &&
        (this.white.info.caps.val.innerHTML = b.position.capCount.white);
    },
    f = WGo.extendClass(WGo.BasicPlayer.component.Component, function (b) {
      this.super(b);
      this.element.className = "wgo-infobox";
      e.call(this, "white");
      e.call(this, "black");
      this.element.appendChild(this.white.box);
      this.element.appendChild(this.black.box);
      b.addEventListener("kifuLoaded", g.bind(this));
      b.addEventListener("update", l.bind(this));
    });
  f.prototype.setPlayerTime = function (b, c) {
    var d = Math.round(c) % 60;
    this[b].info.time.val.innerHTML =
      Math.floor(c / 60) + ":" + (10 > d ? "0" + d : d);
  };
  f.prototype.updateDimensions = function () {
    h(this.black.name);
    h(this.white.name);
  };
  var d = WGo.BasicPlayer.layouts;
  d.right_top.right.push("InfoBox");
  d.right.right.push("InfoBox");
  d.one_column.top.push("InfoBox");
  d.no_comment.top.push("InfoBox");
  WGo.i18n.en.rank = "Rank";
  WGo.i18n.en.caps = "Caps";
  WGo.i18n.en.time = "Time";
  WGo.BasicPlayer.component.InfoBox = f;
})(WGo);
(function (e, k) {
  var g = function (d) {
      var b = d.charCodeAt(0) - 97;
      0 > b && (b += 32);
      7 < b && b--;
      var c = d.charCodeAt(1) - 48;
      2 < d.length && (c = 10 * c + (d.charCodeAt(2) - 48));
      c = this.kifuReader.game.size - c;
      this._tmp_mark = { type: "MA", x: b, y: c };
      this.board.addObject(this._tmp_mark);
    },
    h = function () {
      this.board.removeObject(this._tmp_mark);
      delete this._tmp_mark;
    },
    l = function (d, b) {
      for (var c in d)
        d[c].className && "wgo-move-link" == d[c].className
          ? (d[c].addEventListener("mouseover", g.bind(b, d[c].innerHTML)),
            d[c].addEventListener("mouseout", h.bind(b)))
          : d[c].childNodes && d[c].childNodes.length && l(d[c].childNodes, b);
    },
    f = function (d, b) {
      var c = '<div class="wgo-info-list">';
      b && (c += '<div class="wgo-info-title">' + e.t("gameinfo") + "</div>");
      for (var f in d)
        c +=
          '<div class="wgo-info-item"><span class="wgo-info-label">' +
          f +
          '</span><span class="wgo-info-value">' +
          d[f] +
          "</span></div>";
      return c + "</div>";
    };
  k = e.extendClass(e.BasicPlayer.component.Component, function (d) {
    this.super(d);
    this.player = d;
    this.element.className = "wgo-commentbox";
    this.box = document.createElement("div");
    this.box.className = "wgo-box-wrapper wgo-comments-wrapper";
    this.element.appendChild(this.box);
    this.comments_title = document.createElement("div");
    this.comments_title.className = "wgo-box-title";
    this.comments_title.innerHTML = e.t("comments");
    this.box.appendChild(this.comments_title);
    this.comments = document.createElement("div");
    this.comments.className = "wgo-comments-content";
    this.box.appendChild(this.comments);
    this.help = document.createElement("div");
    this.help.className = "wgo-help";
    this.help.style.display = "none";
    this.comments.appendChild(this.help);
    this.notification = document.createElement("div");
    this.notification.className = "wgo-notification";
    this.notification.style.display = "none";
    this.comments.appendChild(this.notification);
    this.comment_text = document.createElement("div");
    this.comment_text.className = "wgo-comment-text";
    this.comments.appendChild(this.comment_text);
    d.addEventListener(
      "kifuLoaded",
      function (b) {
        b.kifu.hasComments()
          ? ((this.comments_title.innerHTML = e.t("comments")),
            (this.element.className = "wgo-commentbox"),
            (this._update = function (b) {
              this.setComments(b);
            }.bind(this)),
            d.addEventListener("update", this._update))
          : ((this.comments_title.innerHTML = e.t("gameinfo")),
            (this.element.className = "wgo-commentbox wgo-gameinfo"),
            this._update &&
              (d.removeEventListener("update", this._update),
              delete this._update),
            (this.comment_text.innerHTML = f(b.target.getGameInfo())));
      }.bind(this)
    );
    d.notification = function (b) {
      b
        ? ((this.notification.style.display = "block"),
          (this.notification.innerHTML = b),
          (this.is_notification = !0))
        : ((this.notification.style.display = "none"),
          (this.is_notification = !1));
    }.bind(this);
    d.help = function (b) {
      b
        ? ((this.help.style.display = "block"),
          (this.help.innerHTML = b),
          (this.is_help = !0))
        : ((this.help.style.display = "none"), (this.is_help = !1));
    }.bind(this);
  });
  k.prototype.setComments = function (d) {
    this.player._tmp_mark && h.call(this.player);
    var b = "";
    d.node.parent || (b = f(d.target.getGameInfo(), !0));
    this.comment_text.innerHTML =
      b +
      this.getCommentText(
        d.node.comment,
        this.player.config.formatNicks,
        this.player.config.formatMoves
      );
    this.player.config.formatMoves &&
      this.comment_text.childNodes &&
      this.comment_text.childNodes.length &&
      l(this.comment_text.childNodes, this.player);
  };
  k.prototype.getCommentText = function (d, b, c) {
    return d
      ? ((d = "<p>" + e.filterHTML(d).replace(/\n/g, "</p><p>") + "</p>"),
        b &&
          (d = d.replace(
            /(<p>)([^:]{3,}:)\s/g,
            '<p><span class="wgo-comments-nick">$2</span> '
          )),
        c &&
          (d = d.replace(
            /\b[a-zA-Z]1?\d\b/g,
            '<a href="javascript:void(0)" class="wgo-move-link">$&</a>'
          )),
        d)
      : "";
  };
  e.BasicPlayer.default.formatNicks = !0;
  e.BasicPlayer.default.formatMoves = !0;
  e.BasicPlayer.attributes["data-wgo-formatnicks"] = function (d) {
    "false" == d.toLowerCase() && (this.formatNicks = !1);
  };
  e.BasicPlayer.attributes["data-wgo-formatmoves"] = function (d) {
    "false" == d.toLowerCase() && (this.formatMoves = !1);
  };
  e.BasicPlayer.layouts.right_top.right.push("CommentBox");
  e.BasicPlayer.layouts.right.right.push("CommentBox");
  e.BasicPlayer.layouts.one_column.bottom.push("CommentBox");
  e.i18n.en.comments = "Comments";
  e.i18n.en.gameinfo = "Game info";
  e.BasicPlayer.component.CommentBox = k;
})(WGo);
(function (e, k) {
  var g = e.extendClass(e.BasicPlayer.component.Component, function (a) {
    this.super(a);
    this.widgets = [];
    this.element.className = "wgo-player-control";
    this.iconBar = document.createElement("div");
    this.iconBar.className = "wgo-control-wrapper";
    this.element.appendChild(this.iconBar);
    var b;
    for (b in g.widgets) {
      var c = new g.widgets[b].constructor(a, g.widgets[b].args);
      c.appendTo(this.iconBar);
      this.widgets.push(c);
    }
  });
  g.prototype.updateDimensions = function () {
    this.element.className =
      340 > this.element.clientWidth
        ? "wgo-player-control wgo-340"
        : 440 > this.element.clientWidth
        ? "wgo-player-control wgo-440"
        : "wgo-player-control";
  };
  var h = (e.BasicPlayer.control = {}),
    l = function (a) {
      a.node.parent || this.disabled
        ? a.node.parent && this.disabled && this.enable()
        : this.disable();
    },
    f = function (a) {
      a.node.children.length || this.disabled
        ? a.node.children.length && this.disabled && this.enable()
        : this.disable();
    },
    d = function (a) {
      (this._disabled = this.disabled) || this.disable();
    },
    b = function (a) {
      this._disabled || this.enable();
      delete this._disabled;
    };
  h.Widget = function (a, b) {
    this.element = this.element || document.createElement(b.type || "div");
    this.element.className = "wgo-widget-" + b.name;
    this.init(a, b);
  };
  h.Widget.prototype = {
    constructor: h.Widget,
    init: function (a, b) {
      b && (b.disabled && this.disable(), b.init && b.init.call(this, a));
    },
    appendTo: function (a) {
      a.appendChild(this.element);
    },
    disable: function () {
      this.disabled = !0;
      -1 == this.element.className.search("wgo-disabled") &&
        (this.element.className += " wgo-disabled");
    },
    enable: function () {
      this.disabled = !1;
      this.element.className = this.element.className.replace(
        " wgo-disabled",
        ""
      );
      this.element.disabled = "";
    }
  };
  h.Group = e.extendClass(h.Widget, function (a, b) {
    this.element = document.createElement("div");
    this.element.className = "wgo-ctrlgroup wgo-ctrlgroup-" + b.name;
    var c;
    for (c in b.widgets) {
      var d = new b.widgets[c].constructor(a, b.widgets[c].args);
      d.appendTo(this.element);
    }
  });
  h.Clickable = e.extendClass(h.Widget, function (a, b) {
    this.super(a, b);
  });
  h.Clickable.prototype.init = function (a, b) {
    var c = this;
    var d = b.togglable
      ? function () {
          c.disabled || (b.click.call(c, a) ? c.select() : c.unselect());
        }
      : function () {
          c.disabled || b.click.call(c, a);
        };
    this.element.addEventListener("click", d);
    this.element.addEventListener("touchstart", function (a) {
      a.preventDefault();
      d();
      b.multiple &&
        ((c._touch_i = 0),
        (c._touch_int = window.setInterval(function () {
          500 < c._touch_i && d();
          c._touch_i += 100;
        }, 100)));
      return !1;
    });
    b.multiple &&
      this.element.addEventListener("touchend", function (a) {
        window.clearInterval(c._touch_int);
      });
    b.disabled && this.disable();
    b.init && b.init.call(this, a);
  };
  h.Clickable.prototype.select = function () {
    this.selected = !0;
    -1 == this.element.className.search("wgo-selected") &&
      (this.element.className += " wgo-selected");
  };
  h.Clickable.prototype.unselect = function () {
    this.selected = !1;
    this.element.className = this.element.className.replace(
      " wgo-selected",
      ""
    );
  };
  h.Button = e.extendClass(h.Clickable, function (a, b) {
    var c = (this.element = document.createElement("button"));
    c.className = "wgo-button wgo-button-" + b.name;
    c.title = e.t(b.name);
    this.init(a, b);
  });
  h.Button.prototype.disable = function () {
    h.Button.prototype.super.prototype.disable.call(this);
    this.element.disabled = "disabled";
  };
  h.Button.prototype.enable = function () {
    h.Button.prototype.super.prototype.enable.call(this);
    this.element.disabled = "";
  };
  h.MenuItem = e.extendClass(h.Clickable, function (a, b) {
    var c = (this.element = document.createElement("div"));
    c.className = "wgo-menu-item wgo-menu-item-" + b.name;
    c.title = e.t(b.name);
    c.innerHTML = c.title;
    this.init(a, b);
  });
  h.MoveNumber = e.extendClass(h.Widget, function (a) {
    this.element = document.createElement("form");
    this.element.className = "wgo-player-mn-wrapper";
    var b = (this.move = document.createElement("input"));
    b.type = "text";
    b.value = "0";
    b.maxlength = 3;
    b.className = "wgo-player-mn-value";
    this.element.appendChild(b);
    this.element.onsubmit = b.onchange = function (a) {
      a.goTo(this.getValue());
      return !1;
    }.bind(this, a);
    a.addEventListener(
      "update",
      function (a) {
        this.setValue(a.path.m);
      }.bind(this)
    );
    a.addEventListener("kifuLoaded", this.enable.bind(this));
    a.addEventListener("frozen", this.disable.bind(this));
    a.addEventListener("unfrozen", this.enable.bind(this));
  });
  h.MoveNumber.prototype.disable = function () {
    h.MoveNumber.prototype.super.prototype.disable.call(this);
    this.move.disabled = "disabled";
  };
  h.MoveNumber.prototype.enable = function () {
    h.MoveNumber.prototype.super.prototype.enable.call(this);
    this.move.disabled = "";
  };
  h.MoveNumber.prototype.setValue = function (a) {
    this.move.value = a;
  };
  h.MoveNumber.prototype.getValue = function () {
    return parseInt(this.move.value);
  };
  var c = function (a) {
    if (a._menu_tmp) delete a._menu_tmp;
    else {
      if (!a.menu)
        for (d in ((a.menu = document.createElement("div")),
        (a.menu.className = "wgo-player-menu"),
        (a.menu.style.position = "absolute"),
        (a.menu.style.display = "none"),
        this.element.parentElement.appendChild(a.menu),
        g.menu)) {
          var b = new g.menu[d].constructor(a, g.menu[d].args, !0);
          b.appendTo(a.menu);
        }
      if ("none" != a.menu.style.display)
        return (
          (a.menu.style.display = "none"),
          document.removeEventListener("click", a._menu_ev),
          delete a._menu_ev,
          this.unselect(),
          !1
        );
      a.menu.style.display = "block";
      b = this.element.offsetTop;
      var d = this.element.offsetLeft;
      this.element.parentElement.parentElement.parentElement.parentElement ==
      a.regions.bottom.wrapper
        ? ((a.menu.style.left = d + "px"),
          (a.menu.style.top = b - a.menu.offsetHeight + 1 + "px"))
        : ((a.menu.style.left = d + "px"),
          (a.menu.style.top = b + this.element.offsetHeight + "px"));
      a._menu_ev = c.bind(this, a);
      a._menu_tmp = !0;
      document.addEventListener("click", a._menu_ev);
      return !0;
    }
  };
  g.menu = [
    {
      constructor: h.MenuItem,
      args: {
        name: "switch-coo",
        togglable: !0,
        click: function (a) {
          a.setCoordinates(!a.coordinates);
          return a.coordinates;
        },
        init: function (a) {
          a.coordinates && this.select();
        }
      }
    }
  ];
  g.widgets = [
    {
      constructor: h.Group,
      args: {
        name: "left",
        widgets: [
          {
            constructor: h.Button,
            args: { name: "menu", togglable: !0, click: c }
          }
        ]
      }
    },
    {
      constructor: h.Group,
      args: {
        name: "right",
        widgets: [
          {
            constructor: h.Button,
            args: {
              name: "about",
              click: function (a) {
                a.showMessage(e.t("about-text"));
              }
            }
          }
        ]
      }
    },
    {
      constructor: h.Group,
      args: {
        name: "control",
        widgets: [
          {
            constructor: h.Button,
            args: {
              name: "first",
              disabled: !0,
              init: function (a) {
                a.addEventListener("update", l.bind(this));
                a.addEventListener("frozen", d.bind(this));
                a.addEventListener("unfrozen", b.bind(this));
              },
              click: function (a) {
                a.first();
              }
            }
          },
          {
            constructor: h.Button,
            args: {
              name: "multiprev",
              disabled: !0,
              multiple: !0,
              init: function (a) {
                a.addEventListener("update", l.bind(this));
                a.addEventListener("frozen", d.bind(this));
                a.addEventListener("unfrozen", b.bind(this));
              },
              click: function (a) {
                var b = e.clone(a.kifuReader.path);
                b.m -= 10;
                a.goTo(b);
              }
            }
          },
          {
            constructor: h.Button,
            args: {
              name: "previous",
              disabled: !0,
              multiple: !0,
              init: function (a) {
                a.addEventListener("update", l.bind(this));
                a.addEventListener("frozen", d.bind(this));
                a.addEventListener("unfrozen", b.bind(this));
              },
              click: function (a) {
                a.previous();
              }
            }
          },
          { constructor: h.MoveNumber },
          {
            constructor: h.Button,
            args: {
              name: "next",
              disabled: !0,
              multiple: !0,
              init: function (a) {
                a.addEventListener("update", f.bind(this));
                a.addEventListener("frozen", d.bind(this));
                a.addEventListener("unfrozen", b.bind(this));
              },
              click: function (a) {
                a.next();
              }
            }
          },
          {
            constructor: h.Button,
            args: {
              name: "multinext",
              disabled: !0,
              multiple: !0,
              init: function (a) {
                a.addEventListener("update", f.bind(this));
                a.addEventListener("frozen", d.bind(this));
                a.addEventListener("unfrozen", b.bind(this));
              },
              click: function (a) {
                var b = e.clone(a.kifuReader.path);
                b.m += 10;
                a.goTo(b);
              }
            }
          },
          {
            constructor: h.Button,
            args: {
              name: "last",
              disabled: !0,
              init: function (a) {
                a.addEventListener("update", f.bind(this));
                a.addEventListener("frozen", d.bind(this));
                a.addEventListener("unfrozen", b.bind(this));
              },
              click: function (a) {
                a.last();
              }
            }
          }
        ]
      }
    }
  ];
  k = e.BasicPlayer.layouts;
  k.right_top.top.push("Control");
  k.right.right.push("Control");
  k.one_column.top.push("Control");
  k.no_comment.bottom.push("Control");
  k.minimal.bottom.push("Control");
  k = {
    about: "About",
    first: "First",
    multiprev: "10 moves back",
    previous: "Previous",
    next: "Next",
    multinext: "10 moves forward",
    last: "Last",
    "switch-coo": "Display coordinates",
    menu: "Menu"
  };
  for (var q in k) e.i18n.en[q] = k[q];
  e.BasicPlayer.component.Control = g;
})(WGo);
(function (e) {
  var k = function (e, g) {
      this.player.frozen ||
        (this._lastX == e && this._lastY == g) ||
        ((this._lastX = e),
        (this._lastY = g),
        this._last_mark && this.board.removeObject(this._last_mark),
        -1 != e && -1 != g && this.player.kifuReader.game.isValid(e, g)
          ? ((this._last_mark = {
              type: "outline",
              x: e,
              y: g,
              c: this.player.kifuReader.game.turn
            }),
            this.board.addObject(this._last_mark))
          : delete this._last_mark);
    },
    g = function () {
      this._last_mark &&
        (this.board.removeObject(this._last_mark),
        delete this._last_mark,
        delete this._lastX,
        delete this._lastY);
    };
  e.Player.Editable = {};
  e.Player.Editable = function (e, g) {
    this.player = e;
    this.board = g;
    this.editMode = !1;
  };
  e.Player.Editable.prototype.set = function (h) {
    if (!this.editMode && h)
      (this.originalReader = this.player.kifuReader),
        (this.player.kifuReader = new e.KifuReader(
          this.player.kifu.clone(),
          this.originalReader.rememberPath,
          this.originalReader.allow_illegal,
          this.originalReader.allow_illegal
        )),
        this.player.kifuReader.goTo(this.originalReader.path),
        (this._ev_click = this._ev_click || this.play.bind(this)),
        (this._ev_move = this._ev_move || k.bind(this)),
        (this._ev_out = this._ev_out || g.bind(this)),
        this.board.addEventListener("click", this._ev_click),
        this.board.addEventListener("mousemove", this._ev_move),
        this.board.addEventListener("mouseout", this._ev_out),
        (this.editMode = !0);
    else if (this.editMode && !h) {
      this.originalReader.goTo(this.player.kifuReader.path);
      h = this.originalReader;
      for (
        var l = this.player.kifuReader.getPosition(),
          f = this.originalReader.getPosition(),
          d = l.size,
          b = [],
          c = [],
          q = 0;
        q < d * d;
        q++
      )
        l.schema[q] && !f.schema[q]
          ? c.push({ x: Math.floor(q / d), y: q % d })
          : l.schema[q] != f.schema[q] &&
            b.push({ x: Math.floor(q / d), y: q % d, c: f.schema[q] });
      h.change = { add: b, remove: c };
      this.player.kifuReader = this.originalReader;
      this.player.update(!0);
      this.board.removeEventListener("click", this._ev_click);
      this.board.removeEventListener("mousemove", this._ev_move);
      this.board.removeEventListener("mouseout", this._ev_out);
      this.editMode = !1;
    }
  };
  e.Player.Editable.prototype.play = function (g, k) {
    !this.player.frozen &&
      this.player.kifuReader.game.isValid(g, k) &&
      (this.player.kifuReader.node.appendChild(
        new e.KNode({
          move: { x: g, y: k, c: this.player.kifuReader.game.turn },
          _edited: !0
        })
      ),
      this.player.next(this.player.kifuReader.node.children.length - 1));
  };
  e.BasicPlayer &&
    e.BasicPlayer.component.Control &&
    e.BasicPlayer.component.Control.menu.push({
      constructor: e.BasicPlayer.control.MenuItem,
      args: {
        name: "editmode",
        togglable: !0,
        click: function (g) {
          this._editable = this._editable || new e.Player.Editable(g, g.board);
          this._editable.set(!this._editable.editMode);
          return this._editable.editMode;
        },
        init: function (e) {
          var g = this;
          e.addEventListener("frozen", function (f) {
            (g._disabled = g.disabled) || g.disable();
          });
          e.addEventListener("unfrozen", function (f) {
            g._disabled || g.enable();
            delete g._disabled;
          });
        }
      }
    });
  e.i18n.en.editmode = "Edit mode";
})(WGo);
(function (e) {
  var k = function (f, d, b, c) {
      this.originalPosition = f;
      this.position = f.clone();
      this.board = d;
      this.komi = b;
      this.output = c;
    },
    g = (k.state = {
      UNKNOWN: 0,
      BLACK_STONE: 1,
      WHITE_STONE: -1,
      BLACK_CANDIDATE: 2,
      WHITE_CANDIDATE: -2,
      BLACK_NEUTRAL: 3,
      WHITE_NEUTRAL: -3,
      NEUTRAL: 4
    }),
    h = function (f, d, b, c, e) {
      var a = f.get(d, b);
      void 0 !== a &&
        a != c &&
        a != e &&
        (f.set(d, b, c),
        h(f, d - 1, b, c, e),
        h(f, d, b - 1, c, e),
        h(f, d + 1, b, c, e),
        h(f, d, b + 1, c, e));
    },
    l = function (e, d, b, c, g) {
      var a = d.get(b, c);
      e.get(b, c) != a &&
        (e.set(b, c, a),
        l(e, d, b - 1, c, g),
        l(e, d, b, c - 1, g),
        l(e, d, b + 1, c, g),
        l(e, d, b, c + 1, g));
    };
  k.prototype.start = function () {
    this.calculate();
    this.saved_state = this.board.getState();
    this.displayScore();
    this._click = function (f, d) {
      var b = this.originalPosition.get(f, d);
      b == e.W
        ? this.position.get(f, d) == g.WHITE_STONE
          ? h(this.position, f, d, g.BLACK_CANDIDATE, g.BLACK_STONE)
          : (l(this.position, this.originalPosition, f, d, g.BLACK_STONE),
            this.calculate())
        : b == e.B
        ? this.position.get(f, d) == g.BLACK_STONE
          ? h(this.position, f, d, g.WHITE_CANDIDATE, g.WHITE_STONE)
          : (l(this.position, this.originalPosition, f, d, g.WHITE_STONE),
            this.calculate())
        : ((b = this.position.get(f, d)),
          b == g.BLACK_CANDIDATE
            ? this.position.set(f, d, g.BLACK_NEUTRAL)
            : b == g.WHITE_CANDIDATE
            ? this.position.set(f, d, g.WHITE_NEUTRAL)
            : b == g.BLACK_NEUTRAL
            ? this.position.set(f, d, g.BLACK_CANDIDATE)
            : b == g.WHITE_NEUTRAL &&
              this.position.set(f, d, g.WHITE_CANDIDATE));
      this.board.restoreState({ objects: e.clone(this.saved_state.objects) });
      this.displayScore();
    }.bind(this);
    this.board.addEventListener("click", this._click);
  };
  k.prototype.end = function () {
    this.board.restoreState({ objects: e.clone(this.saved_state.objects) });
    this.board.removeEventListener("click", this._click);
  };
  k.prototype.displayScore = function () {
    for (
      var f = [], d = [], b = [], c = [], h = [], a = 0;
      a < this.position.size;
      a++
    )
      for (var k = 0; k < this.position.size; k++)
        (s = this.position.get(a, k)),
          (t = this.originalPosition.get(a, k)),
          s == g.BLACK_CANDIDATE
            ? f.push({ x: a, y: k, type: "mini", c: e.B })
            : s == g.WHITE_CANDIDATE
            ? d.push({ x: a, y: k, type: "mini", c: e.W })
            : s == g.NEUTRAL && b.push({ x: a, y: k }),
          t == e.W && s != g.WHITE_STONE
            ? h.push({ x: a, y: k, type: "outline", c: e.W })
            : t == e.B &&
              s != g.BLACK_STONE &&
              c.push({ x: a, y: k, type: "outline", c: e.B });
    for (a = 0; a < c.length; a++) this.board.removeObjectsAt(c[a].x, c[a].y);
    for (a = 0; a < h.length; a++) this.board.removeObjectsAt(h[a].x, h[a].y);
    this.board.addObject(h);
    this.board.addObject(c);
    this.board.addObject(f);
    this.board.addObject(d);
    b = "<p style='font-weight: bold;'>" + e.t("RE") + "</p>";
    a = f.length + h.length + this.originalPosition.capCount.black;
    k =
      d.length +
      c.length +
      this.originalPosition.capCount.white +
      parseFloat(this.komi);
    b +=
      "<p>" +
      e.t("black") +
      ": " +
      f.length +
      " + " +
      (h.length + this.originalPosition.capCount.black) +
      " = " +
      a +
      "</br>";
    b +=
      e.t("white") +
      ": " +
      d.length +
      " + " +
      (c.length + this.originalPosition.capCount.white) +
      " + " +
      this.komi +
      " = " +
      k +
      "</p>";
    b =
      a > k
        ? b + ("<p style='font-weight: bold;'>" + e.t("bwin", a - k) + "</p>")
        : b + ("<p style='font-weight: bold;'>" + e.t("wwin", k - a) + "</p>");
    this.output(b);
  };
  k.prototype.calculate = function () {
    var e, d;
    var b = this.position;
    for (d = !0; d; ) {
      d = !1;
      for (var c = 0; c < b.size; c++)
        for (var k = 0; k < b.size; k++) {
          var a = b.get(k, c);
          if (
            a == g.UNKNOWN ||
            a == g.BLACK_CANDIDATE ||
            a == g.WHITE_CANDIDATE
          ) {
            var h = [
              b.get(k - 1, c),
              b.get(k, c - 1),
              b.get(k + 1, c),
              b.get(k, c + 1)
            ];
            var m = (e = !1);
            for (var l = 0; 4 > l; l++)
              h[l] == g.BLACK_STONE || h[l] == g.BLACK_CANDIDATE
                ? (e = !0)
                : h[l] == g.WHITE_STONE || h[l] == g.WHITE_CANDIDATE
                ? (m = !0)
                : h[l] == g.NEUTRAL && (m = e = !0);
            h = !1;
            e && m
              ? (h = g.NEUTRAL)
              : e
              ? (h = g.BLACK_CANDIDATE)
              : m && (h = g.WHITE_CANDIDATE);
            h && a != h && ((d = !0), b.set(k, c, h));
          }
        }
    }
  };
  e.ScoreMode = k;
  e.BasicPlayer &&
    e.BasicPlayer.component.Control &&
    e.BasicPlayer.component.Control.menu.push({
      constructor: e.BasicPlayer.control.MenuItem,
      args: {
        name: "scoremode",
        togglable: !0,
        click: function (f) {
          if (this.selected)
            return (
              f.setFrozen(!1),
              this._score_mode.end(),
              delete this._score_mode,
              f.notification(),
              f.help(),
              !1
            );
          f.setFrozen(!0);
          f.help("<p>" + e.t("help_score") + "</p>");
          this._score_mode = new e.ScoreMode(
            f.kifuReader.game.position,
            f.board,
            f.kifu.info.KM || 0.5,
            f.notification
          );
          this._score_mode.start();
          return !0;
        }
      }
    });
  e.i18n.en.scoremode = "Count score";
  e.i18n.en.score = "Score";
  e.i18n.en.bwin = "Black wins by $ points.";
  e.i18n.en.wwin = "White wins by $ points.";
  e.i18n.en.help_score =
    "Click on stones to mark them dead or alive. You can also set and unset territory points by clicking on them. Territories must be completely bordered.";
})(WGo);
(function (e, k) {
  var g = { active: !0, query: {} },
    h = function (e) {
      try {
        g.query = JSON.parse(
          '{"' + window.location.hash.substr(1).replace("=", '":') + "}"
        );
      } catch (d) {
        g.query = {};
      }
    };
  window.addEventListener("hashchange", function () {
    if ("" != window.location.hash && g.active) {
      h();
      for (var e in g.query) {
        var d = document.getElementById(e);
        d && d._wgo_player && d._wgo_player.goTo(l);
      }
    }
  });
  window.addEventListener("DOMContentLoaded", function () {
    "" != window.location.hash && g.active && h();
  });
  window.addEventListener("load", function () {
    if ("" != window.location.hash && g.active)
      for (var e in g.query) {
        var d = document.getElementById(e);
        if (d && d._wgo_player) {
          d.scrollIntoView();
          break;
        }
      }
  });
  var l = function () {
    if (g.query[this.element.id]) return g.query[this.element.id].goto;
  };
  e.Player.default.move = l;
  e.BasicPlayer &&
    e.BasicPlayer.component.Control &&
    e.BasicPlayer.component.Control.menu.push({
      constructor: e.BasicPlayer.control.MenuItem,
      args: {
        name: "permalink",
        click: function (f) {
          var d =
            location.href.split("#")[0] +
            "#" +
            f.element.id +
            '={"goto":' +
            JSON.stringify(f.kifuReader.path) +
            "}";
          f.showMessage(
            "<h1>" +
              e.t("permalink") +
              '</h1><p><input class="wgo-permalink" type="text" value=\'' +
              d +
              '\' onclick="this.select(); event.stopPropagation()"/></p>'
          );
        }
      }
    });
  e.Player.permalink = g;
  e.i18n.en.permalink = "Permanent link";
})(WGo);
