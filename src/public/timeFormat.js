import moment from "moment";
function getDate(time) {
  if (time) {
    if (moment(time).isAfter("0001-01-02")) {
      return moment(time).format("YYYY-MM-DD");
    } else {
      return "";
    }
  } else {
    return "";
  }
}
function getTime(time) {
  if (time) {
    if (moment(time).isAfter("0001-01-02")) {
      return moment(time).format("YYYY-MM-DD HH:mm:ss");
    } else {
      return "";
    }
  } else {
    return "";
  }
}
function getTimeYMDHM(time) {
  if (moment(time).isAfter("0001-01-02")) {
    return moment(time).format("YYYY-MM-DD HH:mm");
  } else {
    return "";
  }
}
function getCustTime(time, format) {
  if (moment(time).isAfter("0001-01-02")) {
    return moment(time).format(format);
  } else {
    return "";
  }
}
export default {
  GetTime(time) {
    return getTime(time);
  },
  GetTimeYMDHM(time) {
    return getTimeYMDHM(time);
  },
  GetDate(time) {
    return getDate(time);
  },
  GetCustTime(time, format) {
    return getCustTime(time, format);
  }
};
