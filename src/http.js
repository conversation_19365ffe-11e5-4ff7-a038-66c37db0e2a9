import axios from "axios";
import router from "./router";
import storage from "@/public/storage.js";
import configApi from "./config.js";
import AwaitLock from "await-lock";

axios.defaults.timeout = 10000;
axios.defaults.baseURL = configApi.BaseApiUrl;

let lock = new AwaitLock();
axios.interceptors.request.use(
  async (config) => {
    if (config.type != "live") {
      config.url = configApi.BaseApiUrl + config.url;
    } else {
      config.headers["live-token"] = `${storage.$getStroage("liveToken")}`;
      config.url = "https://tg-api.estar-go.com" + config.url;
      // config.url = '/api' + config.url;
      // config.headers['student_id'] = `${storage.$getStroage("studentId")}`;
    }

    if (lock.acquired) {
      await lock.acquireAsync({ timeout: 10000 });
    }
    if (config.url == "/api/v1/student/info") {
      lock.tryAcquire();
    }
    if (config.url == "/api/v3/game-service/play/apply-score") {
      axios.defaults.timeout = 30000;
    }

    if (storage.$getStroage("user_token")) {
      config.headers.token = `${storage.$getStroage("user_token")}`;
    }

    return config;
  },
  (err) => {
    return Promise.reject(err);
  }
);

axios.interceptors.response.use(
  (response) => {
    if (lock.acquired) {
      lock.release();
    }

    return response;
  },
  (error) => {
    if (lock.acquired) {
      lock.release();
    }
    if (error.response) {
      if (
        error.response.status === 401 &&
        error.response.config.type === "live"
      ) {
        storage.$removeStroage("liveToken");
        // this.$bus.emit('liveToken');
        // window.location.reload();
        return;
      }
      if (error.response.status === 401) {
        // 401 清除token信息并跳转到登录页面
        storage.$removeStroage("user_token");
        storage.$removeStroage("liveToken");
        // 只有在当前路由不是登录页面才跳转
        router.replace({
          path: "/smsLogin",
          query: {
            redirect: encodeURIComponent(window.location.href)
          }
        });
      }
    }
    return Promise.reject(error.response.data);
  }
);

export default axios;
