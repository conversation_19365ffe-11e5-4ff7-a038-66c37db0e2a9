const { notarize } = require('electron-notarize')
const { isAirSchool } = require("./src/type");

exports.default = async function notarizing (context) {
  const { electronPlatformName, appOutDir } = context
  if (electronPlatformName !== 'darwin') {
    return
  }

  const appName = context.packager.appInfo.productFilename

  return await notarize({
    appBundleId:  isAirSchool() ? 'com.goelfin.school' : 'com.elf-go.HigoApp', //'com.goelfin.school'
    appPath: `${appOutDir}/${appName}.app`,
    appleId: '<EMAIL>',
    appleIdPassword: 'stbg-xtiq-kyug-igoy',
    teamId: 'QCFW6P26AQ',
    tool: 'notarytool'
  })
}
// 浅拷贝
function shallowCopy(obj){
  
  if(typeof obj !== 'object' || obj === null){
    return obj
  }
  const newObj = {}
  for(let key in obj){
    newObj[key] = obj[key]
  }
  return newObj
}