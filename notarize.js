const { notarize } = require('electron-notarize')
const { isAirSchool } = require("./src/type");

exports.default = async function notarizing (context) {
  const { electronPlatformName, appOutDir } = context
  if (electronPlatformName !== 'darwin') {
    return
  }

  const appName = context.packager.appInfo.productFilename

  return await notarize({
    appBundleId:  isAirSchool() ? 'com.goelfin.school' : 'com.elf-go.HigoApp', //'com.goelfin.school'
    appPath: `${appOutDir}/${appName}.app`,
    appleId: '<EMAIL>',
    appleIdPassword: 'stbg-xtiq-kyug-igoy',
    teamId: 'QCFW6P26AQ',
    tool: 'notarytool'
  })
}